/**
 * Speed UI 配置使用示例
 */

import { setSpeedUIConfig } from '../packages/ui/src/config/speedConfig'

// 示例1：修改全局预设尺寸
setSpeedUIConfig({
  medium: {
    base: 52,           // 修改中等尺寸的基础高度
    fontSize: 17,       // 修改字体大小
    paddingTop: 26,     // 修改上边距
    paddingBottom: 5,   // 修改下边距
    labelFloatingTop: 9,
    labelFontSize: 15,
    prefixPadding: 5,
    prefixPaddingLeft: 18,
    prefixPaddingDefault: 22,
    prefixPaddingSquare: 5,
    gap: 14,
  },
  large: {
    base: 60,
    fontSize: 19,
    paddingTop: 30,
    paddingBottom: 5,
    labelFloatingTop: 11,
    labelFontSize: 17,
    prefixPadding: 7,
    prefixPaddingLeft: 22,
    prefixPaddingDefault: 27,
    prefixPaddingSquare: 7,
    gap: 15,
  }
})

// 示例2：修改自定义尺寸映射函数
setSpeedUIConfig({
  customSizeMapper: (size: number) => {
    const ratio = size / 50 // 改变基准从 48 到 50
    return {
      base: size,
      fontSize: Math.round(17 * ratio), // 基础字体从 16 改为 17
      paddingTop: Math.round(26 * ratio), // 基础上边距从 24 改为 26
      paddingBottom: Math.round(5 * ratio), // 基础下边距从 4 改为 5
      labelFloatingTop: Math.round(9 * ratio),
      labelFontSize: Math.round(15 * ratio),
      prefixPadding: Math.round(5 * ratio),
      prefixPaddingLeft: Math.round(18 * ratio),
      prefixPaddingDefault: Math.round(22 * ratio),
      prefixPaddingSquare: Math.round(5 * ratio),
      gap: Math.round(14 * ratio),
    }
  }
})

// 示例3：在组件中使用
/*
<template>
  <!-- 使用预设尺寸，不会生成内联样式 -->
  <sp-input-field size="medium" label="预设尺寸" />
  <sp-input-field size="large" label="大尺寸" />
  
  <!-- 使用数字尺寸，会生成少量内联样式 -->
  <sp-input-field :size="60" label="自定义尺寸" />
</template>
*/
