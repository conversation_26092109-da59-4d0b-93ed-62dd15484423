# Speed UI Case 构建工具

这个构建工具用于处理 Speed UI 组件的 `.case.vue` 文件，将自定义标签格式转换为类似 Element UI 文档风格的展示组件。

## 功能特性

- 🔄 自动转换 `.case.vue` 文件为文档风格的展示组件
- 📝 支持 Markdown 文档渲染
- 💻 自动生成 TypeScript 和 JavaScript 代码示例
- 🎨 美观的文档界面，类似 Element UI 风格
- 👀 文件监听，自动重新构建
- 🚀 自动生成 playground 路由配置
- 📦 使用根目录依赖，无需额外配置

## 效果展示

生成的组件包含以下区域：
1. **标题和描述** - 显示组件名称和 Markdown 文档
2. **演示区域** - 实时展示组件效果
3. **代码展示** - 可切换的 TypeScript/JavaScript 代码示例
4. **交互按钮** - 显示/隐藏代码的切换按钮

## 文件格式

### .case.vue 文件结构

```vue
<text>
# 组件标题

这里可以写 Markdown 格式的文档内容。

## 使用方法

```vue
<SpButton>示例</SpButton>
```

支持完整的 Markdown 语法，包括代码块、列表、链接等。
</text>

<case-ele>
  <div class="demo-container">
    <SpButton>演示按钮</SpButton>
    <SpButton type="primary">主要按钮</SpButton>
  </div>
</case-ele>

<script>
import SpButton from '../Button.vue'

const handleClick = () => {
  console.log('按钮被点击')
}
</script>

<style scoped>
.demo-container {
  display: flex;
  gap: 12px;
}
</style>
```

### 标签说明

- `<text>` - Markdown 文档内容，会被渲染为 HTML 并显示在页面顶部
- `<case-ele>` - 演示区域，相当于 Vue 的 `<template>`，展示组件的实际效果
- `<script>` - JavaScript 逻辑代码，会被自动转换为 TypeScript 和 JavaScript 两个版本
- `<style>` - CSS 样式，应用于演示区域

## 使用方法

### 1. 安装依赖

首先确保根目录已安装必要的依赖：

```bash
# 在项目根目录运行
pnpm install
```

### 2. 一次性构建

```bash
# 在项目根目录运行
npm run build:cases
# 或者
pnpm build:cases
```

### 3. 监听模式（推荐开发时使用）

```bash
# 在项目根目录运行
npm run watch:cases
# 或者
pnpm watch:cases
```

监听模式会：
- 自动监听所有 `.case.vue` 文件的变化
- 实时重新构建修改的文件
- 自动生成/更新路由配置
- 自动删除对应的输出文件当源文件被删除

### 4. 直接使用构建工具

```bash
# 在项目根目录运行
node build/case-processor.js

# 或运行监听
node build/watch-cases.js
```

## 输出结果

构建工具会将 `xxx.case.vue` 文件转换为 playground 中的展示组件：

### 生成的文件结构

```
playground/src/views/components/
├── Button/
│   ├── default.vue          # 从 default.case.vue 生成
│   └── size.vue            # 从 size.case.vue 生成
└── ...

playground/src/router/
└── components.ts           # 自动生成的路由配置
```

### 生成的组件特性

1. **文档区域** - 渲染 `<text>` 标签中的 Markdown 内容
2. **演示区域** - 显示 `<case-ele>` 标签中的组件演示
3. **代码展示** - 自动生成 TypeScript 和 JavaScript 两个版本的代码
4. **交互功能** - 可以切换显示/隐藏代码，切换 TS/JS 代码版本

### 路由配置

自动生成的路由配置包含：
- `componentRoutes` - 所有组件页面的路由配置
- `componentNavigation` - 组件导航数据，可用于生成侧边栏

## 目录结构

```
speed-ui/
├── build/
│   ├── case-processor.js    # 构建处理器
│   ├── watch-cases.js       # 文件监听器
│   └── README.md           # 说明文档
├── packages/ui/src/components/
│   └── Button/
│       ├── Button.vue
│       ├── index.ts
│       └── cases/
│           ├── default.case.vue     # 源文件
│           └── size.case.vue        # 源文件
├── playground/
│   └── src/
│       ├── views/components/        # 生成的展示组件
│       └── router/components.ts     # 生成的路由配置
├── package.json            # 包含构建工具依赖
└── ...
```

## 样式特性

生成的组件使用现代化的设计风格：

- **响应式布局** - 最大宽度 1200px，自动居中
- **清晰的层次** - 标题、描述、演示、代码区域层次分明
- **优雅的交互** - 平滑的动画过渡和悬停效果
- **代码高亮** - 深色主题的代码展示区域
- **标签切换** - TypeScript/JavaScript 代码版本切换

## 依赖

构建工具依赖以下包（已添加到根目录的 `package.json`）：

- `marked` - Markdown 解析器
- `chokidar` - 文件监听器

## 注意事项

1. 确保 `.case.vue` 文件的标签格式正确
2. `<text>` 标签中的内容会被作为 Markdown 解析
3. `<case-ele>` 标签中的内容会被作为 Vue template 处理
4. 生成的组件文件不要手动编辑，会被自动覆盖
5. 构建工具从项目根目录运行，使用根目录的依赖
6. 路由配置会自动更新，可以直接在 playground 中访问组件页面

## 开发工作流

1. 在组件的 `cases/` 目录下创建 `.case.vue` 文件
2. 运行 `npm run watch:cases` 启动监听模式
3. 编辑 `.case.vue` 文件，保存后自动生成展示组件
4. 在 playground 中访问对应路由查看效果
5. 重复步骤 3-4 进行开发和调试 