<template>
  <div class="theme-example">
    <h1>Speed UI 主题自定义示例</h1>

    <!-- 方案 1: 使用主题切换组件 -->
    <section class="example-section">
      <h2>方案 1: 使用主题切换组件</h2>
      <SpThemeSwitcher
        :show-presets="true"
        :show-custom="true"
        :auto-apply-custom="true"
        @theme-change="handleThemeChange"
      />
    </section>

    <!-- 方案 2: 使用组合式函数 -->
    <section class="example-section">
      <h2>方案 2: 使用组合式函数</h2>
      <div class="controls">
        <button @click="setBlueTheme">设置蓝色主题</button>
        <button @click="setCustomTheme">设置自定义主题</button>
        <button @click="resetTheme">重置主题</button>
      </div>
      <div class="theme-info">
        <p>当前主色调: {{ currentTheme.primaryColor }}</p>
      </div>
    </section>

    <!-- 方案 3: ConfigProvider -->
    <section class="example-section">
      <h2>方案 3: ConfigProvider 局部主题</h2>
      <SpConfigProvider :theme="localTheme">
        <div class="local-theme-demo">
          <SpButton primary>局部主题按钮</SpButton>
          <SpButton secondary>次要按钮</SpButton>
          <SpButton dashed>虚线按钮</SpButton>
        </div>
      </SpConfigProvider>
      <div class="controls">
        <button @click="changeLocalTheme('red')">红色局部主题</button>
        <button @click="changeLocalTheme('green')">绿色局部主题</button>
      </div>
    </section>

    <!-- 组件演示 -->
    <section class="example-section">
      <h2>组件演示</h2>
      <div class="component-demo">
        <div class="demo-group">
          <h3>按钮组件</h3>
          <SpButton primary>主要按钮</SpButton>
          <SpButton secondary>次要按钮</SpButton>
          <SpButton dashed>虚线按钮</SpButton>
          <SpButton text>文本按钮</SpButton>
          <SpButton link>链接按钮</SpButton>
        </div>

        <div class="demo-group">
          <h3>按钮状态</h3>
          <SpButton primary type="success">成功按钮</SpButton>
          <SpButton primary type="warning">警告按钮</SpButton>
          <SpButton primary type="danger">危险按钮</SpButton>
          <SpButton primary disabled>禁用按钮</SpButton>
          <SpButton primary loading>加载按钮</SpButton>
        </div>

        <div class="demo-group">
          <h3>开关组件</h3>
          <SpSwitch v-model="switchValue" />
          <SpSwitch v-model="switchValue" size="large" />
          <SpSwitch v-model="switchValue" disabled />
        </div>

        <div class="demo-group">
          <h3>输入框组件</h3>
          <SpInput v-model="inputValue" placeholder="请输入内容" />
          <SpInput v-model="inputValue" clearable placeholder="可清空输入框" />
          <SpInput v-model="inputValue" disabled placeholder="禁用输入框" />
        </div>
      </div>
    </section>

    <!-- CSS 变量演示 -->
    <section class="example-section">
      <h2>CSS 变量演示</h2>
      <div class="css-vars-demo">
        <div class="color-box primary">主色调</div>
        <div class="color-box primary-hover">悬停色</div>
        <div class="color-box primary-active">激活色</div>
        <div class="color-box primary-disabled">禁用色</div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  SpButton, 
  SpSwitch, 
  SpInput, 
  SpThemeSwitcher, 
  SpConfigProvider,
  useTheme,
  type ThemeConfig 
} from '@speed-ui/ui'

// 使用主题
const { theme: currentTheme, setTheme, setPrimaryColor, resetTheme: resetGlobalTheme } = useTheme()

// 组件状态
const switchValue = ref(false)
const inputValue = ref('')

// 局部主题
const localTheme = ref<ThemeConfig>({
  primaryColor: '#ff6b6b',
  successColor: '#51cf66',
  warningColor: '#ffd43b',
  dangerColor: '#ff6b6b',
})

// 主题变化处理
const handleThemeChange = (theme: any) => {
  console.log('主题已切换:', theme)
}

// 设置蓝色主题
const setBlueTheme = () => {
  setTheme('blue')
}

// 设置自定义主题
const setCustomTheme = () => {
  setTheme({
    primaryColor: '#ff6b6b',
    successColor: '#51cf66',
    warningColor: '#ffd43b',
    dangerColor: '#ff6b6b',
  })
}

// 重置主题
const resetTheme = () => {
  resetGlobalTheme()
}

// 改变局部主题
const changeLocalTheme = (color: string) => {
  const colors = {
    red: '#ff6b6b',
    green: '#51cf66',
  }
  
  localTheme.value = {
    ...localTheme.value,
    primaryColor: colors[color as keyof typeof colors],
  }
}
</script>

<style scoped>
.theme-example {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.example-section {
  margin-bottom: 3rem;
  padding: 2rem;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  background: #fafbfc;
}

.example-section h2 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.controls button {
  padding: 0.5rem 1rem;
  border: 1px solid var(--sp-color-primary, #667eea);
  border-radius: 4px;
  background: white;
  color: var(--sp-color-primary, #667eea);
  cursor: pointer;
  transition: all 0.3s ease;
}

.controls button:hover {
  background: var(--sp-color-primary, #667eea);
  color: white;
}

.theme-info {
  padding: 1rem;
  background: white;
  border-radius: 4px;
  border: 1px solid #e1e8ed;
}

.local-theme-demo {
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  margin-bottom: 1rem;
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.component-demo {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.demo-group {
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
}

.demo-group h3 {
  margin: 0 0 1rem 0;
  color: #34495e;
  font-size: 1.2rem;
}

.demo-group > * + * {
  margin-left: 0.5rem;
}

.css-vars-demo {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.color-box {
  width: 120px;
  height: 80px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.color-box.primary {
  background-color: var(--sp-color-primary, #667eea);
}

.color-box.primary-hover {
  background-color: var(--sp-color-primary-hover, #7c8aeb);
}

.color-box.primary-active {
  background-color: var(--sp-color-primary-active, #5a6ce8);
}

.color-box.primary-disabled {
  background-color: var(--sp-color-primary-disabled, #b8c5ff);
  color: #666;
}

/* 为主题切换添加平滑过渡 */
* {
  transition: 
    background-color 0.3s ease,
    border-color 0.3s ease,
    color 0.3s ease;
}
</style>
