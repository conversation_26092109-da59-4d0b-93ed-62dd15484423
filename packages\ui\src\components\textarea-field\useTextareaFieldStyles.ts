import { computed, type Ref } from 'vue'
import { useBEM } from '@speed-ui/bem-helper'
import type { TextareaFieldProps } from './types'

interface TextareaFieldLogicState {
  computedDisabled: Ref<boolean>
  isFocused: Ref<boolean>
  hasValue: Ref<boolean>
  isLabelFloating: Ref<boolean>
  validateState: Ref<string>
}

export function useTextareaFieldStyles(
  props: TextareaFieldProps,
  logicState: TextareaFieldLogicState
) {
  const bem = useBEM('textarea-field')

  // ===== 根容器类名 =====
  const rootClasses = computed(() => [
    bem.b(),
    bem.m(props.size || 'medium'),
    bem.m(props.variant || 'default'),
    bem.m(`effect-${props.effect || 'none'}`),
    bem.m(`resize-${props.resize || 'vertical'}`),
    {
      [bem.m('disabled')]: logicState.computedDisabled.value,
      [bem.m('readonly')]: props.readonly,
      [bem.m('focused')]: logicState.isFocused.value,
      [bem.m('error')]:
        props.error || logicState.validateState.value === 'error',
      [bem.m('warning')]: logicState.validateState.value === 'warning',
      [bem.m('success')]: logicState.validateState.value === 'success',
      [bem.m('loading')]: props.loading,
      [bem.m('has-value')]: logicState.hasValue.value,
      [bem.m('label-floating')]: logicState.isLabelFloating.value,
      [bem.m('persistent-label')]: props.persistentLabel,
      [bem.m('has-functions')]: props.clearable || props.showWordLimit,
      [bem.m('autosize')]: !!props.autosize,
    },
  ])

  // ===== 各部分类名 =====
  const wrapperClasses = computed(() => [bem.e('wrapper')])

  const labelClasses = computed(() => [
    bem.e('label'),
    {
      [bem.em('label', 'floating')]: logicState.isLabelFloating.value,
      [bem.em('label', 'required')]: props.required,
    },
  ])

  const textareaClasses = computed(() => [bem.e('inner')])

  const functionsClasses = computed(() => [bem.e('functions')])

  const clearIconClasses = computed(() => [bem.e('clear')])

  const wordCountClasses = computed(() => [bem.e('count')])

  const loadingBarClasses = computed(() => [bem.e('loading-bar')])

  const loadingProgressClasses = computed(() => [bem.e('loading-progress')])

  const helperTextClasses = computed(() => [
    bem.e('helper'),
    {
      [bem.em('helper', 'error')]:
        props.error || logicState.validateState.value === 'error',
      [bem.em('helper', 'warning')]:
        logicState.validateState.value === 'warning',
      [bem.em('helper', 'success')]:
        logicState.validateState.value === 'success',
    },
  ])

  const messageClasses = computed(() => [
    bem.e('message'),
    bem.em('message', logicState.validateState.value || 'error'),
  ])

  return {
    // 类名
    rootClasses,
    wrapperClasses,
    labelClasses,
    textareaClasses,
    functionsClasses,
    clearIconClasses,
    wordCountClasses,
    loadingBarClasses,
    loadingProgressClasses,
    helperTextClasses,
    messageClasses,
  }
}
