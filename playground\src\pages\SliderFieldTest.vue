<template>
  <div class="slider-field-test">
    <h1>SliderField 测试</h1>
    
    <div class="test-section">
      <h2>基础测试（无边框，无标签）</h2>
      <sp-slider-field
        v-model:value="testValue"
        name="test"
        :min="0"
        :max="100"
        show-value
      />
      <p>当前值: {{ testValue }}</p>
      <button @click="testValue = 75">设置为75</button>
      <button @click="testValue = 25">设置为25</button>
    </div>

    <div class="test-section">
      <h2>带前缀后缀（无标签）</h2>
      <sp-slider-field
        v-model:value="testValue2"
        name="volume"
        prefix="🔇"
        suffix="🔊"
        :min="0"
        :max="100"
        show-value
      />
      <p>当前值: {{ testValue2 }}</p>
    </div>

    <div class="test-section">
      <h2>对比：有边框 vs 无边框</h2>
      <div style="margin-bottom: 20px;">
        <h4>纯滑块（无标签，无边框）</h4>
        <sp-slider-field
          v-model:value="testValue3"
          name="pure-slider"
          :min="0"
          :max="100"
          show-value
        />
        <p>当前值: {{ testValue3 }}</p>
      </div>

      <div>
        <h4>带图标的滑块</h4>
        <sp-slider-field
          v-model:value="testValue4"
          name="icon-slider"
          prepend-icon-inner="VolumeDown"
          append-icon-inner="VolumeUp"
          :min="0"
          :max="100"
          show-value
        />
        <p>当前值: {{ testValue4 }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const testValue = ref(30)
const testValue2 = ref(50)
const testValue3 = ref(70)
const testValue4 = ref(40)
</script>

<style scoped>
.slider-field-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-section h2 {
  margin-top: 0;
  color: #333;
}

.test-section p {
  margin-top: 15px;
  color: #666;
}

.test-section button {
  margin: 10px 5px 0 0;
  padding: 8px 16px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.test-section button:hover {
  background: #5a6fd8;
}
</style>
