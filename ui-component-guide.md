# Speed UI 组件编写指南

本指南旨在为 Speed UI 库的组件开发提供一套统一的编写规则和最佳实践，以确保代码的一致性、可维护性、可扩展性以及良好的用户体验。

## 1. 命名约定

- **组件命名**：
  - 遵循 PascalCase (大驼峰命名法)，例如：`SpButton`, `SpInput`, `SpSelect`。
  - 在文件名和目录名中使用 kebab-case (短横线命名法)，例如：`sp-button/index.vue`, `sp-input/style/index.scss`。

- **Sass 变量**：
  - 统一使用小写字母和短横线，并以 `$前缀-` 开始，例如：`$color-primary`, `$font-size-base`, `$spacing-sm`。
  - 避免使用双横线 `$--` 前缀，以避免 `@use` 导入时的潜在问题。

- **Sass 混入 (Mixins)**：
  - 遵循 kebab-case (短横线命名法)，例如：`button-theme`, `create-flex-style`。

- **CSS 类名**：
  - 遵循 BEM (Block-Element-Modifier) 命名规范，以组件名为 Block，Element 使用双下划线 `__`，Modifier 使用双连字符 `--`。
  - 例如：`sp-button`, `sp-button__content`, `sp-button--primary`, `sp-button--disabled`。

## 2. Sass/样式规则

- **使用设计系统变量**：
  - 优先使用 `packages/theme-default/src/common/var.scss` 中定义的 Sass 变量来管理颜色、字体、间距、圆角、阴影、动画等属性。
  - 通过 `@use './common/var.scss' as *;` 导入变量，然后直接使用 `$variable-name`。

- **避免硬编码值**：
  - 尽量减少在组件样式中直接使用像素值、颜色代码等，除非是组件特有的、不可复用的值。

- **样式层级**：
  - 确保样式规则的特异性合理，避免过度嵌套，保持 CSS 的扁平化。
  - 优先使用父子选择器而不是 `!important`。

- **混入 (Mixins) 的使用**：
  - 将可复用的样式块封装为混入，例如 `button-theme` 用于处理按钮的主题色。
  - 混入内部的 CSS 声明必须放置在样式规则（例如选择器）内部，以避免编译错误。

## 3. 组件结构

- **单文件组件 (SFC)**：
  - 优先使用 Vue 的单文件组件 (.vue 文件) 来组织组件的模板、脚本和样式。

- **脚本部分**：
  - 使用 `<script setup>` 语法简化组件逻辑。
  - 遵循 Vue 3 的最佳实践，如使用 `defineProps`, `defineEmits`, `withDefaults` 等编译器宏。

- **样式部分**：
  - 在 `<style>` 标签中使用 `lang="scss"` 指定 Sass 预处理器。
  - 使用 `scoped` 属性限制样式作用域，防止样式污染。

## 4. 可访问性 (Accessibility)

- **语义化 HTML**：
  - 使用正确的 HTML 标签（例如：`button`, `input`, `a`, `ul`, `li`），而不是滥用 `div`。

- **ARIA 属性**：
  - 在必要时添加 ARIA 属性（`aria-label`, `aria-labelledby`, `role` 等），以增强屏幕阅读器和其他辅助技术的支持。

- **键盘导航**：
  - 确保所有可交互组件都能通过键盘（Tab, Enter, Space 等）进行操作。

## 5. 响应式设计

- **移动优先**：
  - 优先为移动设备编写样式，然后逐步扩展到更大的屏幕。

- **媒体查询**：
  - 使用 Sass 的媒体查询 `@media` 来针对不同屏幕尺寸调整样式。

- **弹性布局**：
  - 优先使用 Flexbox 或 Grid 进行布局，以实现更灵活的响应式设计。

## 6. 文档编写

- **组件 README**：
  - 每个组件目录都应该包含一个 `README.md` 文件，详细说明组件的用途、Props、Events、Slots、使用示例和注意事项。

- **JSDoc/TSDoc**：
  - 为组件的 Props、Methods、Events 编写 JSDoc 或 TSDoc 注释，以便生成 API 文档和提供更好的开发体验。

- **示例和演示**：
  - 提供清晰、可运行的代码示例和交互式演示，方便其他开发者理解和使用组件。

---

遵循本指南将有助于我们构建一个高质量、易于维护和扩展的 Speed UI 库。 