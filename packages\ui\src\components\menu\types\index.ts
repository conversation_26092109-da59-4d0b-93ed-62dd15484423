import type { ComputedRef } from 'vue'

export interface MenuProps {
  // 基础属性
  mode?: 'vertical' | 'horizontal' | 'inline'
  theme?: 'light' | 'dark'

  // 选择相关
  selectedKeys?: string[]
  multiple?: boolean
  selectable?: boolean

  // 展开相关
  openKeys?: string[]
  defaultOpenKeys?: string[]

  // 样式相关
  inlineIndent?: number
  expandIcon?: string

  // 状态
  disabled?: boolean
  loading?: boolean
}

export interface MenuItemProps {
  // 基础属性
  itemKey?: string
  title?: string
  icon?: string

  // 状态
  disabled?: boolean
  danger?: boolean

  // 分组相关
  group?: boolean
  level?: number
}

export interface MenuContext {
  props: MenuProps
  selectedKeys: ComputedRef<string[]>
  openKeys: ComputedRef<string[]>
  handleSelect: (key: string) => void
  handleOpenChange: (key: string, open: boolean) => void
}

export interface MenuSelectInfo {
  key: string
  selectedKeys: string[]
}

export interface MenuClickInfo {
  key: string
  domEvent: Event
}
