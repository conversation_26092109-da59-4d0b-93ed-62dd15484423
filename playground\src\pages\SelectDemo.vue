<template>
  <div class="select-demo">
    <h1>Select 选择器演示</h1>

    <!-- 功能说明 -->
    <div class="feature-description">
      <h3>🎯 Select 组件功能</h3>
      <p>SpSelect 组件提供以下功能：</p>
      <ul>
        <li>
          <strong>单选模式</strong>
          : 从选项中选择一个值
        </li>
        <li>
          <strong>多选模式</strong>
          : 可以选择多个值，以标签形式显示
        </li>
        <li>
          <strong>可清除</strong>
          : 支持一键清除所选内容
        </li>
        <li>
          <strong>禁用状态</strong>
          : 禁用整个选择器或单个选项
        </li>
        <li>
          <strong>尺寸变体</strong>
          : small / default / large
        </li>
        <li>
          <strong>验证状态</strong>
          : success / warning / error
        </li>
        <li>
          <strong>表单集成</strong>
          : 完美集成到 Form 组件中
        </li>
      </ul>
    </div>

    <div class="demo-container">
      <section class="demo-section">
        <h3>📋 基础用法</h3>
        <div class="demo-row">
          <div class="demo-item">
            <label>基础单选：</label>
            <sp-select
              v-model:value="basicValue"
              :options="basicOptions"
              placeholder="请选择一个选项"
              style="width: 200px"
            />
            <span class="demo-value">选中值: {{ basicValue || '无' }}</span>
          </div>
        </div>
      </section>

      <section class="demo-section">
        <h3>🔍 可清除</h3>
        <div class="demo-row">
          <div class="demo-item">
            <label>可清除选择器：</label>
            <sp-select
              v-model:value="clearableValue"
              :options="basicOptions"
              placeholder="请选择"
              clearable
              style="width: 200px"
            />
            <span class="demo-value">选中值: {{ clearableValue || '无' }}</span>
          </div>
        </div>
      </section>

      <section class="demo-section">
        <h3>🏷️ 多选模式</h3>
        <div class="demo-row">
          <div class="demo-item">
            <label>多选选择器：</label>
            <sp-select
              v-model:value="multipleValue"
              :options="basicOptions"
              placeholder="请选择多个选项"
              multiple
              clearable
              style="width: 300px"
            />
            <span class="demo-value">
              选中值:
              {{ multipleValue.length ? multipleValue.join(', ') : '无' }}
            </span>
          </div>
        </div>
      </section>

      <section class="demo-section">
        <h3>📏 尺寸变体</h3>
        <div class="demo-col">
          <div class="demo-item">
            <label>小尺寸：</label>
            <sp-select
              v-model:value="sizeValue"
              :options="basicOptions"
              placeholder="小尺寸"
              size="small"
              style="width: 150px"
            />
          </div>
          <div class="demo-item">
            <label>默认尺寸：</label>
            <sp-select
              v-model:value="sizeValue"
              :options="basicOptions"
              placeholder="默认尺寸"
              size="medium"
              style="width: 180px"
            />
          </div>
          <div class="demo-item">
            <label>大尺寸：</label>
            <sp-select
              v-model:value="sizeValue"
              :options="basicOptions"
              placeholder="大尺寸"
              size="large"
              style="width: 220px"
            />
          </div>
        </div>
      </section>

      <section class="demo-section">
        <h3>🚫 禁用状态</h3>
        <div class="demo-row">
          <div class="demo-item">
            <label>禁用选择器：</label>
            <sp-select
              v-model:value="disabledValue"
              :options="basicOptions"
              placeholder="禁用状态"
              disabled
              style="width: 200px"
            />
          </div>
          <div class="demo-item">
            <label>部分选项禁用：</label>
            <sp-select
              v-model:value="partialDisabledValue"
              :options="partialDisabledOptions"
              placeholder="部分禁用"
              style="width: 200px"
            />
          </div>
        </div>
      </section>

      <section class="demo-section">
        <h3>✅ 验证状态</h3>
        <div class="demo-col">
          <div class="demo-item">
            <label>成功状态：</label>
            <sp-select
              v-model:value="validateValue"
              :options="basicOptions"
              placeholder="成功状态"
              validate-state="success"
              validate-message="验证成功"
              style="width: 200px"
            />
          </div>
          <div class="demo-item">
            <label>警告状态：</label>
            <sp-select
              v-model:value="validateValue"
              :options="basicOptions"
              placeholder="警告状态"
              validate-state="warning"
              validate-message="需要注意"
              style="width: 200px"
            />
          </div>
          <div class="demo-item">
            <label>错误状态：</label>
            <sp-select
              v-model:value="validateValue"
              :options="basicOptions"
              placeholder="错误状态"
              validate-state="error"
              validate-message="验证失败"
              style="width: 200px"
            />
          </div>
        </div>
      </section>

      <section class="demo-section">
        <h3>📝 表单集成</h3>
        <sp-form
          @submit="onSubmit"
          @invalid="onInvalid"
        >
          <sp-form-item
            label="选择城市"
            name="city"
            :rules="cityRules"
            required
          >
            <sp-select
              :options="cityOptions"
              placeholder="请选择城市"
              clearable
            />
          </sp-form-item>

          <sp-form-item
            label="选择技能"
            name="skills"
            :rules="skillsRules"
            required
          >
            <sp-select
              :options="skillOptions"
              placeholder="请选择技能"
              multiple
              clearable
            />
          </sp-form-item>

          <sp-form-item name="buttons">
            <sp-button
              type="primary"
              :toggleable="false"
              @click="submitForm"
            >
              提交表单
            </sp-button>
            <sp-button
              type="outline"
              :toggleable="false"
              @click="resetForm"
              style="margin-left: 12px"
            >
              重置表单
            </sp-button>
          </sp-form-item>
        </sp-form>
      </section>
    </div>

    <!-- 提交结果 -->
    <div
      v-if="submitResult"
      class="result-section"
    >
      <h3>{{ submitResult.message }}</h3>

      <div
        v-if="submitResult.status === 'success'"
        class="form-data-display"
      >
        <h4>📋 表单数据：</h4>
        <pre class="json-display">{{
          JSON.stringify(submitResult.formData, null, 2)
        }}</pre>
      </div>

      <div v-else>
        <h4>❌ 验证错误：</h4>
        <pre>{{ JSON.stringify(submitResult.errors, null, 2) }}</pre>
      </div>

      <p class="timestamp">提交时间: {{ submitResult.timestamp }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue'
  import { Select as SpSelect } from '../../../packages/ui/src/components/select'
  import SpForm from '../../../packages/ui/src/components/Form/Form.vue'
  import SpFormItem from '../../../packages/ui/src/components/Form/FormItem.vue'
  import SpButton from '../../../packages/ui/src/components/button/button.vue'

  // 演示数据
  const basicValue = ref('')
  const clearableValue = ref('')
  const multipleValue = ref([])
  const sizeValue = ref('')
  const disabledValue = ref('option1')
  const partialDisabledValue = ref('')
  const validateValue = ref('')
  const submitResult = ref<any>(null)

  // 基础选项
  const basicOptions = [
    { label: '选项一', value: 1 },
    { label: '选项二', value: 'option2' },
    { label: '选项三', value: 'option3' },
    { label: '选项四', value: 'option4' },
    { label: '选项五', value: 'option5' },
  ]

  // 部分禁用选项
  const partialDisabledOptions = [
    { label: '可选择', value: 'enabled1' },
    { label: '已禁用', value: 'disabled1', disabled: true },
    { label: '可选择', value: 'enabled2' },
    { label: '已禁用', value: 'disabled2', disabled: true },
    { label: '可选择', value: 'enabled3' },
  ]

  // 城市选项
  const cityOptions = [
    { label: '北京', value: 'beijing' },
    { label: '上海', value: 'shanghai' },
    { label: '广州', value: 'guangzhou' },
    { label: '深圳', value: 'shenzhen' },
    { label: '杭州', value: 'hangzhou' },
    { label: '成都', value: 'chengdu' },
  ]

  // 技能选项
  const skillOptions = [
    { label: 'JavaScript', value: 'javascript' },
    { label: 'TypeScript', value: 'typescript' },
    { label: 'Vue.js', value: 'vue' },
    { label: 'React', value: 'react' },
    { label: 'Node.js', value: 'nodejs' },
    { label: 'Python', value: 'python' },
  ]

  // 表单引用
  const formRef = ref()

  // 验证规则
  const cityRules = (value: string) => {
    if (!value) return '请选择城市'
    return true
  }

  const skillsRules = (value: string[]) => {
    if (!value || value.length === 0) return '请至少选择一项技能'
    if (value.length > 3) return '最多只能选择3项技能'
    return true
  }

  // 表单事件处理
  const onSubmit = (values: Record<string, any>) => {
    console.log('🎉 表单提交成功! 获取到的数据:', values)

    submitResult.value = {
      status: 'success',
      message: '✅ 表单提交成功！',
      formData: values,
      timestamp: new Date().toLocaleString(),
    }
  }

  const onInvalid = (errors: Record<string, string>) => {
    console.log('表单验证失败:', errors)
    submitResult.value = {
      status: 'error',
      message: '❌ 表单验证失败，请检查输入内容',
      errors: errors,
      timestamp: new Date().toLocaleString(),
    }
  }

  const submitForm = async () => {
    try {
      await formRef.value?.validate()
    } catch (error) {
      console.error('表单提交失败:', error)
    }
  }

  const resetForm = () => {
    formRef.value?.resetFields()
    submitResult.value = null
  }
</script>

<style scoped>
  .select-demo {
    padding: 20px;
    max-width: 900px;
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .select-demo h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 30px;
  }

  .feature-description {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    margin-bottom: 20px;
  }

  .feature-description h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #495057;
  }

  .feature-description p {
    margin-bottom: 10px;
    color: #6c757d;
    line-height: 1.6;
  }

  .feature-description ul {
    margin: 10px 0;
    padding-left: 20px;
  }

  .feature-description li {
    margin-bottom: 8px;
    color: #6c757d;
    line-height: 1.5;
  }

  .feature-description strong {
    color: #495057;
    font-weight: 600;
  }

  .demo-container {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e4e7ed;
  }

  .demo-section {
    margin-bottom: 40px;
  }

  .demo-section:last-child {
    margin-bottom: 0;
  }

  .demo-section h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #606266;
    font-size: 18px;
  }

  .demo-row {
    display: flex;
    gap: 30px;
    margin-bottom: 20px;
    flex-wrap: wrap;
  }

  .demo-col {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 20px;
  }

  .demo-item {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
  }

  .demo-item label {
    font-weight: 600;
    color: #606266;
    min-width: 120px;
    font-size: 14px;
  }

  .demo-value {
    color: #909399;
    font-size: 12px;
    background: #f5f7fa;
    padding: 4px 8px;
    border-radius: 4px;
    min-width: 100px;
  }

  .result-section {
    margin-top: 30px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
  }

  .result-section h3 {
    margin-top: 0;
    color: #606266;
  }

  .form-data-display {
    margin-top: 20px;
  }

  .json-display {
    background-color: #fff;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    font-size: 12px;
    color: #303133;
    overflow-x: auto;
    white-space: pre-wrap;
  }

  .timestamp {
    margin-top: 10px;
    text-align: right;
    font-size: 0.8em;
    color: #909399;
  }
</style>
