export interface ListCoreProps {
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 是否支持多选 */
  multiple?: boolean
  /** 是否可选择 */
  selectable?: boolean
  /** 是否支持hover效果 */
  hoverable?: boolean
  /** 键盘焦点顺序 */
  tabindex?: number
  /** ARIA标签 */
  ariaLabel?: string
  /** 项目数量（用于键盘导航） */
  itemCount?: number
}

export interface NavigateEvent {
  direction: 'up' | 'down' | 'home' | 'end'
  index: number
}

export interface SelectByKeyboardEvent {
  index: number
}

export interface ListCoreEvents {
  click: [event: MouseEvent]
  keydown: [event: KeyboardEvent]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
  navigate: [event: NavigateEvent]
  'select-by-keyboard': [event: SelectByKeyboardEvent]
  escape: []
}
