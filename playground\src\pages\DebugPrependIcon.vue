<template>
  <div style="padding: 20px">
    <h2>调试图标问题</h2>

    <div style="margin-bottom: 20px">
      <h3>1. 外部前置图标 (prepend-icon)</h3>
      <sp-input-field
        v-model:value="testValue1"
        name="test1"
        label="外部前置图标"
        prepend-icon="Search"
        placeholder="应该在输入框外部左侧显示搜索图标"
      />
      <p>当前值: {{ testValue1 }}</p>
    </div>

    <div style="margin-bottom: 20px">
      <h3>2. 内部前置图标 (prepend-icon-inner)</h3>
      <sp-input-field
        v-model:value="testValue2"
        name="test2"
        label="内部前置图标"
        prepend-icon-inner="Mail"
        placeholder="应该在输入框内部左侧显示邮件图标"
      />
      <p>当前值: {{ testValue2 }}</p>
    </div>

    <div style="margin-bottom: 20px">
      <h3>3. 外部后置图标 (append-icon)</h3>
      <sp-input-field
        v-model:value="testValue3"
        name="test3"
        label="外部后置图标"
        append-icon="Settings"
        placeholder="应该在输入框外部右侧显示设置图标"
      />
      <p>当前值: {{ testValue3 }}</p>
    </div>

    <div style="margin-bottom: 20px">
      <h3>4. 内部后置图标 (append-icon-inner)</h3>
      <sp-input-field
        v-model:value="testValue4"
        name="test4"
        label="内部后置图标"
        append-icon-inner="Person"
        placeholder="应该在输入框内部右侧显示人员图标"
      />
      <p>当前值: {{ testValue4 }}</p>
    </div>

    <div style="margin-bottom: 20px">
      <h3>5. 组合测试</h3>
      <sp-input-field
        v-model:value="testValue5"
        name="test5"
        label="组合图标"
        prepend-icon="Search"
        prepend-icon-inner="Mail"
        append-icon="Settings"
        append-icon-inner="Person"
        placeholder="应该显示所有四个图标"
        :clearable="false"
        :show-password="false"
        :show-word-limit="false"
      />
      <p>当前值: {{ testValue5 }}</p>
    </div>

    <div style="margin-bottom: 20px">
      <h3>6. 最简单测试 - 只有一个外部前置图标</h3>
      <sp-input-field
        v-model:value="testValue6"
        name="test6"
        label="单个外部图标"
        prepend-icon="Search"
        placeholder="只应该有一个搜索图标"
        :clearable="false"
        :show-password="false"
        :show-word-limit="false"
      />
      <p>当前值: {{ testValue6 }}</p>
    </div>

    <div style="margin-top: 20px">
      <h3>调试信息</h3>
      <p>请打开浏览器控制台查看调试输出</p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { InputField as SpInputField } from '@speed-ui/ui'

  const testValue1 = ref('')
  const testValue2 = ref('')
  const testValue3 = ref('')
  const testValue4 = ref('')
  const testValue5 = ref('')
  const testValue6 = ref('')
</script>
