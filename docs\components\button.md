# button 按钮

## 基础用法

:::button/basic

## 多种按钮风格

:::button/types

## 不同边框样式

:::button/border

## 不同尺寸

:::button/sizes

## 倒计时按钮

:::button/time

## 垂直按钮

:::button/vertical

## 禁用切换

:::button/states

## 自定义样式

:::button/attr

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| type | 按钮类型 | `'primary' \| 'secondary' \| 'success' \| 'warning' \| 'danger' \| 'outline'` | `'primary'` |
| size | 按钮尺寸 | `'small' \| 'medium' \| 'large'` | `'medium'` |
| disabled | 是否禁用 | `boolean` | `false` |
| loading | 是否加载中 | `boolean` | `false` |
| toggleable | 是否可切换激活状态 | `boolean` | `true` |

### Events

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| click | 点击事件 | `(event: MouseEvent) => void` |
| toggle | 切换激活状态时触发 | `(active: boolean) => void` |