import type { PropType } from 'vue'
import { makeInputCoreProps, inputCoreDefaults } from '../input/inputCoreProps'
import { propsFactory } from '../../utils/propsFactory'

// Select 选项类型
export interface SelectOption {
  label: string
  value: string | number
  disabled?: boolean
  [key: string]: any
}

// Select 特有的 props
export const makeSelectProps = propsFactory({
  // 继承 InputCore 的所有 props
  ...makeInputCoreProps(),
  
  // Select 特有的属性
  value: [String, Number, Array] as PropType<string | number | Array<string | number>>,
  options: Array as PropType<SelectOption[]>,
  multiple: Boolean,
  emptyText: String,
  valueKey: String,
  labelKey: String,
  
  // 联动选择器相关
  linkedGroup: String,
  linkedLevel: Number,
  linkedData: Array as PropType<any[]>,
}, 'Select')

// Select 默认值
export const selectDefaults = {
  ...inputCoreDefaults,
  
  // 覆盖 InputCore 的默认值
  placeholder: '请选择',
  clearable: false,
  readonly: true, // Select 默认只读
  
  // Select 特有的默认值
  multiple: false,
  emptyText: '暂无数据',
  valueKey: 'value',
  labelKey: 'label',
  options: () => [] as SelectOption[],
}

// 创建带默认值的 Select props
export const createSelectProps = (overrides?: Partial<typeof selectDefaults>) => {
  return makeSelectProps({
    ...selectDefaults,
    ...overrides,
  })
}

// Select 事件类型
export interface SelectEmits {
  'update:value': [value: string | number | Array<string | number> | undefined]
  change: [value: string | number | Array<string | number> | undefined]
  
  // 继承 Input 的所有事件
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
  clear: []
  click: [event: MouseEvent]
  input: [event: Event]
  keydown: [event: KeyboardEvent]
  'prefix-click': [event: MouseEvent]
  'suffix-click': [event: MouseEvent]
  
  // Select 特有的事件
  'option-select': [option: SelectOption]
  'option-remove': [option: SelectOption]
  'dropdown-open': []
  'dropdown-close': []
}
