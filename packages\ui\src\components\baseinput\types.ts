/**
 * BaseInput 组件类型定义
 */

/** BaseInput 支持的输入类型 */
export type BaseInputType = 
  | 'text'
  | 'password'
  | 'email'
  | 'number'
  | 'tel'
  | 'url'
  | 'search'
  | 'range'
  | 'date'
  | 'time'
  | 'datetime-local'
  | 'month'
  | 'week'
  | 'color'
  | 'file'

/** BaseInput 属性接口 */
export interface BaseInputProps {
  /** 输入值 */
  value?: string | number
  /** 输入类型 */
  type?: BaseInputType
  /** 占位符 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 最大长度 */
  maxlength?: number
  /** 最小长度 */
  minlength?: number
  /** 最小值（用于 number、range 等类型） */
  min?: number
  /** 最大值（用于 number、range 等类型） */
  max?: number
  /** 步长（用于 number、range 等类型） */
  step?: number
  /** 自动完成 */
  autocomplete?: string
  /** 自动聚焦 */
  autofocus?: boolean
  /** 输入模式 */
  inputmode?: 'none' | 'text' | 'tel' | 'url' | 'email' | 'numeric' | 'decimal' | 'search'
  /** 表单名称 */
  name?: string
  /** 输入框 ID */
  id?: string
  /** 是否必填 */
  required?: boolean
  /** 自定义类名 */
  class?: string | string[] | Record<string, boolean>
  /** 自定义样式 */
  style?: string | Record<string, any>
  /** 接受的文件类型（用于 file 类型） */
  accept?: string
  /** 是否允许多选（用于 file 类型） */
  multiple?: boolean
}

/** BaseInput 事件接口 */
export interface BaseInputEmits {
  /** 值更新事件 */
  (e: 'update:value', value: string | number): void
  /** 输入事件 */
  (e: 'input', event: Event): void
  /** 值改变事件 */
  (e: 'change', event: Event): void
  /** 获得焦点事件 */
  (e: 'focus', event: FocusEvent): void
  /** 失去焦点事件 */
  (e: 'blur', event: FocusEvent): void
  /** 键盘按下事件 */
  (e: 'keydown', event: KeyboardEvent): void
  /** 键盘抬起事件 */
  (e: 'keyup', event: KeyboardEvent): void
  /** 键盘按键事件 */
  (e: 'keypress', event: KeyboardEvent): void
  /** 点击事件 */
  (e: 'click', event: MouseEvent): void
}

/** BaseInput 实例方法接口 */
export interface BaseInputInstance {
  /** 输入框元素引用 */
  inputRef: HTMLInputElement | null
  /** 聚焦到输入框 */
  focus: () => void
  /** 失去焦点 */
  blur: () => void
  /** 选中输入框内容 */
  select: () => void
  /** 清空输入框 */
  clear: () => void
}

/** BaseInput 属性默认值 */
export const baseInputPropsDefaults: Required<
  Pick<BaseInputProps, 'type' | 'disabled' | 'readonly' | 'autofocus' | 'required' | 'multiple'>
> = {
  type: 'text',
  disabled: false,
  readonly: false,
  autofocus: false,
  required: false,
  multiple: false,
}
