<!--
  SearchControls.vue - 搜索输入框的控制器组件
  包含搜索按钮和清除按钮，用于搜索操作
-->

<template>
  <div
    class="sp-search-controls"
    @click.stop
    @mousedown.stop
  >
    <sp-icon
      name="Search"
      :size="iconSize"
      :clickable="canSearch"
      :class="searchClasses"
      @click.stop.prevent="handleSearchClick"
      @mousedown.stop.prevent
    />
    <sp-icon
      v-if="showClear && hasValue"
      name="Clear"
      :size="iconSize"
      :clickable="canClear"
      :class="clearClasses"
      @click.stop.prevent="handleClearClick"
      @mousedown.stop.prevent
    />
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import SpIcon from '../../icon/Icon.vue'

  interface SearchControlsProps {
    /** 图标大小 */
    iconSize: number
    /** 是否可以搜索 */
    canSearch: boolean
    /** 是否可以清除 */
    canClear: boolean
    /** 是否显示清除按钮 */
    showClear: boolean
    /** 是否有值（用于控制清除按钮显示） */
    hasValue: boolean
  }

  interface SearchControlsEmits {
    /** 执行搜索 */
    (e: 'search'): void
    /** 清除搜索内容 */
    (e: 'clear'): void
    /** 请求聚焦 */
    (e: 'focus'): void
  }

  const props = defineProps<SearchControlsProps>()
  const emit = defineEmits<SearchControlsEmits>()

  // ===== 样式类计算 =====
  const searchClasses = computed(() => ({
    'sp-search-controls__search': true,
    'sp-search-controls__disabled': !props.canSearch,
  }))

  const clearClasses = computed(() => ({
    'sp-search-controls__clear': true,
    'sp-search-controls__disabled': !props.canClear,
  }))

  // ===== 事件处理 =====
  const handleSearchClick = () => {
    if (props.canSearch) {
      emit('focus')
      emit('search')
    }
  }

  const handleClearClick = () => {
    if (props.canClear) {
      emit('focus')
      emit('clear')
    }
  }
</script>

<script lang="ts">
  export default {
    name: 'SpSearchControls',
  }
</script>
<!-- 
<style lang="scss">
  .sp-search-controls {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 0;
    pointer-events: auto;

    &__search,
    &__clear {
      color: var(--sp-color-text-secondary, #666);
      transition: all 0.3s ease;
      cursor: pointer;
      padding: 2px;
      border-radius: 3px;
      font-size: 12px;
      line-height: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 20px;
      min-height: 20px;

      &:hover {
        color: var(--sp-color-primary, #409eff);
        // background: rgba(64, 158, 255, 0.1);
      }

      // &:active {
      //   background: rgba(64, 158, 255, 0.2);
      // }
    }

    // &__search {
    //   &:hover {
    //     color: var(--sp-color-success, #67c23a);
    //     background: rgba(103, 194, 58, 0.1);
    //   }

    //   &:active {
    //     background: rgba(103, 194, 58, 0.2);
    //   }
    // }

    &__clear {
      &:hover {
        color: var(--sp-color-danger, #f56c6c);
        background: rgba(245, 108, 108, 0.1);
      }

      &:active {
        background: rgba(245, 108, 108, 0.2);
      }
    }

    &__disabled {
      color: var(--sp-color-text-disabled, #c0c4cc) !important;
      cursor: not-allowed !important;

      &:hover,
      &:active {
        color: var(--sp-color-text-disabled, #c0c4cc) !important;
        background: transparent !important;
      }
    }
  }
</style> -->
