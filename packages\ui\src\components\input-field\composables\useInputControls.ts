/**
 * 输入框控件管理 Composable
 * 统一管理输入框控件的注册、渲染和事件处理
 */

import { computed, ref } from 'vue'
import type { ControlContext, ControlEventData } from '../controls/types'
import { controlRegistry } from '../controls/registry'
import { builtinControlConfigs } from '../controls/configs'

/** 控件条件缓存 */
interface ControlConditionCache {
  [controlType: string]: {
    result: boolean
    dependencies: string[]
    lastCheck: number
  }
}

// 确保控件在模块加载时就注册（只注册一次）
let controlsRegistered = false
function ensureControlsRegistered() {
  if (!controlsRegistered) {
    builtinControlConfigs.forEach(config => {
      controlRegistry.register(config)
    })
    controlsRegistered = true
  }
}

/** 使用输入框控件系统 */
export function useInputControls(
  props: any,
  emit: Function,
  inputLogic: any,
  computedStates: any
) {
  // 控件条件缓存
  const conditionCache = ref<ControlConditionCache>({})

  // 确保控件已注册
  ensureControlsRegistered()

  /** 创建控件上下文 */
  const createControlContext = (slotProps: any): ControlContext => ({
    props,
    emit,
    slotProps,
    inputLogic,
    computed: computedStates,
  })

  /** 检查控件条件（带缓存） */
  const checkControlCondition = (
    config: any,
    context: ControlContext
  ): boolean => {
    // 为了避免不同组件实例间的缓存冲突，暂时禁用缓存
    // TODO: 后续可以通过组件实例ID来区分缓存
    return config.condition(context)

    /* 原缓存逻辑 - 暂时注释
    const cacheKey = `${config.type}-${config.position}`
    const now = Date.now()
    const cached = conditionCache.value[cacheKey]

    // 缓存有效期 100ms（避免同一渲染周期内重复计算）
    if (cached && now - cached.lastCheck < 100) {
      return cached.result
    }

    const result = config.condition(context)
    conditionCache.value[cacheKey] = {
      result,
      dependencies: [], // 可以后续扩展依赖追踪
      lastCheck: now,
    }

    return result
    */
  }

  /** 是否有前置控件 */
  const hasPrependControls = computed(() => {
    const configs = controlRegistry.getByPosition('prepend')
    if (configs.length === 0) return false

    const tempContext = createControlContext({})
    return configs.some(config => checkControlCondition(config, tempContext))
  })

  /** 是否有后置控件 */
  const hasAppendControls = computed(() => {
    const configs = controlRegistry.getByPosition('append')
    if (configs.length === 0) return false

    const tempContext = createControlContext({})
    return configs.some(config => checkControlCondition(config, tempContext))
  })

  /** 是否有外部前置控件 */
  const hasPrependOuterControls = computed(() => {
    const configs = controlRegistry.getByPosition('prependOuter')
    if (configs.length === 0) return false

    const tempContext = createControlContext({})
    return configs.some(config => checkControlCondition(config, tempContext))
  })

  /** 是否有外部后置控件 */
  const hasAppendOuterControls = computed(() => {
    const configs = controlRegistry.getByPosition('appendOuter')
    if (configs.length === 0) return false

    const tempContext = createControlContext({})
    return configs.some(config => checkControlCondition(config, tempContext))
  })

  /** 防抖处理控件事件 */
  let eventDebounceTimer: NodeJS.Timeout | null = null
  const handleControlEvent = (data: ControlEventData) => {
    // 对于频繁的事件（如输入、滚动等）进行防抖处理
    const debounceEvents = ['input', 'scroll', 'resize']

    if (debounceEvents.includes(data.event)) {
      if (eventDebounceTimer) {
        clearTimeout(eventDebounceTimer)
      }

      eventDebounceTimer = setTimeout(() => {
        console.debug('Debounced control event:', data)
        // 实际的事件处理逻辑
      }, 16) // 约 60fps
    } else {
      // 立即处理非频繁事件
      console.debug('Control event:', data)
    }
  }

  /** 处理聚焦请求 */
  const handleRequestFocus = () => {
    // 这个方法会在 input-field 中被重写
    emit('request-focus')
  }

  return {
    createControlContext,
    hasPrependControls,
    hasAppendControls,
    hasPrependOuterControls,
    hasAppendOuterControls,
    handleControlEvent,
    handleRequestFocus,
  }
}
