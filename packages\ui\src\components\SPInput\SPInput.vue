<template>
  <div
    :class="[
      bem.b(),
      bem.m(variant),
      {
        [bem.m('focused')]: isFocused,
        [bem.m('disabled')]: disabled,
        [bem.m('error')]: hasError,
      },
      densityClasses,
      themeClasses,
    ]"
    :style="[dimensionStyles, style]"
  >
    <!-- Prepend slot -->
    <div v-if="hasPrepend" :class="bem.e('prepend')">
      <slot name="prepend" v-bind="slotProps" />
      <span
        v-if="prependIcon"
        class="sp-input__icon sp-input__prepend-icon"
        @click="$emit('click:prepend', $event)"
      >
        {{ prependIcon }}
      </span>
    </div>

    <!-- Control area -->
    <div :class="bem.e('control')">
      <slot v-bind="slotProps" />
    </div>

    <!-- Append slot -->
    <div v-if="hasAppend" :class="bem.e('append')">
      <span
        v-if="appendIcon"
        class="sp-input__icon sp-input__append-icon"
        @click="$emit('click:append', $event)"
      >
        {{ appendIcon }}
      </span>
      <slot name="append" v-bind="slotProps" />
    </div>

    <!-- Messages/Details -->
    <div
      v-if="hasDetails"
      :id="messagesId"
      :class="bem.e('details')"
      role="alert"
      aria-live="polite"
    >
      <div v-if="hasMessages" :class="bem.e('messages')">
        <div
          v-for="(message, index) in displayMessages"
          :key="index"
          :class="bem.e('message')"
        >
          {{ message }}
        </div>
      </div>
      <slot name="details" v-bind="slotProps" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, useId, useSlots } from 'vue'
import { useBEM } from '@speed-ui/bem-helper'

// Types
interface SPInputSlot {
  id: string
  messagesId: string
  isDirty: boolean
  isDisabled: boolean
  isReadonly: boolean
  isValid: boolean | null
  isFocused: boolean
  reset: () => void
  validate: () => void
}

// Props
interface Props {
  id?: string
  variant?: 'filled' | 'outlined' | 'underlined' | 'plain'
  size?: 'small' | 'medium' | 'large'
  color?: string
  baseColor?: string
  prependIcon?: string
  appendIcon?: string
  iconColor?: string | boolean
  disabled?: boolean
  readonly?: boolean
  focused?: boolean
  error?: boolean
  errorMessages?: string | string[]
  hint?: string
  persistentHint?: boolean
  messages?: string | string[]
  hideDetails?: boolean | 'auto'
  maxWidth?: string | number
  minWidth?: string | number
  width?: string | number
  density?: 'default' | 'comfortable' | 'compact'
  class?: any
  style?: any
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'filled',
  size: 'medium',
  density: 'default',
  hideDetails: false,
  messages: () => [],
  errorMessages: () => [],
})

// Emits
const emit = defineEmits<{
  'click:prepend': [event: MouseEvent]
  'click:append': [event: MouseEvent]
}>()

// Composables
const bem = useBEM('input')
const uid = useId()
const slots = useSlots()

// Computed
const id = computed(() => props.id || `sp-input-${uid}`)
const messagesId = computed(() => `${id.value}-messages`)
const isFocused = computed(() => props.focused)
const isDisabled = computed(() => props.disabled)
const isReadonly = computed(() => props.readonly)
const hasError = computed(() => props.error || (props.errorMessages && props.errorMessages.length > 0))

const hasPrepend = computed(() => !!props.prependIcon || !!slots.prepend)
const hasAppend = computed(() => !!props.appendIcon || !!slots.append)

const displayMessages = computed(() => {
  if (hasError.value && props.errorMessages) {
    return Array.isArray(props.errorMessages) ? props.errorMessages : [props.errorMessages]
  }
  if (props.hint && (props.persistentHint || isFocused.value)) {
    return [props.hint]
  }
  if (props.messages) {
    return Array.isArray(props.messages) ? props.messages : [props.messages]
  }
  return []
})

const hasMessages = computed(() => displayMessages.value.length > 0)
const hasDetails = computed(() => {
  if (props.hideDetails === true) return false
  if (props.hideDetails === 'auto') {
    return hasMessages.value || !!slots.details
  }
  return hasMessages.value || !!slots.details
})

// 移除未使用的 iconSize 和 iconColor 计算属性

const densityClasses = computed(() => {
  return props.density !== 'default' ? bem.m(`density-${props.density}`) : ''
})

const themeClasses = computed(() => {
  const classes = []
  if (props.color) classes.push(bem.m(`color-${props.color}`))
  return classes
})

const dimensionStyles = computed(() => {
  const styles: Record<string, any> = {}
  if (props.width) styles.width = typeof props.width === 'number' ? `${props.width}px` : props.width
  if (props.minWidth) styles.minWidth = typeof props.minWidth === 'number' ? `${props.minWidth}px` : props.minWidth
  if (props.maxWidth) styles.maxWidth = typeof props.maxWidth === 'number' ? `${props.maxWidth}px` : props.maxWidth
  return styles
})

// Slot props
const slotProps = computed<SPInputSlot>(() => ({
  id: id.value,
  messagesId: messagesId.value,
  isDirty: false, // Simplified - would need actual form integration
  isDisabled: isDisabled.value,
  isReadonly: isReadonly.value,
  isValid: hasError.value ? false : null,
  isFocused: isFocused.value,
  reset: () => {
    // Simplified reset function
    emit('click:prepend', new MouseEvent('reset'))
  },
  validate: () => {
    // Simplified validate function
    console.log('Validate called')
  },
}))

// Expose for template refs
defineExpose({
  id,
  reset: slotProps.value.reset,
  validate: slotProps.value.validate,
})
</script>

<script lang="ts">
export default {
  name: 'SPInput',
}
</script>

<style lang="scss">
.sp-input {
  display: flex;
  flex-direction: column;
  position: relative;

  &__prepend,
  &__append {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  &__icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--sp-on-surface-variant);
    transition: color 0.2s ease;

    &:hover {
      color: var(--sp-primary);
    }
  }

  &__control {
    flex: 1;
    min-width: 0;
  }

  &__details {
    margin-top: 4px;
    min-height: 20px;
  }

  &__messages {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  &__message {
    font-size: 12px;
    line-height: 1.4;
  }

  // Variants
  &--filled {
    .sp-input__control {
      background-color: var(--sp-surface-variant);
      border-radius: 4px 4px 0 0;
      border-bottom: 1px solid var(--sp-outline);
    }
  }

  &--outlined {
    .sp-input__control {
      border: 1px solid var(--sp-outline);
      border-radius: 4px;
    }
  }

  &--underlined {
    .sp-input__control {
      border-bottom: 1px solid var(--sp-outline);
    }
  }

  // States
  &--focused {
    .sp-input__control {
      border-color: var(--sp-primary);
    }
  }

  &--error {
    .sp-input__control {
      border-color: var(--sp-error);
    }

    .sp-input__message {
      color: var(--sp-error);
    }
  }

  &--disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  // Sizes
  &--size-small {
    .sp-input__control {
      min-height: 32px;
      padding: 4px 8px;
    }
  }

  &--size-medium {
    .sp-input__control {
      min-height: 40px;
      padding: 8px 12px;
    }
  }

  &--size-large {
    .sp-input__control {
      min-height: 48px;
      padding: 12px 16px;
    }
  }

  // Density
  &--density-compact {
    .sp-input__control {
      padding-top: 4px;
      padding-bottom: 4px;
    }
  }

  &--density-comfortable {
    .sp-input__control {
      padding-top: 12px;
      padding-bottom: 12px;
    }
  }
}
</style>