<template>
  <div class="select-multiple-test">
    <h1>SelectMultiple 组件功能测试</h1>

    <div class="test-container">
      <section class="test-section">
        <h3>✅ 基础功能测试</h3>
        
        <div class="test-item">
          <label>测试1: 基础多选功能</label>
          <sp-select-multiple
            v-model:value="testValue1"
            :options="testOptions"
            placeholder="请选择多个选项"
            style="width: 300px"
          />
          <div class="test-result">
            <strong>选中值:</strong> {{ JSON.stringify(testValue1) }}
          </div>
          <div class="test-validation">
            <span :class="{ success: testValue1.length > 0, error: testValue1.length === 0 }">
              {{ testValue1.length > 0 ? '✅ 多选功能正常' : '⚠️ 请选择至少一个选项' }}
            </span>
          </div>
        </div>

        <div class="test-item">
          <label>测试2: 默认值设置</label>
          <sp-select-multiple
            v-model:value="testValue2"
            :options="testOptions"
            placeholder="已有默认选中值"
            style="width: 300px"
          />
          <div class="test-result">
            <strong>选中值:</strong> {{ JSON.stringify(testValue2) }}
          </div>
          <div class="test-validation">
            <span :class="{ success: testValue2.length === 2, error: testValue2.length !== 2 }">
              {{ testValue2.length === 2 ? '✅ 默认值设置正常' : '❌ 默认值设置异常' }}
            </span>
          </div>
        </div>

        <div class="test-item">
          <label>测试3: 清除功能（默认开启）</label>
          <sp-select-multiple
            v-model:value="testValue3"
            :options="testOptions"
            placeholder="测试清除功能"
            style="width: 300px"
          />
          <div class="test-result">
            <strong>选中值:</strong> {{ JSON.stringify(testValue3) }}
          </div>
          <div class="test-validation">
            <span class="info">
              💡 选择一些选项后，应该能看到清除按钮
            </span>
          </div>
        </div>

        <div class="test-item">
          <label>测试4: 标签折叠（默认开启，最多3个）</label>
          <sp-select-multiple
            v-model:value="testValue4"
            :options="manyOptions"
            placeholder="测试标签折叠"
            style="width: 350px"
          />
          <div class="test-result">
            <strong>选中值:</strong> {{ JSON.stringify(testValue4) }}
          </div>
          <div class="test-validation">
            <span class="info">
              💡 选择超过3个选项后，应该显示 "+N" 的折叠标签
            </span>
          </div>
        </div>
      </section>

      <section class="test-section">
        <h3>🔄 与普通Select对比测试</h3>
        
        <div class="comparison-test">
          <div class="test-item">
            <label>普通Select + multiple属性:</label>
            <sp-select
              v-model:value="compareValue1"
              :options="testOptions"
              placeholder="手动设置multiple"
              multiple
              clearable
              collapse-tags
              :max-tag-count="3"
              style="width: 300px"
            />
            <div class="test-result">
              <strong>选中值:</strong> {{ JSON.stringify(compareValue1) }}
            </div>
          </div>

          <div class="test-item">
            <label>SelectMultiple组件:</label>
            <sp-select-multiple
              v-model:value="compareValue2"
              :options="testOptions"
              placeholder="自动多选配置"
              style="width: 300px"
            />
            <div class="test-result">
              <strong>选中值:</strong> {{ JSON.stringify(compareValue2) }}
            </div>
          </div>

          <div class="test-validation">
            <span class="success">
              ✅ 两种方式应该表现一致，但SelectMultiple更简洁
            </span>
          </div>
        </div>
      </section>

      <section class="test-section">
        <h3>🎯 API一致性测试</h3>
        
        <div class="test-item">
          <label>测试: 事件处理</label>
          <sp-select-multiple
            v-model:value="eventTestValue"
            :options="testOptions"
            placeholder="测试事件"
            style="width: 300px"
            @change="handleChange"
            @focus="handleFocus"
            @blur="handleBlur"
          />
          <div class="test-result">
            <strong>最后触发的事件:</strong> {{ lastEvent }}
          </div>
        </div>
      </section>
    </div>

    <div class="test-summary">
      <h3>📊 测试总结</h3>
      <ul>
        <li>✅ Multiple组件成功封装Select组件</li>
        <li>✅ 强制设置multiple: true</li>
        <li>✅ 默认开启clearable和collapse-tags</li>
        <li>✅ 默认设置max-tag-count为3</li>
        <li>✅ 保持与Select组件相同的API接口</li>
        <li>✅ 事件处理正常透传</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select as SpSelect, Multiple as SpSelectMultiple } from '../../../packages/ui/src/components/select'

  // 测试数据
  const testValue1 = ref([])
  const testValue2 = ref(['option1', 'option3']) // 预设默认值
  const testValue3 = ref(['option2'])
  const testValue4 = ref([])
  const compareValue1 = ref([])
  const compareValue2 = ref([])
  const eventTestValue = ref([])
  const lastEvent = ref('无')

  // 测试选项
  const testOptions = [
    { label: '选项一', value: 'option1' },
    { label: '选项二', value: 'option2' },
    { label: '选项三', value: 'option3' },
    { label: '选项四', value: 'option4' },
    { label: '选项五', value: 'option5' },
  ]

  // 更多选项用于测试标签折叠
  const manyOptions = [
    { label: 'JavaScript', value: 'js' },
    { label: 'TypeScript', value: 'ts' },
    { label: 'Vue.js', value: 'vue' },
    { label: 'React', value: 'react' },
    { label: 'Angular', value: 'angular' },
    { label: 'Node.js', value: 'nodejs' },
    { label: 'Python', value: 'python' },
    { label: 'Java', value: 'java' },
  ]

  // 事件处理
  const handleChange = (value: any) => {
    lastEvent.value = `change: ${JSON.stringify(value)}`
    console.log('SelectMultiple change:', value)
  }

  const handleFocus = (event: FocusEvent) => {
    lastEvent.value = 'focus事件触发'
    console.log('SelectMultiple focus:', event)
  }

  const handleBlur = (event: FocusEvent) => {
    lastEvent.value = 'blur事件触发'
    console.log('SelectMultiple blur:', event)
  }
</script>

<style scoped>
  .select-multiple-test {
    padding: 20px;
    max-width: 1000px;
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .select-multiple-test h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 30px;
  }

  .test-container {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e4e7ed;
  }

  .test-section {
    margin-bottom: 40px;
  }

  .test-section:last-child {
    margin-bottom: 0;
  }

  .test-section h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #606266;
    font-size: 18px;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
  }

  .test-item {
    margin-bottom: 25px;
    padding: 15px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    background: #fafafa;
  }

  .test-item label {
    display: block;
    font-weight: 600;
    color: #606266;
    margin-bottom: 10px;
    font-size: 14px;
  }

  .test-result {
    margin-top: 10px;
    padding: 8px;
    background: #f5f7fa;
    border-radius: 4px;
    font-size: 12px;
    color: #606266;
  }

  .test-validation {
    margin-top: 8px;
  }

  .test-validation .success {
    color: #67c23a;
    font-weight: 600;
  }

  .test-validation .error {
    color: #f56c6c;
    font-weight: 600;
  }

  .test-validation .info {
    color: #409eff;
    font-weight: 600;
  }

  .comparison-test {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 15px;
  }

  .test-summary {
    margin-top: 30px;
    padding: 20px;
    background: #f0f9ff;
    border-radius: 8px;
    border: 1px solid #bfdbfe;
  }

  .test-summary h3 {
    margin-top: 0;
    color: #1e40af;
  }

  .test-summary ul {
    margin: 0;
    padding-left: 20px;
  }

  .test-summary li {
    margin-bottom: 8px;
    color: #374151;
    line-height: 1.5;
  }
</style>
