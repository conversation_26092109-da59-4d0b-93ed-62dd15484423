/**
 * Speed UI 组件尺寸相关类型定义
 */

// 标准尺寸类型 - 适用于大多数组件
export type ComponentSize = 'small' | 'default' | 'large'

// 扩展尺寸类型 - 适用于需要更多尺寸选项的组件
export type ExtendedComponentSize =
  | 'mini'
  | 'small'
  | 'default'
  | 'large'
  | 'huge'

// 滚动条专用尺寸类型（保持向后兼容）
export type ScrollbarSize = 'small' | 'medium' | 'large'

// 尺寸映射配置类型
export interface SizeMapping {
  readonly selectToScrollbar: {
    readonly small: 'small'
    readonly default: 'medium'
    readonly large: 'large'
  }
  // 可以添加更多组件间的映射
  // readonly buttonToInput: { ... }
  // readonly inputToSelect: { ... }
}

// 尺寸相关的 Props 接口
export interface SizeProps {
  /**
   * 组件尺寸
   */
  size?: ComponentSize
}

// 扩展尺寸的 Props 接口
export interface ExtendedSizeProps {
  /**
   * 组件尺寸
   */
  size?: ExtendedComponentSize
}

// 尺寸值映射类型
export interface SizeValueMap {
  readonly small: number
  readonly default: number
  readonly large: number
}

// 尺寸像素映射类型
export interface SizePixelMap {
  readonly small: string
  readonly default: string
  readonly large: string
}
