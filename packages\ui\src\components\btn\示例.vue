<!-- src/components/Button/Button.vue -->
<template>
  <div 
    class="button-wrapper"
    :class="wrapperClasses"
    v-on="wrapperEventHandlers"
  >
    <ButtonCore
      v-bind="coreProps"
      v-on="coreEventHandlers"
    >
      <slot />
    </ButtonCore>
  </div>
</template>

<script setup>
import { computed, useAttrs } from 'vue';
import ButtonCore from './ButtonCore.vue';
import { provideCommonEvents, useCommonEvents } from '../../composables/useCommonEvents';

const props = defineProps({
  size: { type: String, default: 'medium' },
  type: { type: String, default: 'default' },
  disabled: Boolean,
  loading: Boolean
});

const attrs = useAttrs();

// 提供通用事件给子组件
const providedEvents = provideCommonEvents(props);

// 样式层特定事件处理
const wrapperSpecificHandlers = {
  mouseenter: (e) => {
    console.log('Button wrapper mouseenter');
    if (props.tooltip) showTooltip(e.currentTarget, props.tooltip);
  },
  mouseleave: () => {
    console.log('Button wrapper mouseleave');
    if (props.tooltip) hideTooltip();
  }
};

// 合并事件处理器
const wrapperEventHandlers = useCommonEvents(wrapperSpecificHandlers);

// 传递给核心组件的事件处理器
const coreEventHandlers = computed(() => {
  // 过滤掉已在wrapper处理的事件
  const { mouseenter, mouseleave, ...rest } = providedEvents;
  return rest;
});

// 计算样式类
const wrapperClasses = computed(() => [
  `size-${props.size}`,
  `type-${props.type}`,
  { 
    'is-disabled': props.disabled,
    'is-loading': props.loading
  }
]);

// 传递给核心组件的props
const coreProps = computed(() => {
  const { size, type, ...rest } = attrs;
  return {
    ...rest,
    disabled: props.disabled,
    loading: props.loading
  };
});
</script>