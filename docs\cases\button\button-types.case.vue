<text>
    > 提供了多种不同的类型样式，默认样式、成功样式、警告样式、危险样式
</text>

<template>
    <div class="button-demo-row">
        <sp-button primary>主要默认按钮</sp-button>
        <sp-button primary type="success">主要成功按钮</sp-button>
        <sp-button primary type="warning">主要警告按钮</sp-button>
        <sp-button primary type="danger">主要危险按钮</sp-button>
    </div>

    <div class="button-demo-row">
        <sp-button secondary type="default">主要默认按钮</sp-button>
        <sp-button secondary type="success">次要成功按钮</sp-button>
        <sp-button secondary type="warning">次要警告按钮</sp-button>
        <sp-button secondary type="danger">次要危险按钮</sp-button>
    </div>

    <div class="button-demo-row">
        <sp-button dashed type="default">主要默认按钮</sp-button>
        <sp-button dashed type="success">虚线成功按钮</sp-button>
        <sp-button dashed type="warning">虚线警告按钮</sp-button>
        <sp-button dashed type="danger">虚线危险按钮</sp-button>
    </div>

    <div class="button-demo-row">
        <sp-button text type="default">主要默认按钮</sp-button>
        <sp-button text type="success">文本成功按钮</sp-button>
        <sp-button text type="warning">文本警告按钮</sp-button>
        <sp-button text type="danger">文本危险按钮</sp-button>
    </div>

    <div class="button-demo-row">
        <sp-button link>主要默认按钮</sp-button>
        <sp-button link type="success">链接成功按钮</sp-button>
        <sp-button link type="warning">链接警告按钮</sp-button>
        <sp-button link type="danger">链接危险按钮</sp-button>
    </div>
</template> 