<template>
  <div class="linked-select-demo">
    <h2>多级联动选择器演示</h2>
    
    <div class="demo-section">
      <h3>三级联动：地区选择示例</h3>
      <div class="form-row">
        <div class="form-item">
          <label>国家/地区:</label>
          <SpSelect 
            v-model="country" 
            linked-group="area-selection"
            :linked-level="1"
            :linked-data="areaData"
            placeholder="请选择国家/地区"
            clearable
          />
        </div>
        
        <div class="form-item">
          <label>省份/州:</label>
          <SpSelect 
            v-model="province" 
            linked-group="area-selection"
            :linked-level="2"
            placeholder="请先选择国家/地区"
            clearable
          />
        </div>

        <div class="form-item">
          <label>城市:</label>
          <SpSelect 
            v-model="city" 
            linked-group="area-selection"
            :linked-level="3"
            placeholder="请先选择省份/州"
            clearable
          />
        </div>
      </div>
      
      <div class="result">
        <p><strong>选择结果:</strong></p>
        <p>国家/地区: {{ country || '未选择' }}</p>
        <p>省份/州: {{ province || '未选择' }}</p>
        <p>城市: {{ city || '未选择' }}</p>
      </div>
    </div>

    <div class="demo-section">
      <h3>二级联动：职业选择示例</h3>
      <div class="form-row">
        <div class="form-item">
          <label>职业大类:</label>
          <SpSelect 
            v-model="jobCategory" 
            linked-group="job-selection"
            :linked-level="1"
            :linked-data="jobData"
            placeholder="请选择职业大类"
            clearable
          />
        </div>
        
        <div class="form-item">
          <label>职业中类:</label>
          <SpSelect 
            v-model="jobDetail" 
            linked-group="job-selection"
            :linked-level="2"
            placeholder="请先选择职业大类"
            clearable
          />
        </div>
      </div>
      
      <div class="result">
        <p><strong>选择结果:</strong></p>
        <p>职业大类: {{ jobCategory || '未选择' }}</p>
        <p>职业中类: {{ jobDetail || '未选择' }}</p>
      </div>
    </div>

    <div class="demo-section">
      <h3>使用说明</h3>
      <div class="usage-guide">
        <h4>1. 数据格式（支持多级嵌套）</h4>
        <pre><code>const linkedData = [
  {
    value: 'china',
    label: '中国',
    children: [
      {
        value: 'guangdong',
        label: '广东省',
        children: [
          { value: 'shenzhen', label: '深圳市' },
          { value: 'guangzhou', label: '广州市' }
        ]
      }
    ]
  }
]</code></pre>

        <h4>2. 组件使用（任意级别）</h4>
        <pre><code><!-- 第一级选择器 -->
&lt;SpSelect 
  v-model="level1Value" 
  linked-group="unique-group-id"
  :linked-level="1"
  :linked-data="linkedData"
/&gt;

<!-- 第二级选择器 -->
&lt;SpSelect 
  v-model="level2Value" 
  linked-group="unique-group-id"
  :linked-level="2"
/&gt;

<!-- 第三级选择器 -->
&lt;SpSelect 
  v-model="level3Value" 
  linked-group="unique-group-id"
  :linked-level="3"
/&gt;</code></pre>

        <h4>3. 新特点</h4>
        <ul>
          <li>✅ 支持任意级别的联动（2级、3级、4级...）</li>
          <li>✅ 使用 linkedLevel 数字表示级别</li>
          <li>✅ 只需在第一级传入 linkedData</li>
          <li>✅ 自动级联清空后续级别</li>
          <li>✅ 支持多个独立的联动组</li>
          <li>✅ 数据结构完全嵌套，层次清晰</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SpSelect from '../../../packages/ui/src/components/Select/Select.vue'

// 三级联动：国家-省份-城市
const country = ref()
const province = ref()
const city = ref()

const areaData = [
  {
    value: 'china',
    label: '中国',
    children: [
      {
        value: 'guangdong',
        label: '广东省',
        children: [
          { value: 'shenzhen', label: '深圳市' },
          { value: 'guangzhou', label: '广州市' },
          { value: 'dongguan', label: '东莞市' },
          { value: 'foshan', label: '佛山市' }
        ]
      },
      {
        value: 'beijing',
        label: '北京市',
        children: [
          { value: 'chaoyang', label: '朝阳区' },
          { value: 'haidian', label: '海淀区' },
          { value: 'xicheng', label: '西城区' },
          { value: 'dongcheng', label: '东城区' }
        ]
      },
      {
        value: 'shanghai',
        label: '上海市',
        children: [
          { value: 'huangpu', label: '黄浦区' },
          { value: 'pudong', label: '浦东新区' },
          { value: 'xuhui', label: '徐汇区' },
          { value: 'changning', label: '长宁区' }
        ]
      }
    ]
  },
  {
    value: 'usa',
    label: '美国',
    children: [
      {
        value: 'california',
        label: '加利福尼亚州',
        children: [
          { value: 'los_angeles', label: '洛杉矶' },
          { value: 'san_francisco', label: '旧金山' },
          { value: 'san_diego', label: '圣地亚哥' }
        ]
      },
      {
        value: 'new_york_state',
        label: '纽约州',
        children: [
          { value: 'new_york_city', label: '纽约市' },
          { value: 'albany', label: '奥尔巴尼' },
          { value: 'buffalo', label: '布法罗' }
        ]
      }
    ]
  }
]

// 二级联动：职业选择
const jobCategory = ref()
const jobDetail = ref()

const jobData = [
  {
    value: 'it',
    label: '计算机从业类人员',
    children: [
      { value: 'frontend', label: '前端工程师' },
      { value: 'backend', label: '后端工程师' },
      { value: 'tester', label: '测试工程师' },
      { value: 'devops', label: 'DevOps工程师' },
      { value: 'mobile', label: '移动端开发工程师' }
    ]
  },
  {
    value: 'design',
    label: '设计类人员',
    children: [
      { value: 'ui', label: 'UI设计师' },
      { value: 'ux', label: 'UX设计师' },
      { value: 'graphic', label: '平面设计师' },
      { value: 'product', label: '产品设计师' }
    ]
  },
  {
    value: 'marketing',
    label: '市场营销类人员',
    children: [
      { value: 'digital', label: '数字营销专员' },
      { value: 'content', label: '内容营销专员' },
      { value: 'social', label: '社媒运营专员' },
      { value: 'seo', label: 'SEO专员' }
    ]
  }
]
</script>

<style scoped>
.linked-select-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: #f8f9fa;
  min-height: 100vh;
}

.demo-section {
  margin-bottom: 40px;
  padding: 24px;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.demo-section h2 {
  color: #2c3e50;
  text-align: center;
  margin-bottom: 30px;
  font-size: 28px;
  border-bottom: 3px solid #409eff;
  padding-bottom: 12px;
}

.demo-section h3 {
  margin-top: 0;
  color: #409eff;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
  font-size: 20px;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.form-item {
  flex: 1;
  min-width: 200px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #606266;
  font-size: 14px;
}

.result {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.result p {
  margin: 8px 0;
  color: #606266;
  font-weight: 500;
}

.usage-guide h4 {
  color: #409eff;
  margin-top: 24px;
  margin-bottom: 12px;
  font-size: 16px;
}

.usage-guide pre {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  border: 1px solid #e1e5e9;
  margin: 12px 0;
}

.usage-guide code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #333;
  line-height: 1.5;
}

.usage-guide ul {
  background: #f8f9fa;
  padding: 16px 20px;
  border-radius: 8px;
  border-left: 4px solid #52c41a;
}

.usage-guide li {
  margin: 8px 0;
  color: #555;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 16px;
  }
  
  .linked-select-demo {
    padding: 12px;
  }
  
  .demo-section {
    padding: 16px;
  }
}
</style> 