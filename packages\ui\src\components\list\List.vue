<template>
  <ListLogic
    v-bind="$attrs"
    :size="props.size"
    :variant="props.variant"
    :bordered="props.bordered"
    :split="props.split"
    :hoverable="props.hoverable"
    :selectable="props.selectable"
    :multiple="props.multiple"
    :selected-keys="props.selectedKeys"
    :max-height="props.maxHeight"
    :loading="props.loading"
    :aria-label="props.ariaLabel"
    @update:selectedKeys="$emit('update:selectedKeys', $event)"
    @select="
      (key, selected, selectedKeys) =>
        $emit('select', key, selected, selectedKeys)
    "
    @click="(key, event) => $emit('click', key, event)"
  >
    <slot />
  </ListLogic>
</template>

<script setup lang="ts">
  import type { ListProps } from './types'
  import ListLogic from './ListLogic.vue'

  defineOptions({
    name: 'SpList',
  })

  // 用户接口层：定义对外API和属性默认值
  const props = withDefaults(defineProps<ListProps>(), {
    size: 'medium',
    variant: 'default',
    bordered: false,
    split: true,
    hoverable: true,
    selectable: false,
    multiple: false,
    selectedKeys: () => [],
    loading: false,
  })

  // 定义事件，传递给逻辑层
  const emit = defineEmits<{
    'update:selectedKeys': [keys: string[]]
    select: [key: string, selected: boolean, selectedKeys: string[]]
    click: [key: string, event: MouseEvent]
  }>()
</script>
