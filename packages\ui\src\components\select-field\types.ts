/**
 * SelectField 组件类型定义
 * 继承 Select 组件所有功能，并添加表单集成和浮动标签
 */

import type { SelectProps, SelectEmits, SelectOption } from '../select/select'
import type { InputVariant } from '../input/types'

/** SelectField 专有属性 */
export interface SelectFieldOwnProps {
  /** 标签文本 - 支持浮动动画 */
  label?: string
  /** 表单字段名 - 用于表单验证 */
  name: string
  /** 验证规则 */
  rules?: any
  /** 是否必填 */
  required?: boolean
  /** 是否显示验证消息 */
  showMessage?: boolean
  /** 帮助文本 */
  helperText?: string
  /** 标签是否始终浮动（不回落） */
  persistentLabel?: boolean
}

/** SelectField 完整属性，继承 Select 的所有属性，但使用 InputField 的 variant */
export interface SelectFieldProps
  extends Omit<SelectProps, 'validateState' | 'validateMessage' | 'variant'>,
    SelectFieldOwnProps {
  /** 样式变体 - 使用 InputField 的变体类型 */
  variant?: InputVariant
}

/** SelectField 事件 */
export interface SelectFieldEmits {
  'update:value': [value: string | number | Array<string | number> | undefined]
  change: [value: string | number | Array<string | number> | undefined]
  focus: []
  blur: []
  clear: []
  validate: [name: string, isValid: boolean, message: string]
}

/** SelectField 实例方法 */
export interface SelectFieldInstance {
  /** 使选择框获得焦点 */
  focus: () => void
  /** 使选择框失去焦点 */
  blur: () => void
  /** 清空选择框 */
  clear: () => void
  /** 验证字段 */
  validate: () => Promise<boolean>
  /** 重置字段 */
  resetField: () => void
  /** 清除验证 */
  clearValidate: () => void
  /** 选择框元素引用 */
  select: HTMLDivElement | null
  /** 包装器元素引用 */
  wrapper: HTMLDivElement | null
}

/** SelectField 默认属性 */
export const selectFieldPropsDefaults: Partial<SelectFieldProps> = {
  placeholder: '请选择',
  clearable: false,
  disabled: false,
  multiple: false,
  size: 'default',
  variant: 'default',
  emptyText: '暂无数据',
  valueKey: 'value',
  labelKey: 'label',
  required: false,
  showMessage: true,
  persistentLabel: false,
  options: [] as SelectOption[],
}
