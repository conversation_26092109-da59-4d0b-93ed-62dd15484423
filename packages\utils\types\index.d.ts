export * from '../../ui/src/utils';
/**
 * 深度克隆对象
 * @param obj 要克隆的对象
 * @returns 克隆后的对象
 */
export declare function deepClone<T>(obj: T): T;
/**
 * 判断是否为空值
 * @param value 要判断的值
 * @returns 是否为空
 */
export declare function isEmpty(value: any): boolean;
/**
 * 生成唯一ID
 * @param prefix 前缀
 * @returns 唯一ID字符串
 */
export declare function generateId(prefix?: string): string;
/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param decimals 小数位数
 * @returns 格式化后的文件大小字符串
 */
export declare function formatFileSize(bytes: number, decimals?: number): string;
/**
 * 延迟执行
 * @param ms 延迟毫秒数
 * @returns Promise
 */
export declare function sleep(ms: number): Promise<void>;
/**
 * 获取数据类型
 * @param value 要检测的值
 * @returns 数据类型字符串
 */
export declare function getType(value: any): string;
/**
 * 数组去重
 * @param array 要去重的数组
 * @param key 对象数组时指定的唯一键
 * @returns 去重后的数组
 */
export declare function unique<T>(array: T[], key?: keyof T): T[];
/**
 * 对象属性路径取值
 * @param obj 对象
 * @param path 属性路径，如 'a.b.c'
 * @param defaultValue 默认值
 * @returns 取到的值或默认值
 */
export declare function get(obj: any, path: string, defaultValue?: any): any;
/**
 * 对象属性路径设值
 * @param obj 对象
 * @param path 属性路径，如 'a.b.c'
 * @param value 要设置的值
 */
export declare function set(obj: any, path: string, value: any): void;
/**
 * 防抖函数
 */
export declare function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void;
/**
 * 节流函数
 */
export declare function throttle<T extends (...args: any[]) => any>(func: T, limit: number): (...args: Parameters<T>) => void;
//# sourceMappingURL=index.d.ts.map