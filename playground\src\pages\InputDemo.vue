<template>
  <div class="input-demo">
    <h1>🎯 Input 输入框组件演示</h1>
    <p>基于 InputCore + InputAffix + BaseInputWrapper 架构的高性能输入框组件</p>

    <!-- 基础用法 -->
    <section class="demo-section">
      <h2>基础用法 (Basic)</h2>
      <div class="demo-container">
        <div class="input-wrapper">
          <sp-input
            v-model:value="basicValue"
            placeholder="请输入内容"
            style="width: 300px"
          />
          <button
            @click="basicValue = '测试内容'"
            style="margin-top: 10px; padding: 5px 10px"
          >
            设置测试内容
          </button>
          <button
            @click="basicValue = ''"
            style="margin-left: 10px; padding: 5px 10px"
          >
            清空内容
          </button>
        </div>
        <div class="demo-info">
          <h4>输入值:</h4>
          <code>{{ JSON.stringify(basicValue) }}</code>
          <p><small>基础的输入功能</small></p>
        </div>
      </div>
    </section>

    <!-- 不同类型 -->
    <section class="demo-section">
      <h2>不同类型 (Types)</h2>
      <div class="demo-container">
        <div class="input-wrapper">
          <div class="type-group">
            <div class="type-item">
              <label>文本输入:</label>
              <sp-input
                v-model:value="textValue"
                type="text"
                placeholder="文本输入"
                style="width: 200px"
              />
            </div>
            <div class="type-item">
              <label>密码输入:</label>
              <sp-input-password
                v-model:value="passwordValue"
                placeholder="密码输入"
                style="width: 200px"
              />
            </div>
            <div class="type-item">
              <label>邮箱输入:</label>
              <sp-input-email
                v-model:value="emailValue"
                placeholder="邮箱输入"
                style="width: 200px"
              />
            </div>
            <div class="type-item">
              <label>数字输入:</label>
              <sp-input-number
                v-model:value="numberValue"
                placeholder="数字输入"
                style="width: 200px"
              />
            </div>
          </div>
        </div>
        <div class="demo-info">
          <h4>输入值:</h4>
          <code>文本: {{ JSON.stringify(textValue) }}</code>
          <code>密码: {{ JSON.stringify(passwordValue) }}</code>
          <code>邮箱: {{ JSON.stringify(emailValue) }}</code>
          <code>数字: {{ JSON.stringify(numberValue) }}</code>
          <p><small>不同输入类型的演示</small></p>
        </div>
      </div>
    </section>

    <!-- 不同尺寸 -->
    <section class="demo-section">
      <h2>不同尺寸 (Sizes)</h2>
      <div class="demo-container">
        <div class="input-wrapper">
          <div class="size-group">
            <div class="size-item">
              <label>Small:</label>
              <sp-input
                v-model:value="sizeValue1"
                placeholder="小尺寸"
                size="small"
                style="width: 150px"
              />
            </div>
            <div class="size-item">
              <label>Medium:</label>
              <sp-input
                v-model:value="sizeValue2"
                placeholder="中等尺寸"
                size="medium"
                style="width: 180px"
              />
            </div>
            <div class="size-item">
              <label>Large:</label>
              <sp-input
                v-model:value="sizeValue3"
                placeholder="大尺寸"
                size="large"
                style="width: 200px"
              />
            </div>
          </div>
        </div>
        <div class="demo-info">
          <h4>输入值:</h4>
          <code>Small: {{ JSON.stringify(sizeValue1) }}</code>
          <code>Medium: {{ JSON.stringify(sizeValue2) }}</code>
          <code>Large: {{ JSON.stringify(sizeValue3) }}</code>
          <p><small>三种不同的尺寸规格</small></p>
        </div>
      </div>
    </section>

    <!-- 清除和字数限制 -->
    <section class="demo-section">
      <h2>清除和字数限制 (Clearable & Maxlength)</h2>
      <div class="demo-container">
        <div class="input-wrapper">
          <div class="feature-group">
            <div class="feature-item">
              <label>可清除输入:</label>
              <sp-input
                v-model:value="clearableValue"
                placeholder="可清除的输入框"
                clearable
                style="width: 250px"
              />
            </div>
            <div class="feature-item">
              <label>字数限制 (50字符):</label>
              <sp-input
                v-model:value="maxlengthValue"
                placeholder="最多50个字符"
                :maxlength="50"
                showWordLimit
                style="width: 250px"
              />
            </div>
            <div class="feature-item">
              <label>组合功能:</label>
              <sp-input
                v-model:value="combinedValue"
                placeholder="清除 + 字数限制"
                clearable
                :maxlength="30"
                showWordLimit
                style="width: 250px"
              />
            </div>
          </div>
        </div>
        <div class="demo-info">
          <h4>输入值:</h4>
          <code>可清除: {{ JSON.stringify(clearableValue) }}</code>
          <code>字数限制: {{ JSON.stringify(maxlengthValue) }}</code>
          <code>组合功能: {{ JSON.stringify(combinedValue) }}</code>
          <p><small>清除按钮和字数统计功能</small></p>
        </div>
      </div>
    </section>

    <!-- 文本域模式 -->
    <section class="demo-section">
      <h2>文本域模式 (Textarea)</h2>
      <div class="demo-container">
        <div class="input-wrapper">
          <div class="textarea-group">
            <div class="textarea-item">
              <label>基础文本域:</label>
              <sp-input
                v-model:value="textareaValue"
                textarea
                placeholder="请输入多行文本..."
                :rows="4"
                style="width: 350px"
              />
            </div>
            <div class="textarea-item">
              <label>带字数限制:</label>
              <sp-input
                v-model:value="textareaLimitValue"
                textarea
                placeholder="最多200个字符..."
                :rows="3"
                :maxlength="200"
                showWordLimit
                style="width: 350px"
              />
            </div>
            <div class="textarea-item">
              <label>自动高度调整:</label>
              <sp-input
                v-model:value="textareaAutoValue"
                textarea
                placeholder="自动调整高度..."
                :autosize="{ minRows: 2, maxRows: 6 }"
                style="width: 350px"
              />
            </div>
          </div>
        </div>
        <div class="demo-info">
          <h4>文本域值:</h4>
          <code>基础: {{ JSON.stringify(textareaValue) }}</code>
          <code>限制: {{ JSON.stringify(textareaLimitValue) }}</code>
          <code>自动: {{ JSON.stringify(textareaAutoValue) }}</code>
          <p><small>支持多行文本输入</small></p>
        </div>
      </div>
    </section>

    <!-- 验证状态演示 -->
    <section class="demo-section">
      <h2>✅ 表单验证状态演示</h2>
      <div class="demo-container">
        <div class="input-wrapper">
          <div class="validation-group">
            <div class="validation-item">
              <label>✅ 成功状态:</label>
              <sp-input
                v-model:value="successValue"
                placeholder="验证成功"
                validateState="success"
                validateMessage="验证成功！输入格式正确"
                style="width: 250px"
              />
            </div>

            <div class="validation-item">
              <label>⚠️ 警告状态:</label>
              <sp-input
                v-model:value="warningValue"
                placeholder="需要注意"
                validateState="warning"
                validateMessage="警告：建议使用更强的密码"
                style="width: 250px"
              />
            </div>

            <div class="validation-item">
              <label>❌ 错误状态:</label>
              <sp-input
                v-model:value="errorValue"
                placeholder="验证失败"
                validateState="error"
                validateMessage="错误：邮箱格式不正确"
                style="width: 250px"
              />
            </div>
          </div>
        </div>
        <div class="demo-info">
          <h4>验证状态值:</h4>
          <code>成功: {{ JSON.stringify(successValue) }}</code>
          <code>警告: {{ JSON.stringify(warningValue) }}</code>
          <code>错误: {{ JSON.stringify(errorValue) }}</code>
          <p><small>不同验证状态下的边框颜色和消息提示</small></p>
        </div>
      </div>
    </section>

    <!-- 禁用和只读状态 -->
    <section class="demo-section">
      <h2>禁用和只读状态 (Disabled & Readonly)</h2>
      <div class="demo-container">
        <div class="input-wrapper">
          <div class="state-group">
            <div class="state-item">
              <label>正常状态:</label>
              <sp-input
                v-model:value="normalValue"
                placeholder="正常输入"
                style="width: 200px"
              />
            </div>
            <div class="state-item">
              <label>禁用状态:</label>
              <sp-input
                v-model:value="disabledValue"
                placeholder="禁用状态"
                disabled
                style="width: 200px"
              />
            </div>
            <div class="state-item">
              <label>只读状态:</label>
              <sp-input
                v-model:value="readonlyValue"
                placeholder="只读状态"
                readonly
                style="width: 200px"
              />
            </div>
          </div>
        </div>
        <div class="demo-info">
          <h4>状态说明:</h4>
          <p>
            •
            <strong>disabled</strong>
            : 完全禁用，无法操作
          </p>
          <p>
            •
            <strong>readonly</strong>
            : 只读模式，可以查看但不能修改
          </p>
          <p>
            •
            <strong>normal</strong>
            : 正常可操作状态
          </p>
        </div>
      </div>
    </section>

    <!-- Slip 动画标签 -->
    <section class="demo-section">
      <h2>✨ Slip 动画标签</h2>
      <div class="demo-container">
        <div class="input-wrapper">
          <div class="slip-group">
            <div class="slip-item">
              <label>基础 Slip:</label>
              <sp-input
                v-model:value="slipValue1"
                slip
                placeholder="用户名"
                style="width: 200px"
              />
            </div>
            <div class="slip-item">
              <label>Slip + 密码:</label>
              <sp-input-password
                v-model:value="slipValue2"
                slip
                placeholder="密码"
                style="width: 200px"
              />
            </div>
            <div class="slip-item">
              <label>Slip + 清除:</label>
              <sp-input
                v-model:value="slipValue3"
                slip
                clearable
                placeholder="邮箱地址"
                style="width: 200px"
              />
            </div>
          </div>
        </div>
        <div class="demo-info">
          <h4>Slip 值:</h4>
          <code>基础: {{ JSON.stringify(slipValue1) }}</code>
          <code>密码: {{ JSON.stringify(slipValue2) }}</code>
          <code>清除: {{ JSON.stringify(slipValue3) }}</code>
          <p><small>优雅的浮动标签动画效果</small></p>
        </div>
      </div>
    </section>

    <!-- 格子输入模式 -->
    <section class="demo-section">
      <h2>📝 格子输入模式 (Texttabel)</h2>
      <div class="demo-container">
        <div class="input-wrapper">
          <div class="tabel-group">
            <div class="tabel-item">
              <label>6位验证码:</label>
              <sp-input
                v-model:value="tabelValue6"
                texttabel
                :td="6"
                placeholder="验证码"
              />
            </div>
            <div class="tabel-item">
              <label>4位PIN码:</label>
              <sp-input
                v-model:value="tabelValue4"
                texttabel
                :td="4"
                placeholder="PIN"
              />
            </div>
          </div>
        </div>
        <div class="demo-info">
          <h4>格子输入值:</h4>
          <code>6位: {{ JSON.stringify(tabelValue6) }}</code>
          <code>4位: {{ JSON.stringify(tabelValue4) }}</code>
          <p><small>适用于验证码、PIN码等场景</small></p>
        </div>
      </div>
    </section>

    <!-- 组合演示 -->
    <section class="demo-section">
      <h2>🎨 完整功能组合</h2>
      <div class="demo-container">
        <div class="input-wrapper">
          <div class="full-feature-item">
            <label>完整功能演示:</label>
            <sp-input
              v-model:value="fullFeatureValue"
              slip
              clearable
              showWordLimit
              :maxlength="100"
              placeholder="用户信息"
              validateState="success"
              validateMessage="输入格式正确"
              style="width: 350px"
            />
          </div>
        </div>
        <div class="demo-info">
          <h4>完整功能值:</h4>
          <code>{{ JSON.stringify(fullFeatureValue) }}</code>
          <p><small>Slip + 清除 + 字数统计 + 验证状态的完整组合</small></p>
        </div>
      </div>
    </section>

    <!-- 组件架构说明 -->
    <section class="demo-section">
      <h2>🚀 组件架构优势</h2>
      <div class="architecture-info">
        <div class="architecture-item">
          <strong>InputCore:</strong>
          核心输入逻辑，处理所有输入事件和状态管理
        </div>
        <div class="architecture-item">
          <strong>InputAffix:</strong>
          前后缀系统，支持图标、按钮等各种附件
        </div>
        <div class="architecture-item">
          <strong>BaseInputWrapper:</strong>
          基础包装器，统一样式和布局规范
        </div>
        <div class="architecture-item">
          <strong>验证系统:</strong>
          集成表单验证，支持多种验证状态和消息提示
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  // 基础演示数据
  const basicValue = ref('')

  // 不同类型的输入值
  const textValue = ref('')
  const passwordValue = ref('')
  const emailValue = ref('')
  const numberValue = ref('')

  // 不同尺寸的输入值
  const sizeValue1 = ref('')
  const sizeValue2 = ref('')
  const sizeValue3 = ref('')

  // 功能特性的输入值
  const clearableValue = ref('可以清除的内容')
  const maxlengthValue = ref('')
  const combinedValue = ref('')

  // 文本域值
  const textareaValue = ref('')
  const textareaLimitValue = ref('')
  const textareaAutoValue = ref('')

  // 验证状态值
  const successValue = ref('<EMAIL>')
  const warningValue = ref('123456')
  const errorValue = ref('invalid-email')

  // 状态演示值
  const normalValue = ref('')
  const disabledValue = ref('禁用状态的内容')
  const readonlyValue = ref('只读状态的内容')

  // Slip 动画值
  const slipValue1 = ref('')
  const slipValue2 = ref('')
  const slipValue3 = ref('')

  // 格子输入值
  const tabelValue6 = ref('')
  const tabelValue4 = ref('')

  // 完整功能演示值
  const fullFeatureValue = ref('')
</script>

<style scoped>
  .input-demo {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .input-demo h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 10px;
  }

  .input-demo > p {
    text-align: center;
    color: #7f8c8d;
    margin-bottom: 40px;
  }

  .demo-section {
    margin-bottom: 50px;
    padding: 30px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background: #fafafa;
  }

  .demo-section h2 {
    color: #34495e;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #ecf0f1;
  }

  .demo-container {
    display: flex;
    gap: 30px;
    align-items: flex-start;
  }

  .input-wrapper {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .demo-info {
    flex: 1;
    min-width: 250px;
  }

  .demo-info h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
  }

  .demo-info code {
    display: block;
    padding: 10px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    color: #495057;
    word-break: break-all;
    margin-bottom: 8px;
    font-size: 12px;
  }

  .demo-info p {
    margin: 10px 0 0 0;
    color: #6c757d;
  }

  .demo-info small {
    font-size: 12px;
    color: #999;
  }

  /* 组件组样式 */
  .type-group,
  .size-group,
  .feature-group,
  .textarea-group,
  .validation-group,
  .state-group,
  .slip-group,
  .tabel-group {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .type-item,
  .size-item,
  .feature-item,
  .textarea-item,
  .validation-item,
  .state-item,
  .slip-item,
  .tabel-item,
  .full-feature-item {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .type-item label,
  .size-item label,
  .feature-item label,
  .textarea-item label,
  .validation-item label,
  .state-item label,
  .slip-item label,
  .tabel-item label,
  .full-feature-item label {
    width: 120px;
    font-weight: 500;
    color: #2c3e50;
    flex-shrink: 0;
  }

  .textarea-item,
  .full-feature-item {
    align-items: flex-start;
  }

  .textarea-item label,
  .full-feature-item label {
    margin-top: 8px;
  }

  /* 架构说明样式 */
  .architecture-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
  }

  .architecture-item {
    padding: 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #ddd;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .architecture-item strong {
    color: #2c3e50;
  }
</style>
