<template>
  <div class="select-new-test">
    <h1>🎯 新版 Select 组件测试</h1>
    <p>基于设计文档实现的 Select 组件，复用 Input、Dropdown 和 Menu 组件</p>

    <div class="demo-container">
      <section class="demo-section">
        <h3>📋 基础单选功能</h3>
        <div class="demo-item">
          <label>基础单选：</label>
          <sp-select
            v-model:value="basicValue"
            :options="basicOptions"
            placeholder="请选择一个选项"
            style="width: 200px"
          />
          <span class="demo-value">选中值: {{ basicValue || '无' }}</span>
        </div>

        <div class="demo-item">
          <label>可清除：</label>
          <sp-select
            v-model:value="clearableValue"
            :options="basicOptions"
            placeholder="请选择"
            clearable
            style="width: 200px"
          />
          <span class="demo-value">选中值: {{ clearableValue || '无' }}</span>
        </div>

        <div class="demo-item">
          <label>禁用状态：</label>
          <sp-select
            v-model:value="disabledValue"
            :options="basicOptions"
            placeholder="禁用状态"
            disabled
            style="width: 200px"
          />
          <span class="demo-value">选中值: {{ disabledValue || '无' }}</span>
        </div>
      </section>

      <section class="demo-section">
        <h3>🏷️ 多选模式</h3>
        <div class="demo-item">
          <label>基础多选：</label>
          <sp-select
            v-model:value="multipleValue"
            :options="basicOptions"
            placeholder="请选择多个选项"
            mode="multiple"
            style="width: 300px"
          />
          <span class="demo-value">
            选中值: {{ multipleValue.length ? multipleValue.join(', ') : '无' }}
          </span>
        </div>

        <div class="demo-item">
          <label>多选 + 清除：</label>
          <sp-select
            v-model:value="multipleClearableValue"
            :options="basicOptions"
            placeholder="多选 + 清除"
            mode="multiple"
            clearable
            style="width: 300px"
          />
          <span class="demo-value">
            选中值:
            {{
              multipleClearableValue.length
                ? multipleClearableValue.join(', ')
                : '无'
            }}
          </span>
        </div>
      </section>

      <section class="demo-section">
        <h3>📏 尺寸变体</h3>
        <div class="demo-col">
          <div class="demo-item">
            <label>小尺寸：</label>
            <sp-select
              v-model:value="sizeValue"
              :options="basicOptions"
              placeholder="小尺寸"
              size="small"
              style="width: 150px"
            />
          </div>
          <div class="demo-item">
            <label>默认尺寸：</label>
            <sp-select
              v-model:value="sizeValue"
              :options="basicOptions"
              placeholder="默认尺寸"
              size="medium"
              style="width: 180px"
            />
          </div>
          <div class="demo-item">
            <label>大尺寸：</label>
            <sp-select
              v-model:value="sizeValue"
              :options="basicOptions"
              placeholder="大尺寸"
              size="large"
              style="width: 220px"
            />
          </div>
        </div>
      </section>

      <section class="demo-section">
        <h3>🎯 测试按钮</h3>
        <div class="demo-row">
          <button @click="testSetValue">设置值为 2</button>
          <button @click="testClearValue">清空值</button>
          <button @click="testMultipleValue">设置多选值</button>
          <button @click="logCurrentValues">打印当前值</button>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select as SpSelect } from '../../../packages/ui/src/components/select'

  // 测试数据
  const basicValue = ref('')
  const clearableValue = ref('')
  const disabledValue = ref('option2')
  const multipleValue = ref([])
  const multipleClearableValue = ref(['option1'])
  const sizeValue = ref('')

  // 基础选项
  const basicOptions = [
    { label: '选项一', value: 1 },
    { label: '选项二', value: 'option2' },
    { label: '选项三', value: 'option3' },
    { label: '选项四', value: 'option4' },
    { label: '选项五', value: 'option5' },
  ]

  // 测试函数
  const testSetValue = () => {
    basicValue.value = 'option2'
    console.log('设置基础值为 option2')
  }

  const testClearValue = () => {
    basicValue.value = ''
    clearableValue.value = ''
    console.log('清空单选值')
  }

  const testMultipleValue = () => {
    multipleValue.value = [1, 'option3']
    console.log('设置多选值:', multipleValue.value)
  }

  const logCurrentValues = () => {
    console.log('📊 当前所有值:')
    console.log('基础值:', basicValue.value)
    console.log('可清除值:', clearableValue.value)
    console.log('禁用值:', disabledValue.value)
    console.log('多选值:', multipleValue.value)
    console.log('多选清除值:', multipleClearableValue.value)
    console.log('尺寸值:', sizeValue.value)
  }
</script>

<style scoped>
  .select-new-test {
    padding: 20px;
    max-width: 1000px;
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .select-new-test h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 10px;
  }

  .select-new-test p {
    text-align: center;
    color: #666;
    margin-bottom: 30px;
  }

  .demo-container {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e4e7ed;
  }

  .demo-section {
    margin-bottom: 40px;
  }

  .demo-section:last-child {
    margin-bottom: 0;
  }

  .demo-section h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #606266;
    font-size: 18px;
  }

  .demo-row {
    display: flex;
    gap: 30px;
    margin-bottom: 20px;
    flex-wrap: wrap;
  }

  .demo-col {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 20px;
  }

  .demo-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
    flex-wrap: wrap;
  }

  .demo-item label {
    font-weight: 600;
    color: #606266;
    min-width: 120px;
    font-size: 14px;
  }

  .demo-value {
    color: #909399;
    font-size: 12px;
    background: #f5f7fa;
    padding: 4px 8px;
    border-radius: 4px;
    min-width: 100px;
  }

  button {
    padding: 8px 16px;
    background: #409eff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
  }

  button:hover {
    background: #66b1ff;
  }
</style>
