/**
 * 输入框控件系统入口
 * 导出所有控件相关的类型、组件和工具
 */

// 类型定义
export type * from './types'

// 控件注册表
export { 
  controlRegistry, 
  registerControl, 
  getControl, 
  getAllControls, 
  getControlsByPosition 
} from './registry'

// 控件渲染器
export { default as ControlRenderer } from './ControlRenderer.vue'

// 内置控件组件
export { default as ClearControl } from './ClearControl.vue'
export { default as PasswordControl } from './PasswordControl.vue'
export { default as IconControl } from './IconControl.vue'
export { default as WordLimitControl } from './WordLimitControl.vue'

// 现有控件组件（保持向后兼容）
export { default as NumberControls } from './number.vue'
export { default as SearchControls } from './search.vue'
export { default as TelControls } from './tel.vue'
export { default as SmsCodeControls } from './smscode.vue'

// 控件配置
export * from './configs'

// Composable
export { useInputControls } from '../composables/useInputControls'
