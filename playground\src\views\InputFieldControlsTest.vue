<!--
  InputField 控件系统测试页面
  测试新的统一控件系统是否正常工作
-->

<template>
  <div class="input-field-controls-test">
    <h1>InputField 控件系统测试</h1>

    <div class="test-section">
      <h2>基础控件测试</h2>

      <!-- 清除按钮测试 -->
      <div class="test-item">
        <h3>清除按钮 - 测试点击后聚焦</h3>
        <sp-input-field
          v-model:value="clearableValue"
          label="可清除输入框"
          placeholder="输入内容测试清除功能"
          clearable
        />
        <p>当前值: {{ clearableValue }}</p>
      </div>

      <!-- 密码切换测试 -->
      <div class="test-item">
        <h3>密码切换</h3>
        <sp-input-field
          v-model:value="passwordValue"
          label="密码输入框"
          type="password"
          placeholder="输入密码测试显示/隐藏"
          show-password
        />
        <p>当前值: {{ passwordValue }}</p>
      </div>

      <!-- 数字控制测试 -->
      <div class="test-item">
        <h3>数字控制</h3>
        <sp-input-field
          v-model:value="numberValue"
          label="数字输入框"
          type="number"
          placeholder="数字输入"
          :min="0"
          :max="100"
          :step="1"
        />
        <p>当前值: {{ numberValue }}</p>
      </div>

      <!-- 搜索控制测试 -->
      <div class="test-item">
        <h3>搜索控制</h3>
        <sp-input-field
          v-model:value="searchValue"
          label="搜索输入框"
          type="search"
          placeholder="输入搜索内容"
          @search="handleSearch"
        />
        <p>当前值: {{ searchValue }}</p>
        <p>搜索历史: {{ searchHistory.join(', ') }}</p>
      </div>

      <!-- 字数统计测试 -->
      <div class="test-item">
        <h3>字数统计</h3>
        <sp-input-field
          v-model:value="limitedValue"
          label="限制字数输入框"
          placeholder="最多输入50个字符"
          :maxlength="50"
          show-word-limit
        />
        <p>当前值: {{ limitedValue }}</p>
      </div>

      <!-- 图标控件测试 -->
      <div class="test-item">
        <h3>图标控件</h3>
        <sp-input-field
          v-model:value="iconValue"
          label="带图标输入框"
          placeholder="前后都有图标"
          prepend-icon-inner="Search"
          append-icon-inner="Settings"
          @click:prepend-inner="handlePrependClick"
          @click:append-inner="handleAppendClick"
        />
        <p>当前值: {{ iconValue }}</p>
        <p>点击事件: {{ clickEvents.join(', ') }}</p>
      </div>

      <!-- 组合控件测试 -->
      <div class="test-item">
        <h3>组合控件</h3>
        <sp-input-field
          v-model:value="combinedValue"
          label="组合控件输入框"
          type="password"
          placeholder="密码 + 清除 + 字数限制"
          :maxlength="20"
          show-password
          clearable
          show-word-limit
        />
        <p>当前值: {{ combinedValue }}</p>
      </div>
    </div>

    <div class="test-section">
      <h2>控件系统信息</h2>
      <div class="system-info">
        <p>✅ 控件系统已启用</p>
        <p>✅ 统一控件渲染</p>
        <p>✅ 动态控件配置</p>
        <p>✅ 事件统一处理</p>
        <p>✅ 类型安全支持</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  // import { SpInputField } from '@packages/ui'

  // 测试数据
  const clearableValue = ref('可以清除的内容')
  const passwordValue = ref('secret123')
  const numberValue = ref(42)
  const searchValue = ref('')
  const limitedValue = ref('测试字数限制')
  const iconValue = ref('图标测试')
  const combinedValue = ref('combined')

  // 搜索历史
  const searchHistory = ref<string[]>([])

  // 点击事件记录
  const clickEvents = ref<string[]>([])

  // 事件处理
  const handleSearch = () => {
    if (searchValue.value.trim()) {
      searchHistory.value.push(searchValue.value)
      console.log('搜索:', searchValue.value)
    }
  }

  const handlePrependClick = () => {
    clickEvents.value.push('前置图标点击')
    console.log('前置图标点击')
  }

  const handleAppendClick = () => {
    clickEvents.value.push('后置图标点击')
    console.log('后置图标点击')
  }
</script>

<script lang="ts">
  export default {
    name: 'InputFieldControlsTest',
  }
</script>

<style scoped>
  .input-field-controls-test {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
  }

  .test-section {
    margin-bottom: 40px;
  }

  .test-item {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;
  }

  .test-item h3 {
    margin-top: 0;
    color: #333;
  }

  .test-item p {
    margin: 10px 0;
    color: #666;
    font-size: 14px;
  }

  .system-info {
    padding: 20px;
    background: #f0f8ff;
    border-radius: 8px;
    border-left: 4px solid #007acc;
  }

  .system-info p {
    margin: 8px 0;
    color: #007acc;
    font-weight: 500;
  }

  h1 {
    color: #333;
    text-align: center;
    margin-bottom: 30px;
  }

  h2 {
    color: #555;
    border-bottom: 2px solid #007acc;
    padding-bottom: 10px;
  }
</style>
