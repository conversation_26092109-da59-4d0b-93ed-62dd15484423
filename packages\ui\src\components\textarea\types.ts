/**
 * Textarea 组件类型定义 - 复用 Input 的类型结构
 */

import type { InputProps, InputEmits } from '../input/types'

/** 文本域调整大小方式 */
export type TextareaResize = 'none' | 'both' | 'horizontal' | 'vertical'

/** 文本域属性 - 基于 InputProps 扩展，但值类型只支持 string */
export interface TextareaProps extends Omit<InputProps, 'type' | 'value'> {
  /** 绑定值 - 使用 v-model:value，只支持字符串 */
  value?: string
  /** 文本域行数 */
  rows?: number
  /** 文本域列数 */
  cols?: number
  /** 调整大小方式 */
  resize?: TextareaResize
  /** 是否自动调整高度 */
  autosize?: boolean | { minRows?: number; maxRows?: number }
}

/** 文本域事件 - 只支持字符串值 */
export interface TextareaEmits {
  /** 值更新事件 - 用于 v-model:value */
  (e: 'update:value', value: string | undefined): void
  /** 值改变事件 */
  (e: 'change', value: string | undefined): void
  /** 输入事件 */
  (e: 'input', event: Event): void
  /** 获得焦点事件 */
  (e: 'focus', event: FocusEvent): void
  /** 失去焦点事件 */
  (e: 'blur', event: FocusEvent): void
  /** 键盘按下事件 */
  (e: 'keydown', event: KeyboardEvent): void
  /** 清除事件 */
  (e: 'clear'): void
}

/** 文本域实例方法 */
export interface TextareaInstance {
  /** 使文本域获得焦点 */
  focus: () => void
  /** 使文本域失去焦点 */
  blur: () => void
  /** 选中文本域中的文字 */
  select: () => void
  /** 清空文本域 */
  clear: () => void
  /** 调整文本域高度（autosize 模式下） */
  adjustHeight: () => void
  /** 重置用户手动调整状态，恢复自动调整 */
  resetUserResize: () => void
  /** 文本域元素引用 */
  textarea: HTMLTextAreaElement | null
  /** 包装器元素引用 */
  wrapper: HTMLDivElement | null
}
