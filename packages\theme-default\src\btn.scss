// ================================
// Speed UI Btn 组件样式
// ================================

@use 'sass:map';
@use './common/var.scss' as *;

// Btn 组件样式已经在组件内部定义
// 这里可以添加全局主题变量覆盖或扩展样式

// 响应式设计支持
@media (max-width: 768px) {
  .sp-btn {
    // 在小屏幕上稍微增加触摸目标大小
    min-height: 36px;
    padding: 10px 18px;
  }

  .sp-btn.sp-btn--small {
    min-height: 28px;
    padding: 6px 14px;
  }

  .sp-btn.sp-btn--large {
    min-height: 44px;
    padding: 14px 26px;
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .sp-btn {
    border-width: 2px;
  }

  .sp-btn:focus-visible {
    outline-width: 3px;
  }
}

// 减少动画模式支持
@media (prefers-reduced-motion: reduce) {
  .sp-btn {
    transition: none;
  }
}

// 深色模式支持（如果需要）
@media (prefers-color-scheme: dark) {
  .sp-btn.sp-btn--outlined {
    background-color: rgba(255, 255, 255, 0.05);
  }

  .sp-btn.sp-btn--text {
    background-color: transparent;
  }

  .sp-btn.sp-btn--text:hover:not(:disabled) {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

.sp-btn {
  /* 基础样式重置 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: none;
  padding: 0;
  margin: 0;
  font: inherit;
  color: inherit;
  cursor: pointer;
  outline: none;
  text-decoration: none;
  box-sizing: border-box;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;

  /* 基础尺寸和样式 */
  min-height: 32px;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  /* 默认变体样式 */
  background-color: var(--sp-color-primary, #1890ff);
  color: #ffffff;
  border: 1px solid var(--sp-color-primary, #1890ff);

  // 确保使用全局主题变量
  --sp-color-primary: var(--sp-color-primary, #1890ff);
  --sp-color-primary-hover: var(--sp-color-primary-hover, #40a9ff);
  --sp-color-primary-active: var(--sp-color-primary-active, #096dd9);
  --sp-color-primary-lightest: var(--sp-color-primary-lightest, #f0f9ff);
  --sp-color-primary-light: var(--sp-color-primary-light, #e6f7ff);
}

/* 禁用状态 */
.sp-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* 默认变体 */
.sp-btn.sp-btn--default {
  background-color: var(--sp-color-primary, #1890ff);
  color: #ffffff;
  border: 1px solid var(--sp-color-primary, #1890ff);
}

.sp-btn.sp-btn--default:hover:not(:disabled),
.sp-btn.sp-btn--default.sp-btn--hovered:not(:disabled) {
  background-color: var(--sp-color-primary-hover, #40a9ff);
  border-color: var(--sp-color-primary-hover, #40a9ff);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.sp-btn.sp-btn--default:active:not(:disabled),
.sp-btn.sp-btn--default.sp-btn--pressed:not(:disabled) {
  background-color: var(--sp-color-primary-active, #096dd9);
  border-color: var(--sp-color-primary-active, #096dd9);
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.4);
}

/* Outlined 变体 */
.sp-btn.sp-btn--outlined {
  background-color: transparent;
  color: var(--sp-color-primary, #1890ff);
  border: 1px solid var(--sp-color-primary, #1890ff);
}

.sp-btn.sp-btn--outlined:hover:not(:disabled),
.sp-btn.sp-btn--outlined.sp-btn--hovered:not(:disabled) {
  background-color: var(--sp-color-primary-lightest, #f0f9ff);
  color: var(--sp-color-primary-hover, #40a9ff);
  border-color: var(--sp-color-primary-hover, #40a9ff);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.sp-btn.sp-btn--outlined:active:not(:disabled),
.sp-btn.sp-btn--outlined.sp-btn--pressed:not(:disabled) {
  background-color: var(--sp-color-primary-light, #e6f7ff);
  color: var(--sp-color-primary-active, #096dd9);
  border-color: var(--sp-color-primary-active, #096dd9);
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);
}

/* Text 变体 */
.sp-btn.sp-btn--text {
  background-color: transparent;
  color: var(--sp-color-primary, #1890ff);
  border: 1px solid transparent;
  padding: 4px 8px;
}

.sp-btn.sp-btn--text:hover:not(:disabled),
.sp-btn.sp-btn--text.sp-btn--hovered:not(:disabled) {
  background-color: var(--sp-color-primary-lightest, #f0f9ff);
  color: var(--sp-color-primary-hover, #40a9ff);
}

.sp-btn.sp-btn--text:active:not(:disabled),
.sp-btn.sp-btn--text.sp-btn--pressed:not(:disabled) {
  background-color: var(--sp-color-primary-light, #e6f7ff);
  color: var(--sp-color-primary-active, #096dd9);
}

/* 尺寸变体 */
.sp-btn.sp-btn--small {
  min-height: 24px;
  padding: 4px 12px;
  font-size: 12px;
  border-radius: 4px;
}

.sp-btn.sp-btn--medium {
  min-height: 32px;
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 6px;
}

.sp-btn.sp-btn--large {
  min-height: 40px;
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 8px;
}

/* 焦点样式 */
.sp-btn:focus-visible {
  outline: 2px solid var(--sp-color-primary, #1890ff);
  outline-offset: 2px;
}

/* 确保内容居中 */
.sp-btn > * {
  display: flex;
  align-items: center;
  justify-content: center;
}
