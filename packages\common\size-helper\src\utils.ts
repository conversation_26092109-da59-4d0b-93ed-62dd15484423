/**
 * Speed UI 组件尺寸工具函数
 */

import type { ComponentSize, ScrollbarSize } from './types'
import {
  SIZE_MAPPING,
  SIZE_VALUE_MAP,
  SIZE_PIXEL_MAP,
  COMPONENT_SIZES,
} from './constants'

/**
 * 将标准组件尺寸映射到滚动条尺寸
 * @param size 组件尺寸
 * @returns 滚动条尺寸
 */
export function mapSizeToScrollbar(size: ComponentSize): ScrollbarSize {
  return SIZE_MAPPING.selectToScrollbar[size]
}

/**
 * 获取尺寸对应的数值（用于计算）
 * @param size 组件尺寸
 * @returns 尺寸数值
 */
export function getSizeValue(size: ComponentSize): number {
  return SIZE_VALUE_MAP[size]
}

/**
 * 获取尺寸对应的像素值（字符串格式）
 * @param size 组件尺寸
 * @returns 像素值字符串
 */
export function getSizePixels(size: ComponentSize): string {
  return SIZE_PIXEL_MAP[size]
}

/**
 * 验证尺寸是否有效
 * @param size 待验证的尺寸
 * @returns 是否为有效尺寸
 */
export function isValidSize(size: string): size is ComponentSize {
  return COMPONENT_SIZES.includes(size as ComponentSize)
}

/**
 * 获取尺寸对应的 CSS 类名后缀
 * @param size 组件尺寸
 * @returns CSS 类名后缀
 */
export function getSizeClass(size: ComponentSize): string {
  return size
}

/**
 * 获取尺寸对应的 CSS 修饰符类名
 * @param size 组件尺寸
 * @param prefix 类名前缀
 * @returns 完整的修饰符类名
 */
export function getSizeModifier(size: ComponentSize, prefix: string): string {
  return `${prefix}--${size}`
}

/**
 * 标准化尺寸值（处理 undefined 或无效值）
 * @param size 尺寸值
 * @param defaultSize 默认尺寸
 * @returns 标准化后的尺寸值
 */
export function normalizeSizeValue(
  size: ComponentSize | undefined,
  defaultSize: ComponentSize = 'default'
): ComponentSize {
  if (!size || !isValidSize(size)) {
    return defaultSize
  }
  return size
}

/**
 * 比较两个尺寸的大小
 * @param size1 尺寸1
 * @param size2 尺寸2
 * @returns 比较结果：-1(小于), 0(等于), 1(大于)
 */
export function compareSize(
  size1: ComponentSize,
  size2: ComponentSize
): number {
  const value1 = getSizeValue(size1)
  const value2 = getSizeValue(size2)

  if (value1 < value2) return -1
  if (value1 > value2) return 1
  return 0
}
