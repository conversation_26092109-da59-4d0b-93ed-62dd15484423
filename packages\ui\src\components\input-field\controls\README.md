# 输入框控件系统

这是一个统一的输入框控件管理系统，用于管理 InputField 组件中的所有控件（清除按钮、密码切换、数字增减、搜索等）。

## 🎯 设计目标

- **统一管理**: 所有控件通过统一的配置系统管理
- **可扩展性**: 支持自定义控件的注册和使用
- **类型安全**: 完整的 TypeScript 类型支持
- **事件统一**: 统一的事件处理机制
- **条件渲染**: 基于条件的动态控件显示

## 🏗️ 架构概览

```
控件系统架构
├── types.ts           # 类型定义
├── registry.ts        # 控件注册表
├── configs.ts         # 内置控件配置
├── ControlRenderer.vue # 控件渲染器
├── 控件组件/
│   ├── ClearControl.vue
│   ├── PasswordControl.vue
│   ├── IconControl.vue
│   ├── WordLimitControl.vue
│   └── ... (现有控件)
└── composables/
    └── useInputControls.ts
```

## 📦 核心组件

### 1. ControlRenderer
统一的控件渲染器，负责根据配置动态渲染控件。

```vue
<ControlRenderer
  position="append"
  :context="controlContext"
  @control-event="handleControlEvent"
/>
```

### 2. 控件配置系统
每个控件都有一个配置对象，定义其行为：

```typescript
const clearControlConfig: ControlConfig = {
  type: 'clear',
  component: ClearControl,
  position: 'append',
  order: 100,
  condition: (context) => context.inputLogic.showClearIcon.value,
  propsFactory: (context) => ({ /* props */ }),
  eventsFactory: (context) => ({ /* events */ })
}
```

### 3. 控件注册表
管理所有控件的注册和获取：

```typescript
import { registerControl, getControl } from './registry'

// 注册自定义控件
registerControl(myCustomControlConfig)

// 获取控件配置
const config = getControl('clear')
```

## 🔧 使用方式

### 在 InputField 中使用

```vue
<template>
  <FieldContainer>
    <!-- 前置控件 -->
    <template #prepend="slotProps">
      <ControlRenderer
        position="prepend"
        :context="createControlContext(slotProps)"
        @control-event="handleControlEvent"
      />
    </template>
    
    <!-- 后置控件 -->
    <template #append="slotProps">
      <ControlRenderer
        position="append"
        :context="createControlContext(slotProps)"
        @control-event="handleControlEvent"
      />
    </template>
  </FieldContainer>
</template>
```

### 创建自定义控件

1. **创建控件组件**:
```vue
<!-- MyCustomControl.vue -->
<template>
  <button @click="handleClick">{{ text }}</button>
</template>

<script setup lang="ts">
interface Props {
  text: string
  disabled?: boolean
}

interface Emits {
  (e: 'custom-action'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handleClick = () => {
  if (!props.disabled) {
    emit('custom-action')
  }
}
</script>
```

2. **创建控件配置**:
```typescript
const customControlConfig: ControlConfig = {
  type: 'custom',
  component: MyCustomControl,
  position: 'append',
  order: 30,
  condition: (context) => context.props.showCustom,
  propsFactory: (context) => ({
    text: context.props.customText || 'Action',
    disabled: context.computed.computedDisabled
  }),
  eventsFactory: (context) => ({
    'custom-action': () => context.emit('custom-action')
  })
}
```

3. **注册控件**:
```typescript
import { registerControl } from './registry'
registerControl(customControlConfig)
```

## 📋 内置控件列表

| 控件类型 | 组件 | 位置 | 顺序 | 说明 |
|---------|------|------|------|------|
| tel | TelControls | prepend | 10 | 电话号码国家选择 |
| icon | IconControl | prepend | 20 | 前置图标 |
| icon | IconControl | append | 40 | 后置图标 |
| wordLimit | WordLimitControl | append | 50 | 字数统计 |
| smscode | SmsCodeControls | append | 60 | 短信验证码 |
| search | SearchControls | append | 70 | 搜索控制 |
| number | NumberControls | append | 80 | 数字增减 |
| password | PasswordControl | append | 90 | 密码切换 |
| clear | ClearControl | append | 100 | 清除按钮 |

## 🎨 优势

### 相比原来的实现

**之前 (410行)**:
```vue
<!-- 硬编码的控件 -->
<sp-icon v-if="showClearIcon" ... />
<sp-icon v-if="showPasswordIcon" ... />
<number-controls v-if="showNumberControls" ... />
<!-- ... 更多硬编码控件 -->
```

**现在 (简洁)**:
```vue
<!-- 统一的控件渲染 -->
<ControlRenderer
  position="append"
  :context="controlContext"
  @control-event="handleControlEvent"
/>
```

### 主要改进

1. **代码减少**: 模板代码从 ~100 行减少到 ~20 行
2. **可维护性**: 控件逻辑集中管理，易于维护
3. **可扩展性**: 新增控件只需注册配置，无需修改主组件
4. **类型安全**: 完整的 TypeScript 支持
5. **事件统一**: 统一的事件处理机制

## 🔄 向后兼容

现有的控件组件（NumberControls、SearchControls 等）保持不变，只是通过新的配置系统来管理，确保 100% 向后兼容。
