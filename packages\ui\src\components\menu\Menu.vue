<template>
  <MenuLogic
    v-bind="$attrs"
    :mode="props.mode"
    :theme="props.theme"
    :selectedKeys="props.selectedKeys"
    :multiple="props.multiple"
    :selectable="props.selectable"
    :openKeys="props.openKeys"
    :defaultOpenKeys="props.defaultOpenKeys"
    :inlineIndent="props.inlineIndent"
    :expandIcon="props.expandIcon"
    :disabled="props.disabled"
    @update:selectedKeys="$emit('update:selectedKeys', $event)"
    @update:openKeys="$emit('update:openKeys', $event)"
    @select="$emit('select', $event)"
    @open-change="$emit('openChange', $event)"
  >
    <slot />
  </MenuLogic>
</template>

<script setup lang="ts">
  import type { MenuProps } from './types'
  import MenuLogic from './MenuLogic.vue'

  // 用户接口层：定义对外API和属性默认值
  const props = withDefaults(defineProps<MenuProps>(), {
    mode: 'vertical',
    theme: 'light',
    selectedKeys: () => [],
    openKeys: () => [],
    inlineIndent: 24,
    selectable: true,
  })

  // 定义事件，传递给逻辑层
  const emit = defineEmits<{
    'update:selectedKeys': [keys: string[]]
    'update:openKeys': [keys: string[]]
    select: [info: { key: string; selectedKeys: string[] }]
    openChange: [openKeys: string[]]
  }>()
</script>
