<template>
  <div class="home">
    <!-- 语言切换器 -->
    <div class="language-switcher">
      <span>{{ t('nav.language') }}:</span>
      <button
        v-for="localeItem in SUPPORTED_LOCALES"
        :key="localeItem"
        :class="{ active: locale === localeItem }"
        @click="changeLanguage(localeItem)"
        class="lang-btn"
      >
        {{ LOCALE_NAMES[localeItem] }}
      </button>
    </div>

    <!-- 主题切换器 -->
    <div class="theme-switcher">
      <h3>🎨 主题切换器</h3>

      <!-- 预设主题 -->
      <div class="preset-themes">
        <span class="theme-label">预设主题:</span>
        <button
          v-for="theme in presetThemes"
          :key="theme"
          :class="['theme-btn', { active: currentTheme === theme }]"
          @click="switchToPresetTheme(theme)"
          :style="{ backgroundColor: getThemeColor(theme) }"
        >
          {{ themeNames[theme] }}
        </button>
      </div>

      <!-- 自定义主题色 -->
      <div class="custom-theme">
        <span class="theme-label">自定义主题色:</span>
        <input
          type="color"
          v-model="customColor"
          @change="applyCustomTheme"
          class="color-picker"
        />
        <button
          @click="applyCustomTheme"
          class="apply-btn"
        >
          应用自定义主题
        </button>
        <span class="current-color">当前颜色: {{ customColor }}</span>
      </div>

      <!-- 当前主题信息 -->
      <div class="theme-info">
        <span class="theme-label">当前主题:</span>
        <span class="current-theme">
          {{ currentTheme === 'custom' ? '自定义' : themeNames[currentTheme] }}
        </span>
        <button
          @click="resetToDefault"
          class="reset-btn"
        >
          重置为默认
        </button>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="app">
      <header class="header">
        <h1>{{ t('title') }}</h1>
        <p>{{ t('subtitle') }}</p>

        <!-- 导航链接 -->
        <nav class="navigation">
          <router-link
            :to="`/${locale}/button-demo`"
            class="nav-link"
          >
            Button 按钮演示
          </router-link>
          <router-link
            :to="`/${locale}/btn-demo`"
            class="nav-link"
            style="
              background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
              color: white;
              font-weight: bold;
            "
          >
            🎯 Btn 新按钮组件演示 (NEW)
          </router-link>
          <router-link
            :to="`/${locale}/vee-validate-demo`"
            class="nav-link"
          >
            表单演示
          </router-link>
          <router-link
            :to="`/${locale}/form-label-control-demo`"
            class="nav-link"
          >
            表单标签控制演示
          </router-link>
          <router-link
            :to="`/${locale}/input-demo`"
            class="nav-link"
          >
            🎯 Input 输入框组件演示
          </router-link>
          <router-link
            :to="`/${locale}/input-new-demo`"
            class="nav-link"
            style="
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              font-weight: bold;
            "
          >
            ✨ 全新 Input 组件演示 (简洁版)
          </router-link>
          <router-link
            :to="`/${locale}/select-demo`"
            class="nav-link"
          >
            Select 选择器演示
          </router-link>
          <router-link
            :to="`/${locale}/select-multiple-demo`"
            class="nav-link"
          >
            🎯 SelectMultiple 多选组件演示
          </router-link>
          <router-link
            :to="`/${locale}/select-multiple-test`"
            class="nav-link"
          >
            🧪 SelectMultiple 功能测试
          </router-link>
          <router-link
            :to="`/${locale}/linkage-demo`"
            class="nav-link"
          >
            v-linkage 指令演示
          </router-link>
          <router-link
            :to="`/${locale}/input-enhanced-demo`"
            class="nav-link"
          >
            Input 增强功能演示
          </router-link>
          <router-link
            :to="`/${locale}/input-icon-test`"
            class="nav-link"
          >
            Input 图标占位测试
          </router-link>
          <router-link
            :to="`/${locale}/input-v2-demo`"
            class="nav-link"
          >
            Input V2 架构优化演示
          </router-link>
          <router-link
            :to="`/${locale}/input-components-demo`"
            class="nav-link"
          >
            🎯 Input 组件系列演示 (Password/Search/TextArea)
          </router-link>
          <router-link
            :to="`/${locale}/icon-demo`"
            class="nav-link"
          >
            🎨 SpeedIcon 通用图标组件演示
          </router-link>
          <router-link
            :to="`/${locale}/maxlength-test`"
            class="nav-link"
          >
            🧪 Maxlength 属性测试
          </router-link>
          <router-link
            :to="`/${locale}/InputWrapperTest`"
            class="nav-link"
          >
            🧪 InputWrapperTest 属性测试222
          </router-link>
          <router-link
            :to="`/${locale}/menu-demo`"
            class="nav-link"
          >
            🎯 Menu 菜单组件演示 (基础设施)
          </router-link>
          <router-link
            :to="`/${locale}/list-demo`"
            class="nav-link"
          >
            📝 List 列表组件演示 (基础设施)
          </router-link>
          <router-link
            :to="`/${locale}/tag-demo`"
            class="nav-link"
          >
            🏷️ Tag 标签组件演示 (基础设施)
          </router-link>
          <router-link
            :to="`/${locale}/dropdown-demo`"
            class="nav-link"
          >
            📋 Dropdown 下拉菜单组件演示 (基础设施)
          </router-link>
          <router-link
            :to="`/${locale}/select-demo-new`"
            class="nav-link"
          >
            🎯 Select 选择器组件演示 (NEW)
          </router-link>
          <router-link
            :to="`/${locale}/textarea-field-demo`"
            class="nav-link"
            style="
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              font-weight: bold;
            "
          >
            ✨ TextareaField 文本域字段组件演示 (NEW)
          </router-link>
          <router-link
            :to="`/${locale}/slider-demo`"
            class="nav-link"
            style="
              background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
              color: white;
              font-weight: bold;
            "
          >
            🎚️ Slider 滑块组件演示 (NEW)
          </router-link>
          <router-link
            :to="`/${locale}/sp-input-field-demo`"
            class="nav-link"
            style="
              background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
              color: white;
              font-weight: bold;
            "
          >
            ✨ SPInputField 简化版 VTextField 演示 (NEW)
          </router-link>
        </nav>
      </header>

      <main class="main">
        <section class="section">
          <h2>{{ t('button.title') }}</h2>

          <h3>{{ t('button.basicTypes') }}</h3>
          <div class="demo-row">
            <sp-switch
              v-model="switchValue"
              on-text="开2222222"
              off-text="关"
              square
            />
            <sp-switch
              v-model="switchValue"
              square
              vertical
            />
            <sp-switch
              v-model="switchValue"
              on-text="开2222222"
              off-text="关"
              vertical
              size="large"
            />
            <sp-switch
              v-model:value="switchValue"
              on-text="开2222222"
              off-text="关"
              vertical
              size="huge"
            />

            <!-- 外部文本示例 -->
            <sp-switch
              v-model="switchValue"
              on-out-text="开2222222"
              off-out-text="关"
              square
            />
            <sp-switch
              v-model="switchValue"
              on-out-text="启用"
              off-out-text="禁用"
              size="large"
            />
            <p>Switch value: {{ switchValue }}</p>
            <sp-switch
              v-model:value="switchValue"
              disabled
            />
            <sp-switch
              v-model:value="switchValue"
              :time="60"
            />
            <sp-switch
              v-model:value="switchValue"
              :time="30"
              size="small"
            />
            <sp-switch
              v-model:value="switchValue"
              :time="120"
              size="large"
            />
            <sp-switch
              v-model:value="switchValue"
              :time="10"
              size="huge"
            />
            <sp-switch
              v-model:value="switchValue"
              switch-color="#ff6b6b"
              button-color="#fff"
            />
            <sp-switch
              v-model:value="switchValue"
              switch-color="#4ecdc4"
              button-color="#2c3e50"
            />
            <sp-switch
              v-model:value="switchValue"
              switch-color="#45b7d1"
              button-color="#f39c12"
              size="large"
            />

            <!-- 状态相关颜色示例 -->
            <sp-switch
              v-model:value="switchValue"
              switch-on-color="#ff6b6b"
              switch-off-color="#95a5a6"
            />
            <sp-switch
              v-model:value="switchValue"
              button-on-color="#fff"
              button-off-color="#2c3e50"
            />
            <sp-switch
              v-model:value="switchValue"
              switch-on-color="#e74c3c"
              switch-off-color="#bdc3c7"
              button-on-color="#fff"
              button-off-color="#34495e"
            />
            <sp-switch
              v-model:value="switchValue"
              switch-on-color="#2ecc71"
              button-on-color="#fff"
              size="large"
            />
            <sp-switch
              v-model:value="switchValue"
              switch-off-color="#e67e22"
              button-off-color="#fff"
              size="small"
            />
            <sp-switch v-model:value="switchValue">开关文本</sp-switch>

            <!-- 带文本的开关示例 -->
            <sp-switch
              v-model:value="switchValue"
              on-text="开2222222"
              off-text="关"
            />
            <sp-switch
              v-model:value="switchValue"
              on-text="ON"
              off-text="OFF"
            />
            <sp-switch
              v-model:value="switchValue"
              on-text="是"
              off-text="否"
            />
            <!-- 纯图标 -->
            <sp-switch
              :on-icon="RefreshOutline"
              :off-icon="RefreshOutline"
            />

            <!-- 图标 + 文字 -->
            <sp-switch
              :on-icon="Close"
              on-text="开启"
              :off-icon="Close"
              off-text="关闭"
            />

            <!-- 小圆圈图标 -->
            <sp-switch
              v-model:value="switchValue"
              :button-icon="RefreshOutline"
            />

            <!-- 小圆圈 loading 动画 -->
            <sp-switch
              v-model:value="switchValue"
              :button-icon="RefreshOutline"
              loading
            />

            <!-- 纯 loading 动画（无图标） -->
            <sp-switch
              v-model:value="switchValue"
              loading
            />

            <!-- 小圆圈图标 + 文字 -->
            <sp-switch
              v-model:value="switchValue"
              :button-icon="Close"
              on-text="开启"
              off-text="关闭"
              loading
            />

            <!-- 尺寸演示 -->
            <h4>开关尺寸</h4>
            <sp-switch
              v-model:value="switchValue"
              size="small"
            >
              小尺寸
            </sp-switch>
            <sp-switch
              v-model:value="switchValue"
              size="medium"
            >
              默认尺寸
            </sp-switch>
            <sp-switch
              v-model:value="switchValue"
              size="large"
            >
              大尺寸
            </sp-switch>
            <sp-switch
              v-model:value="switchValue"
              size="huge"
            >
              超大尺寸
            </sp-switch>

            <!-- 不同尺寸的 loading 效果 -->
            <h4>不同尺寸的 Loading 效果</h4>
            <sp-switch
              v-model:value="switchValue"
              size="small"
              loading
            >
              小尺寸 Loading
            </sp-switch>
            <sp-switch
              v-model:value="switchValue"
              size="medium"
              loading
            >
              默认尺寸 Loading
            </sp-switch>
            <sp-switch
              v-model:value="switchValue"
              size="large"
              loading
            >
              大尺寸 Loading
            </sp-switch>
            <sp-switch
              v-model:value="switchValue"
              size="huge"
              loading
            >
              超大尺寸 Loading
            </sp-switch>

            <!-- 不同尺寸的图标效果 -->
            <h4>不同尺寸的图标效果</h4>
            <sp-switch
              v-model:value="switchValue"
              size="small"
              :button-icon="RefreshOutline"
            >
              小尺寸图标
            </sp-switch>
            <sp-switch
              v-model:value="switchValue"
              size="medium"
              :button-icon="RefreshOutline"
            >
              默认尺寸图标
            </sp-switch>
            <sp-switch
              v-model:value="switchValue"
              size="large"
              :button-icon="RefreshOutline"
            >
              大尺寸图标
            </sp-switch>
            <sp-switch
              v-model:value="switchValue"
              size="huge"
              :button-icon="RefreshOutline"
            >
              超大尺寸图标
            </sp-switch>
            <sp-button
              primary
              :press-down="1000"
            >
              按住是是是1秒
            </sp-button>
            <sp-button
              primary
              type="warning"
              tip="测试版"
              :press-down="1000"
            >
              按住是是是1秒
            </sp-button>
            <!-- 1. 主按钮 -->
            <sp-button primary>Primary Button</sp-button>

            <!-- 2. 次按钮 -->
            <sp-button secondary>Secondary Button</sp-button>

            <!-- 3. 虚线按钮 -->
            <sp-button dashed>Dashed Button</sp-button>

            <!-- 4. 文本按钮 -->
            <sp-button text>Text Button</sp-button>

            <!-- 5. 链接按钮 -->
            <sp-button link>Link Button</sp-button>
            <div class="sp-button-group">
              <sp-button size="large">Large</sp-button>
              <sp-button>Default</sp-button>
              <sp-button size="small">Small</sp-button>
              <sp-button
                loading
                :loading-icon="Close"
                size="huge"
              >
                巨大按钮
              </sp-button>
            </div>
            <!-- 6. 带其他属性 -->
            <sp-button
              primary
              loading
            >
              Loading Primary
            </sp-button>
            <sp-button
              secondary
              disabled
            >
              Disabled Secondary
            </sp-button>

            <!-- 7. 自定义样式（依然支持） -->
            <sp-button
              primary
              :attrStyle="{ borderRadius: '20px' }"
            >
              Rounded Primary
            </sp-button>

            <!-- 8. 完全自定义 -->
            <sp-button
              :attrStyle="{ backgroundColor: '#ff6b6b', color: 'white' }"
            >
              Custom Button
            </sp-button>

            <sp-button tip="测试版">AI小助手</sp-button>
            <sp-button
              :tip="{
                text: '体验版',
                bgColor: '#409EFF', // 背景色
                color: '#fff', // 文字颜色
                top: '-15px', // 距离顶部
                right: '45px', // 距离右侧
                fontSize: '11px', // 字体大小
                padding: '2px 8px', // 内边距
              }"
            >
              AI小助手
            </sp-button>
            <sp-button
              tip="测试版测试版"
              size="huge"
              primary
              type="success"
            >
              AI小助手ssss
            </sp-button>
            <sp-button type="default">哈哈哈哈</sp-button>
            <sp-button
              secondary
              type="default"
            >
              主要成功按钮222
            </sp-button>
            <sp-button
              dashed
              type="default"
            >
              主要成功按钮222
            </sp-button>
            <sp-button
              text
              type="default"
            >
              主要成功按钮222
            </sp-button>
            <sp-button
              primary
              type="success"
              loading
            >
              主要成功按钮
            </sp-button>
            <sp-button
              primary
              type="warning"
            >
              主要警告按钮
            </sp-button>
            <sp-button
              primary
              square
              type="danger"
            >
              主要危险按钮
            </sp-button>
            <sp-button
              secondary
              square
              type="danger"
            >
              次级危险按钮
            </sp-button>
            <sp-button
              dashed
              round
              type="warning"
            >
              虚线警告按钮
            </sp-button>
            <sp-button
              text
              type="success"
            >
              文字成功按钮
            </sp-button>
            <sp-button
              link
              type="danger"
            >
              链接危险按钮
            </sp-button>
            <sp-button
              primary
              :icon="Close"
            >
              搜索
            </sp-button>
            <sp-button
              primary
              :icon="Close"
            >
              搜索2
            </sp-button>
            <span class="sp-button__content">
              <svg
                width="20"
                height="20"
              >
                <circle
                  cx="10"
                  cy="10"
                  r="8"
                  fill="red"
                />
              </svg>
              搜索
            </span>

            <sp-button
              :loading="true"
              :loading-icon="RefreshOutline"
              dashed
              size="small"
              type="danger"
            >
              加载中
            </sp-button>

            <sp-button
              :loading="true"
              primary
            >
              默认加载
            </sp-button>

            <sp-button
              time="90"
              primary
              size="huge"
            >
              发送验证码333
            </sp-button>

            <!-- <sp-button 
  vertical
  type="danger"
>
  223321321
</sp-button>
<sp-button 
  vertical
>
  abcdefg
</sp-button> -->
            <sp-button
              vertical
              type="success"
              time="90"
            >
              垂直按钮
            </sp-button>
            <sp-button
              type="primary"
              plain
            >
              {{ t('button.default') }}
            </sp-button>
            <sp-button link>{{ t('button.default') }}</sp-button>
            <sp-button text>{{ t('button.default') }}</sp-button>
            <sp-button
              plain
              type="outline"
            >
              {{ t('button.outline') }}
            </sp-button>
            <sp-button
              plain
              type="secondary"
            >
              {{ t('button.secondary') }}
            </sp-button>
            <sp-button type="success">{{ t('button.success') }}</sp-button>
            <sp-button type="warning">{{ t('button.warning') }}</sp-button>
            <sp-button type="danger">{{ t('button.danger') }}</sp-button>
            <sp-button
              type="danger"
              link
            >
              {{ t('button.danger') }}
            </sp-button>
            <sp-button
              type="danger"
              text
            >
              {{ t('button.danger') }}
            </sp-button>
          </div>

          <h3>{{ t('button.sizes') }}</h3>
          <div class="demo-row">
            <sp-button size="small">{{ t('button.small') }}</sp-button>
            <sp-button size="medium">{{ t('button.medium') }}</sp-button>
            <sp-button size="large">{{ t('button.large') }}</sp-button>
          </div>

          <h3>{{ t('button.states') }}</h3>
          <div class="demo-row">
            <sp-button
              vertical
              type="success"
            >
              垂直按钮
            </sp-button>
            <sp-button disabled>{{ t('button.disabled') }}</sp-button>
            <sp-button loading>{{ t('button.loading') }}</sp-button>
            <sp-button :toggleable="false">
              {{ t('button.notToggleable') }}
            </sp-button>
          </div>
        </section>

        <section class="section">
          <h2>{{ t('layout.title') }}</h2>

          <h3>{{ t('layout.basic') }}</h3>
          <div class="layout-demo">
            <sp-row class="demo-row-bg">
              <sp-col :span="24">
                <div class="grid-content bg-purple-dark"></div>
              </sp-col>
            </sp-row>
            <sp-row class="demo-row-bg">
              <sp-col :span="12">
                <div class="grid-content bg-purple"></div>
              </sp-col>
              <sp-col :span="12">
                <div class="grid-content bg-purple-light"></div>
              </sp-col>
            </sp-row>
            <sp-row class="demo-row-bg">
              <sp-col :span="8">
                <div class="grid-content bg-purple"></div>
              </sp-col>
              <sp-col :span="8">
                <div class="grid-content bg-purple-light"></div>
              </sp-col>
              <sp-col :span="8">
                <div class="grid-content bg-purple"></div>
              </sp-col>
            </sp-row>
            <sp-row class="demo-row-bg">
              <sp-col :span="6">
                <div class="grid-content bg-purple"></div>
              </sp-col>
              <sp-col :span="6">
                <div class="grid-content bg-purple-light"></div>
              </sp-col>
              <sp-col :span="6">
                <div class="grid-content bg-purple"></div>
              </sp-col>
              <sp-col :span="6">
                <div class="grid-content bg-purple-light"></div>
              </sp-col>
            </sp-row>
          </div>

          <h3>{{ t('layout.gutter') }}</h3>
          <div class="layout-demo">
            <sp-row
              :gutter="20"
              class="demo-row-bg"
            >
              <sp-col :span="6">
                <div class="grid-content bg-purple"></div>
              </sp-col>
              <sp-col :span="6">
                <div class="grid-content bg-purple"></div>
              </sp-col>
              <sp-col :span="6">
                <div class="grid-content bg-purple"></div>
              </sp-col>
              <sp-col :span="6">
                <div class="grid-content bg-purple"></div>
              </sp-col>
            </sp-row>
          </div>

          <h3>{{ t('layout.offset') }}</h3>
          <div class="layout-demo">
            <sp-row class="demo-row-bg">
              <sp-col :span="6">
                <div class="grid-content bg-purple"></div>
              </sp-col>
              <sp-col
                :span="6"
                :offset="6"
              >
                <div class="grid-content bg-purple"></div>
              </sp-col>
            </sp-row>
            <sp-row class="demo-row-bg">
              <sp-col
                :span="6"
                :offset="6"
              >
                <div class="grid-content bg-purple"></div>
              </sp-col>
              <sp-col
                :span="6"
                :offset="6"
              >
                <div class="grid-content bg-purple"></div>
              </sp-col>
            </sp-row>
          </div>

          <h3>{{ t('layout.align') }}</h3>
          <div class="layout-demo">
            <sp-row
              justify="center"
              class="demo-row-bg"
            >
              <sp-col :span="4">
                <div class="grid-content bg-purple"></div>
              </sp-col>
              <sp-col :span="4">
                <div class="grid-content bg-purple-light"></div>
              </sp-col>
              <sp-col :span="4">
                <div class="grid-content bg-purple"></div>
              </sp-col>
            </sp-row>
            <sp-row
              justify="end"
              class="demo-row-bg"
            >
              <sp-col :span="4">
                <div class="grid-content bg-purple"></div>
              </sp-col>
              <sp-col :span="4">
                <div class="grid-content bg-purple-light"></div>
              </sp-col>
              <sp-col :span="4">
                <div class="grid-content bg-purple"></div>
              </sp-col>
            </sp-row>
            <sp-row
              justify="space-between"
              class="demo-row-bg"
            >
              <sp-col :span="4">
                <div class="grid-content bg-purple"></div>
              </sp-col>
              <sp-col :span="4">
                <div class="grid-content bg-purple-light"></div>
              </sp-col>
              <sp-col :span="4">
                <div class="grid-content bg-purple"></div>
              </sp-col>
            </sp-row>
            <sp-row
              justify="space-around"
              class="demo-row-bg"
            >
              <sp-col :span="4">
                <div class="grid-content bg-purple"></div>
              </sp-col>
              <sp-col :span="4">
                <div class="grid-content bg-purple-light"></div>
              </sp-col>
              <sp-col :span="4">
                <div class="grid-content bg-purple"></div>
              </sp-col>
            </sp-row>
          </div>

          <h3>{{ t('layout.responsive') }}</h3>
          <div class="layout-demo">
            <sp-row
              :gutter="10"
              class="demo-row-bg"
            >
              <sp-col
                :xs="8"
                :sm="6"
                :md="4"
                :lg="3"
                :xl="1"
              >
                <div class="grid-content bg-purple"></div>
              </sp-col>
              <sp-col
                :xs="4"
                :sm="6"
                :md="8"
                :lg="9"
                :xl="11"
              >
                <div class="grid-content bg-purple-light"></div>
              </sp-col>
              <sp-col
                :xs="4"
                :sm="6"
                :md="8"
                :lg="9"
                :xl="11"
              >
                <div class="grid-content bg-purple"></div>
              </sp-col>
              <sp-col
                :xs="8"
                :sm="6"
                :md="4"
                :lg="3"
                :xl="1"
              >
                <div class="grid-content bg-purple-light"></div>
              </sp-col>
            </sp-row>
          </div>
        </section>
        1
        <section class="section">
          <h2>{{ t('input.title') }}</h2>

          <h3>{{ t('input.basic') }}</h3>
          <div class="demo-row">
            <sp-input
              v-model:value="basicInputValue"
              :placeholder="t('input.placeholder')"
              style="width: 200px; margin-right: 20px"
            />
            <sp-input
              v-model:value="slipInputValue"
              :placeholder="t('input.placeholder')"
              slip
              style="width: 200px; margin-right: 20px"
            />

            <sp-input
              v-model:value="slipInputValue"
              placeholder="我噶理工"
              slip
              style="width: 200px; margin-right: 20px"
            />
            <!-- <sp-input v-mode:value="xxxx"  /> -->
            <!-- <sp-input v-mode:value="xxxx" textarea /> -->
            <!-- <sp-input v-mode:value="xxxx" textline /> -->
            <!-- <sp-input v-mode:value="xxxx" texttabel td="12" /> -->

            <span>输入值: {{ basicInputValue }}</span>
          </div>

          <!-- 普通输入框 -->
          <sp-input v-model:value="basicInputValue" />

          <!-- 文本域 -->
          <sp-input
            v-model:value="basicInputValue"
            textarea
          />

          <!-- 线条输入框 -->
          <sp-input
            type="password"
            v-model:value="basicInputValue"
            textline
            clearable
            showPassword
          />

          <sp-input
            v-model:value="basicInputValue"
            texttabel
            :td="12"
            clearable
          />
          <h3>{{ t('input.clearable') }}</h3>
          <div class="demo-row">
            <sp-input
              v-model="clearableInputValue"
              :placeholder="t('input.placeholder')"
              clearable
              style="width: 200px; margin-right: 20px"
            />
            <span>输入值: {{ clearableInputValue }}</span>
          </div>

          <h3>{{ t('input.password') }}</h3>
          <div class="demo-row">
            <sp-input
              v-model="passwordValue"
              type="password"
              :placeholder="t('input.placeholderPassword')"
              showPassword
              style="width: 200px; margin-right: 20px"
            />
            <span>密码值: {{ passwordValue }}</span>
          </div>

          <h3>{{ t('input.sizes') }}</h3>
          <div class="demo-row">
            <sp-input
              v-model="sizeInputValue"
              size="small"
              :placeholder="t('input.small')"
              style="width: 150px; margin-right: 10px"
            />
            <sp-input
              v-model="sizeInputValue"
              size="default"
              :placeholder="t('input.default')"
              style="width: 150px; margin-right: 10px"
            />
            <sp-input
              v-model="sizeInputValue"
              size="large"
              :placeholder="t('input.large')"
              style="width: 150px"
            />
          </div>

          <h3>{{ t('input.maxlength') }}</h3>
          <div class="demo-row">
            <sp-input
              v-model="maxlengthInputValue"
              :maxlength="10"
              :placeholder="t('input.placeholder')"
              showWordLimit
              style="width: 200px; margin-right: 20px"
            />
            <span>输入值: {{ maxlengthInputValue }}</span>
          </div>

          <h3>{{ t('input.textarea') }}</h3>
          <div class="demo-row">
            <sp-input
              v-model="textareaValue"
              type="textarea"
              :placeholder="t('input.placeholderTextarea')"
              :rows="3"
              style="width: 300px; margin-right: 20px"
            />
            <div>
              <p>文本域值:</p>
              <p style="max-width: 200px; word-break: break-all">
                {{ textareaValue }}
              </p>
            </div>
          </div>

          <h3>{{ t('input.autosize') }}</h3>
          <div class="demo-row">
            <sp-input
              v-model:value="autosizeTextareaValue"
              type="textarea"
              :placeholder="t('input.placeholderTextarea')"
              :autosize="{ minRows: 2, maxRows: 6 }"
              style="width: 300px; margin-right: 20px"
            />
            <span>自动高度调整2</span>
          </div>

          <h3>{{ t('input.validateStates') }}</h3>
          <div class="demo-col">
            <sp-input
              v-model="successInputValue"
              :placeholder="t('input.placeholder')"
              validateState="success"
              :validateMessage="t('input.successMessage')"
              style="width: 250px; margin-bottom: 40px"
            />
            <sp-input
              v-model="warningInputValue"
              :placeholder="t('input.placeholder')"
              validateState="warning"
              :validateMessage="t('input.warningMessage')"
              style="width: 250px; margin-bottom: 40px"
            />
            <sp-input
              v-model="errorInputValue"
              :placeholder="t('input.placeholder')"
              validateState="error"
              :validateMessage="t('input.errorMessage')"
              style="width: 250px; margin-bottom: 40px"
            />
          </div>

          <h3>{{ t('input.disabled') }}</h3>
          <div class="demo-row">
            <sp-input
              v-model="disabledInputValue"
              :placeholder="t('input.placeholder')"
              disabled
              style="width: 200px; margin-right: 20px"
            />
            <sp-input
              v-model="disabledTextareaValue"
              type="textarea"
              :placeholder="t('input.placeholderTextarea')"
              disabled
              :rows="3"
              style="width: 200px"
            />
          </div>

          <!-- 新增功能演示区域 -->
          <h2
            style="
              margin-top: 40px;
              color: #409eff;
              border-bottom: 2px solid #409eff;
              padding-bottom: 10px;
            "
          >
            🚀 增强功能演示
          </h2>

          <h3>📝 格子输入模式 (Texttabel)</h3>
          <div class="demo-col">
            <div class="demo-item">
              <label>6位验证码:</label>
              <sp-input
                v-model:value="tabelValue6"
                texttabel
                :td="6"
                placeholder="验证码"
                style="margin-bottom: 10px"
              />
              <p>值: {{ tabelValue6 }}</p>
            </div>

            <div class="demo-item">
              <label>4位PIN码:</label>
              <sp-input
                v-model:value="tabelValue4"
                texttabel
                :td="4"
                placeholder="PIN"
                style="margin-bottom: 10px"
              />
              <p>值: {{ tabelValue4 }}</p>
            </div>
          </div>

          <h3>📄 文本域模式 (Textarea)</h3>
          <div class="demo-col">
            <div class="demo-item">
              <label>普通文本域:</label>
              <sp-input
                v-model:value="enhancedTextareaValue"
                textarea
                placeholder="请输入多行文本..."
                :rows="4"
                style="width: 400px; margin-bottom: 10px"
              />
              <p>值: {{ enhancedTextareaValue }}</p>
            </div>

            <div class="demo-item">
              <label>带字数统计的文本域:</label>
              <sp-input
                v-model:value="limitedTextareaValue"
                textarea
                placeholder="最多200个字符..."
                :rows="3"
                :maxlength="200"
                showWordLimit
                style="width: 400px; margin-bottom: 10px"
              />
              <p>值: {{ limitedTextareaValue }}</p>
            </div>
          </div>

          <h3>✨ Slip 动画标签</h3>
          <div class="demo-row">
            <sp-input
              v-model:value="slipDemoValue"
              slip
              placeholder="用户名"
              style="width: 200px; margin-right: 20px"
            />
            <sp-input
              v-model:value="slipEmailValue"
              slip
              placeholder="邮箱地址"
              type="email"
              style="width: 200px; margin-right: 20px"
            />
            <span>Slip值: {{ slipDemoValue }} | {{ slipEmailValue }}</span>
          </div>

          <h3>🎯 前缀后缀图标</h3>
          <div class="demo-col">
            <div class="demo-item">
              <label>前缀图标:</label>
              <sp-input
                v-model:value="prefixIconValue"
                prefixIcon="icon-user"
                placeholder="用户名"
                style="width: 250px; margin-bottom: 10px"
              />
              <p>值: {{ prefixIconValue }}</p>
            </div>

            <div class="demo-item">
              <label>后缀图标:</label>
              <sp-input
                v-model:value="suffixIconValue"
                suffixIcon="icon-search"
                placeholder="搜索内容"
                style="width: 250px; margin-bottom: 10px"
              />
              <p>值: {{ suffixIconValue }}</p>
            </div>

            <div class="demo-item">
              <label>前缀+后缀+清除:</label>
              <sp-input
                v-model:value="combinedIconValue"
                prefixIcon="icon-user"
                suffixIcon="icon-search"
                clearable
                placeholder="组合功能"
                style="width: 250px; margin-bottom: 10px"
              />
              <p>值: {{ combinedIconValue }}</p>
            </div>
          </div>

          <h3>🔢 字数统计功能</h3>
          <div class="demo-col">
            <div class="demo-item">
              <label>限制50字符:</label>
              <sp-input
                v-model:value="countValue50"
                :maxlength="50"
                showWordLimit
                placeholder="最多50个字符"
                style="width: 300px; margin-bottom: 10px"
              />
              <p>值: {{ countValue50 }}</p>
            </div>

            <div class="demo-item">
              <label>限制20字符:</label>
              <sp-input
                v-model:value="countValue20"
                :maxlength="20"
                showWordLimit
                placeholder="最多20个字符"
                style="width: 300px; margin-bottom: 10px"
              />
              <p>值: {{ countValue20 }}</p>
            </div>
          </div>

          <h3>🔐 增强密码功能</h3>
          <div class="demo-row">
            <sp-input
              v-model:value="enhancedPasswordValue"
              type="password"
              showPassword
              clearable
              placeholder="密码"
              style="width: 200px; margin-right: 20px"
            />
            <sp-input
              v-model:value="slipPasswordValue"
              type="password"
              showPassword
              slip
              placeholder="确认密码"
              style="width: 200px; margin-right: 20px"
            />
            <span>
              密码: {{ enhancedPasswordValue }} | 确认: {{ slipPasswordValue }}
            </span>
          </div>

          <h3>🎨 组合演示</h3>
          <div class="demo-col">
            <div class="demo-item">
              <label>完整功能组合:</label>
              <sp-input
                v-model:value="fullFeatureValue"
                slip
                clearable
                showWordLimit
                :maxlength="100"
                prefixIcon="icon-user"
                placeholder="用户信息"
                validateState="success"
                validateMessage="输入格式正确"
                style="width: 350px; margin-bottom: 10px"
              />
              <p>值: {{ fullFeatureValue }}</p>
            </div>
          </div>
        </section>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useI18n } from 'vue-i18n'
  import { useRouter } from 'vue-router'
  import { ref, onMounted, onUnmounted } from 'vue'
  import { SUPPORTED_LOCALES, LOCALE_NAMES } from '../i18n'
  import type { Locale } from '../i18n'
  import { Close, RefreshOutline } from '@vicons/ionicons5'

  const { locale, t } = useI18n()
  const router = useRouter()
  let switchValue = ref(false)

  // 主题切换功能（简化版本，暂时不使用 hooks 包）
  const currentTheme = ref('default')
  const customColor = ref('#667eea')

  // 预设主题
  const presetThemes = ['default', 'blue', 'green', 'red', 'orange', 'purple']

  // 主题名称映射
  const themeNames: Record<string, string> = {
    default: '默认紫色',
    blue: '蓝色',
    green: '绿色',
    red: '红色',
    orange: '橙色',
    purple: '紫色',
  }

  // 预设主题颜色
  const presetColors: Record<string, string> = {
    default: '#667eea',
    blue: '#1890ff',
    green: '#52c41a',
    red: '#ff4d4f',
    orange: '#fa8c16',
    purple: '#722ed1',
  }

  // 获取主题颜色
  const getThemeColor = (theme: string): string => {
    return presetColors[theme] || presetColors.default
  }

  // 应用主题到 CSS 变量
  const applyThemeColors = (primaryColor: string) => {
    const root = document.documentElement

    // 生成相关颜色
    const hoverColor = lightenColor(primaryColor, 0.1)
    const activeColor = darkenColor(primaryColor, 0.1)
    const disabledColor = lightenColor(primaryColor, 0.4)
    const lightestColor = lightenColor(primaryColor, 0.8)
    const lightColor = lightenColor(primaryColor, 0.6)

    // 设置 CSS 变量
    root.style.setProperty('--sp-color-primary', primaryColor)
    root.style.setProperty('--sp-color-primary-hover', hoverColor)
    root.style.setProperty('--sp-color-primary-active', activeColor)
    root.style.setProperty('--sp-color-primary-disabled', disabledColor)
    root.style.setProperty('--sp-color-primary-lightest', lightestColor)
    root.style.setProperty('--sp-color-primary-light', lightColor)
  }

  // 颜色处理函数
  const lightenColor = (color: string, amount: number): string => {
    const hex = color.replace('#', '')
    const num = parseInt(hex, 16)
    const r = Math.min(255, Math.floor((num >> 16) + 255 * amount))
    const g = Math.min(255, Math.floor(((num >> 8) & 0x00ff) + 255 * amount))
    const b = Math.min(255, Math.floor((num & 0x0000ff) + 255 * amount))
    return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`
  }

  const darkenColor = (color: string, amount: number): string => {
    const hex = color.replace('#', '')
    const num = parseInt(hex, 16)
    const r = Math.max(0, Math.floor((num >> 16) * (1 - amount)))
    const g = Math.max(0, Math.floor(((num >> 8) & 0x00ff) * (1 - amount)))
    const b = Math.max(0, Math.floor((num & 0x0000ff) * (1 - amount)))
    return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`
  }

  // 主题切换方法
  const switchToPresetTheme = (theme: string) => {
    currentTheme.value = theme
    const color = getThemeColor(theme)
    applyThemeColors(color)
    customColor.value = color
    console.log(`切换到主题: ${themeNames[theme]} (${color})`)
  }

  const applyCustomTheme = () => {
    currentTheme.value = 'custom'
    applyThemeColors(customColor.value)
    console.log(`应用自定义主题: ${customColor.value}`)
  }

  const resetToDefault = () => {
    switchToPresetTheme('default')
  }

  // 初始化主题
  onMounted(() => {
    // 应用默认主题
    switchToPresetTheme('default')
  })
  // Input 演示数据
  const basicInputValue = ref('')
  const slipInputValue = ref('')
  const clearableInputValue = ref('')
  const passwordValue = ref('')
  const sizeInputValue = ref('')
  const maxlengthInputValue = ref('')
  const textareaValue = ref('')
  const autosizeTextareaValue = ref('')
  const successInputValue = ref('正确的输入')
  const warningInputValue = ref('需要注意的输入')
  const errorInputValue = ref('错误的输入')
  const disabledInputValue = ref('禁用状态')
  const disabledTextareaValue = ref('禁用的文本域')

  // 新增功能演示数据
  const tabelValue6 = ref('')
  const tabelValue4 = ref('')
  const enhancedTextareaValue = ref('')
  const limitedTextareaValue = ref('')
  const slipDemoValue = ref('')
  const slipEmailValue = ref('')
  const prefixIconValue = ref('')
  const suffixIconValue = ref('')
  const combinedIconValue = ref('')
  const countValue50 = ref('')
  const countValue20 = ref('')
  const enhancedPasswordValue = ref('')
  const slipPasswordValue = ref('')
  const fullFeatureValue = ref('')

  // 切换语言
  const changeLanguage = (newLocale: Locale) => {
    locale.value = newLocale
    router.push(`/${newLocale}`)
  }
</script>

<style scoped>
  .sp-button-group {
    display: flex;
    align-items: center; /* 让所有按钮的中线对齐 */
    gap: 12px; /* 按钮间距可自定义 */
  }
  .language-switcher {
    text-align: center;
    padding: 1rem;
    border-bottom: 1px solid #eee;
    margin-bottom: 2rem;
  }

  .lang-btn {
    padding: 0.25rem 0.75rem;
    margin: 0 0.25rem;
    border: 1px solid #ddd;
    background: #fff;
    color: #666;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .lang-btn:hover {
    border-color: #409eff;
    color: #409eff;
  }

  .lang-btn.active {
    background: #409eff;
    color: #fff;
    border-color: #409eff;
  }

  /* 主题切换器样式 */
  .theme-switcher {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border: 1px solid #e1e8ed;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .theme-switcher h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 1.4rem;
    font-weight: 600;
    text-align: center;
  }

  .preset-themes,
  .custom-theme,
  .theme-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
    flex-wrap: wrap;
  }

  .theme-label {
    font-weight: 500;
    color: #34495e;
    min-width: 100px;
  }

  .theme-btn {
    padding: 8px 16px;
    border: 2px solid transparent;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .theme-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .theme-btn.active {
    border-color: #fff;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
  }

  .color-picker {
    width: 50px;
    height: 35px;
    border: 2px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .color-picker:hover {
    border-color: var(--sp-color-primary, #667eea);
    transform: scale(1.1);
  }

  .apply-btn,
  .reset-btn {
    padding: 8px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
  }

  .apply-btn:hover,
  .reset-btn:hover {
    border-color: var(--sp-color-primary, #667eea);
    color: var(--sp-color-primary, #667eea);
    background: var(--sp-color-primary-lightest, #eef2ff);
  }

  .current-color {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    font-size: 0.9rem;
  }

  .current-theme {
    font-weight: 600;
    color: var(--sp-color-primary, #667eea);
    background: var(--sp-color-primary-lightest, #eef2ff);
    padding: 4px 12px;
    border-radius: 6px;
    border: 1px solid var(--sp-color-primary-light, #d6e0ff);
  }

  /* 为主题切换添加平滑过渡 */
  :root {
    transition: --sp-color-primary 0.3s ease, --sp-color-primary-hover 0.3s ease,
      --sp-color-primary-active 0.3s ease, --sp-color-primary-disabled 0.3s ease,
      --sp-color-primary-lightest 0.3s ease, --sp-color-primary-light 0.3s ease;
  }

  .app {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .header {
    text-align: center;
    margin-bottom: 3rem;
  }

  .header h1 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
  }

  .header p {
    color: #7f8c8d;
    font-size: 1.1rem;
  }

  .navigation {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #ecf0f1;
  }

  .nav-link {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  }

  .nav-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    color: white;
  }

  .section {
    margin-bottom: 3rem;
  }

  .section h2 {
    color: #2c3e50;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #ecf0f1;
  }

  .section h3 {
    color: #34495e;
    margin: 2rem 0 1rem 0;
    font-size: 1.2rem;
    font-weight: 600;
  }

  .demo-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
  }

  .demo-col {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .demo-item {
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;
    margin-bottom: 10px;
  }

  .demo-item label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
  }

  .demo-item p {
    margin: 8px 0 0 0;
    padding: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #666;
    word-break: break-all;
  }

  /* 图标样式 */
  .icon-user::before {
    content: '👤';
    font-style: normal;
  }

  .icon-search::before {
    content: '🔍';
    font-style: normal;
  }

  /* 布局演示样式 */
  .layout-demo {
    margin-bottom: 2rem;
  }

  .demo-row-bg {
    margin-bottom: 20px;
  }

  .demo-row-bg:last-child {
    margin-bottom: 0;
  }

  .grid-content {
    border-radius: 4px;
    min-height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 500;
    font-size: 14px;
  }

  .bg-purple-dark {
    background: #99a9bf;
  }

  .bg-purple {
    background: #d3dce6;
    color: #333;
  }

  .bg-purple-light {
    background: #e5e9f2;
    color: #333;
  }
</style>
