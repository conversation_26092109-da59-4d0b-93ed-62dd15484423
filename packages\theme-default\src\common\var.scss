// ================================
// Speed UI 主题变量
// ================================

// ========== 颜色系统 ==========
// 主色调的CSS自定义属性定义
:root {
  --sp-color-primary: #667eea;
  --sp-color-primary-hover: #7c8aeb;
  --sp-color-primary-active: #5a6ce8;
  --sp-color-primary-disabled: #b8c5ff;
  --sp-color-primary-lightest: #eef2ff; // hover用，非常浅的紫色
  --sp-color-primary-light: #d6e0ff; // active用，略深一点的紫色
}

// Sass变量引用CSS自定义属性，并提供默认值
$color-primary: var(--sp-color-primary) !default;
$color-primary-hover: var(--sp-color-primary-hover) !default;
$color-primary-active: var(--sp-color-primary-active) !default;
$color-primary-disabled: var(--sp-color-primary-disabled) !default;
$color-primary-lightest: var(--sp-color-primary-lightest) !default;
$color-primary-light: var(--sp-color-primary-light) !default;

// 功能色彩
$color-success: #52c41a !default;
$color-success-hover: #73d13d !default;
$color-success-active: #389e0d !default;

$color-warning: #faad14 !default;
$color-warning-hover: #ffec3d !default;
$color-warning-active: #d48806 !default;

$color-warning-hover-bg: rgb(226, 173, 28) !default;

$color-danger: #ff4d4f !default;
$color-danger-hover: #ff7875 !default;
$color-danger-active: #d9363e !default;

$color-info: #1890ff !default;
$color-info-hover: #40a9ff !default;
$color-info-active: #096dd9 !default;

// 中性色
$color-text-primary: #000000d9 !default;
$color-text-secondary: #00000073 !default;
$color-text-disabled: #00000040 !default;

$border-color-base: #d9d9d9 !default;
$border-color-hover: $color-primary-hover !default; // 引用 $color-primary-hover
$border-color-active: #096dd9 !default;
$border-color-disabled: #d9d9d9 !default;

$background-color-base: #ffffff !default;
$background-color-hover: #f5f5f5 !default;
$background-color-active: #f0f0f0 !default;
$background-color-disabled: #f5f5f5 !default;

// 深色主题变量 (可选)
$background-color-dark: #1f1f1f !default;
$border-color-dark: #434343 !default;
$border-color-hover-dark: #595959 !default;
$color-text-dark: #ffffff !default;
$color-text-placeholder-dark: #8c8c8c !default;
$color-primary-dark: #1890ff !default;

// ========== 按钮组件变量 ==========
// 按钮尺寸
$button-height-small: 24px !default;
$button-height-medium: 32px !default;
$button-height-large: 40px !default;

$button-padding-horizontal-small: 8px !default;
$button-padding-horizontal-medium: 15px !default;
$button-padding-horizontal-large: 15px !default;

$button-font-size-small: 12px !default;
$button-font-size-medium: 14px !default;
$button-font-size-large: 16px !default;

// ========== 尺寸系统 ==========
// 圆角
$border-radius-small: 4px !default;
$border-radius-base: 6px !default;
$border-radius-large: 8px !default;

// 间距系统
$spacing-xs: 4px !default;
$spacing-sm: 8px !default;
$spacing-md: 16px !default;
$spacing-lg: 24px !default;
$spacing-xl: 32px !default;

// ========== 动画系统 ==========
$transition-duration-base: 0.3s !default;
$transition-timing-base: cubic-bezier(0.215, 0.61, 0.355, 1) !default;

// ========== 阴影系统 ==========
$box-shadow-light: 0 1px 3px rgba(0, 0, 0, 0.12) !default;
$box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15) !default;
$box-shadow-dark: 0 4px 16px rgba(0, 0, 0, 0.15) !default;

// ========== 字体系统 ==========
$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
  'Helvetica Neue', Arial, 'Noto Sans', sans-serif !default;
$font-size-base: 14px !default;
$font-size-sm: 12px !default;
$font-size-lg: 16px !default;
$font-weight-base: 400 !default;
$line-height-base: 1.5 !default;

$button-theme-map: (
  default: (
    color: $color-primary,
    hover: $color-primary-hover,
    active: $color-primary-active,
    bg: $color-primary-lightest,
    bg-hover: $color-primary-light,
    bg-active: $color-primary-light,
    text: $color-primary,
    text-hover: $color-primary-hover,
    text-active: $color-primary-active,
  ),
  success: (
    color: $color-success,
    hover: $color-success-hover,
    active: $color-success-active,
    bg: #f6ffed,
    bg-hover: #d9f7be,
    bg-active: #d9f7be,
    text: $color-success,
    text-hover: $color-success-hover,
    text-active: $color-success-active,
  ),
  warning: (
    color: $color-warning,
    hover: $color-warning-hover-bg,
    active: $color-warning-active,
    bg: #fffbe6,
    bg-hover: #fffbe6,
    bg-active: #fff1b8,
    text: $color-warning,
    text-hover: $color-warning-active,
    text-active: $color-warning-active,
  ),
  danger: (
    color: $color-danger,
    hover: $color-danger-hover,
    active: $color-danger-active,
    bg: #fff1f0,
    bg-hover: #ffccc7,
    bg-active: #ffccc7,
    text: $color-danger,
    text-hover: $color-danger-hover,
    text-active: $color-danger-active,
  ),
);
