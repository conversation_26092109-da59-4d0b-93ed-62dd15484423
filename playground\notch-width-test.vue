<template>
  <div class="notch-width-test">
    <h2>缺口宽度测试 - 占位元素方案</h2>
    <p>使用隐藏占位元素自动撑开缺口宽度，无需计算</p>
    <p><strong>修复：</strong>解决了聚焦时容器高度变化的问题</p>
    
    <div class="test-section">
      <h3>小尺寸 (30px)</h3>
      <SPInputField
        :size="30"
        label="30px高度"
        variant="default"
        placeholder="请输入内容"
        style="margin-bottom: 20px;"
      />
      
      <SPInputField
        :size="30"
        label="必填字段"
        variant="default"
        placeholder="请输入内容"
        required
        style="margin-bottom: 20px;"
      />
    </div>

    <div class="test-section">
      <h3>中等尺寸 (48px - 默认)</h3>
      <SPInputField
        size="medium"
        label="48px高度"
        variant="default"
        placeholder="请输入内容"
        style="margin-bottom: 20px;"
      />
      
      <SPInputField
        size="medium"
        label="必填字段"
        variant="default"
        placeholder="请输入内容"
        required
        style="margin-bottom: 20px;"
      />
    </div>

    <div class="test-section">
      <h3>大尺寸 (80px)</h3>
      <SPInputField
        :size="80"
        label="80px高度"
        variant="default"
        placeholder="请输入内容"
        style="margin-bottom: 20px;"
      />
      
      <SPInputField
        :size="80"
        label="必填字段"
        variant="default"
        placeholder="请输入内容"
        required
        style="margin-bottom: 20px;"
      />
    </div>

    <div class="test-section">
      <h3>长标签测试</h3>
      <SPInputField
        :size="30"
        label="这是一个很长的标签文本用来测试缺口宽度"
        variant="default"
        placeholder="请输入内容"
        style="margin-bottom: 20px;"
      />
      
      <SPInputField
        size="medium"
        label="这是一个很长的标签文本用来测试缺口宽度"
        variant="default"
        placeholder="请输入内容"
        style="margin-bottom: 20px;"
      />
      
      <SPInputField
        :size="80"
        label="这是一个很长的标签文本用来测试缺口宽度"
        variant="default"
        placeholder="请输入内容"
        style="margin-bottom: 20px;"
      />
    </div>

    <div class="test-section">
      <h3>特殊字符测试</h3>
      <SPInputField
        size="medium"
        label="Email地址"
        variant="default"
        placeholder="请输入邮箱"
        required
        style="margin-bottom: 20px;"
      />
      
      <SPInputField
        size="medium"
        label="用户名/Username"
        variant="default"
        placeholder="请输入用户名"
        style="margin-bottom: 20px;"
      />
      
      <SPInputField
        size="medium"
        label="密码 (Password)"
        variant="default"
        placeholder="请输入密码"
        required
        style="margin-bottom: 20px;"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import SPInputField from '../packages/ui/src/components/input-field/input-field.vue'
</script>

<style scoped>
.notch-width-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
}
</style>
