# Menu 组件

基础菜单组件，提供灵活的导航功能，是整个组件库的基础设施之一。

## 🎯 设计目标

- 📦 **可复用**: 作为基础组件被 Select、Dropdown、Navigation 等组件复用
- 🎨 **主题友好**: 支持亮色和暗色主题
- ⌨️ **无障碍**: 完整的键盘导航和屏幕阅读器支持
- 🚀 **高性能**: 最小化的重渲染和内存占用

## 📋 组件架构

```
<PERSON>u (容器组件)
  ├── 状态管理 (selectedKeys, openKeys)
  ├── 主题控制 (light/dark)
  ├── 上下文提供 (menuContext)
  └── 事件处理 (select, openChange)

MenuItem (子项组件)
  ├── 选中状态计算
  ├── 点击事件处理
  ├── 键盘导航
  └── 样式类计算
```

## 🔧 API

### Menu Props

| 属性         | 类型                                     | 默认值       | 说明         |
| ------------ | ---------------------------------------- | ------------ | ------------ |
| mode         | `'vertical' \| 'horizontal' \| 'inline'` | `'vertical'` | 菜单模式     |
| theme        | `'light' \| 'dark'`                      | `'light'`    | 主题样式     |
| selectedKeys | `string[]`                               | `[]`         | 选中的菜单项 |
| multiple     | `boolean`                                | `false`      | 是否允许多选 |
| selectable   | `boolean`                                | `true`       | 是否可选择   |
| disabled     | `boolean`                                | `false`      | 是否禁用     |

### Menu Events

| 事件                | 类型                                                      | 说明             |
| ------------------- | --------------------------------------------------------- | ---------------- |
| select              | `(info: { key: string; selectedKeys: string[] }) => void` | 选择菜单项时触发 |
| update:selectedKeys | `(keys: string[]) => void`                                | 选中项变化时触发 |

### MenuItem Props

| 属性     | 类型      | 默认值  | 说明           |
| -------- | --------- | ------- | -------------- |
| key      | `string`  | -       | 菜单项唯一标识 |
| title    | `string`  | -       | 菜单项标题     |
| icon     | `string`  | -       | 图标名称       |
| disabled | `boolean` | `false` | 是否禁用       |
| danger   | `boolean` | `false` | 危险样式       |

## 📖 使用示例

### 基础用法

```vue
<template>
  <Menu v-model:selected-keys="selectedKeys">
    <MenuItem
      key="home"
      title="首页"
    />
    <MenuItem
      key="about"
      title="关于"
    />
    <MenuItem
      key="contact"
      title="联系我们"
    />
  </Menu>
</template>

<script setup>
  import { ref } from 'vue'
  import { Menu, MenuItem } from '@speed-ui/components'

  const selectedKeys = ref(['home'])
</script>
```

### 水平菜单

```vue
<template>
  <Menu
    mode="horizontal"
    v-model:selected-keys="selectedKeys"
  >
    <MenuItem
      key="dashboard"
      title="控制台"
    />
    <MenuItem
      key="users"
      title="用户管理"
    />
    <MenuItem
      key="settings"
      title="设置"
    />
  </Menu>
</template>
```

### 暗色主题

```vue
<template>
  <Menu
    theme="dark"
    v-model:selected-keys="selectedKeys"
  >
    <MenuItem
      key="file"
      title="文件"
    />
    <MenuItem
      key="edit"
      title="编辑"
    />
    <MenuItem
      key="view"
      title="查看"
    />
  </Menu>
</template>
```

### 多选模式

```vue
<template>
  <Menu
    :multiple="true"
    v-model:selected-keys="selectedKeys"
  >
    <MenuItem
      key="feature1"
      title="功能一"
    />
    <MenuItem
      key="feature2"
      title="功能二"
    />
    <MenuItem
      key="feature3"
      title="功能三"
    />
  </Menu>
</template>
```

## 🚀 复用场景

此 Menu 组件将被以下组件复用：

- **Select**: 下拉选项列表
- **Dropdown**: 下拉菜单
- **Navigation**: 导航菜单
- **ContextMenu**: 右键菜单
- **Cascader**: 级联选择器

## ✅ Day 1 完成状态

- ✅ Menu 基础组件
- ✅ MenuItem 子组件
- ✅ TypeScript 类型定义
- ✅ 样式系统（亮色/暗色主题）
- ✅ 状态管理和事件处理
- ✅ 键盘导航支持
- ✅ 使用示例

## 🎯 下一步计划

**Day 3-4**: 开发 List 和 ListItem 组件，为虚拟滚动和复杂选项功能做准备。
