# Layout 布局

通过基础的 24 分栏，迅速简便地创建布局。

## 基础用法

<script setup>
import { Row, Col } from '@speed-ui/ui'
</script>

使用列创建基础网格布局。

通过 `sp-row` 和 `sp-col` 组件，并通过 `sp-col` 组件的 `span` 属性我们就可以自由地组合布局。

<div style="margin: 16px 0;">
  <Row style="margin-bottom: 20px;">
    <Col :span="24">
      <div style="background: #99a9bf; height: 36px; border-radius: 4px;"></div>
    </Col>
  </Row>
  <Row style="margin-bottom: 20px;">
    <Col :span="12">
      <div style="background: #d3dce6; height: 36px; border-radius: 4px;"></div>
    </Col>
    <Col :span="12">
      <div style="background: #e5e9f2; height: 36px; border-radius: 4px;"></div>
    </Col>
  </Row>
  <Row style="margin-bottom: 20px;">
    <Col :span="8">
      <div style="background: #d3dce6; height: 36px; border-radius: 4px;"></div>
    </Col>
    <Col :span="8">
      <div style="background: #e5e9f2; height: 36px; border-radius: 4px;"></div>
    </Col>
    <Col :span="8">
      <div style="background: #d3dce6; height: 36px; border-radius: 4px;"></div>
    </Col>
  </Row>
</div>

```vue
<template>
  <SpRow>
    <SpCol :span="24">
      <div class="grid-content"></div>
    </SpCol>
  </SpRow>
  <SpRow>
    <SpCol :span="12">
      <div class="grid-content"></div>
    </SpCol>
    <SpCol :span="12">
      <div class="grid-content"></div>
    </SpCol>
  </SpRow>
  <SpRow>
    <SpCol :span="8">
      <div class="grid-content"></div>
    </SpCol>
    <SpCol :span="8">
      <div class="grid-content"></div>
    </SpCol>
    <SpCol :span="8">
      <div class="grid-content"></div>
    </SpCol>
  </SpRow>
</template>
```

## 分栏间隔

支持列间距。

行提供 `gutter` 属性来指定列之间的间距，其默认值为 0。

<div style="margin: 16px 0;">
  <Row :gutter="20">
    <Col :span="6">
      <div style="background: #d3dce6; height: 36px; border-radius: 4px;"></div>
    </Col>
    <Col :span="6">
      <div style="background: #d3dce6; height: 36px; border-radius: 4px;"></div>
    </Col>
    <Col :span="6">
      <div style="background: #d3dce6; height: 36px; border-radius: 4px;"></div>
    </Col>
    <Col :span="6">
      <div style="background: #d3dce6; height: 36px; border-radius: 4px;"></div>
    </Col>
  </Row>
</div>

```vue
<template>
  <SpRow :gutter="20">
    <SpCol :span="6">
      <div class="grid-content"></div>
    </SpCol>
    <SpCol :span="6">
      <div class="grid-content"></div>
    </SpCol>
    <SpCol :span="6">
      <div class="grid-content"></div>
    </SpCol>
    <SpCol :span="6">
      <div class="grid-content"></div>
    </SpCol>
  </SpRow>
</template>
```

## 列偏移

支持偏移指定的列数。

<div style="margin: 16px 0;">
  <Row style="margin-bottom: 20px;">
    <Col :span="6">
      <div style="background: #d3dce6; height: 36px; border-radius: 4px;"></div>
    </Col>
    <Col :span="6" :offset="6">
      <div style="background: #d3dce6; height: 36px; border-radius: 4px;"></div>
    </Col>
  </Row>
  <Row>
    <Col :span="6" :offset="6">
      <div style="background: #d3dce6; height: 36px; border-radius: 4px;"></div>
    </Col>
    <Col :span="6" :offset="6">
      <div style="background: #d3dce6; height: 36px; border-radius: 4px;"></div>
    </Col>
  </Row>
</div>

```vue
<template>
  <SpRow>
    <SpCol :span="6">
      <div class="grid-content"></div>
    </SpCol>
    <SpCol :span="6" :offset="6">
      <div class="grid-content"></div>
    </SpCol>
  </SpRow>
  <SpRow>
    <SpCol :span="6" :offset="6">
      <div class="grid-content"></div>
    </SpCol>
    <SpCol :span="6" :offset="6">
      <div class="grid-content"></div>
    </SpCol>
  </SpRow>
</template>
```

## 对齐方式

通过 `justify` 属性来对分栏进行灵活的对齐。

<div style="margin: 16px 0;">
  <Row justify="center" style="margin-bottom: 20px;">
    <Col :span="4">
      <div style="background: #d3dce6; height: 36px; border-radius: 4px;"></div>
    </Col>
    <Col :span="4">
      <div style="background: #e5e9f2; height: 36px; border-radius: 4px;"></div>
    </Col>
    <Col :span="4">
      <div style="background: #d3dce6; height: 36px; border-radius: 4px;"></div>
    </Col>
  </Row>
  <Row justify="end" style="margin-bottom: 20px;">
    <Col :span="4">
      <div style="background: #d3dce6; height: 36px; border-radius: 4px;"></div>
    </Col>
    <Col :span="4">
      <div style="background: #e5e9f2; height: 36px; border-radius: 4px;"></div>
    </Col>
    <Col :span="4">
      <div style="background: #d3dce6; height: 36px; border-radius: 4px;"></div>
    </Col>
  </Row>
  <Row justify="space-between">
    <Col :span="4">
      <div style="background: #d3dce6; height: 36px; border-radius: 4px;"></div>
    </Col>
    <Col :span="4">
      <div style="background: #e5e9f2; height: 36px; border-radius: 4px;"></div>
    </Col>
    <Col :span="4">
      <div style="background: #d3dce6; height: 36px; border-radius: 4px;"></div>
    </Col>
  </Row>
</div>

```vue
<template>
  <SpRow justify="center">
    <SpCol :span="4">
      <div class="grid-content"></div>
    </SpCol>
    <SpCol :span="4">
      <div class="grid-content"></div>
    </SpCol>
    <SpCol :span="4">
      <div class="grid-content"></div>
    </SpCol>
  </SpRow>
  <SpRow justify="end">
    <SpCol :span="4">
      <div class="grid-content"></div>
    </SpCol>
    <SpCol :span="4">
      <div class="grid-content"></div>
    </SpCol>
    <SpCol :span="4">
      <div class="grid-content"></div>
    </SpCol>
  </SpRow>
  <SpRow justify="space-between">
    <SpCol :span="4">
      <div class="grid-content"></div>
    </SpCol>
    <SpCol :span="4">
      <div class="grid-content"></div>
    </SpCol>
    <SpCol :span="4">
      <div class="grid-content"></div>
    </SpCol>
  </SpRow>
</template>
```

## 响应式布局

参照了 Bootstrap 的响应式设计，预设了五个响应尺寸：`xs`、`sm`、`md`、`lg` 和 `xl`。

<div style="margin: 16px 0;">
  <Row :gutter="10">
    <Col :xs="8" :sm="6" :md="4" :lg="3" :xl="1">
      <div style="background: #d3dce6; height: 36px; border-radius: 4px;"></div>
    </Col>
    <Col :xs="4" :sm="6" :md="8" :lg="9" :xl="11">
      <div style="background: #e5e9f2; height: 36px; border-radius: 4px;"></div>
    </Col>
    <Col :xs="4" :sm="6" :md="8" :lg="9" :xl="11">
      <div style="background: #d3dce6; height: 36px; border-radius: 4px;"></div>
    </Col>
    <Col :xs="8" :sm="6" :md="4" :lg="3" :xl="1">
      <div style="background: #e5e9f2; height: 36px; border-radius: 4px;"></div>
    </Col>
  </Row>
</div>

```vue
<template>
  <SpRow :gutter="10">
    <SpCol :xs="8" :sm="6" :md="4" :lg="3" :xl="1">
      <div class="grid-content"></div>
    </SpCol>
    <SpCol :xs="4" :sm="6" :md="8" :lg="9" :xl="11">
      <div class="grid-content"></div>
    </SpCol>
    <SpCol :xs="4" :sm="6" :md="8" :lg="9" :xl="11">
      <div class="grid-content"></div>
    </SpCol>
    <SpCol :xs="8" :sm="6" :md="4" :lg="3" :xl="1">
      <div class="grid-content"></div>
    </SpCol>
  </SpRow>
</template>
```

## Row API

### Row Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| gutter | 栅格间隔 | `number` | `0` |
| justify | flex 布局下的水平排列方式 | `'start' \| 'end' \| 'center' \| 'space-around' \| 'space-between' \| 'space-evenly'` | `'start'` |
| align | flex 布局下的垂直排列方式 | `'top' \| 'middle' \| 'bottom'` | `'top'` |
| tag | 自定义元素标签 | `string` | `'div'` |

### Row Slots

| 插槽名 | 说明 | 子标签 |
| --- | --- | --- |
| default | 自定义默认内容 | Col |

## Col API

### Col Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| span | 栅格占据的列数 | `number` | `24` |
| offset | 栅格左侧的间隔格数 | `number` | `0` |
| push | 栅格向右移动格数 | `number` | `0` |
| pull | 栅格向左移动格数 | `number` | `0` |
| xs | `<768px` 响应式栅格数或者栅格属性对象 | `number \| ResponsiveProps` | — |
| sm | `≥768px` 响应式栅格数或者栅格属性对象 | `number \| ResponsiveProps` | — |
| md | `≥992px` 响应式栅格数或者栅格属性对象 | `number \| ResponsiveProps` | — |
| lg | `≥1200px` 响应式栅格数或者栅格属性对象 | `number \| ResponsiveProps` | — |
| xl | `≥1920px` 响应式栅格数或者栅格属性对象 | `number \| ResponsiveProps` | — |
| tag | 自定义元素标签 | `string` | `'div'` |

### ResponsiveProps

| 参数 | 说明 | 类型 |
| --- | --- | --- |
| span | 栅格占据的列数 | `number` |
| offset | 栅格左侧的间隔格数 | `number` |
| push | 栅格向右移动格数 | `number` |
| pull | 栅格向左移动格数 | `number` |

### Col Slots

| 插槽名 | 说明 |
| --- | --- |
| default | 自定义默认内容 | 