<template>
  <ComponentShowcase
    page-title="Button 按钮"
    page-description="常用的操作按钮组件，支持多种类型、尺寸和状态。"
  >
    <!-- 使用 case 文件展示 -->
    <DemoShowcase case-file="./default.case.vue" />
    <DemoShowcase case-file="./default2.case.vue" />

    <!-- 基础用法演示 -->
    <DemoShowcase
      title="基础"
      description="按钮的 type 分别为 default 、 outline 、 secondary 、 success 、 warning 和 danger 。"
      :code-content="basicButtonCode"
    >
      <template #demo>
        <div class="button-group">
          <sp-button>Default</sp-button>
          <sp-button type="outline">Outline2</sp-button>
          <sp-button type="secondary">Secondary</sp-button>
          <sp-button type="success">Success</sp-button>
          <sp-button type="warning">Warning</sp-button>
          <sp-button type="danger">Danger</sp-button>
        </div>
      </template>
    </DemoShowcase>

    <!-- 按钮尺寸演示 -->
    <DemoShowcase
      title="尺寸"
      description="按钮有大、中、小三种尺寸。"
      :code-content="sizeButtonCode"
    >
      <template #demo>
        <div class="button-group">
          <sp-button size="large">大型</sp-button>
          <sp-button size="medium">中型</sp-button>
          <sp-button size="small">小型</sp-button>
          <sp-button size="small">小下型</sp-button>
        </div>
      </template>
    </DemoShowcase>

    <!-- 禁用状态演示 -->
    <DemoShowcase
      title="禁用状态"
      description="按钮不可用状态。"
      :code-content="disabledButtonCode"
    >
      <template #demo>
        <div class="button-group">
          <sp-button disabled>默认按钮</sp-button>
          <sp-button type="secondary" disabled>次要按钮</sp-button>
          <sp-button type="success" disabled>成功按钮</sp-button>
        </div>
      </template>
    </DemoShowcase>

    <!-- 加载状态演示 -->
    <DemoShowcase
      title="加载状态"
      description="点击按钮后进行数据加载操作，在按钮上显示加载状态。"
      :code-content="loadingButtonCode"
    >
      <template #demo>
        <div class="button-group">
          <sp-button :loading="loading1" @click="handleClick1">
            点击加载
          </sp-button>
          <sp-button type="secondary" :loading="loading2" @click="handleClick2">
            加载中
          </sp-button>
        </div>
      </template>
    </DemoShowcase>
  </ComponentShowcase>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ComponentShowcase from './ComponentShowcase.vue'
import DemoShowcase from './DemoShowcase.vue'

const loading1 = ref(false)
const loading2 = ref(false)

const handleClick1 = () => {
  loading1.value = true
  setTimeout(() => {
    loading1.value = false
  }, 2000)
}

const handleClick2 = () => {
  loading2.value = true
  setTimeout(() => {
    loading2.value = false
  }, 2000)
}

// 代码示例
const basicButtonCode = `<template>
  <div class="button-group">
    <sp-button>Default</sp-button>
    <sp-button type="outline">Outline</sp-button>
    <sp-button type="secondary">Secondary</sp-button>
    <sp-button type="success">Success</sp-button>
    <sp-button type="warning">Warning</sp-button>
    <sp-button type="danger">Danger</sp-button>
  </div>
</template>`

const sizeButtonCode = `<template>
  <div class="button-group">
    <sp-button size="large">大型</sp-button>
    <sp-button size="medium">中型</sp-button>
    <sp-button size="small">小型</sp-button>
  </div>
</template>`

const disabledButtonCode = `<template>
  <div class="button-group">
    <sp-button disabled>默认按钮</sp-button>
    <sp-button type="secondary" disabled>次要按钮</sp-button>
    <sp-button type="success" disabled>成功按钮</sp-button>
  </div>
</template>`

const loadingButtonCode = `<template>
  <div class="button-group">
    <sp-button :loading="loading1" @click="handleClick1">
      点击加载
    </sp-button>
    <sp-button type="secondary" :loading="loading2" @click="handleClick2">
      加载中
    </sp-button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const loading1 = ref(false)
const loading2 = ref(false)

const handleClick1 = () => {
  loading1.value = true
  setTimeout(() => {
    loading1.value = false
  }, 2000)
}

const handleClick2 = () => {
  loading2.value = true
  setTimeout(() => {
    loading2.value = false
  }, 2000)
}
<\/script>`
</script>

<style scoped>
.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}
</style> 