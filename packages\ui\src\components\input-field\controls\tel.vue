<!--
  TelControls.vue - 电话号码输入框的控制器组件
  包含电话图标和国家/地区代码下拉选择功能
-->

<template>
  <div
    class="sp-tel-controls"
    @click.stop
    @mousedown.stop
  >
    <!-- 电话图标和下拉箭头 -->
    <div
      class="sp-tel-controls__trigger"
      :class="triggerClasses"
      @click.stop.prevent="handleTriggerClick"
      @mousedown.stop.prevent
    >
      <sp-icon
        name="Call"
        :size="iconSize"
        class="sp-tel-controls__phone-icon"
      />
      <sp-icon
        name="ChevronDown"
        :size="Math.max(iconSize - 2, 10)"
        class="sp-tel-controls__dropdown-icon"
        :class="{ 'sp-tel-controls__dropdown-icon--open': isDropdownOpen }"
      />
    </div>

    <!-- 下拉选择框 -->
    <SelectDropdown
      :visible="isDropdownOpen"
      :options="countryOptions"
      :prefixCls="'sp-tel-controls'"
      :dropdownStyle="{
        position: 'absolute',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
        top: '100%',
        left: '0',
        zIndex: 1000,
        marginTop: '4px',
        minWidth: '200px',
        width: 'max-content',
      }"
      :value="props.selectedCountryCode"
      :multiple="false"
      :valueKey="'code'"
      :labelKey="'name'"
      :emptyText="'暂无国家数据'"
      :variant="'simple'"
      @optionClick="handleCountryOptionClick"
    >
      <!-- 自定义选项内容 -->
      <template #option="{ option, selected }">
        <div class="sp-tel-controls__option-content">
          <span class="sp-tel-controls__flag">{{ option.flag }}</span>
          <span class="sp-tel-controls__name">{{ option.name }}</span>
          <span class="sp-tel-controls__code">{{ option.dialCode }}</span>
        </div>
      </template>

      <!-- 隐藏默认的选中图标，因为我们用不同的视觉反馈 -->
      <template #option-suffix>
        <!-- 空的，不显示 checkmark -->
      </template>
    </SelectDropdown>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, onMounted, onUnmounted } from 'vue'
  import { getCountries, getCountryCallingCode } from 'libphonenumber-js'
  import SpIcon from '../../icon/Icon.vue'
  import SelectDropdown from '../../select/internal/SelectDropdown.vue'
  import type { SelectOption } from '../../select/select'

  interface Country {
    code: string
    name: string
    dialCode: string
    flag: string
  }

  interface TelControlsProps {
    /** 图标大小 */
    iconSize: number
    /** 是否禁用 */
    disabled?: boolean
    /** 当前选中的国家代码 */
    selectedCountryCode?: string
  }

  interface TelControlsEmits {
    /** 国家代码选择 */
    (e: 'country-select', country: Country): void
    /** 更新选中的国家代码 */
    (e: 'update:selectedCountryCode', code: string): void
    /** 请求聚焦 */
    (e: 'focus'): void
  }

  const props = withDefaults(defineProps<TelControlsProps>(), {
    disabled: false,
    selectedCountryCode: 'CN',
  })
  const emit = defineEmits<TelControlsEmits>()

  // ===== 状态管理 =====
  const isDropdownOpen = ref(false)

  // ===== 国家名称映射（中文） =====
  const countryNames: Record<string, string> = {
    CN: '中国',
    US: '美国',
    JP: '日本',
    KR: '韩国',
    GB: '英国',
    DE: '德国',
    FR: '法国',
    AU: '澳大利亚',
    CA: '加拿大',
    SG: '新加坡',
    IN: '印度',
    BR: '巴西',
    RU: '俄罗斯',
    IT: '意大利',
    ES: '西班牙',
    NL: '荷兰',
    SE: '瑞典',
    NO: '挪威',
    DK: '丹麦',
    FI: '芬兰',
    CH: '瑞士',
    AT: '奥地利',
    BE: '比利时',
    IE: '爱尔兰',
    PT: '葡萄牙',
    GR: '希腊',
    PL: '波兰',
    CZ: '捷克',
    HU: '匈牙利',
    RO: '罗马尼亚',
    BG: '保加利亚',
    HR: '克罗地亚',
    SI: '斯洛文尼亚',
    SK: '斯洛伐克',
    LT: '立陶宛',
    LV: '拉脱维亚',
    EE: '爱沙尼亚',
    MT: '马耳他',
    CY: '塞浦路斯',
    LU: '卢森堡',
    IS: '冰岛',
    LI: '列支敦士登',
    MC: '摩纳哥',
    SM: '圣马力诺',
    VA: '梵蒂冈',
    AD: '安道尔',
  }

  // ===== 国旗映射 =====
  const countryFlags: Record<string, string> = {
    CN: '🇨🇳',
    US: '🇺🇸',
    JP: '🇯🇵',
    KR: '🇰🇷',
    GB: '🇬🇧',
    DE: '🇩🇪',
    FR: '🇫🇷',
    AU: '🇦🇺',
    CA: '🇨🇦',
    SG: '🇸🇬',
    IN: '🇮🇳',
    BR: '🇧🇷',
    RU: '🇷🇺',
    IT: '🇮🇹',
    ES: '🇪🇸',
    NL: '🇳🇱',
    SE: '🇸🇪',
    NO: '🇳🇴',
    DK: '🇩🇰',
    FI: '🇫🇮',
    CH: '🇨🇭',
    AT: '🇦🇹',
    BE: '🇧🇪',
    IE: '🇮🇪',
    PT: '🇵🇹',
    GR: '🇬🇷',
    PL: '🇵🇱',
    CZ: '🇨🇿',
    HU: '🇭🇺',
    RO: '🇷🇴',
    BG: '🇧🇬',
    HR: '🇭🇷',
    SI: '🇸🇮',
    SK: '🇸🇰',
    LT: '🇱🇹',
    LV: '🇱🇻',
    EE: '🇪🇪',
    MT: '🇲🇹',
    CY: '🇨🇾',
    LU: '🇱🇺',
    IS: '🇮🇸',
    LI: '🇱🇮',
    MC: '🇲🇨',
    SM: '🇸🇲',
    VA: '🇻🇦',
    AD: '🇦🇩',
  }

  // ===== 基础国家数据（保证基本功能） =====
  const baseCountries: Country[] = [
    { code: 'CN', name: '中国', dialCode: '+86', flag: '🇨🇳' },
    { code: 'US', name: '美国', dialCode: '+1', flag: '🇺🇸' },
    { code: 'JP', name: '日本', dialCode: '+81', flag: '🇯🇵' },
    { code: 'KR', name: '韩国', dialCode: '+82', flag: '🇰🇷' },
    { code: 'GB', name: '英国', dialCode: '+44', flag: '🇬🇧' },
    { code: 'DE', name: '德国', dialCode: '+49', flag: '🇩🇪' },
    { code: 'FR', name: '法国', dialCode: '+33', flag: '🇫🇷' },
    { code: 'AU', name: '澳大利亚', dialCode: '+61', flag: '🇦🇺' },
    { code: 'CA', name: '加拿大', dialCode: '+1', flag: '🇨🇦' },
    { code: 'SG', name: '新加坡', dialCode: '+65', flag: '🇸🇬' },
  ]

  // ===== 扩展的国家数据（使用 libphonenumber-js） =====
  const countries = ref<Country[]>(baseCountries)

  // 尝试用 libphonenumber-js 扩展国家列表
  const expandCountriesWithLibphone = () => {
    try {
      const allCountries = getCountries()
      const additionalCountries = [
        'IN',
        'BR',
        'RU',
        'IT',
        'ES',
        'NL',
        'SE',
        'NO',
        'DK',
        'FI',
        'CH',
        'AT',
        'BE',
        'IE',
        'PT',
      ]

      const expandedList = [...baseCountries]

      additionalCountries.forEach(countryCode => {
        if (allCountries.includes(countryCode as any)) {
          try {
            const dialCode = getCountryCallingCode(countryCode as any)
            expandedList.push({
              code: countryCode,
              name: countryNames[countryCode] || countryCode,
              dialCode: `+${dialCode}`,
              flag: countryFlags[countryCode] || '🏳️',
            })
          } catch (error) {
            console.warn(
              `Failed to get calling code for ${countryCode}:`,
              error
            )
          }
        }
      })

      countries.value = expandedList
      console.log('Successfully expanded countries list with libphonenumber-js')
    } catch (error) {
      console.warn(
        'Failed to expand countries with libphonenumber-js, using base list:',
        error
      )
      countries.value = baseCountries
    }
  }

  // ===== 计算属性 =====
  const selectedCountry = computed(() => {
    return (
      countries.value.find(
        (country: Country) => country.code === props.selectedCountryCode
      ) || countries.value[0]
    )
  })

  // 将 Country 数据转换为 SelectOption 格式
  const countryOptions = computed(() => {
    return countries.value.map(
      (country: Country): SelectOption => ({
        value: country.code,
        label: country.name,
        // 保留原始的 Country 属性，用于插槽显示
        ...country,
      })
    )
  })

  const triggerClasses = computed(() => ({
    'sp-tel-controls__trigger--disabled': props.disabled,
    'sp-tel-controls__trigger--open': isDropdownOpen.value,
  }))

  // ===== 事件处理 =====
  const handleTriggerClick = () => {
    if (props.disabled) return

    isDropdownOpen.value = !isDropdownOpen.value
    emit('focus')
  }

  const handleCountrySelect = (country: Country) => {
    emit('country-select', country)
    emit('update:selectedCountryCode', country.code)
    isDropdownOpen.value = false
    emit('focus')
  }

  // 处理 SelectDropdown 的选项点击事件
  const handleCountryOptionClick = (option: SelectOption) => {
    // 将 SelectOption 转换回 Country 格式
    const country: Country = {
      code: option.code as string,
      name: option.name as string,
      dialCode: option.dialCode as string,
      flag: option.flag as string,
    }

    handleCountrySelect(country)
  }

  // ===== 外部点击关闭下拉框 =====
  const handleOutsideClick = (event: MouseEvent) => {
    const target = event.target as HTMLElement
    if (!target.closest('.sp-tel-controls')) {
      isDropdownOpen.value = false
    }
  }

  onMounted(() => {
    // 尝试扩展国家列表
    expandCountriesWithLibphone()
    // 添加外部点击监听
    document.addEventListener('click', handleOutsideClick)
  })

  onUnmounted(() => {
    document.removeEventListener('click', handleOutsideClick)
  })
</script>

<script lang="ts">
  export default {
    name: 'SpTelControls',
  }
</script>

<!-- <style lang="scss">
  .sp-tel-controls {
    position: relative;
    display: flex;
    align-items: center;
    pointer-events: auto;

    &__trigger {
      display: flex;
      align-items: center;
      gap: 2px;
      padding: 2px 4px;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;
      color: var(--sp-color-text-secondary, #666);

      &:hover:not(&--disabled) {
        color: var(--sp-color-primary, #409eff);
        background: rgba(64, 158, 255, 0.1);
      }

      &--open {
        color: var(--sp-color-primary, #409eff);
        background: rgba(64, 158, 255, 0.1);
      }

      &--disabled {
        color: var(--sp-color-text-disabled, #c0c4cc);
        cursor: not-allowed;
      }
    }

    &__phone-icon {
      flex-shrink: 0;
    }

    &__dropdown-icon {
      flex-shrink: 0;
      transition: transform 0.2s ease;

      &--open {
        transform: rotate(180deg);
      }
    }

    // 下拉框样式已通过 dropdownStyle 传递给 SelectDropdown 组件

    &__option-content {
      display: flex;
      align-items: center;
      gap: 8px;
      width: 100%;
      font-size: 14px;
      white-space: nowrap;
      padding: 0; // 移除额外的 padding，因为 ListItem 会处理
    }

    &__flag {
      font-size: 16px;
      flex-shrink: 0;
    }

    &__name {
      min-width: 80px; // 确保国家名称有足够空间
      margin-right: 12px; // 与代码之间的间距
    }

    &__code {
      color: var(--sp-color-text-secondary, #666);
      font-size: 12px;
      flex-shrink: 0;
    }
  }

  // 动画由 SelectDropdown 组件内部处理
</style> -->
