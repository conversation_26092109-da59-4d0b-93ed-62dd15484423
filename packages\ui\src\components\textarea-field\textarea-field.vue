<!--
  TextareaField.vue - 接口层
  职责：对外暴露API，接收props和events，处理业务逻辑
-->

<template>
  <TextareaFieldCore
    ref="textareaFieldCoreRef"
    v-bind="coreProps"
    @update:value="handleValueUpdate"
    @input="handleInput"
    @change="handleChange"
    @focus="handleFocus"
    @blur="handleBlur"
    @clear="handleClear"
    @validate="handleValidate"
  >
    <!-- 不再支持前缀/后缀插槽，只保留内置功能 -->
  </TextareaFieldCore>
</template>

<script setup lang="ts">
  import { ref, computed, inject } from 'vue'
  import { useVModel } from '@speed-ui/hooks'
  import TextareaFieldCore from './textarea-field-core.vue'
  import type { TextareaFieldProps, TextareaFieldEmits } from './types'
  import { textareaFieldPropsDefaults } from './types'
  import type { FormContext } from '../Form/types'

  /**
   * TextareaField 组件 - 接口层
   *
   * 结合 Textarea 和 FormItem 功能的综合组件：
   * textarea-field.vue (接口层) → textarea-field-core.vue (实现层)
   */

  const props = withDefaults(
    defineProps<TextareaFieldProps>(),
    textareaFieldPropsDefaults
  )
  const emit = defineEmits<TextareaFieldEmits>()

  // 表单上下文注入
  const formContext = inject<FormContext>('spForm', {})

  // 组件引用
  const textareaFieldCoreRef = ref<InstanceType<
    typeof TextareaFieldCore
  > | null>(null)

  // 状态管理
  const focused = ref(false)

  // 使用 useVModel 进行双向绑定
  const {
    value: currentValue,
    updateValue,
    hasValue: hasValueComputed,
  } = useVModel<string | undefined>(props, emit, {
    prop: 'value',
    event: 'update:value',
    defaultValue: props.value,
    onAfterUpdate: newValue => {
      emit('change', newValue)
    },
    debug: false,
  })

  // 从表单上下文继承配置
  const computedSize = computed(() => {
    return props.size || formContext?.size || 'medium'
  })

  const computedDisabled = computed(() => {
    return props.disabled || formContext?.disabled || false
  })

  const computedShowMessage = computed(() => {
    return props.showMessage ?? formContext?.showMessage ?? true
  })

  // 传递给核心层的属性
  const coreProps = computed(() => ({
    ...props,
    value: currentValue.value,
    size: computedSize.value,
    disabled: computedDisabled.value,
    showMessage: computedShowMessage.value,
    focused: focused.value,
    hasValue: hasValueComputed.value,
  }))

  // 事件处理
  const handleValueUpdate = (value: string | undefined) => {
    updateValue(value)
  }

  const handleInput = (event: Event) => {
    emit('input', event)
  }

  const handleChange = (value: string | undefined) => {
    // change 事件已经通过 useVModel 的 onAfterUpdate 处理
  }

  const handleFocus = (event: FocusEvent) => {
    focused.value = true
    emit('focus', event)
  }

  const handleBlur = (event: FocusEvent) => {
    focused.value = false
    emit('blur', event)
  }

  const handleClear = () => {
    updateValue(undefined)
    emit('clear')
  }

  const handleValidate = (name: string, isValid: boolean, message: string) => {
    emit('validate', name, isValid, message)
  }

  // 暴露的方法
  const focus = () => {
    textareaFieldCoreRef.value?.focus?.()
  }

  const blur = () => {
    textareaFieldCoreRef.value?.blur?.()
  }

  const select = () => {
    textareaFieldCoreRef.value?.select?.()
  }

  const clear = () => {
    updateValue(undefined)
    emit('clear')
  }

  const validate = async (): Promise<boolean> => {
    return (await textareaFieldCoreRef.value?.validate?.()) || false
  }

  const resetField = () => {
    textareaFieldCoreRef.value?.resetField?.()
  }

  const clearValidate = () => {
    textareaFieldCoreRef.value?.clearValidate?.()
  }

  const adjustHeight = () => {
    textareaFieldCoreRef.value?.adjustHeight?.()
  }

  // 暴露给外部使用
  defineExpose({
    /** 使文本域获得焦点 */
    focus,
    /** 使文本域失去焦点 */
    blur,
    /** 选中文本域中的文字 */
    select,
    /** 清空文本域 */
    clear,
    /** 验证字段 */
    validate,
    /** 重置字段 */
    resetField,
    /** 清除验证 */
    clearValidate,
    /** 调整文本域高度（autosize 模式下） */
    adjustHeight,
    /** 文本域元素引用 */
    get textarea() {
      return textareaFieldCoreRef.value?.textarea || null
    },
    /** 包装器元素引用 */
    get wrapper() {
      return textareaFieldCoreRef.value?.wrapper || null
    },
  })
</script>

<script lang="ts">
  export default {
    name: 'SpTextareaField',
  }
</script>
