const chokidar = require('chokidar')
const path = require('path')
const CaseProcessor = require('./case-processor')

class CaseWatcher {
  constructor() {
    this.processor = new CaseProcessor()
    // 从根目录运行时的路径
    this.watchDir = path.join(process.cwd(), 'packages/ui/src/components')
  }

  start() {
    console.log('👀 开始监听 .case.vue 文件变化...')
    console.log(`📁 监听目录: ${path.relative(process.cwd(), this.watchDir)}`)

    // 初始构建
    this.processor.processAll()

    // 创建文件监听器
    const watcher = chokidar.watch('**/*.case.vue', {
      cwd: this.watchDir,
      ignored: /node_modules/,
      persistent: true,
      ignoreInitial: true
    })

    // 监听文件变化
    watcher
      .on('add', (filePath) => {
        console.log(`\n📄 新增文件: ${filePath}`)
        this.handleFileChange(filePath)
      })
      .on('change', (filePath) => {
        console.log(`\n📝 文件变化: ${filePath}`)
        this.handleFileChange(filePath)
      })
      .on('unlink', (filePath) => {
        console.log(`\n🗑️  删除文件: ${filePath}`)
        this.handleFileDelete(filePath)
      })
      .on('error', (error) => {
        console.error('❌ 监听错误:', error)
      })

    console.log('✅ 文件监听已启动，按 Ctrl+C 停止')

    // 优雅退出
    process.on('SIGINT', () => {
      console.log('\n👋 停止文件监听...')
      watcher.close()
      process.exit(0)
    })
  }

  handleFileChange(filePath) {
    const fullPath = path.join(this.watchDir, filePath)
    
    // 延迟处理，避免文件正在写入时读取
    setTimeout(() => {
      this.processor.processFile(fullPath)
    }, 100)
  }

  handleFileDelete(filePath) {
    const fullPath = path.join(this.watchDir, filePath)
    const outputPath = fullPath.replace('.case.vue', '.cases.vue')
    
    try {
      const fs = require('fs')
      if (fs.existsSync(outputPath)) {
        fs.unlinkSync(outputPath)
        console.log(`🗑️  已删除对应的输出文件: ${path.relative(process.cwd(), outputPath)}`)
      }
    } catch (error) {
      console.error(`❌ 删除输出文件失败: ${path.relative(process.cwd(), outputPath)}`, error.message)
    }
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const watcher = new CaseWatcher()
  watcher.start()
}

module.exports = CaseWatcher 