<template>
  <div
    ref="formItemRef"
    :class="[
      'sp-form-item',
      `sp-form-item--${sizeComputed}`,
      `sp-form-item--label-${labelPositionComputed}`,
      {
        'sp-form-item--error': currentValidateState === 'error',
        'sp-form-item--success': currentValidateState === 'success',
        'sp-form-item--validating': currentValidateState === 'validating',
        'sp-form-item--required': isRequired,
        'sp-form-item--no-asterisk': formContext?.hideRequiredAsterisk,
      },
    ]"
  >
    <!-- 标签区域 -->
    <label
      v-if="shouldShowLabel"
      :class="[
        'sp-form-item__label',
        {
          'sp-form-item__label--required': showRequiredAsterisk,
        },
      ]"
      :style="labelStyle"
      :for="labelFor"
    >
      <!-- 验证状态图标 -->
      <span v-if="currentValidateState === 'success'" class="sp-form-item__label-icon sp-form-item__label-icon--success">✓</span>
      <span v-else-if="showRequiredAsterisk" class="sp-form-item__label-icon sp-form-item__label-icon--required">*</span>
      <slot name="label">{{ labelTextComputed }}</slot>
      <span v-if="formContext?.labelSuffix" class="sp-form-item__label-suffix">
        {{ formContext.labelSuffix }}
      </span>
    </label>

    <!-- 内容区域 -->
    <div class="sp-form-item__content" :style="contentStyle">
      <!-- 默认插槽 - 简单使用 -->
      <slot></slot>
      
      <!-- 高级插槽 - 需要访问验证状态时使用 -->
      <slot
        v-if="$slots.advanced"
        name="advanced"
        :value="veeField?.value.value"
        :setValue="veeField?.setValue"
        :meta="veeField?.meta"
        :errors="veeField?.errors"
        :handleChange="veeField?.handleChange"
        :handleBlur="veeField?.handleBlur"
      ></slot>
      
      <!-- 验证消息 -->
      <transition name="sp-form-item-message">
        <div
          v-if="shouldShowMessage"
          :class="[
            'sp-form-item__error',
            `sp-form-item__error--${currentValidateState}`,
            {
              'sp-form-item__error--inline': formContext?.inlineMessage,
            },
          ]"
        >
          <i
            v-if="formContext?.statusIcon && currentValidateState"
            :class="[
              'sp-form-item__error-icon',
              statusIconClass,
            ]"
          ></i>
          {{ veeField?.errorMessage.value }}
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  ref, 
  computed, 
  inject, 
  onMounted, 
  onUnmounted, 
  reactive,
  provide,
  useSlots
} from 'vue'
import { useField } from 'vee-validate'
import type { FormContext } from './types'
import './style'

// Label 配置接口
interface LabelConfig {
  text?: string
  position?: 'left' | 'right' | 'top'
  show?: boolean
  width?: string | number
}

interface FormItemProps {
  label?: string | LabelConfig
  labelFor?: string
  name: string
  rules?: any // 用户自定义的验证规则
  size?: 'small' | 'default' | 'large'
  showMessage?: boolean
  inlineMessage?: boolean
  required?: boolean
}

const props = withDefaults(defineProps<FormItemProps>(), {
  showMessage: true,
  inlineMessage: false,
  required: false,
})

const emit = defineEmits<{
  validate: [name: string, isValid: boolean, message: string]
}>()

const formItemRef = ref<HTMLDivElement>()
const slots = useSlots()

// 注入 Form 组件上下文
const formContext = inject<FormContext>('spForm', {})

// VeeValidate 字段
const veeField = useField(props.name, props.rules as any, {
  validateOnValueUpdate: false,
})

// 包装的失焦处理函数，确保触发验证
const handleBlurWithValidation = async () => {
  // 首先调用 VeeValidate 的原始 handleBlur
  veeField.handleBlur()
  
  // 然后手动触发验证以确保错误消息更新
  await veeField.validate()
}

// Label 相关计算属性
const labelConfig = computed<LabelConfig>(() => {
  if (typeof props.label === 'string') {
    return { text: props.label, show: true }
  }
  return props.label || {}
})

const labelTextComputed = computed(() => {
  return labelConfig.value.text || ''
})

const shouldShowLabel = computed(() => {
  // 如果 Form 组件设置了 hideLabel 为 true，则隐藏所有标签
  if (formContext?.hideLabel === true) {
    return false
  }
  
  // 如果明确设置了 show 为 false，则不显示
  if (labelConfig.value.show === false) {
    return false
  }
  
  // 有 label 文本或有 label 插槽时显示
  return !!(labelTextComputed.value || slots.label)
})

const labelPositionComputed = computed(() => {
  return labelConfig.value.position || formContext?.labelPosition || 'right'
})

const labelWidthComputed = computed(() => {
  // 优先使用 label 配置中的 width
  if (labelConfig.value.width) {
    return typeof labelConfig.value.width === 'number' 
      ? `${labelConfig.value.width}px` 
      : labelConfig.value.width
  }
  // 否则使用 form 上下文的 labelWidth
  return formContext?.labelWidth
})

// 计算属性
const sizeComputed = computed(() => {
  return props.size || formContext?.size || 'default'
})

const isRequired = computed(() => {
  return props.required || (typeof props.rules === 'string' && props.rules.includes('required'))
})

const showRequiredAsterisk = computed(() => {
  return isRequired.value && !formContext?.hideRequiredAsterisk
})

const currentValidateState = computed(() => {
  if (veeField.meta.pending) return 'validating'
  if (veeField.meta.touched && !veeField.meta.valid) return 'error'
  if (veeField.meta.touched && veeField.meta.valid && props.rules) return 'success'
  return ''
})

const shouldShowMessage = computed(() => {
  // 显示消息的条件：
  // 1. 有错误消息且字段被触摸过
  const hasErrorMessage = !!veeField.errorMessage.value
  const isTouched = veeField.meta.touched
  const shouldShow = props.showMessage ?? formContext?.showMessage ?? true
  
  console.log('shouldShowMessage debug:', {
    fieldName: props.name,
    hasErrorMessage,
    isTouched,
    errorMessage: veeField.errorMessage.value,
    metaValid: veeField.meta.valid,
    metaTouched: veeField.meta.touched,
    shouldShow
  })
  
  return hasErrorMessage && isTouched && shouldShow
})

const labelStyle = computed(() => {
  const styles: Record<string, any> = {}
  
  // 设置宽度
  const width = labelWidthComputed.value
  if (width && labelPositionComputed.value !== 'top') {
    styles.width = width
  }
  
  return styles
})

const contentStyle = computed(() => {
  const styles: Record<string, any> = {}
  
  // 内容区域的布局完全由 CSS 控制，不在这里设置 marginLeft
  // 这样可以避免输入框宽度被压缩的问题
  
  return styles
})

const statusIconClass = computed(() => {
  const iconMap = {
    success: 'sp-icon-circle-check',
    warning: 'sp-icon-warning',
    error: 'sp-icon-circle-close',
    validating: 'sp-icon-loading',
  }
  
  return iconMap[currentValidateState.value as keyof typeof iconMap] || ''
})

// 验证字段
const validateField = async (): Promise<boolean> => {
  const result = await veeField.validate()
  const isValid = result.valid
  const message = result.errors?.[0] || ''
  
  emit('validate', props.name, isValid, message)
  formContext?.emit?.('validate', props.name, isValid, message)
  
  return isValid
}

// 重置字段
const resetField = () => {
  veeField.resetField()
}

// 清除验证结果
const clearValidate = () => {
  veeField.setErrors([])
}

// 滚动到视图
const scrollIntoView = () => {
  formItemRef.value?.scrollIntoView({
    behavior: 'smooth',
    block: 'center',
  })
}

// 组件实例
const formItemContext = reactive({
  name: props.name,
  size: sizeComputed,
  validateState: currentValidateState,
  validate: validateField,
  resetField,
  clearValidate,
  scrollIntoView,
})

// 生命周期
onMounted(() => {
  if (props.name) {
    formContext?.addFormItem?.(formItemContext)
  }
})

onUnmounted(() => {
  if (props.name) {
    formContext?.removeFormItem?.(props.name)
  }
})

// 暴露方法
defineExpose({
  validate: validateField,
  resetField,
  clearValidate,
  scrollIntoView,
  veeField, // 暴露 VeeValidate 字段实例
})

// 向子组件提供字段信息
provide('spFormItemField', {
  value: veeField.value,
  setValue: veeField.setValue,
  handleBlur: handleBlurWithValidation, // 使用包装的方法
  handleChange: veeField.handleChange,
  name: props.name,
  errors: veeField.errors,
  meta: veeField.meta,
  validateState: currentValidateState // 提供计算后的验证状态
})
</script>

<script lang="ts">
export default {
  name: 'SpFormItem',
}
</script> 