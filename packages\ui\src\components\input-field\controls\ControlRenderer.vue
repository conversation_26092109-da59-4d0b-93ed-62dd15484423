<!--
  ControlRenderer.vue - 控件渲染器
  统一渲染输入框控件，支持动态控件配置
-->

<template>
  <template
    v-for="control in renderedControls"
    :key="control.key"
  >
    <component
      :is="control.component"
      v-bind="control.props"
      v-on="control.events"
    />
  </template>

  <!-- 插槽内容（用于自定义控件） -->
  <slot />
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import type {
    ControlContext,
    ControlPosition,
    RenderedControl,
    ControlEventData,
  } from './types'
  import { getControlsByPosition } from './registry'

  interface ControlRendererProps {
    /** 控件位置 */
    position: ControlPosition
    /** 控件上下文 */
    context: ControlContext
  }

  interface ControlRendererEmits {
    /** 控件事件 */
    (e: 'control-event', data: ControlEventData): void
  }

  const props = defineProps<ControlRendererProps>()
  const emit = defineEmits<ControlRendererEmits>()

  /** 渲染的控件列表 */
  const renderedControls = computed<RenderedControl[]>(() => {
    const configs = getControlsByPosition(props.position)

    return configs
      .filter(config => config.condition(props.context))
      .map(config => {
        const controlProps = config.propsFactory(props.context)
        const controlEvents = config.eventsFactory(props.context)

        // 包装事件处理器，添加控件事件通知
        const wrappedEvents: Record<string, Function> = {}
        Object.entries(controlEvents).forEach(([eventName, handler]) => {
          wrappedEvents[eventName] = (...args: any[]) => {
            // 先触发原始事件处理器
            const result = handler(...args)

            // 然后发送控件事件通知
            emit('control-event', {
              type: config.type,
              event: eventName,
              data: args[0],
            })

            return result
          }
        })

        return {
          type: config.type,
          component: config.component,
          props: controlProps,
          events: wrappedEvents,
          order: config.order,
          key: `${config.type}-${config.position}`,
        }
      })
      .sort((a, b) => a.order - b.order)
  })
</script>

<script lang="ts">
  export default {
    name: 'ControlRenderer',
  }
</script>
