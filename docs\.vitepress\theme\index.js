// 引入 VitePress 默认主题
import DefaultTheme from 'vitepress/theme'

import SpeedUI from '@speed-ui/ui'
// import '@speed-ui/ui/styles'
// 引入打包后的主题样式
import '../../../packages/theme-default/lib/index.css'
import DemoShowcase from '../components/DemoShowcase.vue'
import CustomLayout from './components/CustomLayout.vue'
import CustomNavbar from './components/CustomNavbar.vue'

import './index.scss'

export default {
  extends: DefaultTheme,
  // 使用自定义布局替代默认布局
  Layout: CustomLayout,
  enhanceApp({ app }) {
    // 安装 Speed UI 组件库
    app.use(SpeedUI)
    
    // 注册全局组件
    app.component('DemoShowcase', DemoShowcase)
    app.component('CustomNavbar', CustomNavbar)
    app.component('CustomLayout', CustomLayout)
  }
}