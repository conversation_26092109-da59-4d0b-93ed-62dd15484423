<!--
  FieldContainer.vue - 纯样式表单字段容器
  仅提供布局、样式和基础插槽功能
  不包含表单验证、事件处理等复杂逻辑
-->

<template>
  <div
    :class="[rootClasses, sizeClass]"
    :style="dynamicSizeVars"
  >
    <!-- 整体容器：flex 布局包含外部图标和字段容器 -->
    <div class="sp-field-container__layout">
      <!-- 外部前置区域 -->
      <div
        v-if="$slots.prependOuter"
        class="sp-field-container__prepend-icon-outer"
      >
        <!-- 外部前置插槽 -->
        <slot
          name="prependOuter"
          :prepend-outer-classes="prependIconOuterClasses"
          :icon-size="iconSize"
        />
      </div>

      <!-- 字段容器 -->
      <div
        ref="wrapperRef"
        :class="['sp-field-container__field-wrapper', ...wrapperClasses]"
      >
        <!-- 字段包装器 -->
        <!-- <div
          ref="wrapperRef"
          :class="wrapperClasses"
        > -->
        <!-- Outline 轮廓线系统 - 仅在 default 变体且激活时显示 -->
        <div
          v-if="variant === 'default' && isLabelFloating"
          class="sp-field-container__outline"
        >
          <div class="sp-field-container__outline__start" />

          <div class="sp-field-container__outline__notch">
            <!-- 隐藏的占位元素，用于撑开缺口宽度 -->
            <div
              v-if="props.label"
              class="sp-field-container__outline__notch-placeholder"
              :class="{
                'sp-field-container__outline__notch-placeholder--visible':
                  isLabelFloating,
              }"
            >
              <span
                v-if="props.required"
                class="sp-field-container__label-asterisk"
              >
                *
              </span>
              {{ props.label }}
            </div>
          </div>

          <div class="sp-field-container__outline__end" />
        </div>
        <!-- 前置区域插槽 -->
        <slot
          name="prepend"
          :prepend-classes="prependClasses"
          :icon-size="iconSize"
        />

        <!-- 输入容器（包含标签和输入框） -->
        <div class="sp-field-container__input-container">
          <!-- 浮动标签 -->
          <FieldLabel
            :label="label"
            :field-id="fieldId"
            :required="required"
            :label-classes="labelClasses"
          />

          <!-- 前缀区域 -->
          <div
            v-if="prefix && isLabelFloating"
            :class="prefixClasses"
          >
            {{ prefix }}
          </div>

          <!-- 输入元素插槽 -->
          <slot
            :field-id="fieldId"
            :input-classes="inputClasses"
            :input-style="inputStyle"
            :placeholder="computedPlaceholder"
            :disabled="disabled"
            :readonly="readonly"
            :icon-size="iconSize"
          />

          <!-- 后缀区域 -->
          <div
            v-if="suffix && isLabelFloating"
            :class="suffixClasses"
          >
            {{ suffix }}
          </div>
        </div>

        <!-- 后置区域插槽 -->
        <slot
          name="append"
          :append-classes="appendClasses"
          :icon-size="iconSize"
        />

        <!-- 功能区域插槽（保持向后兼容） -->
        <slot
          name="functions"
          :functions-classes="functionsClasses"
          :icon-size="iconSize"
        />
        <!-- </div> -->

        <!-- 加载动画条 -->
        <div
          v-if="loading"
          :class="loadingBarClasses"
        >
          <div :class="loadingProgressClasses"></div>
        </div>

        <!-- 验证消息 -->
        <transition name="sp-field-container-message">
          <div
            v-if="validateMessage"
            :class="messageClasses"
          >
            {{ validateMessage }}
          </div>
        </transition>

        <!-- 帮助文本 -->
        <div
          v-if="helperText && !validateMessage"
          :class="helperTextClasses"
        >
          {{ helperText }}
        </div>
      </div>

      <!-- 外部后置区域 -->
      <div
        v-if="$slots.appendOuter"
        class="sp-field-container__append-icon-outer"
      >
        <!-- 外部后置插槽 -->
        <slot
          name="appendOuter"
          :append-outer-classes="appendIconOuterClasses"
          :icon-size="iconSize"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, useId, ref, useSlots } from 'vue'
  import { useFieldContainerStyles } from './useFieldContainerStyles'
  import { useConfigurableSize } from '../../composables/useConfigurableSize'
  import FieldLabel from './FieldLabel.vue'

  // ===== 简化的 Props 接口 =====
  interface SimpleFieldContainerProps {
    /** 标签文本 */
    label?: string
    /** 占位符文本 */
    placeholder?: string
    /** 是否禁用 */
    disabled?: boolean
    /** 是否只读 */
    readonly?: boolean
    /** 是否必填 */
    required?: boolean
    /** 是否错误状态 */
    error?: boolean
    /** 是否警告状态 */
    warning?: boolean
    /** 是否成功状态 */
    success?: boolean
    /** 是否加载中 */
    loading?: boolean
    /** 外观变体 */
    variant?: 'default' | 'underlined' | 'filled' | 'square' | 'unborder'
    /** 视觉效果 */
    effect?: 'none' | 'glow'
    /** 组件尺寸 */
    size?: 'small' | 'medium' | 'large'
    /** 帮助文本 */
    helperText?: string
    /** 标签是否始终浮动 */
    persistentLabel?: boolean
    /** 是否聚焦状态（外部控制） */
    focused?: boolean
    /** 是否有值（外部控制） */
    hasValue?: boolean
    /** 验证状态 */
    validateState?: 'success' | 'warning' | 'error' | ''
    /** 验证消息 */
    validateMessage?: string
    /** 前缀文本 */
    prefix?: string
    /** 后缀文本 */
    suffix?: string
  }

  // ===== Props =====
  const props = withDefaults(defineProps<SimpleFieldContainerProps>(), {
    variant: 'default',
    effect: 'none',
    size: 'medium',
    disabled: false,
    readonly: false,
    error: false,
    warning: false,
    success: false,
    loading: false,
    required: false,
    persistentLabel: false,
    focused: false,
    hasValue: false,
    validateState: '',
    validateMessage: '',
  })

  // ===== 基础状态 =====
  const fieldId = useId()
  const wrapperRef = ref<HTMLDivElement>()

  // ===== 计算属性 =====
  const isFocused = computed(() => props.focused)
  const hasValueComputed = computed(() => props.hasValue)
  const computedDisabled = computed(() => props.disabled)
  const isLabelFloating = computed(
    () => props.persistentLabel || isFocused.value || hasValueComputed.value
  )

  const iconSize = computed(() => {
    const sizeMap = {
      small: 16,
      medium: 18,
      large: 20,
    }
    return sizeMap[props.size] || 18
  })

  // 计算 placeholder - 只有在标签浮动时才显示
  const computedPlaceholder = computed(() =>
    isLabelFloating.value ? props.placeholder : ''
  )

  // ===== 外部图标样式工厂函数 =====
  const createIconOuterClasses = (position: 'prepend' | 'append') => {
    return computed(() => {
      const classes = [`sp-field-container__${position}-icon-outer-icon`]

      if (computedDisabled.value)
        classes.push(
          `sp-field-container__${position}-icon-outer-icon--disabled`
        )
      if (isFocused.value)
        classes.push(`sp-field-container__${position}-icon-outer-icon--focused`)
      if (validateStateComputed.value === 'error')
        classes.push(`sp-field-container__${position}-icon-outer-icon--error`)
      if (validateStateComputed.value === 'warning')
        classes.push(`sp-field-container__${position}-icon-outer-icon--warning`)
      if (validateStateComputed.value === 'success')
        classes.push(`sp-field-container__${position}-icon-outer-icon--success`)

      return classes.join(' ')
    })
  }

  // ===== 外部图标样式 =====
  const prependIconOuterClasses = createIconOuterClasses('prepend')
  const appendIconOuterClasses = createIconOuterClasses('append')

  // ===== 验证状态管理 =====
  const validateStateComputed = computed(() => {
    // 优先使用传入的 validateState
    if (props.validateState) return props.validateState

    // 检查 props 状态
    if (props.error) return 'error'
    if (props.warning) return 'warning'
    if (props.success) return 'success'

    return ''
  })

  // 是否显示 outline
  const hasOutline = computed(
    () => props.variant === 'default' && isLabelFloating.value
  )

  // 获取插槽
  const slots = useSlots()

  // 是否有前置插槽
  const hasPrepend = computed(() => !!slots.prepend)

  // ===== 配置系统 =====
  const sizeRef = computed(() => props.size || 'medium')
  const { dynamicSizeVars, sizeClass } = useConfigurableSize(sizeRef)

  // ===== 样式系统 =====
  const {
    rootClasses,
    wrapperClasses,
    labelClasses,
    inputClasses,
    inputStyle,
    prependClasses,
    prefixClasses,
    suffixClasses,
    appendClasses,
    functionsClasses,
    loadingBarClasses,
    loadingProgressClasses,
    helperTextClasses,
    messageClasses,
  } = useFieldContainerStyles(props, {
    computedDisabled,
    isFocused,
    hasValue: hasValueComputed,
    isLabelFloating,
    validateState: validateStateComputed,
    hasOutline,
    hasPrepend,
  })

  // ===== 暴露方法和状态 =====
  defineExpose({
    get wrapper() {
      return wrapperRef.value || null
    },
    // 暴露状态供子组件使用
    isFocused,
    hasValue: hasValueComputed,
    isLabelFloating,
    validateState: validateStateComputed,
  })
</script>

<script lang="ts">
  export default {
    name: 'FieldContainer',
  }
</script>
