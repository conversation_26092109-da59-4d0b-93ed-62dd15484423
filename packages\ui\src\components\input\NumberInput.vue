<!-- 
  数字输入框组件示例 - 展示如何使用 eventFactory 处理数字输入
-->
<template>
  <div class="sp-number-input">
    <InputCore
      v-bind="inputCoreProps"
      @click="handleWrapperClick"
      @focus="handleFocus"
      @blur="handleBlur"
      @input="handleInput"
      @change="handleChange"
      @keydown="handleKeydown"
      @clear="handleClear"
      @prefix-click="handlePrefixClick"
      @suffix-click="handleSuffixClick"
    >
      <!-- 数字输入框的增减按钮 -->
      <template #suffix>
        <div class="sp-number-input__controls">
          <button
            type="button"
            class="sp-number-input__button sp-number-input__button--up"
            :disabled="props.disabled || props.readonly || isAtMax"
            @click="handleIncrement"
          >
            ▲
          </button>
          <button
            type="button"
            class="sp-number-input__button sp-number-input__button--down"
            :disabled="props.disabled || props.readonly || isAtMin"
            @click="handleDecrement"
          >
            ▼
          </button>
        </div>
      </template>
    </InputCore>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits, ref } from 'vue'
import InputCore from './inputcore'
import { createInputCoreProps } from './inputCoreProps'
import { createNumberEventHandlers, createEventHandler } from '../../utils/eventFactory'

// 扩展 InputCore 的 props
const props = defineProps({
  ...createInputCoreProps({
    type: 'number',
    clearable: true,
  }),
  
  // 数字输入框特有的 props
  modelValue: {
    type: Number,
    default: 0,
  },
  min: {
    type: Number,
    default: -Infinity,
  },
  max: {
    type: Number,
    default: Infinity,
  },
  step: {
    type: Number,
    default: 1,
  },
  precision: {
    type: Number,
    default: undefined,
  },
})

// 定义事件
const emit = defineEmits<{
  'update:modelValue': [value: number]
  input: [event: Event]
  change: [event: Event]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
  clear: []
  increment: [value: number]
  decrement: [value: number]
  'prefix-click': [event: MouseEvent]
  'suffix-click': [event: MouseEvent]
}>()

// 输入框引用
const inputRef = ref<HTMLInputElement>()

// 计算属性
const isAtMin = computed(() => props.modelValue <= props.min)
const isAtMax = computed(() => props.modelValue >= props.max)

const inputCoreProps = computed(() => {
  const { modelValue, min, max, step, precision, ...coreProps } = props
  return {
    ...coreProps,
    value: String(modelValue),
    hasValue: Boolean(modelValue),
  }
})

// 使用数字事件处理器工厂（自动限制数字输入）
const {
  handleWrapperClick,
  handleFocus,
  handleBlur,
  handleInput: baseHandleInput,
  handleChange: baseHandleChange,
  handleKeydown,
  handlePrefixClick,
  handleSuffixClick,
  handleClear: baseHandleClear,
} = createNumberEventHandlers(emit, inputRef, props)

// 数字格式化函数
const formatNumber = (value: number): number => {
  if (props.precision !== undefined) {
    return Number(value.toFixed(props.precision))
  }
  return value
}

// 数字范围限制函数
const clampNumber = (value: number): number => {
  return Math.max(props.min, Math.min(props.max, value))
}

// 自定义输入处理
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = parseFloat(target.value)
  
  if (!isNaN(value)) {
    const clampedValue = clampNumber(formatNumber(value))
    emit('update:modelValue', clampedValue)
  } else if (target.value === '') {
    emit('update:modelValue', 0)
  }
  
  baseHandleInput(event)
}

// 自定义变化处理
const handleChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = parseFloat(target.value)
  
  if (!isNaN(value)) {
    const clampedValue = clampNumber(formatNumber(value))
    // 如果值被限制了，更新输入框显示
    if (clampedValue !== value) {
      target.value = String(clampedValue)
      emit('update:modelValue', clampedValue)
    }
  }
  
  baseHandleChange(event)
}

// 自定义清除处理
const handleClear = () => {
  emit('update:modelValue', 0)
  baseHandleClear()
}

// 增加按钮事件处理器
const handleIncrement = createEventHandler(emit, 'increment', {
  stopPropagation: true,
  before: () => !props.disabled && !props.readonly && !isAtMax.value,
  after: () => {
    const newValue = clampNumber(formatNumber(props.modelValue + props.step))
    emit('update:modelValue', newValue)
    emit('increment', newValue)
  },
})

// 减少按钮事件处理器
const handleDecrement = createEventHandler(emit, 'decrement', {
  stopPropagation: true,
  before: () => !props.disabled && !props.readonly && !isAtMin.value,
  after: () => {
    const newValue = clampNumber(formatNumber(props.modelValue - props.step))
    emit('update:modelValue', newValue)
    emit('decrement', newValue)
  },
})

// 暴露方法
defineExpose({
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur(),
  clear: handleClear,
  increment: () => handleIncrement(new MouseEvent('click')),
  decrement: () => handleDecrement(new MouseEvent('click')),
})
</script>

<style scoped>
.sp-number-input {
  position: relative;
}

.sp-number-input__controls {
  display: flex;
  flex-direction: column;
  margin-left: 4px;
}

.sp-number-input__button {
  width: 20px;
  height: 12px;
  border: 1px solid #dcdfe6;
  background: #f5f7fa;
  color: #606266;
  cursor: pointer;
  font-size: 10px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.sp-number-input__button:hover:not(:disabled) {
  background: #e6f7ff;
  border-color: #40a9ff;
  color: #40a9ff;
}

.sp-number-input__button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.sp-number-input__button--up {
  border-bottom: none;
  border-radius: 2px 2px 0 0;
}

.sp-number-input__button--down {
  border-radius: 0 0 2px 2px;
}
</style>
