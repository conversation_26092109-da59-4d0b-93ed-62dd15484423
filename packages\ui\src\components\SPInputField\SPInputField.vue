<template>
  <SPInput
    ref="spInputRef"
    v-model="model"
    :class="[
      'sp-text-field',
      {
        'sp-text-field--prefixed': prefix,
        'sp-text-field--suffixed': suffix,
        'sp-input--plain-underlined': isPlainOrUnderlined,
      },
      $attrs.class,
    ]"
    :style="$attrs.style"
    v-bind="inputProps"
    :focused="isFocused"
    @click:prepend="$emit('click:prepend', $event)"
    @click:append="$emit('click:append', $event)"
  >
    <!-- 传递所有插槽，除了 default -->
    <template
      v-for="(_, name) in $slots"
      :key="name"
      #[name]="slotProps"
    >
      <slot
        v-if="name !== 'default'"
        :name="name"
        v-bind="slotProps"
      />
    </template>

    <!-- 默认插槽：包含 SPField 和 input -->
    <template #default="{ id, isDisabled, isReadonly, isValid, reset }">
      <SPField
        ref="spFieldRef"
        v-bind="fieldProps"
        :id="id"
        :active="isActive || isDirty"
        :dirty="isDirty || dirty"
        :disabled="isDisabled"
        :focused="isFocused"
        :error="isValid === false"
        @mousedown="onControlMousedown"
        @click="onControlClick"
        @click:clear="(e) => onClear(e, reset)"
        @click:prepend-inner="$emit('click:prepend-inner', $event)"
        @click:append-inner="$emit('click:append-inner', $event)"
      >
        <!-- 传递 SPField 的插槽 -->
        <template
          v-for="(_, name) in fieldSlots"
          :key="name"
          #[name]="slotProps"
        >
          <slot
            :name="name"
            v-bind="slotProps"
          />
        </template>

        <!-- 默认插槽：包含前缀、输入框、后缀 -->
        <template #default="{ props: { class: fieldClass, ...slotProps } }">
          <!-- 前缀 -->
          <span
            v-if="prefix"
            class="sp-text-field__prefix"
          >
            <span class="sp-text-field__prefix__text">
              {{ prefix }}
            </span>
          </span>

          <!-- 输入框 -->
          <input
            ref="inputRef"
            :value="model"
            :class="fieldClass"
            :readonly="isReadonly"
            :disabled="isDisabled"
            :name="name"
            :placeholder="placeholder"
            :type="type"
            :autofocus="autofocus"
            :maxlength="maxlength"
            size="1"
            v-bind="slotProps"
            @input="onInput"
            @focus="onFocus"
            @blur="onBlur"
          />

          <!-- 后缀 -->
          <span
            v-if="suffix"
            class="sp-text-field__suffix"
          >
            <span class="sp-text-field__suffix__text">
              {{ suffix }}
            </span>
          </span>
        </template>
      </SPField>
    </template>

    <!-- Details 插槽：包含计数器 -->
    <template
      v-if="hasDetails"
      #details="slotProps"
    >
      <slot
        name="details"
        v-bind="slotProps"
      />

      <!-- 计数器 -->
      <div
        v-if="hasCounter"
        class="sp-text-field__counter"
      >
        <slot
          name="counter"
          :value="counterValue"
          :max="max"
        >
          <span class="sp-text-field__counter__text">
            {{ counterValue }}{{ max ? ` / ${max}` : '' }}
          </span>
        </slot>
      </div>
    </template>
  </SPInput>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, useSlots } from 'vue'
import SPInput from '../SPInput/SPInput.vue'
import SPField from '../field-container/SPField.vue'

// Props 接口
interface Props {
  // 基础属性
  modelValue?: string | number
  type?: string
  placeholder?: string
  name?: string
  autofocus?: boolean
  maxlength?: number | string

  // 前缀后缀
  prefix?: string
  suffix?: string

  // 计数器
  counter?: boolean | number | string
  counterValue?: number | ((value: any) => number)
  persistentCounter?: boolean

  // 状态
  dirty?: boolean
  focused?: boolean

  // 继承 SPInput 的属性
  variant?: 'filled' | 'outlined' | 'underlined' | 'plain'
  size?: 'small' | 'medium' | 'large'
  color?: string
  baseColor?: string
  prependIcon?: string
  appendIcon?: string
  disabled?: boolean
  readonly?: boolean
  error?: boolean
  errorMessages?: string | string[]
  hint?: string
  persistentHint?: boolean
  messages?: string | string[]
  hideDetails?: boolean | 'auto'

  // 继承 SPField 的属性
  label?: string
  prependInnerIcon?: string
  appendInnerIcon?: string
  clearable?: boolean
  persistentClear?: boolean
  clearIcon?: string
  flat?: boolean
  singleLine?: boolean
  centerAffix?: boolean
  reverse?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  variant: 'filled',
  size: 'medium',
  counter: false,
  persistentCounter: false,
  dirty: false,
  focused: false,
  disabled: false,
  readonly: false,
  error: false,
  hideDetails: false,
  clearable: false,
  persistentClear: false,
  clearIcon: '×',
  flat: false,
  singleLine: false,
  centerAffix: true,
  reverse: false,
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string | number | null]
  'update:focused': [focused: boolean]
  'click:control': [event: MouseEvent]
  'mousedown:control': [event: MouseEvent]
  'click:clear': [event: MouseEvent]
  'click:prepend': [event: MouseEvent]
  'click:append': [event: MouseEvent]
  'click:prepend-inner': [event: MouseEvent]
  'click:append-inner': [event: MouseEvent]
  'focus': [event: FocusEvent]
  'blur': [event: FocusEvent]
  'input': [event: Event]
}>()

// 响应式引用
const spInputRef = ref<InstanceType<typeof SPInput>>()
const spFieldRef = ref<InstanceType<typeof SPField>>()
const inputRef = ref<HTMLInputElement>()
const isFocused = ref(props.focused)

// 获取插槽
const slots = useSlots()

// 双向绑定
const model = computed({
  get: () => props.modelValue ?? '',
  set: (value) => emit('update:modelValue', value),
})

// 计算属性
const isDirty = computed(() => {
  return !!(model.value && model.value.toString().length > 0)
})

const isPlainOrUnderlined = computed(() =>
  ['plain', 'underlined'].includes(props.variant)
)

const activeTypes = ['color', 'file', 'time', 'date', 'datetime-local', 'week', 'month']
const isActive = computed(() =>
  activeTypes.includes(props.type) ||
  isFocused.value ||
  isDirty.value
)

// 计数器相关
const counterValue = computed(() => {
  if (typeof props.counterValue === 'function') {
    return props.counterValue(model.value)
  }
  if (typeof props.counterValue === 'number') {
    return props.counterValue
  }
  return (model.value ?? '').toString().length
})

const max = computed(() => {
  if (props.maxlength) return Number(props.maxlength)
  if (!props.counter || (typeof props.counter !== 'number' && typeof props.counter !== 'string')) {
    return undefined
  }
  return Number(props.counter)
})

const hasCounter = computed(() =>
  props.counter !== false && props.counter != null
)

const hasDetails = computed(() =>
  hasCounter.value || !!slots.details
)

// 属性分离
const inputProps = computed(() => {
  const {
    modelValue, type, placeholder, name, autofocus, maxlength,
    prefix, suffix, counter, counterValue, persistentCounter,
    dirty, focused, label, prependInnerIcon, appendInnerIcon,
    clearable, persistentClear, clearIcon, flat, singleLine,
    centerAffix, reverse, ...rest
  } = props
  return {
    ...rest,
    centerAffix: !isPlainOrUnderlined.value ? centerAffix : undefined,
  }
})

const fieldProps = computed(() => ({
  label: props.label,
  variant: props.variant,
  prependInnerIcon: props.prependInnerIcon,
  appendInnerIcon: props.appendInnerIcon,
  clearable: props.clearable,
  persistentClear: props.persistentClear,
  clearIcon: props.clearIcon,
  flat: props.flat,
  singleLine: props.singleLine,
  centerAffix: props.centerAffix,
  reverse: props.reverse,
  color: props.color,
}))

// 插槽过滤
const fieldSlots = computed(() => {
  const slotNames = ['label', 'prepend-inner', 'append-inner', 'clear']
  return Object.fromEntries(
    Object.entries(slots).filter(([name]) => slotNames.includes(name))
  )
})

// 事件处理函数
const onFocus = (event: FocusEvent) => {
  isFocused.value = true
  emit('update:focused', true)
  emit('focus', event)

  nextTick(() => {
    if (inputRef.value !== document.activeElement) {
      inputRef.value?.focus()
    }
  })
}

const onBlur = (event: FocusEvent) => {
  isFocused.value = false
  emit('update:focused', false)
  emit('blur', event)
}

const onInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  model.value = target.value
  emit('input', event)
}

const onControlClick = (event: MouseEvent) => {
  emit('click:control', event)
}

const onControlMousedown = (event: MouseEvent) => {
  emit('mousedown:control', event)

  if (event.target === inputRef.value) return

  onFocus(new FocusEvent('focus'))
  event.preventDefault()
}

const onClear = (event: MouseEvent, reset: () => void) => {
  event.stopPropagation()

  onFocus(new FocusEvent('focus'))

  nextTick(() => {
    model.value = ''
    reset()
    emit('click:clear', event)
  })
}

// 暴露方法
defineExpose({
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur(),
  reset: () => { model.value = '' },
  inputRef,
  spInputRef,
  spFieldRef,
})
</script>

<script lang="ts">
export default {
  name: 'SPInputField',
}
</script>

<style lang="scss">
.sp-text-field {
  // 前缀样式
  &__prefix {
    display: flex;
    align-items: center;
    padding-right: 4px;

    &__text {
      color: var(--sp-on-surface-variant);
      font-size: 14px;
      white-space: nowrap;
    }
  }

  // 后缀样式
  &__suffix {
    display: flex;
    align-items: center;
    padding-left: 4px;

    &__text {
      color: var(--sp-on-surface-variant);
      font-size: 14px;
      white-space: nowrap;
    }
  }

  // 计数器样式
  &__counter {
    display: flex;
    justify-content: flex-end;
    margin-left: auto;

    &__text {
      font-size: 12px;
      color: var(--sp-on-surface-variant);
      line-height: 1.4;
    }
  }

  // 变体特定样式
  &--prefixed {
    .v-field__input {
      padding-left: 0;
    }
  }

  &--suffixed {
    .v-field__input {
      padding-right: 0;
    }
  }

  // 输入框样式
  input {
    flex: 1;
    min-width: 0;
    background: transparent;
    border: none;
    outline: none;
    font-size: inherit;
    font-family: inherit;
    color: inherit;

    &::placeholder {
      color: var(--sp-on-surface-variant);
      opacity: 0.6;
    }

    &:disabled {
      cursor: not-allowed;
    }

    &:read-only {
      cursor: default;
    }
  }
}

// 与 plain/underlined 变体的兼容性
.sp-input--plain-underlined {
  .sp-text-field__prefix,
  .sp-text-field__suffix {
    align-self: flex-end;
    padding-bottom: 4px;
  }
}
</style>