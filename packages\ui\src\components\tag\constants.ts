// Tag 组件常量定义

// Tag 尺寸
export const TAG_SIZES = ['small', 'medium', 'large'] as const

// Tag 类型
export const TAG_TYPES = ['default', 'primary', 'success', 'warning', 'error', 'info'] as const

// Tag 变体
export const TAG_VARIANTS = ['filled', 'outlined', 'light'] as const

// Tag 组件常量
export const TAG_CONSTANTS = {
  SIZES: TAG_SIZES,
  TYPES: TAG_TYPES,
  VARIANTS: TAG_VARIANTS,
} as const

// 默认值
export const TAG_DEFAULTS = {
  size: 'medium',
  type: 'default',
  variant: 'filled',
  closable: false,
  disabled: false,
  checkable: false,
  checked: false,
  round: false,
  bordered: true,
  truncate: false,
} as const
