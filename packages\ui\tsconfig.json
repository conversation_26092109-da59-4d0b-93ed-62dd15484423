{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "types2", "rootDir": "../../", "composite": true, "baseUrl": ".", "paths": {"@speed-ui/utils": ["../utils/src"], "@speed-ui/utils/*": ["../utils/src/*"], "@speed-ui/config": ["../common/config/src"], "@speed-ui/config/*": ["../common/config/src/*"]}, "types": ["vitest/globals", "node", "jsdom"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*.d.ts", "src/**/*.test.ts", "src/**/*.spec.ts", "../common/config/src/**/*.ts"], "exclude": ["node_modules", "lib", "es", "types2"]}