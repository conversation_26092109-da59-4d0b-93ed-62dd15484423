<template>
  <div class="tag-demo">
    <h1>🏷️ Tag 标签组件演示</h1>
    <p>可复用的标签组件，支持多种类型、尺寸和交互功能</p>

    <!-- 基础标签 -->
    <section class="demo-section">
      <h2>基础标签 (Basic Tags)</h2>
      <div class="demo-container">
        <div class="tag-group">
          <sp-tag label="默认标签" />
          <sp-tag
            v-for="tag in closableTags"
            :key="tag.id"
            :label="tag.label"
            :value="tag.id"
            closable
            @close="handleClose"
          />
          <sp-tag
            label="禁用状态"
            disabled
          />
          <sp-tag
            label="圆角标签"
            round
          />
        </div>
        <div style="margin-top: 16px">
          <button
            @click="resetClosableTags"
            class="reset-btn"
          >
            🔄 重置可关闭标签
          </button>
        </div>
      </div>
    </section>

    <!-- 类型变体 -->
    <section class="demo-section">
      <h2>类型变体 (Type Variants)</h2>
      <div class="demo-container">
        <div class="tag-group">
          <sp-tag
            label="默认"
            type="default"
          />
          <sp-tag
            label="主要"
            type="primary"
          />
          <sp-tag
            label="成功"
            type="success"
          />
          <sp-tag
            label="警告"
            type="warning"
          />
          <sp-tag
            label="错误"
            type="error"
          />
          <sp-tag
            label="信息"
            type="info"
          />
        </div>
      </div>
    </section>

    <!-- 样式变体 -->
    <section class="demo-section">
      <h2>样式变体 (Style Variants)</h2>
      <div class="demo-container">
        <div class="variant-demo">
          <div class="variant-group">
            <h3>填充样式 (Filled)</h3>
            <div class="tag-group">
              <sp-tag
                label="默认"
                type="default"
                variant="filled"
              />
              <sp-tag
                label="主要"
                type="primary"
                variant="filled"
              />
              <sp-tag
                label="成功"
                type="success"
                variant="filled"
              />
              <sp-tag
                label="警告"
                type="warning"
                variant="filled"
              />
              <sp-tag
                label="错误"
                type="error"
                variant="filled"
              />
            </div>
          </div>
          <div class="variant-group">
            <h3>轮廓样式 (Outlined)</h3>
            <div class="tag-group">
              <sp-tag
                label="默认"
                type="default"
                variant="outlined"
              />
              <sp-tag
                label="主要"
                type="primary"
                variant="outlined"
              />
              <sp-tag
                label="成功"
                type="success"
                variant="outlined"
              />
              <sp-tag
                label="警告"
                type="warning"
                variant="outlined"
              />
              <sp-tag
                label="错误"
                type="error"
                variant="outlined"
              />
            </div>
          </div>
          <div class="variant-group">
            <h3>浅色样式 (Light)</h3>
            <div class="tag-group">
              <sp-tag
                label="默认"
                type="default"
                variant="light"
              />
              <sp-tag
                label="主要"
                type="primary"
                variant="light"
              />
              <sp-tag
                label="成功"
                type="success"
                variant="light"
              />
              <sp-tag
                label="警告"
                type="warning"
                variant="light"
              />
              <sp-tag
                label="错误"
                type="error"
                variant="light"
              />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 尺寸变体 -->
    <section class="demo-section">
      <h2>尺寸变体 (Size Variants)</h2>
      <div class="demo-container">
        <div class="size-demo">
          <div class="size-item">
            <h3>Small</h3>
            <div class="tag-group">
              <sp-tag
                label="小标签"
                size="small"
                type="primary"
              />
              <sp-tag
                v-for="tag in sizeClosableTags.small"
                :key="`small-${tag.id}`"
                :label="tag.label"
                :value="tag.id"
                size="small"
                type="success"
                closable
                @close="value => handleSizeClose('small', value)"
              />
            </div>
          </div>
          <div class="size-item">
            <h3>Medium (默认)</h3>
            <div class="tag-group">
              <sp-tag
                label="中等标签"
                size="medium"
                type="primary"
              />
              <sp-tag
                v-for="tag in sizeClosableTags.medium"
                :key="`medium-${tag.id}`"
                :label="tag.label"
                :value="tag.id"
                size="medium"
                type="success"
                closable
                @close="(value: any) => handleSizeClose('medium', value)"
              />
            </div>
          </div>
          <div class="size-item">
            <h3>Large</h3>
            <div class="tag-group">
              <sp-tag
                label="大标签"
                size="large"
                type="primary"
              />
              <sp-tag
                v-for="tag in sizeClosableTags.large"
                :key="`large-${tag.id}`"
                :label="tag.label"
                :value="tag.id"
                size="large"
                type="success"
                closable
                @close="(value: any) => handleSizeClose('large', value)"
              />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 可选中标签 -->
    <section class="demo-section">
      <h2>可选中标签 (Checkable Tags)</h2>
      <div class="demo-container">
        <div class="tag-group">
          <sp-tag
            v-for="tag in checkableTags"
            :key="tag.id"
            :label="tag.label"
            :checked="tag.checked"
            checkable
            type="primary"
            variant="outlined"
            @check="handleCheck(tag.id, $event)"
          />
        </div>
        <div class="demo-info">
          <h4>选中状态:</h4>
          <code>
            {{
              JSON.stringify(
                checkableTags.filter(t => t.checked).map(t => t.label)
              )
            }}
          </code>
        </div>
      </div>
    </section>

    <!-- 自定义颜色 -->
    <section class="demo-section">
      <h2>自定义颜色 (Custom Colors)</h2>
      <div class="demo-container">
        <div class="tag-group">
          <sp-tag
            label="粉色"
            color="#ff6b6b"
            variant="filled"
          />
          <sp-tag
            label="紫色"
            color="#9c88ff"
            variant="filled"
          />
          <sp-tag
            label="青色"
            color="#4ecdc4"
            variant="filled"
          />
          <sp-tag
            label="橙色"
            color="#ffa726"
            variant="outlined"
          />
          <sp-tag
            label="绿色"
            color="#66bb6a"
            variant="light"
          />
        </div>
      </div>
    </section>

    <!-- 主题演示 -->
    <section class="demo-section">
      <h2>🎨 主题切换演示</h2>
      <div class="theme-demo">
        <div class="theme-controls">
          <button
            @click="changeTheme('#667eea')"
            class="theme-btn"
            style="background: #667eea"
          >
            默认紫色
          </button>
          <button
            @click="changeTheme('#52c41a')"
            class="theme-btn"
            style="background: #52c41a"
          >
            成功绿色
          </button>
          <button
            @click="changeTheme('#ff4d4f')"
            class="theme-btn"
            style="background: #ff4d4f"
          >
            危险红色
          </button>
          <button
            @click="changeTheme('#1890ff')"
            class="theme-btn"
            style="background: #1890ff"
          >
            信息蓝色
          </button>
          <button
            @click="changeTheme('#faad14')"
            class="theme-btn"
            style="background: #faad14"
          >
            警告橙色
          </button>
        </div>
        <div
          class="tag-group"
          style="margin-top: 16px"
        >
          <sp-tag
            label="主题标签"
            type="primary"
            variant="filled"
          />
          <sp-tag
            label="主题轮廓"
            type="primary"
            variant="outlined"
          />
          <sp-tag
            label="主题浅色"
            type="primary"
            variant="light"
          />
          <sp-tag
            v-for="tag in themeClosableTags"
            :key="`theme-${tag.id}`"
            :label="tag.label"
            :value="tag.id"
            type="primary"
            closable
            @close="handleThemeClose"
          />
        </div>
      </div>
    </section>

    <!-- 复用说明 -->
    <section class="demo-section">
      <h2>🚀 复用场景</h2>
      <div class="reuse-info">
        <div class="reuse-item">
          <strong>Select 多选:</strong>
          Tag 作为选中项的标签显示
        </div>
        <div class="reuse-item">
          <strong>标签输入:</strong>
          用户输入的标签列表
        </div>
        <div class="reuse-item">
          <strong>分类标签:</strong>
          内容分类、筛选标签
        </div>
        <div class="reuse-item">
          <strong>状态标签:</strong>
          显示状态信息
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  // 可关闭标签数据
  const closableTags = ref([
    { id: 1, label: '可关闭标签1' },
    { id: 2, label: '可关闭标签2' },
    { id: 3, label: '可关闭标签3' },
  ])

  // 不同尺寸的可关闭标签
  const sizeClosableTags = ref({
    small: [
      { id: 1, label: '小标签1' },
      { id: 2, label: '小标签2' },
    ],
    medium: [
      { id: 1, label: '中标签1' },
      { id: 2, label: '中标签2' },
    ],
    large: [
      { id: 1, label: '大标签1' },
      { id: 2, label: '大标签2' },
    ],
  })

  // 可选中标签数据
  const checkableTags = ref([
    { id: 1, label: 'JavaScript', checked: true },
    { id: 2, label: 'TypeScript', checked: false },
    { id: 3, label: 'Vue.js', checked: true },
    { id: 4, label: 'React', checked: false },
    { id: 5, label: 'Node.js', checked: false },
  ])

  // 主题演示可关闭标签
  const themeClosableTags = ref([
    { id: 1, label: '主题标签1' },
    { id: 2, label: '主题标签2' },
  ])

  // 事件处理
  const handleClose = (value: any) => {
    console.log('标签关闭:', value)
    // 从可关闭标签列表中移除
    const index = closableTags.value.findIndex(tag => tag.id === value)
    if (index > -1) {
      closableTags.value.splice(index, 1)
    }
  }

  const handleSizeClose = (size: 'small' | 'medium' | 'large', value: any) => {
    console.log(`${size}标签关闭:`, value)
    // 从对应尺寸的标签列表中移除
    const index = sizeClosableTags.value[size].findIndex(
      tag => tag.id === value
    )
    if (index > -1) {
      sizeClosableTags.value[size].splice(index, 1)
    }
  }

  const handleThemeClose = (value: any) => {
    console.log('主题标签关闭:', value)
    // 从主题标签列表中移除
    const index = themeClosableTags.value.findIndex(tag => tag.id === value)
    if (index > -1) {
      themeClosableTags.value.splice(index, 1)
    }
  }

  const handleCheck = (id: number, checked: boolean) => {
    const tag = checkableTags.value.find(t => t.id === id)
    if (tag) {
      tag.checked = checked
    }
    console.log('标签选中状态变化:', { id, checked })
  }

  // 重置标签函数
  const resetClosableTags = () => {
    // 重置基础可关闭标签
    closableTags.value = [
      { id: 1, label: '可关闭标签1' },
      { id: 2, label: '可关闭标签2' },
      { id: 3, label: '可关闭标签3' },
    ]

    // 重置尺寸标签
    sizeClosableTags.value = {
      small: [
        { id: 1, label: '小标签1' },
        { id: 2, label: '小标签2' },
      ],
      medium: [
        { id: 1, label: '中标签1' },
        { id: 2, label: '中标签2' },
      ],
      large: [
        { id: 1, label: '大标签1' },
        { id: 2, label: '大标签2' },
      ],
    }

    // 重置主题标签
    themeClosableTags.value = [
      { id: 1, label: '主题标签1' },
      { id: 2, label: '主题标签2' },
    ]

    console.log('所有可关闭标签已重置')
  }

  // 主题切换 - 复制自 ListDemo.vue
  const changeTheme = (color: string) => {
    const root = document.documentElement

    // 设置主色
    root.style.setProperty('--sp-color-primary', color)

    // 计算 hover 颜色 (稍微亮一些)
    const hoverColor = lightenColor(color, 10)
    root.style.setProperty('--sp-color-primary-hover', hoverColor)

    // 计算 active 颜色 (稍微暗一些)
    const activeColor = darkenColor(color, 10)
    root.style.setProperty('--sp-color-primary-active', activeColor)

    // 计算浅色背景
    const lightestColor = lightenColor(color, 45)
    root.style.setProperty('--sp-color-primary-lightest', lightestColor)

    const lightColor = lightenColor(color, 30)
    root.style.setProperty('--sp-color-primary-light', lightColor)

    // 计算禁用颜色
    const disabledColor = lightenColor(color, 35)
    root.style.setProperty('--sp-color-primary-disabled', disabledColor)
  }

  // 颜色工具函数 - 复制自 ListDemo.vue
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null
  }

  const rgbToHex = (r: number, g: number, b: number) => {
    return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
  }

  const lightenColor = (hex: string, percent: number) => {
    const rgb = hexToRgb(hex)
    if (!rgb) return hex

    const factor = percent / 100
    const r = Math.min(255, Math.round(rgb.r + (255 - rgb.r) * factor))
    const g = Math.min(255, Math.round(rgb.g + (255 - rgb.g) * factor))
    const b = Math.min(255, Math.round(rgb.b + (255 - rgb.b) * factor))

    return rgbToHex(r, g, b)
  }

  const darkenColor = (hex: string, percent: number) => {
    const rgb = hexToRgb(hex)
    if (!rgb) return hex

    const factor = 1 - percent / 100
    const r = Math.max(0, Math.round(rgb.r * factor))
    const g = Math.max(0, Math.round(rgb.g * factor))
    const b = Math.max(0, Math.round(rgb.b * factor))

    return rgbToHex(r, g, b)
  }
</script>

<style scoped>
  .tag-demo {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .tag-demo h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 10px;
  }

  .tag-demo > p {
    text-align: center;
    color: #7f8c8d;
    margin-bottom: 40px;
  }

  .demo-section {
    margin-bottom: 50px;
    padding: 30px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background: #fafafa;
  }

  .demo-section h2 {
    color: #34495e;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #ecf0f1;
  }

  .demo-container {
    display: flex;
    gap: 30px;
    align-items: flex-start;
    flex-wrap: wrap;
  }

  .tag-group {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
  }

  .variant-demo {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
  }

  .variant-group h3 {
    margin: 0 0 10px 0;
    color: #555;
    font-size: 14px;
  }

  .size-demo {
    display: flex;
    gap: 40px;
    flex-wrap: wrap;
  }

  .size-item h3 {
    margin: 0 0 10px 0;
    color: #555;
    font-size: 14px;
  }

  .demo-info {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
  }

  .demo-info h4 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 14px;
  }

  .demo-info code {
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #495057;
  }

  .theme-demo {
    width: 100%;
  }

  .theme-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }

  .theme-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    color: white;
    cursor: pointer;
    font-size: 12px;
    transition: opacity 0.2s;
  }

  .theme-btn:hover {
    opacity: 0.8;
  }

  .reuse-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
  }

  .reuse-item {
    padding: 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
  }

  .reuse-item strong {
    color: #2c3e50;
    display: block;
    margin-bottom: 5px;
  }

  .reset-btn {
    padding: 8px 16px;
    background: #409eff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s;
  }

  .reset-btn:hover {
    background: #337ecc;
  }
</style>
