# Changelog

## [1.0.0] - 2024-01-XX

### Added

- 🎉 Initial release of @speed-ui/size
- 📦 提供统一的组件尺寸类型定义 (`ComponentSize`, `ExtendedComponentSize`)
- 🔄 支持组件间尺寸映射 (`mapSizeToScrollbar`)
- 🛠️ 丰富的尺寸处理工具函数 (`getSizeValue`, `getSizePixels`, `isValidSize` 等)
- ⚡ Vue Composables 支持 (`useSize`, `useSizeComparison`)
- 📖 完整的 TypeScript 类型定义和推断
- 📚 详细的 API 文档和使用示例

### Features

- 标准尺寸类型：`'small' | 'default' | 'large'`
- 扩展尺寸类型：`'mini' | 'small' | 'default' | 'large' | 'huge'`
- 可配置的尺寸映射系统
- 响应式尺寸处理逻辑
- 工具函数支持尺寸验证、标准化和比较
