<!--
  IconControl.vue - 通用图标控件
  提供可点击的图标功能
-->

<template>
  <sp-icon
    :name="iconName"
    :size="iconSize"
    :clickable="clickable && !disabled"
    :class="iconClass"
    @click="handleClick"
    @mousedown.stop.prevent
  />
</template>

<script setup lang="ts">
  import SpIcon from '../../icon/Icon.vue'
  import type { BaseControlProps } from './types'

  interface IconControlProps extends BaseControlProps {
    /** 图标名称 */
    iconName: string
    /** 是否可点击 */
    clickable?: boolean
    /** 图标样式类 */
    iconClass?: string
  }

  interface IconControlEmits {
    /** 图标点击事件 */
    (e: 'click', event: MouseEvent): void
  }

  const props = withDefaults(defineProps<IconControlProps>(), {
    clickable: true,
    iconClass: ''
  })
  const emit = defineEmits<IconControlEmits>()

  /** 处理图标点击 */
  const handleClick = (event: MouseEvent) => {
    if (props.clickable && !props.disabled) {
      event.stopPropagation()
      event.preventDefault()
      emit('click', event)
    }
  }
</script>

<script lang="ts">
  export default {
    name: 'IconControl',
  }
</script>
