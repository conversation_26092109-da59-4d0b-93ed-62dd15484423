import { resolve, join } from 'path'
import { readFileSync, existsSync } from 'fs'

export function caseResolverPlugin() {
  return {
    name: 'case-resolver-plugin',
    enforce: 'pre',
    resolveId(id, importer) {
      console.log('🔍 Resolving:', id, 'from:', importer)
      
      // 如果是相对路径的 case 文件，尝试解析到源头
      if (id.endsWith('.case.vue') && id.startsWith('./')) {
        const caseFileName = id.replace('./', '')
        console.log('📁 Case file name:', caseFileName)
        
        // 从文件名推断组件名（例如：button-basic.case.vue -> Button）
        const componentName = caseFileName.split('-')[0]
        const componentNameCapitalized = componentName.charAt(0).toUpperCase() + componentName.slice(1)
        console.log('🧩 Component name:', componentNameCapitalized)
        
        // 构建源头文件路径
        const sourcePath = resolve(
          process.cwd(),
          `packages/ui/src/components/${componentNameCapitalized}/cases/${caseFileName}`
        )
        console.log('🎯 Source path:', sourcePath)
        console.log('✅ File exists:', existsSync(sourcePath))
        
        // 如果源头文件存在，返回解析后的路径
        if (existsSync(sourcePath)) {
          console.log('🚀 Resolved to source:', sourcePath)
          return sourcePath
        }
      }
      return null
    },
    load(id) {
      if (id.includes('/cases/') && id.endsWith('.case.vue')) {
        console.log('📦 Loading case file:', id)
        try {
          const content = readFileSync(id, 'utf-8')
          return content
        } catch (error) {
          console.error(`Failed to load case file: ${id}`, error)
          return null
        }
      }
      return null
    }
  }
} 