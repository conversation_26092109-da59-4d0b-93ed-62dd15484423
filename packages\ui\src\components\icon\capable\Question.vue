<template>
  <span
    v-if="visible"
    :class="questionClass"
    @click="handleQuestion"
    @mousedown.prevent
  >
    <slot name="icon">
      <Icon
        name="Help"
        :size="iconSize"
        :clickable="true"
        class="sp-input__question-icon"
      />
    </slot>
  </span>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { bemHelper } from '@speed-ui/config'
  import Icon from '../Icon.vue'

  /**
   * Question - 问题提示组件
   *
   * 用于输入框的问题提示功能
   */

  interface QuestionProps {
    // 问题文本
    question?: string
    // 是否显示
    visible?: boolean
    // 是否禁用
    disabled?: boolean
    // 是否只读
    readonly?: boolean
    // 尺寸
    size?: 'small' | 'medium' | 'large'
  }

  interface QuestionEmits {
    (e: 'question-click', question: string): void
  }

  const props = withDefaults(defineProps<QuestionProps>(), {
    question: '',
    visible: false,
    disabled: false,
    readonly: false,
    size: 'medium',
  })

  const emit = defineEmits<QuestionEmits>()

  // BEM helper
  const bem = bemHelper('input')

  // 计算图标尺寸
  const iconSize = computed(() => {
    switch (props.size) {
      case 'small':
        return 12
      case 'large':
        return 18
      case 'medium':
      default:
        return 14
    }
  })

  // 计算样式类名
  const questionClass = computed(() => [
    bem.e('question'),
    bem.em('question', props.size),
    {
      [bem.em('question', 'visible')]: props.visible,
      [bem.em('question', 'disabled')]: props.disabled,
    },
  ])

  // 处理问题点击事件
  const handleQuestion = () => {
    if (props.disabled || props.readonly) return

    console.log('问题被点击:', props.question)
    emit('question-click', props.question)
  }

  // 调试信息
  console.log('Question组件 - props:', {
    question: props.question,
    visible: props.visible,
    disabled: props.disabled,
    readonly: props.readonly,
    size: props.size,
  })
  console.log('Question组件 - questionClass:', questionClass.value)
</script>

<script lang="ts">
  export default {
    name: 'InputQuestion',
  }
</script>

<style scoped>
  /* 问题按钮样式由主题文件提供 */
</style>
