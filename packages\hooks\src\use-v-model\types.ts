import type { Ref, ComputedRef } from 'vue'

/**
 * 表单字段接口（兼容不同表单库）
 */
export interface FormField {
  value: {
    value: any
  }
  setValue: (value: any) => void
  errors?: {
    value: string[]
  }
  validateState?: {
    value: 'success' | 'warning' | 'error' | ''
  }
  handleBlur?: () => void
  handleChange?: () => void
}

/**
 * useVModel 配置选项
 */
export interface UseVModelOptions<T> {
  // 基础配置
  prop?: string // prop 名称，默认 'value'
  event?: string // 事件名称，默认 'update:value'
  defaultValue?: T // 默认值

  // 表单集成
  formField?: FormField | any // 表单字段注入

  // 自定义处理钩子
  onBeforeUpdate?: (newValue: T, oldValue: T) => T | false // 更新前处理
  onAfterUpdate?: (newValue: T, oldValue: T) => void // 更新后处理

  // 验证
  validator?: (value: T) => boolean // 值验证

  // 类型转换
  parser?: (value: any) => T // 输入值转换
  formatter?: (value: T) => any // 输出值转换

  // 调试选项
  debug?: boolean // 是否开启调试日志
}

/**
 * useVModel 返回值
 */
export interface UseVModelReturn<T> {
  // 响应式状态
  value: Ref<T> // 当前值

  // 方法
  updateValue: (newValue: T) => void // 更新值
  resetValue: () => void // 重置值

  // 计算属性
  hasValue: ComputedRef<boolean> // 是否有值
  isEmpty: ComputedRef<boolean> // 是否为空
}

/**
 * 常用的 v-model 类型
 */
export type StringOrNumber = string | number
export type StringArray = string[]
export type NumberArray = number[]
export type MixedArray = Array<string | number>
export type SelectValue = string | number | Array<string | number>
export type CheckboxValue = boolean | Array<string | number>
export type InputValue = string | number
