import markdownItContainer from 'markdown-it-container'

export function markdownCasePlugin(md) {
  // 注册自定义容器渲染器，支持 ::: switch/basic 语法（自闭合）
  md.use(markdownItContainer, '', {
    validate: function(params) {
      // 匹配组件名/case名的格式，如 switch/basic
      const trimmed = params.trim()
      return trimmed.match(/^[A-Za-z]+\/[a-z-]+$/)
    },
    
    render: function (tokens, idx) {
      const token = tokens[idx]
      
      if (token.nesting === 1) {
        // 开始标签 - 直接渲染完整的组件，不需要结束标签
        const info = token.info.trim()
        const [component, caseName] = info.split('/')
        // 使用基于 docs 根目录的绝对路径
        const caseFile = `/docs/cases/${component.toLowerCase()}/${component.toLowerCase()}-${caseName}.case.vue`
        
        return `<DemoShowcase case-file="${caseFile}"></DemoShowcase>\n`
      } else {
        // 结束标签 - 返回空字符串，因为我们已经在开始标签中完成了渲染
        return ''
      }
    }
  })
}