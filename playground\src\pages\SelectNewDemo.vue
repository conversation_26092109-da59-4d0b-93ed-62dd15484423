<template>
  <div class="page-container">
    <h1>Select New 组件演示</h1>
    <p>基于 sp-input 作为基底的新 Select 组件</p>

    <div class="demo-section">
      <h2>基础用法</h2>
      <div class="demo-item">
        <label>基础选择器：</label>
        <Select
          v-model:value="basicValue"
          :options="options"
          placeholder="请选择一个选项"
          clearable
          size="large"

          @change="handleChange"
        />
        <span class="value-display">当前值：{{ basicValue || '(空)' }}</span>
      </div>
    </div>

    <div class="demo-section">
      <h2>多选模式</h2>
      <div class="demo-item">
        <label>多选选择器：</label>
        <Select
          v-model:value="multipleValue"
          :options="options"
          placeholder="请选择多个选项"
          multiple
          clearable
          @change="handleMultipleChange"
        />
        <span class="value-display">当前值：{{ JSON.stringify(multipleValue) }}</span>
      </div>
    </div>

    <div class="demo-section">
      <h2>不同尺寸</h2>
      <div class="demo-item">
        <label>小尺寸：</label>
        <Select
          v-model:value="smallValue"
          :options="options"
          placeholder="小尺寸"
          size="small"
        />
      </div>
      <div class="demo-item">
        <label>默认尺寸：</label>
        <Select
          v-model:value="defaultValue"
          :options="options"
          placeholder="默认尺寸"
          size="medium"
        />
      </div>
      <div class="demo-item">
        <label>大尺寸：</label>
        <Select
          v-model:value="largeValue"
          :options="options"
          placeholder="大尺寸"
          size="large"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>🎨 变体样式</h2>
      <div class="demo-item">
        <label>默认变体：</label>
        <Select
          v-model:value="variantDefaultValue"
          :options="options"
          placeholder="默认变体"
          variant="default"
          clearable
        />
      </div>
      <div class="demo-item">
        <label>下划线变体：</label>
        <Select
          v-model:value="variantUnderlinedValue"
          :options="options"
          placeholder="下划线变体"
          variant="underlined"
          clearable
        />
      </div>
      <div class="demo-item">
        <label>填充变体：</label>
        <Select
          v-model:value="variantFilledValue"
          :options="options"
          placeholder="填充变体"
          variant="filled"
          clearable
        />
      </div>
      <div class="demo-item">
        <label>胶囊变体：</label>
        <Select
          v-model:value="variantPillValue"
          :options="options"
          placeholder="胶囊变体"
          variant="pill"
          clearable
        />
      </div>
      <div class="demo-item">
        <label>方形变体：</label>
        <Select
          v-model:value="variantSquareValue"
          :options="options"
          placeholder="方形变体"
          variant="square"
          clearable
        />
      </div>
      <div class="demo-item">
        <label>无边框变体：</label>
        <Select
          v-model:value="variantUnborderValue"
          :options="options"
          placeholder="无边框变体"
          variant="unborder"
          clearable
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>禁用状态</h2>
      <div class="demo-item">
        <label>禁用状态：</label>
        <Select
          v-model:value="disabledValue"
          :options="options"
          placeholder="禁用状态"
          disabled
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>错误状态</h2>
      <div class="demo-item">
        <label>错误状态：</label>
        <Select
          v-model:value="errorValue"
          :options="options"
          placeholder="错误状态"
          validate-state="error"
          validate-message="这是一个错误信息"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>🔥 变体 + 尺寸组合展示</h2>
      <div class="variant-grid">
        <!-- 默认变体 -->
        <div class="variant-group">
          <h3>Default 变体</h3>
          <div class="size-row">
            <Select v-model:value="combo1" :options="options" placeholder="Small" variant="default" size="small" />
            <Select v-model:value="combo2" :options="options" placeholder="Medium" variant="default" size="medium" />
            <Select v-model:value="combo3" :options="options" placeholder="Large" variant="default" size="large" />
          </div>
        </div>

        <!-- 下划线变体 -->
        <div class="variant-group">
          <h3>Underlined 变体</h3>
          <div class="size-row">
            <Select v-model:value="combo4" :options="options" placeholder="Small" variant="underlined" size="small" />
            <Select v-model:value="combo5" :options="options" placeholder="Medium" variant="underlined" size="medium" />
            <Select v-model:value="combo6" :options="options" placeholder="Large" variant="underlined" size="large" />
          </div>
        </div>

        <!-- 填充变体 -->
        <div class="variant-group">
          <h3>Filled 变体</h3>
          <div class="size-row">
            <Select v-model:value="combo7" :options="options" placeholder="Small" variant="filled" size="small" />
            <Select v-model:value="combo8" :options="options" placeholder="Medium" variant="filled" size="medium" />
            <Select v-model:value="combo9" :options="options" placeholder="Large" variant="filled" size="large" />
          </div>
        </div>

        <!-- 胶囊变体 -->
        <div class="variant-group">
          <h3>Pill 变体</h3>
          <div class="size-row">
            <Select v-model:value="combo10" :options="options" placeholder="Small" variant="pill" size="small" />
            <Select v-model:value="combo11" :options="options" placeholder="Medium" variant="pill" size="medium" />
            <Select v-model:value="combo12" :options="options" placeholder="Large" variant="pill" size="large" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import Select from '../../../packages/ui/src/components/select/select.vue'

  // 测试数据
  const options = [
    { label: '选项1', value: '1' },
    { label: '选项2', value: '2' },
    { label: '选项3', value: '3' },
    { label: '禁用选项', value: '4', disabled: true },
    { label: '选项5', value: '5' },
    { label: '选项6', value: '6' },
    { label: '选项7', value: '7' },
    { label: '选项8', value: '8' },
    { label: '选项9', value: '9' },
    { label: '选项10', value: '10' },
    { label: '选项11', value: '11' },
    { label: '选项12', value: '12' },
  ]

  // 响应式数据
  const basicValue = ref('')
  const multipleValue = ref([])
  const smallValue = ref('')
  const defaultValue = ref('')
  const largeValue = ref('')
  const disabledValue = ref('')
  const errorValue = ref('')

  // 变体演示数据
  const variantDefaultValue = ref('')
  const variantUnderlinedValue = ref('')
  const variantFilledValue = ref('')
  const variantPillValue = ref('')
  const variantSquareValue = ref('')
  const variantUnborderValue = ref('')

  // 组合演示数据
  const combo1 = ref('')
  const combo2 = ref('')
  const combo3 = ref('')
  const combo4 = ref('')
  const combo5 = ref('')
  const combo6 = ref('')
  const combo7 = ref('')
  const combo8 = ref('')
  const combo9 = ref('')
  const combo10 = ref('')
  const combo11 = ref('')
  const combo12 = ref('')

  // 事件处理
  const handleChange = (value: any) => {
    console.log('单选值变化:', value)
  }

  const handleMultipleChange = (value: any) => {
    console.log('多选值变化:', value)
  }
</script>

<style scoped>
  .page-container {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
  }

  .demo-section {
    margin: 30px 0;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
  }

  .demo-section h2 {
    margin-top: 0;
    color: #303133;
  }

  .demo-item {
    margin: 15px 0;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .demo-item label {
    min-width: 100px;
    font-weight: 500;
  }

  .value-display {
    margin-left: 10px;
    color: #909399;
    font-size: 12px;
  }

  /* 变体组合展示样式 */
  .variant-grid {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .variant-group {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
  }

  .variant-group h3 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
  }

  .size-row {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
  }

  .size-row > * {
    flex: 1;
    min-width: 150px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .size-row {
      flex-direction: column;
      align-items: stretch;
    }

    .size-row > * {
      min-width: auto;
    }

    .demo-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .demo-item label {
      min-width: auto;
    }
  }
</style>
