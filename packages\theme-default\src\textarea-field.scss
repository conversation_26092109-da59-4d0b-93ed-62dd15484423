@use './common/var.scss' as *;

// ===== TextareaField 基础变量 =====
:root {
  --sp-color-white: #ffffff;
}

// ===== TextareaField 主样式 =====
.sp-textarea-field {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: top;

  // ===== 包装器 =====
  &__wrapper {
    position: relative;
    display: flex;
    align-items: stretch; // 改为 stretch 以适应 textarea 高度
    width: 100%;
    transition: border-color 0.3s ease, box-shadow 0.3s ease; // 只对边框和阴影应用过渡
    cursor: text;
    min-height: 80px; // 为 textarea 设置最小高度
    overflow: visible; // 确保拖拽图标可见

    // 确保拖拽图标不被边框影响
    box-sizing: border-box;
  }

  // ===== 浮动标签 =====
  &__label {
    position: absolute;
    left: 12px;
    top: 16px; // 调整为适合 textarea 的位置
    color: $color-text-secondary;
    font-size: 16px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    transform-origin: left top;
    z-index: 1;
    user-select: none;
    cursor: text;
    background: $background-color-base;
    padding: 0 4px;
    border-radius: 4px;
    white-space: nowrap;

    // 必填星号
    &-asterisk {
      color: $color-danger;
      margin-right: 2px;
    }

    // 浮动状态
    &--floating {
      top: 0;
      transform: translateY(-50%) scale(0.85);
      color: $color-text-secondary;
      font-weight: 500;
    }
  }

  // ===== 聚焦状态下的标签颜色 =====
  &--focused {
    .sp-textarea-field__label {
      color: $color-primary;
    }
  }

  // ===== 文本域内核 =====
  &__inner {
    flex: 1;
    width: 100%;
    padding: 16px 12px;
    border: none;
    outline: none;
    background: transparent;
    color: $color-text-primary;
    font-size: 16px;
    font-family: inherit;
    line-height: 1.5;
    resize: vertical; // 默认允许垂直调整大小
    min-height: 48px; // 设置最小高度

    // 移除所有可能影响拖拽性能的样式
    transition: none; // 完全移除过渡动画
    transform: none; // 确保没有变换
    will-change: auto; // 重置 will-change

    // 确保拖拽图标区域可用
    position: relative;
    z-index: 1; // 确保在正确的层级

    // 确保拖拽图标紧贴右下角
    box-sizing: border-box;

    // 重要：确保拖拽图标在正确位置
    // 拖拽图标会出现在 textarea 的右下角，不受 padding 影响

    &::placeholder {
      color: $color-text-secondary;
      opacity: 0.8;
    }

    &:disabled {
      color: $color-text-disabled;
      cursor: not-allowed;
      resize: none;

      &::placeholder {
        color: $color-text-disabled;
      }
    }

    &:read-only {
      resize: none;
    }
  }

  // ===== 内置功能区域 =====
  &__functions {
    position: absolute;
    top: 16px;
    right: 16px;
    bottom: 20px; // 为拖拽图标预留空间
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: flex-start;
    gap: 8px;
    color: $color-text-secondary;
    pointer-events: none; // 不阻止拖拽操作
    z-index: 2; // 确保在拖拽图标之上

    // 功能元素可交互
    > * {
      pointer-events: auto;
    }
  }

  // ===== 有功能区域时的布局调整 =====
  &--has-functions {
    .sp-textarea-field__inner {
      // 为功能区域预留空间
      padding-right: 60px;
    }
  }

  // ===== 清除按钮样式 =====
  &__clear {
    color: $color-text-secondary;
    cursor: pointer;
    transition: color 0.3s ease;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    padding: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:hover {
      color: $color-primary;
      background: rgba(255, 255, 255, 1);
      border-color: $color-primary;
    }
  }

  // ===== 字数统计 =====
  &__count {
    font-size: 11px;
    color: $color-text-secondary;
    white-space: nowrap;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 2px 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 4px;
  }

  // ===== 加载动画 =====
  &__loading-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    overflow: hidden;
    background: transparent;
  }

  &__loading-progress {
    height: 100%;
    background: $color-primary;
    width: 30%;
    animation: sp-textarea-field-loading 1.5s infinite ease-in-out;
  }

  // ===== 消息区域 =====
  &__message,
  &__helper {
    margin-top: 6px;
    font-size: 12px;
    line-height: 1.4;
    transition: all 0.3s ease;
  }

  &__message {
    color: $color-danger;

    &--warning {
      color: $color-warning;
    }

    &--success {
      color: $color-success;
    }
  }

  &__helper {
    color: $color-text-secondary;

    &--error {
      color: $color-danger;
    }

    &--warning {
      color: $color-warning;
    }

    &--success {
      color: $color-success;
    }
  }

  // ===== 尺寸变体 =====
  &--small {
    .sp-textarea-field__wrapper {
      min-height: 60px;
    }

    .sp-textarea-field__inner {
      padding: 12px 10px;
      font-size: 14px;
      min-height: 36px;
    }

    .sp-textarea-field__label {
      left: 10px;
      top: 12px;
      font-size: 14px;

      &--floating {
        transform: translateY(-45%) scale(0.8);
      }
    }

    .sp-textarea-field__functions {
      top: 12px;
      right: 12px;
    }

    // 小尺寸填充变体的特殊处理
    &.sp-textarea-field--filled {
      .sp-textarea-field__inner {
        padding-top: 28px;
        padding-bottom: 8px;
      }

      .sp-textarea-field__label {
        top: 8px;

        &--floating {
          top: 6px;
        }
      }
    }
  }

  &--large {
    .sp-textarea-field__wrapper {
      min-height: 100px;
    }

    .sp-textarea-field__inner {
      padding: 20px 14px;
      font-size: 18px;
      min-height: 60px;
    }

    .sp-textarea-field__label {
      left: 14px;
      top: 20px;
      font-size: 18px;

      &--floating {
        transform: translateY(-55%) scale(0.9);
      }
    }

    .sp-textarea-field__functions {
      top: 20px;
      right: 20px;
    }

    // 大尺寸填充变体的特殊处理
    &.sp-textarea-field--filled {
      .sp-textarea-field__inner {
        padding-top: 44px;
        padding-bottom: 16px;
      }

      .sp-textarea-field__label {
        top: 16px;

        &--floating {
          top: 12px;
        }
      }
    }
  }

  // ===== 调整大小变体 =====
  &--resize-none {
    .sp-textarea-field__inner {
      resize: none;
    }
  }

  &--resize-both {
    .sp-textarea-field__inner {
      resize: both;
    }
  }

  &--resize-horizontal {
    .sp-textarea-field__inner {
      resize: horizontal;
    }
  }

  &--resize-vertical {
    .sp-textarea-field__inner {
      resize: vertical;
    }
  }

  // ===== 自动调整大小 =====
  &--autosize {
    .sp-textarea-field__inner {
      resize: none;
      overflow: hidden;
    }
  }

  // ===== 变体样式 =====

  // 默认变体
  &--default {
    .sp-textarea-field__wrapper {
      border: 1px solid $border-color-base;
      border-radius: 6px;
      background: var(--sp-color-white);
    }

    // 确保 textarea 能够正确定位拖拽图标
    .sp-textarea-field__inner {
      // 让 textarea 完全填充包装器（除了边框）
      margin: 0;
      border-radius: inherit;
    }

    .sp-textarea-field__label {
      background: var(--sp-color-white);
      padding: 0 4px;

      &--floating {
        top: 0;
        transform: translateY(-50%) scale(0.85);
      }
    }

    &.sp-textarea-field--focused {
      .sp-textarea-field__wrapper {
        border-color: $color-primary;
        box-shadow: 0 0 0 1px rgba($color-primary, 0.2);
      }
    }
  }

  // ===== 下划线变体 =====
  &--underlined {
    .sp-textarea-field__wrapper {
      border: none;
      border-bottom: 1px solid $border-color-base;
      border-radius: 0;
      background: transparent;
    }

    .sp-textarea-field__label {
      background: transparent;
      padding: 0;

      &--floating {
        top: 0;
        transform: translateY(-50%) scale(0.85);
      }
    }

    &.sp-textarea-field--focused {
      .sp-textarea-field__wrapper {
        border-bottom-color: $color-primary;
        box-shadow: 0 1px 0 0 $color-primary;
      }
    }
  }

  // ===== 填充变体 =====
  &--filled {
    .sp-textarea-field__wrapper {
      border: none;
      border-bottom: 1px solid $border-color-base;
      border-radius: 6px 6px 0 0;
      background: $background-color-hover;
    }

    .sp-textarea-field__inner {
      // 大幅增加顶部内边距，让输入区域更往下
      padding-top: 36px;
      padding-bottom: 12px;
    }

    .sp-textarea-field__label {
      background: transparent;
      padding: 0;
      // 调整标签位置，让它在填充区域内
      top: 12px;

      &--floating {
        top: 8px;
        transform: translateY(0) scale(0.85);
      }
    }

    &.sp-textarea-field--focused {
      .sp-textarea-field__wrapper {
        border-bottom-color: $color-primary;
        background: $background-color-base;
        box-shadow: 0 1px 0 0 $color-primary;
      }
    }
  }

  // ===== 胶囊变体 =====
  &--pill {
    .sp-textarea-field__wrapper {
      border: 1px solid $border-color-base;
      border-radius: 50px;
      background: var(--sp-color-white);
    }

    .sp-textarea-field__label {
      background: var(--sp-color-white);
      padding: 0 4px;

      &--floating {
        top: 0;
        transform: translateY(-50%) scale(0.85);
      }
    }

    &.sp-textarea-field--focused {
      .sp-textarea-field__wrapper {
        border-color: $color-primary;
        box-shadow: 0 0 0 1px rgba($color-primary, 0.2);
      }
    }
  }

  // ===== 方形变体 =====
  &--square {
    .sp-textarea-field__wrapper {
      border: 1px solid $border-color-base;
      border-radius: 0;
      background: var(--sp-color-white);
    }

    .sp-textarea-field__label {
      background: var(--sp-color-white);
      padding: 0 4px;

      &--floating {
        top: 0;
        transform: translateY(-50%) scale(0.85);
      }
    }

    &.sp-textarea-field--focused {
      .sp-textarea-field__wrapper {
        border-color: $color-primary;
        box-shadow: 0 0 0 1px rgba($color-primary, 0.2);
      }
    }
  }

  // ===== 无边框变体 =====
  &--unborder {
    .sp-textarea-field__wrapper {
      border: none;
      border-radius: 6px;
      background: transparent;
    }

    .sp-textarea-field__label {
      background: transparent;
      padding: 0;

      &--floating {
        top: 0;
        transform: translateY(-50%) scale(0.85);
      }
    }

    &.sp-textarea-field--focused {
      .sp-textarea-field__wrapper {
        background: $background-color-hover;
      }
    }

    &:hover {
      .sp-textarea-field__wrapper {
        background: rgba($background-color-hover, 0.5);
      }
    }
  }

  // ===== 状态样式 =====
  &--error {
    .sp-textarea-field__wrapper {
      border-color: $color-danger !important;
    }

    .sp-textarea-field__label {
      color: $color-danger !important;
    }

    &.sp-textarea-field--focused {
      .sp-textarea-field__wrapper {
        box-shadow: 0 0 0 1px rgba($color-danger, 0.2) !important;
      }
    }
  }

  &--disabled {
    .sp-textarea-field__wrapper {
      background: $background-color-disabled;
      border-color: $border-color-disabled;
      cursor: not-allowed;
    }

    .sp-textarea-field__label {
      color: $color-text-disabled;
    }
  }

  // ===== 发光效果 =====
  &--effect-glow {
    &.sp-textarea-field--focused {
      .sp-textarea-field__wrapper {
        box-shadow: 0 0 0 1px rgba($color-primary, 0.2),
          0 4px 12px rgba($color-primary, 0.15);
      }
    }

    &.sp-textarea-field--error.sp-textarea-field--focused {
      .sp-textarea-field__wrapper {
        box-shadow: 0 0 0 1px rgba($color-danger, 0.2),
          0 4px 12px rgba($color-danger, 0.15);
      }
    }
  }
}

// ===== 动画 =====
@keyframes sp-textarea-field-loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(350%);
  }
}

// ===== 消息过渡动画 =====
.sp-textarea-field-message-enter-active,
.sp-textarea-field-message-leave-active {
  transition: all 0.3s ease;
}

.sp-textarea-field-message-enter-from,
.sp-textarea-field-message-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
