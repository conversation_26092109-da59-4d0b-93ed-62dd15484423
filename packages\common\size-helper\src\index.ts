/**
 * Speed UI 组件尺寸助手工具
 * 用于统一管理组件尺寸相关的类型、常量、工具函数和 composables
 */

// 导出所有类型
export * from './types'

// 导出所有常量
export * from './constants'

// 导出所有工具函数
export * from './utils'

// 导出所有 composables
export * from './composables'

// 重新导出常用的内容（方便使用）
export { DEFAULT_SIZE, COMPONENT_SIZES, SIZE_MAPPING } from './constants'

export {
  mapSizeToScrollbar,
  getSizeValue,
  getSizePixels,
  isValidSize,
  getSizeClass,
  normalizeSizeValue,
} from './utils'

export { useSize, useSizeComparison } from './composables'

export type {
  ComponentSize,
  ExtendedComponentSize,
  ScrollbarSize,
  SizeProps,
  ExtendedSizeProps,
} from './types'

export type { UseSizeOptions, UseSizeReturn } from './composables'
