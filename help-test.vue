<template>
  <div style="padding: 20px">
    <h2>SP-Input 帮助功能测试</h2>

    <!-- 测试不同的组件名称和属性写法 -->
    <div style="margin: 20px 0">
      <h3>1. 使用 SpInput 组件名 (PascalCase)</h3>
      <SpInput
        help="这是帮助信息"
        placeholder="测试输入框"
      />
    </div>

    <div style="margin: 20px 0">
      <h3>2. 使用 sp-input 组件名 (kebab-case)</h3>
      <sp-input
        help="这是帮助信息"
        placeholder="测试输入框"
      />
    </div>

    <div style="margin: 20px 0">
      <h3>3. 使用 SpeedInput 组件名</h3>
      <SpeedInput
        help="这是帮助信息"
        placeholder="测试输入框"
      />
    </div>

    <div style="margin: 20px 0">
      <h3>4. 使用 question 属性</h3>
      <sp-input
        question="这是question属性"
        placeholder="测试输入框"
      />
    </div>

    <div style="margin: 20px 0">
      <h3>5. 完整功能测试</h3>
      <sp-input
        v-model:value="testValue"
        help="这是完整的帮助信息"
        clearable
        placeholder="测试输入框"
        @question-click="handleQuestionClick"
      />
    </div>

    <div
      v-if="clickResult"
      style="margin-top: 20px; padding: 10px; background: #f0f0f0"
    >
      <strong>点击结果：</strong>
      {{ clickResult }}
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue'

  // 尝试不同的导入方式
  import SpInput from './packages/ui/src/components/input/Input.vue'
  // 或者如果你有完整的包导入
  // import { SpInput, SpeedInput } from '@speed-ui/ui'

  const testValue = ref('测试内容')
  const clickResult = ref('')

  const handleQuestionClick = helpText => {
    clickResult.value = `帮助按钮被点击，内容：${helpText}`
    console.log('帮助按钮点击事件:', helpText)
  }
</script>

<style scoped>
  h2,
  h3 {
    color: #333;
  }
</style>
