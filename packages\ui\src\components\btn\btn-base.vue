<template>
  <button
    class="sp-btn-core"
    :disabled="disabled || loading"
    v-on="mergedEventHandlers"
  >
    <slot />
    <span v-if="loading" class="sp-btn-loading" />
  </button>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { useCommonEvents } from '../../composables/useCommonEvents'
  import type { BtnBaseProps, BtnEmits } from './types'

  // 逻辑层组件，负责核心功能和状态管理
  const props = withDefaults(defineProps<BtnBaseProps>(), {
    disabled: false,
    loading: false,
  })

  const emit = defineEmits<BtnEmits>()

  // 内部状态管理
  const isHovered = ref(false)
  const isPressed = ref(false)

  // 核心组件特定事件处理（处理业务逻辑）
  const coreSpecificHandlers = {
    click: () => {
      console.log('Button core clicked - 处理业务逻辑')
      if (!props.disabled && !props.loading) {
        emit('click')
      }
    },
    mousedown: () => {
      if (!props.disabled) {
        isPressed.value = true
        emit('mousedown')
      }
    },
    mouseup: () => {
      isPressed.value = false
      emit('mouseup')
    },
    focus: () => {
      console.log('Button core focused - 处理焦点逻辑')
      emit('focus')
    },
    blur: () => {
      console.log('Button core blurred - 处理焦点逻辑')
      emit('blur')
    }
  }

  // 合并事件处理器（注入的事件 + 本地事件）
  const mergedEventHandlers = useCommonEvents(coreSpecificHandlers)

  // 暴露状态给父组件
  defineExpose({
    isHovered,
    isPressed,
  })
</script>
