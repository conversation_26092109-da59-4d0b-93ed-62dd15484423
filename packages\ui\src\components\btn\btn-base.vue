<template>
  <button
    class="sp-btn-core"
    :disabled="disabled || loading"
    @click="handleClick"
    @mousedown="handleMouseDown"
    @mouseup="handleMouseUp"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <slot />
    <span v-if="loading" class="sp-btn-loading" />
  </button>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import type { BtnBaseProps, BtnEmits } from './types'

  // 逻辑层组件，负责核心功能和状态管理
  const props = withDefaults(defineProps<BtnBaseProps>(), {
    disabled: false,
    loading: false,
  })

  const emit = defineEmits<BtnEmits>()

  // 内部状态管理
  const isHovered = ref(false)
  const isPressed = ref(false)

  // 事件处理器
  const handleClick = () => {
    console.log('🔥 Button core clicked - 处理业务逻辑')
    if (!props.disabled && !props.loading) {
      console.log('✅ Emitting click event')
      emit('click')
    } else {
      console.log('❌ Click blocked - disabled or loading')
    }
  }

  const handleMouseDown = () => {
    if (!props.disabled) {
      isPressed.value = true
      emit('mousedown')
    }
  }

  const handleMouseUp = () => {
    isPressed.value = false
    emit('mouseup')
  }

  const handleMouseEnter = () => {
    if (!props.disabled) {
      isHovered.value = true
      emit('mouseenter')
    }
  }

  const handleMouseLeave = () => {
    isHovered.value = false
    isPressed.value = false
    emit('mouseleave')
  }

  // 暴露状态给父组件
  defineExpose({
    isHovered,
    isPressed,
  })
</script>
