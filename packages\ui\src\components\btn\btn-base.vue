<template>
  <button
    :disabled="disabled"
    v-on="eventHandlers"
  >
    <slot />
  </button>
</template>

<script setup lang="ts">
  import { toRef } from 'vue'
  import { useBaseEvents } from '../../composables/useBaseEvents'
  import type { BtnBaseProps, BtnEmits } from './types'

  // 逻辑层组件，负责状态管理和事件处理
  const props = withDefaults(defineProps<BtnBaseProps>(), {
    disabled: false,
  })

  const emit = defineEmits<BtnEmits>()

  // 使用基础事件处理 composable
  const { isHovered, isPressed, eventHandlers } = useBaseEvents({
    disabled: toRef(props, 'disabled'),
    emit,
  })

  // 暴露状态给父组件
  defineExpose({
    isHovered,
    isPressed,
  })
</script>
