<template>
  <div class="input-field-refactor-demo">
    <h1>🎉 InputField 重构成果展示</h1>

    <div class="demo-section">
      <h2>📋 重构前后对比</h2>
      <div class="comparison-grid">
        <div class="before">
          <h3>❌ 重构前（三层嵌套）</h3>
          <div class="architecture">
            <div class="layer">input-field.vue（接口层）</div>
            <div class="arrow">⬇️</div>
            <div class="layer">input-field-core.vue（核心层）</div>
            <div class="arrow">⬇️</div>
            <div class="layer">FieldContainer.vue（容器层）</div>
          </div>
          <div class="problems">
            <p><strong>问题：</strong></p>
            <ul>
              <li>三层嵌套过深，Props 传递复杂</li>
              <li>职责重叠，状态管理分散</li>
              <li>维护困难，调试复杂</li>
            </ul>
          </div>
        </div>

        <div class="after">
          <h3>✅ 重构后（两层结构）</h3>
          <div class="architecture">
            <div class="layer">input-field.vue（单一组件）</div>
            <div class="arrow">⬇️</div>
            <div class="layer">FieldContainer.vue（容器层）</div>
          </div>
          <div class="advantages">
            <p><strong>优势：</strong></p>
            <ul>
              <li>结构简洁，逻辑清晰</li>
              <li>Props 传递直接，无中间层</li>
              <li>易于维护和扩展</li>
              <li>保持了所有原有功能</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>🧪 功能测试</h2>
      <div class="test-grid">
        <div class="test-item">
          <h3>基础功能</h3>
          <SpInputField
            v-model:value="basicValue"
            label="基础输入"
            placeholder="请输入内容..."
            name="basic"
          />
          <p>当前值：{{ basicValue }}</p>
        </div>

        <div class="test-item">
          <h3>表单验证</h3>
          <SpInputField
            v-model:value="validateValue"
            label="必填字段"
            placeholder="请输入内容..."
            name="validate"
            required
            :rules="[{ required: true, message: '此字段为必填项' }]"
          />
          <p>当前值：{{ validateValue }}</p>
        </div>

        <div class="test-item">
          <h3>密码输入</h3>
          <SpInputField
            v-model:value="passwordValue"
            label="密码"
            placeholder="请输入密码..."
            name="password"
            type="password"
            show-password
            clearable
          />
          <p>当前值：{{ passwordValue }}</p>
        </div>

        <div class="test-item">
          <h3>前缀后缀</h3>
          <SpInputField
            v-model:value="prefixValue"
            label="搜索"
            placeholder="搜索内容..."
            name="search"
            prefix-icon="Search"
            suffix-icon="Filter"
            clearable
          />
          <p>当前值：{{ prefixValue }}</p>
        </div>

        <div class="test-item">
          <h3>字数统计</h3>
          <SpInputField
            v-model:value="lengthValue"
            label="限制长度"
            placeholder="最多50字..."
            name="length"
            :maxlength="50"
            show-word-limit
          />
          <p>当前值：{{ lengthValue }}</p>
        </div>

        <div class="test-item">
          <h3>不同状态</h3>
          <SpInputField
            v-model:value="statusValue"
            label="状态演示"
            placeholder="不同状态..."
            name="status"
            :error="showError"
            :warning="showWarning"
            :success="showSuccess"
            helper-text="这是帮助文本"
          />
          <div class="status-controls">
            <button @click="toggleStatus('error')">错误状态</button>
            <button @click="toggleStatus('warning')">警告状态</button>
            <button @click="toggleStatus('success')">成功状态</button>
            <button @click="clearStatus">清除状态</button>
          </div>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>🔧 技术实现</h2>
      <div class="tech-details">
        <h3>核心改进</h3>
        <ul>
          <li>
            <strong>合并组件：</strong>
            将 input-field.vue 和 input-field-core.vue 合并为单一组件
          </li>
          <li>
            <strong>直接使用 FieldContainer：</strong>
            消除中间层，直接与 FieldContainer 交互
          </li>
          <li>
            <strong>简化 Props 传递：</strong>
            减少了属性传递的复杂性
          </li>
          <li>
            <strong>统一事件处理：</strong>
            事件处理逻辑更加统一和清晰
          </li>
          <li>
            <strong>保持完整功能：</strong>
            所有原有功能都得到保留
          </li>
        </ul>

        <h3>代码结构</h3>
        <div class="code-structure">
          <div class="code-block">
            <h4>主要组成部分：</h4>
            <ul>
              <li>模板：直接使用 FieldContainer 作为根容器</li>
              <li>逻辑：使用 useInputLogic Composable 提供输入框功能</li>
              <li>类型：继承 InputProps 和 InputEmits</li>
              <li>事件：统一的事件处理机制</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>✅ 重构总结</h2>
      <div class="summary">
        <div class="achievements">
          <h3>重构成果</h3>
          <ul>
            <li>✅ 删除了 input-field-core.vue 中间层</li>
            <li>✅ 将三层嵌套简化为两层结构</li>
            <li>✅ 保持了所有原有功能和 API</li>
            <li>✅ 提高了代码的可维护性</li>
            <li>✅ 优化了性能和调试体验</li>
            <li>✅ 类型安全得到保证</li>
          </ul>
        </div>

        <div class="next-steps">
          <h3>后续建议</h3>
          <ul>
            <li>📝 更新相关文档</li>
            <li>🧪 添加更多单元测试</li>
            <li>🔄 考虑对其他复杂组件应用相同模式</li>
            <li>📊 监控性能改进效果</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { SpInputField } from './packages/ui/src/components/input-field'

  // 测试数据
  const basicValue = ref('')
  const validateValue = ref('')
  const passwordValue = ref('')
  const prefixValue = ref('')
  const lengthValue = ref('')
  const statusValue = ref('')

  // 状态控制
  const showError = ref(false)
  const showWarning = ref(false)
  const showSuccess = ref(false)

  const toggleStatus = (type: 'error' | 'warning' | 'success') => {
    clearStatus()
    if (type === 'error') showError.value = true
    if (type === 'warning') showWarning.value = true
    if (type === 'success') showSuccess.value = true
  }

  const clearStatus = () => {
    showError.value = false
    showWarning.value = false
    showSuccess.value = false
  }
</script>

<style scoped>
  .input-field-refactor-demo {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .demo-section {
    margin-bottom: 40px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 8px;
  }

  .comparison-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
  }

  .before,
  .after {
    padding: 20px;
    border-radius: 8px;
    background: white;
    border: 2px solid #e0e0e0;
  }

  .before {
    border-color: #ff6b6b;
  }

  .after {
    border-color: #51cf66;
  }

  .architecture {
    text-align: center;
    margin: 20px 0;
  }

  .layer {
    padding: 10px;
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 4px;
    margin: 5px 0;
  }

  .arrow {
    font-size: 20px;
    margin: 5px 0;
  }

  .problems ul,
  .advantages ul {
    margin: 10px 0;
    padding-left: 20px;
  }

  .problems li {
    color: #d32f2f;
    margin: 5px 0;
  }

  .advantages li {
    color: #2e7d32;
    margin: 5px 0;
  }

  .test-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
  }

  .test-item {
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
  }

  .test-item h3 {
    margin-top: 0;
    color: #1976d2;
  }

  .test-item p {
    margin: 10px 0;
    padding: 8px;
    background: #f5f5f5;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
  }

  .status-controls {
    display: flex;
    gap: 10px;
    margin-top: 10px;
  }

  .status-controls button {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    font-size: 12px;
  }

  .status-controls button:hover {
    background: #f5f5f5;
  }

  .tech-details {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;
  }

  .tech-details h3 {
    color: #1976d2;
    margin-top: 0;
  }

  .tech-details ul {
    margin: 10px 0;
    padding-left: 20px;
  }

  .tech-details li {
    margin: 8px 0;
    line-height: 1.5;
  }

  .code-structure {
    margin-top: 20px;
  }

  .code-block {
    background: #f5f5f5;
    padding: 15px;
    border-radius: 4px;
    border-left: 4px solid #2196f3;
  }

  .code-block h4 {
    margin-top: 0;
    color: #1976d2;
  }

  .summary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
  }

  .achievements,
  .next-steps {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
  }

  .achievements h3 {
    color: #2e7d32;
    margin-top: 0;
  }

  .next-steps h3 {
    color: #1976d2;
    margin-top: 0;
  }

  .achievements li {
    color: #2e7d32;
    margin: 8px 0;
  }

  .next-steps li {
    color: #1976d2;
    margin: 8px 0;
  }

  h1 {
    text-align: center;
    color: #1976d2;
    margin-bottom: 30px;
  }

  h2 {
    color: #1976d2;
    margin-top: 0;
    border-bottom: 2px solid #e3f2fd;
    padding-bottom: 10px;
  }
</style>
