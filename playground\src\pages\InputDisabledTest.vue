<template>
  <div class="input-disabled-test">
    <h1>🚫 输入组件禁用状态测试</h1>
    <p>测试输入组件在禁用状态下的样式和交互行为</p>

    <div class="demo-section">
      <h2>基础输入框禁用状态</h2>

      <div class="demo-item">
        <label>正常状态:</label>
        <sp-input
          v-model:value="normalValue"
          placeholder="正常输入框"
          style="width: 300px"
        />
      </div>

      <div class="demo-item">
        <label>禁用状态:</label>
        <sp-input
          v-model:value="disabledValue"
          placeholder="禁用输入框"
          disabled
          style="width: 300px"
        />
      </div>

      <div class="demo-item">
        <label>只读状态:</label>
        <sp-input
          v-model:value="readonlyValue"
          placeholder="只读输入框"
          readonly
          style="width: 300px"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>密码输入框禁用状态</h2>

      <div class="demo-item">
        <label>正常密码框:</label>
        <sp-input-password
          v-model:value="normalPassword"
          placeholder="正常密码框"
          style="width: 300px"
        />
      </div>

      <div class="demo-item">
        <label>禁用密码框:</label>
        <sp-input-password
          v-model:value="disabledPassword"
          placeholder="禁用密码框"
          disabled
          style="width: 300px"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>搜索输入框禁用状态</h2>

      <div class="demo-item">
        <label>正常搜索框:</label>
        <sp-input-search
          v-model:value="normalSearch"
          placeholder="正常搜索框"
          style="width: 300px"
        />
      </div>

      <div class="demo-item">
        <label>禁用搜索框:</label>
        <sp-input-search
          v-model:value="disabledSearch"
          placeholder="禁用搜索框"
          disabled
          style="width: 300px"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>带前后缀的禁用状态</h2>

      <div class="demo-item">
        <label>带前缀图标 (禁用):</label>
        <sp-input
          v-model:value="disabledWithPrefix"
          placeholder="禁用带前缀"
          prefix-icon="User"
          disabled
          style="width: 300px"
        />
      </div>

      <div class="demo-item">
        <label>带后缀图标 (禁用):</label>
        <sp-input
          v-model:value="disabledWithSuffix"
          placeholder="禁用带后缀"
          suffix-icon="Search"
          disabled
          style="width: 300px"
        />
      </div>

      <div class="demo-item">
        <label>可清空 (禁用):</label>
        <sp-input
          v-model:value="disabledClearable"
          placeholder="禁用可清空"
          clearable
          disabled
          style="width: 300px"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>不同尺寸的禁用状态</h2>

      <div class="demo-item">
        <label>小尺寸 (禁用):</label>
        <sp-input
          v-model:value="disabledSmall"
          placeholder="小尺寸禁用"
          size="small"
          disabled
          style="width: 300px"
        />
      </div>

      <div class="demo-item">
        <label>中等尺寸 (禁用):</label>
        <sp-input
          v-model:value="disabledMedium"
          placeholder="中等尺寸禁用"
          size="medium"
          disabled
          style="width: 300px"
        />
      </div>

      <div class="demo-item">
        <label>大尺寸 (禁用):</label>
        <sp-input
          v-model:value="disabledLarge"
          placeholder="大尺寸禁用"
          size="large"
          disabled
          style="width: 300px"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>测试说明</h2>
      <div class="test-instructions">
        <h3>预期行为（参考 Ant Design）：</h3>
        <ul>
          <li>✅ 禁用状态的输入框应该有灰色背景和边框</li>
          <li>✅ 鼠标悬停时边框颜色保持不变（不高亮）</li>
          <li>✅ 点击时不应该获得焦点</li>
          <li>✅ 文本颜色应该是灰色</li>
          <li>✅ 光标应该显示为 "not-allowed"</li>
          <li>✅ 所有图标和按钮都应该被禁用</li>
          <li>✅ 禁用状态下允许悬停，但不改变边框颜色</li>
        </ul>

        <h3>修复内容：</h3>
        <ul>
          <li>🔧 移除了过度的 pointer-events: none 和 !important 样式</li>
          <li>🔧 采用 Ant Design 的禁用状态策略</li>
          <li>🔧 禁用状态下悬停时边框颜色保持不变</li>
          <li>🔧 简化了事件处理逻辑</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h2>动态切换测试</h2>

      <div class="demo-item">
        <label>
          <input
            type="checkbox"
            v-model="isDynamicDisabled"
          />
          动态禁用状态
        </label>
        <sp-input
          v-model:value="dynamicValue"
          placeholder="动态切换禁用状态"
          :disabled="isDynamicDisabled"
          style="width: 300px"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>调试信息</h2>
      <div class="debug-info">
        <p><strong>请在浏览器开发者工具中检查以下内容：</strong></p>
        <ol>
          <li>
            禁用状态的输入框是否有
            <code>sp-input__wrapper--disabled</code>
            类名
          </li>
          <li>
            是否有
            <code>$background-color-disabled</code>
            样式被应用
          </li>
          <li>检查样式文件是否正确加载</li>
          <li>检查是否有其他样式覆盖了禁用状态样式</li>
        </ol>
        <p><strong>预期的类名结构：</strong></p>
        <pre><code>
.sp-input__wrapper.sp-input__wrapper--disabled {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
}
        </code></pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  // 测试数据
  const normalValue = ref('正常输入框')
  const disabledValue = ref('禁用输入框')
  const readonlyValue = ref('只读输入框')

  const normalPassword = ref('password123')
  const disabledPassword = ref('disabled123')

  const normalSearch = ref('搜索内容')
  const disabledSearch = ref('禁用搜索')

  const disabledWithPrefix = ref('带前缀')
  const disabledWithSuffix = ref('带后缀')
  const disabledClearable = ref('可清空内容')

  const disabledSmall = ref('小尺寸')
  const disabledMedium = ref('中等尺寸')
  const disabledLarge = ref('大尺寸')

  const dynamicValue = ref('动态测试')
  const isDynamicDisabled = ref(false)
</script>

<style scoped>
  .input-disabled-test {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
  }

  .demo-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background-color: #fafafa;
  }

  .demo-section h2 {
    margin-top: 0;
    color: #409eff;
    border-bottom: 2px solid #409eff;
    padding-bottom: 10px;
  }

  .demo-item {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .demo-item label {
    min-width: 150px;
    font-weight: 500;
    color: #606266;
  }

  .test-instructions {
    background-color: #f0f9ff;
    padding: 16px;
    border-radius: 6px;
    border-left: 4px solid #409eff;
  }

  .test-instructions h3 {
    margin-top: 0;
    color: #409eff;
  }

  .test-instructions ul {
    margin: 0;
    padding-left: 20px;
  }

  .test-instructions li {
    margin-bottom: 8px;
    color: #606266;
  }

  .debug-info {
    background-color: #fff3cd;
    padding: 16px;
    border-radius: 6px;
    border-left: 4px solid #ffc107;
  }

  .debug-info code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    color: #e83e8c;
  }

  .debug-info pre {
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: 4px;
    overflow-x: auto;
    margin: 8px 0;
  }

  .debug-info pre code {
    background: none;
    padding: 0;
    color: #495057;
  }
</style>
