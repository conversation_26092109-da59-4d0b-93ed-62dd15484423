/**
 * SliderField 组件类型定义
 * 继承 Slider 组件所有功能，并添加表单集成和浮动标签
 */

import type { SliderProps, SliderEmits } from '../slider/slider'
import type { FieldContainerProps } from '../field-container/types'

/** SliderField 专有属性 */
export interface SliderFieldOwnProps {
  /** 标签文本 - 支持浮动动画 */
  label?: string
  /** 表单字段名 - 用于表单验证 */
  name?: string
  /** 验证规则 */
  rules?: any
  /** 是否必填 */
  required?: boolean
  /** 是否显示验证消息 */
  showMessage?: boolean
  /** 帮助文本 */
  helperText?: string
  /** 标签是否始终浮动（不回落） */
  persistentLabel?: boolean
  /** 是否显示当前值 */
  showValue?: boolean
  /** 值格式化函数 */
  valueFormatter?: (value: number) => string
  /** 外部前置图标（位于输入框外部左侧） */
  prependIcon?: string
  /** 内部前置图标（位于输入框内部左侧） */
  prependIconInner?: string
  /** 外部后置图标（位于输入框外部右侧） */
  appendIcon?: string
  /** 内部后置图标（位于输入框内部右侧） */
  appendIconInner?: string
  /** 前缀文本 */
  prefix?: string
  /** 后缀文本 */
  suffix?: string
}

/** SliderField 完整属性，继承 Slider 和 FieldContainer 的属性 */
export interface SliderFieldProps
  extends Omit<SliderProps, 'modelValue' | 'variant'>,
    Omit<FieldContainerProps, 'value' | 'variant'>,
    SliderFieldOwnProps {
  /** 当前值 */
  value?: number
  /** 样式变体 - 使用 FieldContainer 的变体类型 */
  variant?: 'default' | 'underlined' | 'filled' | 'square' | 'unborder'
}

/** SliderField 事件定义 */
export interface SliderFieldEmits
  extends Omit<SliderEmits, 'update:modelValue'> {
  /** 值更新事件 */
  'update:value': [value: number]
  /** 输入事件 */
  input: [event: Event]
  /** 聚焦事件 */
  focus: [event?: FocusEvent]
  /** 失焦事件 */
  blur: [event?: FocusEvent]
  /** 键盘按下事件 */
  keydown: [event: KeyboardEvent]
  /** 键盘抬起事件 */
  keyup: [event: KeyboardEvent]
  /** 键盘按键事件 */
  keypress: [event: KeyboardEvent]
  /** 点击事件 */
  click: [event: MouseEvent]
  /** 外部前置图标点击事件 */
  'click:prepend': [event: MouseEvent]
  /** 内部前置图标点击事件 */
  'click:prepend-inner': [event: MouseEvent]
  /** 外部后置图标点击事件 */
  'click:append': [event: MouseEvent]
  /** 内部后置图标点击事件 */
  'click:append-inner': [event: MouseEvent]
  /** 表单验证事件 */
  validate: [name: string, isValid: boolean, message: string]
}

/** SliderField 实例接口 */
export interface SliderFieldInstance {
  /** 滑块引用 */
  slider: any
  /** 字段容器引用 */
  fieldContainer: any
  /** 设置焦点 */
  focus: () => void
  /** 失去焦点 */
  blur: () => void
  /** 验证字段 */
  validate: () => Promise<boolean>
  /** 重置字段 */
  resetField: () => void
  /** 清除验证 */
  clearValidate: () => void
}

/** SliderField 默认属性值 */
export const sliderFieldPropsDefaults = {
  value: 0,
  min: 0,
  max: 100,
  step: 1,
  disabled: false,
  variant: 'unborder' as const, // 默认使用无边框变体
  size: 'medium' as const,
  required: false,
  showMessage: true,
  persistentLabel: false,
  showValue: false,
  marks: () => ({}),
}
