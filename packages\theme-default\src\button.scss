// ================================
// Speed UI Button 组件样式
// ================================

@use 'sass:map';
@use './common/var.scss' as *;

.sp-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
  cursor: pointer;
  user-select: none;
  outline: none;
  text-decoration: none;
  font-family: $font-family-base;
  font-weight: $font-weight-base;
  transition: all $transition-duration-base $transition-timing-base;

  // 默认尺寸
  height: $button-height-medium;
  padding: 0 $button-padding-horizontal-medium;
  font-size: $button-font-size-medium;
  border-radius: $border-radius-base;

  &:focus {
    outline: none;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  // ========== 按钮类型 ==========
  // Primary 按钮
  &--primary {
    background-color: $color-primary;
    border-color: $color-primary;
    color: #ffffff;

    &:hover:not(:disabled) {
      background-color: $color-primary-hover;
      border-color: $color-primary-hover;
    }

    &:active:not(:disabled) {
      background-color: $color-primary-active;
      border-color: $color-primary-active;
    }
  }

  // Secondary 按钮
  &--secondary {
    background-color: $background-color-base;
    border-color: $border-color-base;
    color: $color-text-primary;

    &:hover:not(:disabled) {
      background-color: $color-primary-lightest;
      border-color: $color-primary-hover;
      color: $color-primary-hover;
    }

    &:active:not(:disabled) {
      background-color: $color-primary-light;
      border-color: $color-primary-active;
      color: $color-primary-active;
    }
  }

  // Dashed 按钮
  &--dashed {
    background-color: $background-color-base;
    border-color: $border-color-base;
    border-style: dashed;
    color: $color-text-primary;

    &:hover:not(:disabled) {
      background-color: $color-primary-lightest;
      border-color: $color-primary-hover;
      border-style: dashed;
      color: $color-primary-hover;
    }

    &:active:not(:disabled) {
      background-color: $color-primary-light;
      border-color: $color-primary-active;
      border-style: dashed;
      color: $color-primary-active;
    }
  }

  // Text 按钮
  &--text {
    background-color: transparent;
    border-color: transparent;
    color: $color-text-primary;
    height: $button-height-medium;
    padding: 0 $button-padding-horizontal-medium;
    border-radius: $border-radius-base;

    &:hover:not(:disabled) {
      background-color: $color-primary-lightest;
      color: $color-primary-hover;
    }

    &:active:not(:disabled) {
      background-color: $color-primary-light;
      color: $color-primary-active;
    }
  }

  // Link 按钮
  &--link {
    background-color: transparent;
    border-color: transparent;
    color: $color-primary;
    height: $button-height-medium;
    padding: 0 $button-padding-horizontal-medium;
    border-radius: $border-radius-base;

    &:hover:not(:disabled) {
      background-color: $color-primary-lightest;
      color: $color-primary-hover;
    }

    &:active:not(:disabled) {
      background-color: $color-primary-light;
      color: $color-primary-active;
    }
  }

  // ========== 尺寸变体 ==========
  &--small {
    height: $button-height-small;
    padding: 0 $button-padding-horizontal-small;
    font-size: $button-font-size-small;
    border-radius: $border-radius-small;
  }

  &--large {
    height: $button-height-large;
    padding: 0 $button-padding-horizontal-large;
    font-size: $button-font-size-large;
    border-radius: $border-radius-large;
  }

  &--huge {
    height: 56px;
    padding: 0 28px;
    font-size: 20px;
    border-radius: 12px;
  }

  // 圆形按钮
  &--circle {
    border-radius: 50%;
    padding: 0;
    width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
  }

  // 不同尺寸下的圆形按钮
  &--small.sp-button--circle {
    width: 24px;
    height: 24px;
    font-size: 14px;
  }
  &--medium.sp-button--circle {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
  &--large.sp-button--circle {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  &--huge.sp-button--circle {
    width: 56px;
    height: 56px;
    font-size: 22px;
  }

  // ========== 状态 ==========
  &--loading {
    pointer-events: none;

    .sp-button__content {
      opacity: 0.6;
    }
  }

  // ========== 子元素 ==========
  &__loading {
    margin-right: $spacing-sm;
  }

  &__spinner {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: sp-button-spin 1s linear infinite;
  }

  &__content {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 4px;
    white-space: nowrap;
  }

  &__icon {
    display: inline-flex;
    align-items: center;
    vertical-align: middle;
    margin-right: 4px;
    height: 1em;
    width: 1em;
    font-size: inherit;
  }

  &__icon svg {
    display: inline-block;
    vertical-align: middle;
    width: 1em;
    height: 1em;
    fill: currentColor;
  }
}

// ========== 主题支持 ==========
// 主题类名，用于响应全局主题变量
.sp-button-custom {
  // 定义组件级主题变量，引用全局主题变量
  --button-primary-color: var(--sp-color-primary);
  --button-primary-hover: var(--sp-color-primary-hover);
  --button-primary-active: var(--sp-color-primary-active);
  --button-primary-disabled: var(--sp-color-primary-disabled);
  --button-primary-lightest: var(--sp-color-primary-lightest);
  --button-primary-light: var(--sp-color-primary-light);

  // Primary 按钮主题样式
  &.sp-button--primary {
    background-color: var(--button-primary-color);
    border-color: var(--button-primary-color);

    &:hover:not(:disabled) {
      background-color: var(--button-primary-hover);
      border-color: var(--button-primary-hover);
    }

    &:active:not(:disabled) {
      background-color: var(--button-primary-active);
      border-color: var(--button-primary-active);
    }
  }

  // Secondary 按钮主题样式
  &.sp-button--secondary {
    &:hover:not(:disabled) {
      background-color: var(--button-primary-lightest);
      border-color: var(--button-primary-hover);
      color: var(--button-primary-hover);
    }

    &:active:not(:disabled) {
      background-color: var(--button-primary-light);
      border-color: var(--button-primary-active);
      color: var(--button-primary-active);
    }
  }

  // Dashed 按钮主题样式
  &.sp-button--dashed {
    &:hover:not(:disabled) {
      background-color: var(--button-primary-lightest);
      border-color: var(--button-primary-hover);
      color: var(--button-primary-hover);
    }

    &:active:not(:disabled) {
      background-color: var(--button-primary-light);
      border-color: var(--button-primary-active);
      color: var(--button-primary-active);
    }
  }

  // Text 按钮主题样式
  &.sp-button--text {
    &:hover:not(:disabled) {
      background-color: var(--button-primary-lightest);
      color: var(--button-primary-hover);
    }

    &:active:not(:disabled) {
      background-color: var(--button-primary-light);
      color: var(--button-primary-active);
    }
  }

  // Link 按钮主题样式
  &.sp-button--link {
    color: var(--button-primary-color);

    &:hover:not(:disabled) {
      background-color: var(--button-primary-lightest);
      color: var(--button-primary-hover);
    }

    &:active:not(:disabled) {
      background-color: var(--button-primary-light);
      color: var(--button-primary-active);
    }
  }
}

// 动画
@keyframes sp-button-spin {
  to {
    transform: rotate(360deg);
  }
}

// 主题色变体（自动生成）
@mixin button-theme($type, $map) {
  // 主按钮
  .sp-button--primary.sp-button--#{$type} {
    background: map.get($map, color);
    border-color: map.get($map, color);
    color: #fff;
    &:hover:not(:disabled) {
      background: map.get($map, hover);
      border-color: map.get($map, hover);
    }
    &:active:not(:disabled) {
      background: map.get($map, active);
      border-color: map.get($map, active);
    }
  }
  // 次按钮
  .sp-button--secondary.sp-button--#{$type} {
    color: map.get($map, text);
    border-color: map.get($map, hover);
    background: map.get($map, bg);
    &:hover:not(:disabled) {
      background: map.get($map, bg-hover);
      color: map.get($map, text-hover);
      border-color: map.get($map, hover);
    }
    &:active:not(:disabled) {
      background: map.get($map, bg-active);
      color: map.get($map, text-active);
      border-color: map.get($map, active);
    }
  }
  // 虚线按钮
  .sp-button--dashed.sp-button--#{$type} {
    color: map.get($map, text);
    border-color: map.get($map, hover);
    background: map.get($map, bg);
    border-style: dashed;
    &:hover:not(:disabled) {
      background: map.get($map, bg-hover);
      color: map.get($map, text-hover);
      border-color: map.get($map, hover);
    }
    &:active:not(:disabled) {
      background: map.get($map, bg-active);
      color: map.get($map, text-active);
      border-color: map.get($map, active);
    }
  }
  // 文字按钮
  .sp-button--text.sp-button--#{$type} {
    color: map.get($map, text);
    background: transparent;
    &:hover:not(:disabled) {
      background: map.get($map, bg);
      color: map.get($map, text-hover);
    }
    &:active:not(:disabled) {
      background: map.get($map, bg-active);
      color: map.get($map, text-active);
    }
  }
  // 链接按钮
  .sp-button--link.sp-button--#{$type} {
    color: map.get($map, text);
    background: transparent;
    &:hover:not(:disabled) {
      color: map.get($map, text-hover);
      background: map.get($map, bg);
    }
    &:active:not(:disabled) {
      color: map.get($map, text-active);
      background: map.get($map, bg-active);
    }
  }
}

@each $type, $map in $button-theme-map {
  @include button-theme($type, $map);
}

.sp-button--vertical {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  padding: 8px 4px;
  box-sizing: border-box;
  height: auto;
  vertical-align: top;
}
.sp-button__content--vertical {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
}
.sp-button__vertical-text {
  writing-mode: vertical-rl;
  text-orientation: upright; /* 让数字/英文正着竖排 */
  text-align: center;
  letter-spacing: 0.2em;
  font-size: inherit;
  white-space: nowrap;
}
.sp-button--vertical .sp-button__icon {
  margin: 0;
}
.sp-button--round {
  border-radius: 999px !important;
}
.sp-button--square {
  border-radius: 0 !important;
}
