<template>
  <div class="menu-demo">
    <h1>🎯 Menu 菜单组件演示</h1>
    <p>基础菜单组件，为整个组件库提供基础设施支持</p>

    <!-- 垂直菜单 -->
    <section class="demo-section">
      <h2>垂直菜单 (Vertical)</h2>
      <div class="demo-container">
        <sp-menu
          v-model:selectedKeys="selectedKeys1"
          mode="vertical"
          style="width: 240px"
        >
          <sp-menu-item
            item-key="home"
            title="首页"
          />
          <sp-menu-item
            item-key="about"
            title="关于我们"
          />
          <sp-menu-item
            item-key="services"
            title="服务"
          />
          <sp-menu-item
            item-key="contact"
            title="联系我们"
          />
          <sp-menu-item
            item-key="disabled"
            title="禁用项"
            disabled
          />
          <sp-menu-item
            item-key="danger"
            title="危险项"
            danger
          />
        </sp-menu>
        <div class="demo-info">
          <h4>选中状态:</h4>
          <code>{{ JSON.stringify(selectedKeys1) }}</code>
        </div>
      </div>
    </section>

    <!-- 水平菜单 -->
    <section class="demo-section">
      <h2>水平菜单 (Horizontal)</h2>
      <div class="demo-container">
        <sp-menu
          v-model:selectedKeys="selectedKeys2"
          mode="horizontal"
        >
          <sp-menu-item
            item-key="dashboard"
            title="控制台"
          />
          <sp-menu-item
            item-key="users"
            title="用户管理"
          />
          <sp-menu-item
            item-key="settings"
            title="设置"
          />
          <sp-menu-item
            item-key="help"
            title="帮助"
          />
        </sp-menu>
        <div class="demo-info">
          <h4>选中状态:</h4>
          <code>{{ JSON.stringify(selectedKeys2) }}</code>
        </div>
      </div>
    </section>

    <!-- 暗色主题 -->
    <section class="demo-section">
      <h2>暗色主题 (Dark Theme)</h2>
      <div class="demo-container">
        <sp-menu
          v-model:selectedKeys="selectedKeys3"
          mode="vertical"
          theme="dark"
          style="width: 240px"
        >
          <sp-menu-item
            item-key="file"
            title="文件"
          />
          <sp-menu-item
            item-key="edit"
            title="编辑"
          />
          <sp-menu-item
            item-key="view"
            title="查看"
          />
          <sp-menu-item
            item-key="tools"
            title="工具"
          />
        </sp-menu>
        <div class="demo-info">
          <h4>选中状态:</h4>
          <code>{{ JSON.stringify(selectedKeys3) }}</code>
        </div>
      </div>
    </section>

    <!-- 多选模式 -->
    <section class="demo-section">
      <h2>多选模式 (Multiple)</h2>
      <div class="demo-container">
        <sp-menu
          v-model:selectedKeys="selectedKeys4"
          mode="vertical"
          :multiple="true"
          style="width: 240px"
        >
          <sp-menu-item
            item-key="feature1"
            title="功能一"
          />
          <sp-menu-item
            item-key="feature2"
            title="功能二"
          />
          <sp-menu-item
            item-key="feature3"
            title="功能三"
          />
          <sp-menu-item
            item-key="feature4"
            title="功能四"
          />
        </sp-menu>
        <div class="demo-info">
          <h4>选中状态:</h4>
          <code>{{ JSON.stringify(selectedKeys4) }}</code>
          <p><small>可以选择多个项目</small></p>
        </div>
      </div>
    </section>

    <!-- 自定义内容 -->
    <section class="demo-section">
      <h2>自定义内容 (Custom Content)</h2>
      <div class="demo-container">
        <sp-menu
          v-model:selectedKeys="selectedKeys5"
          mode="vertical"
          style="width: 280px"
        >
          <sp-menu-item item-key="custom1">
            <template #suffix>
              <span style="color: #999; font-size: 12px">Ctrl+N</span>
            </template>
            新建文件
          </sp-menu-item>
          <sp-menu-item item-key="custom2">
            <template #suffix>
              <span style="color: #999; font-size: 12px">Ctrl+S</span>
            </template>
            保存文件
          </sp-menu-item>
          <sp-menu-item
            item-key="custom3"
            danger
          >
            <template #suffix>
              <span style="color: #ff4d4f; font-size: 12px">Del</span>
            </template>
            删除文件
          </sp-menu-item>
        </sp-menu>
        <div class="demo-info">
          <h4>选中状态:</h4>
          <code>{{ JSON.stringify(selectedKeys5) }}</code>
          <p><small>支持自定义后缀内容</small></p>
        </div>
      </div>
    </section>

    <!-- 复用说明 -->
    <section class="demo-section">
      <h2>🚀 未来复用场景</h2>
      <div class="reuse-info">
        <div class="reuse-item">
          <strong>Select 组件:</strong>
          Menu 作为下拉选项容器
        </div>
        <div class="reuse-item">
          <strong>Dropdown 组件:</strong>
          Menu 作为下拉菜单容器
        </div>
        <div class="reuse-item">
          <strong>Navigation 组件:</strong>
          Menu 作为导航菜单基础
        </div>
        <div class="reuse-item">
          <strong>ContextMenu 组件:</strong>
          Menu 作为右键菜单基础
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  // 各种菜单的选中状态
  const selectedKeys1 = ref(['home'])
  const selectedKeys2 = ref(['dashboard'])
  const selectedKeys3 = ref(['file'])
  const selectedKeys4 = ref(['feature1', 'feature3'])
  const selectedKeys5 = ref(['custom1'])
</script>

<style scoped>
  .menu-demo {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .menu-demo h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 10px;
  }

  .menu-demo > p {
    text-align: center;
    color: #7f8c8d;
    margin-bottom: 40px;
  }

  .demo-section {
    margin-bottom: 50px;
    padding: 30px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background: #fafafa;
  }

  .demo-section h2 {
    color: #34495e;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #ecf0f1;
  }

  .demo-container {
    display: flex;
    gap: 30px;
    align-items: flex-start;
  }

  .demo-info {
    flex: 1;
    min-width: 200px;
  }

  .demo-info h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
  }

  .demo-info code {
    display: block;
    padding: 10px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    color: #495057;
    word-break: break-all;
  }

  .demo-info p {
    margin: 10px 0 0 0;
    color: #6c757d;
  }

  .demo-info small {
    font-size: 12px;
    color: #999;
  }

  .reuse-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
  }

  .reuse-item {
    padding: 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #ddd;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .reuse-item strong {
    color: #2c3e50;
  }
</style>
