<template>
  <div
    :class="itemClasses"
    :style="itemStyles"
    @click="handleClick"
  >
    <!-- 选择框 -->
    <div
      v-if="showCheckbox"
      class="sp-list-item__checkbox"
      @click.stop="handleSelect"
    >
      <div
        :class="checkboxClasses"
        role="checkbox"
        :aria-checked="isSelected"
        tabindex="0"
        @keydown.enter.space.prevent="handleSelect"
      >
        <span
          v-if="isSelected"
          class="sp-list-item__checkbox-icon"
        >
          ✓
        </span>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="sp-list-item__content">
      <!-- 主要内容 -->
      <div class="sp-list-item__main">
        <slot>
          <div
            v-if="props.title"
            class="sp-list-item__title"
          >
            {{ props.title }}
          </div>
          <div
            v-if="props.description"
            class="sp-list-item__description"
          >
            {{ props.description }}
          </div>
        </slot>
      </div>

      <!-- 后缀内容 -->
      <div
        v-if="$slots.suffix"
        class="sp-list-item__suffix"
      >
        <slot name="suffix" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, inject } from 'vue'
  import type { ListItemProps } from './types'
  import { listKey } from './constants'

  defineOptions({
    name: 'SpListItem',
  })

  const props = withDefaults(defineProps<ListItemProps>(), {
    disabled: false,
  })

  const emit = defineEmits<{
    click: [key: string, event: MouseEvent]
    select: [key: string, selected: boolean]
  }>()

  const listContext = inject(listKey)

  const isSelectable = computed(() => {
    return listContext?.props.selectable || false
  })

  const isSelected = computed(() => {
    return listContext?.selectedKeys.value.includes(props.itemKey) || false
  })

  const showCheckbox = computed(() => {
    return isSelectable.value && !props.disabled
  })

  const itemClasses = computed(() => {
    return [
      'sp-list-item',
      {
        'sp-list-item--disabled': props.disabled,
        'sp-list-item--selected': isSelected.value,
        'sp-list-item--selectable': isSelectable.value,
        'sp-list-item--hoverable':
          listContext?.props.hoverable && !props.disabled,
      },
      props.className,
    ]
  })

  const checkboxClasses = computed(() => {
    return [
      'sp-list-item__checkbox-input',
      {
        'sp-list-item__checkbox-input--checked': isSelected.value,
        'sp-list-item__checkbox-input--disabled': props.disabled,
      },
    ]
  })

  const itemStyles = computed(() => {
    return props.style || {}
  })

  const handleClick = (event: MouseEvent) => {
    if (props.disabled) return

    emit('click', props.itemKey, event)
    listContext?.onItemClick(props.itemKey, event)

    // 如果是可选择的，点击时自动触发选择
    if (isSelectable.value && !props.disabled) {
      handleSelect()
    }
  }

  const handleSelect = () => {
    if (props.disabled || !isSelectable.value) return

    const newSelected = !isSelected.value
    emit('select', props.itemKey, newSelected)
    listContext?.onItemSelect(props.itemKey, newSelected)
  }
</script>
