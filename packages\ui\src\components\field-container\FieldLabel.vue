<!--
  FieldLabel.vue - 字段标签组件
  提供浮动标签功能，支持必填标记
-->

<template>
  <label
    v-if="label"
    :class="labelClasses"
    :for="fieldId"
  >
    <span
      v-if="required"
      class="sp-field-container__label-asterisk"
    >
      *
    </span>
    {{ label }}
  </label>
</template>

<script setup lang="ts">
  /** FieldLabel 属性接口 */
  interface FieldLabelProps {
    /** 标签文本 */
    label?: string
    /** 字段 ID */
    fieldId?: string
    /** 是否必填 */
    required?: boolean
    /** 标签样式类名 */
    labelClasses?: any // 使用 any 类型以兼容 computed 返回的复杂类型
  }

  // ===== Props =====
  const props = withDefaults(defineProps<FieldLabelProps>(), {
    required: false,
  })
</script>

<script lang="ts">
  export default {
    name: 'FieldLabel',
  }
</script>
