<template>
  <div class="form-demo">
    <h1>表单验证演示 - VeeValidate + Label 对象配置</h1>
    
    <!-- 功能说明 -->
    <div class="feature-description">
      <h3>🎯 Label 对象配置功能</h3>
      <p>FormItem 的 <code>label</code> 属性现在支持对象格式，包含以下属性：</p>
      <ul>
        <li><strong>text</strong>: string - 标签文本内容</li>
        <li><strong>position</strong>: 'left' | 'right' | 'top' - 标签位置</li>
        <li><strong>show</strong>: boolean - 是否显示标签</li>
        <li><strong>width</strong>: string | number - 标签宽度</li>
      </ul>
      <p>同时仍然兼容原有的字符串方式：<code>label="用户名"</code></p>
    </div>
    
    <div class="demo-container">
      <sp-form
        ref="formRef"
        @submit="onSubmit"
        @invalid="onInvalid"
      >
        <!-- 用户名 - 使用字符串 label (原有方式) -->
        <sp-form-item 
          label="用户名（字符串方式）" 
          name="username" 
          :rules="usernameRules"
          required
        >
          <sp-input
            placeholder="请输入用户名"
            clearable
          />
        </sp-form-item>
        
        <!-- 邮箱 - 使用对象 label，自定义位置为 left -->
        <sp-form-item 
          :label="{ text: '邮箱（左对齐）', position: 'left', width: '120px' }"
          name="email" 
        >
          <sp-input
            type="email"
            placeholder="请输入邮箱"
            clearable
          />
        </sp-form-item>
        
        <!-- 密码 - 使用对象 label，自定义位置为 top -->
        <sp-form-item 
          :label="{ text: '密码（顶部标签）', position: 'top' }"
          name="password" 
          :rules="passwordRules"
          required
        >
          <sp-input
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </sp-form-item>
        
        <!-- 年龄 - 使用对象 label，自定义宽度 -->
        <sp-form-item 
          :label="{ text: '年龄（右对齐，宽度150px）', position: 'top', width: '180px' }"
          name="age" 
          :rules="ageRules"
          required
        >
          <sp-input
            type="number"
            placeholder="请输入年龄"
            clearable
          />
        </sp-form-item>
        
        <!-- 隐藏标签的字段 -->
        <sp-form-item 
          :label="{ text: '这个标签被隐藏了', show: false }"
          name="hidden_label_field"
          :rules="ageRules"
          type="number"

          
        >
          <sp-input
            placeholder="这个字段的标签被隐藏了"
            clearable
          />
        </sp-form-item>
        
        <!-- 测试宽度为数字的情况 -->
        <sp-form-item 
          :label="{ text: '宽度200像素', position: 'left', width: 200 }"
          name="number_width_field"
        >
          <sp-input
            placeholder="标签宽度设置为数字200"
            clearable
          />
        </sp-form-item>

        <sp-form-item name="buttons">
          <sp-button type="primary" :toggleable="false" @click="submitForm">
            提交
          </sp-button>
          <sp-button type="outline" :toggleable="false" @click="resetForm" style="margin-left: 12px;">
            重置
          </sp-button>
        </sp-form-item>
      </sp-form>
    </div>

    <!-- 提交结果 -->
    <div v-if="submitResult" class="result-section">
      <h3>{{ submitResult.message }}</h3>
      
      <!-- 成功时显示表单数据 -->
      <div v-if="submitResult.status === 'success'" class="form-data-display">
        <h4>📋 获取到的表单数据对象：</h4>
        <div class="data-object">
          <div v-for="(value, key) in submitResult.formData" :key="key" class="data-item">
            <span class="data-key">{{ key }}:</span>
            <span class="data-value">{{ value }}</span>
          </div>
        </div>
        
        <h4>🔑 包含的字段：</h4>
        <div class="data-keys">
          <span v-for="key in submitResult.dataKeys" :key="key" class="key-tag">{{ key }}</span>
        </div>
        
        <h4>📄 完整 JSON 格式：</h4>
        <pre class="json-display">{{ JSON.stringify(submitResult.formData, null, 2) }}</pre>
      </div>
      
      <!-- 失败时显示错误 -->
      <div v-else>
        <pre>{{ JSON.stringify(submitResult, null, 2) }}</pre>
      </div>
      
      <p class="timestamp">提交时间: {{ submitResult.timestamp }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SpForm from '../../../packages/ui/src/components/Form/Form.vue'
import SpFormItem from '../../../packages/ui/src/components/Form/FormItem.vue'
import SpInput from '../../../packages/ui/src/components/Input/input.vue'
import SpButton from '../../../packages/ui/src/components/button/button.vue'

const formRef = ref()
const submitResult = ref<any>(null)

// 完全自定义的验证规则 - 用户可以写任何逻辑
const usernameRules = (value: string) => {
  if (!value) return '用户名不能为空'
  if (value.length < 3) return '用户名至少3个字符'
  if (value.length > 20) return '用户名不能超过20个字符'
  if (!/^[a-zA-Z0-9_]+$/.test(value)) return '用户名只能包含字母、数字和下划线'
  return true
}

const emailRules = (value: string) => {
  if (!value) return '邮箱不能为空'
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(value)) return '请输入有效的邮箱地址'
  return true
}

const passwordRules = (value: string) => {
  if (!value) return '密码不能为空'
  if (value.length < 6) return '密码至少6个字符'
  if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
    return '密码必须包含大小写字母和数字'
  }
  return true
}

const confirmPasswordRules = (value: string) => {
  if (!value) return '请确认密码'
  const password = formRef.value?.veeForm?.values?.password
  if (value !== password) return '两次输入的密码不一致'
  return true
}

const ageRules = (value: string | number) => {
  if (!value) return '年龄不能为空'
  const numValue = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(numValue)) return '年龄必须是数字'
  if (numValue < 18) return '年龄不能小于18岁'
  if (numValue > 100) return '年龄不能大于100岁'
  return true
}

// 表单事件处理
const onSubmit = (values: Record<string, any>) => {
  console.log('🎉 表单提交成功! 获取到的数据:', values)
  
  // 显示一个更清晰的结果
  submitResult.value = {
    status: 'success',
    message: '✅ 表单提交成功！下面是获取到的数据：',
    formData: values, // 这就是表单的完整数据对象
    dataKeys: Object.keys(values), // 显示所有字段名
    timestamp: new Date().toLocaleString()
  }
  
  // 在控制台也输出，方便调试
  console.table(values) // 用表格形式显示数据，更清晰
}

const onInvalid = (errors: Record<string, string>) => {
  console.log('表单验证失败:', errors)
  submitResult.value = {
    status: 'error',
    message: '表单验证失败，请检查输入内容',
    errors: errors,
    timestamp: new Date().toLocaleString()
  }
}

const submitForm = async () => {
  try {
    await formRef.value?.validate()
  } catch (error) {
    console.error('表单提交失败:', error)
  }
}

const resetForm = () => {
  formRef.value?.resetFields()
  submitResult.value = null
}
</script>

<style scoped>
.form-demo {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.form-demo h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 30px;
}

.feature-description {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  margin-bottom: 20px;
  
  h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #495057;
  }
  
  p {
    margin-bottom: 10px;
    color: #6c757d;
    line-height: 1.6;
  }
  
  ul {
    margin: 10px 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;
      color: #6c757d;
      line-height: 1.5;
      
      strong {
        color: #495057;
        font-weight: 600;
      }
      
      code {
        background: #e9ecef;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 0.9em;
        color: #e83e8c;
      }
    }
  }
  
  code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.9em;
    color: #e83e8c;
  }
}

.demo-container {
  background: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e4e7ed;
}

.result-section {
  margin-top: 30px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.result-section h3 {
  margin-top: 0;
  color: #606266;
}

.result-section pre {
  background-color: #fff;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  font-size: 12px;
  color: #303133;
  overflow-x: auto;
  white-space: pre-wrap;
}

.form-data-display {
  margin-top: 20px;
}

.data-object {
  margin-bottom: 10px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.data-key {
  font-weight: bold;
}

.data-value {
  margin-left: 10px;
}

.data-keys {
  margin-top: 10px;
  margin-bottom: 10px;
}

.key-tag {
  background-color: #e6f7ff;
  padding: 2px 4px;
  border-radius: 4px;
  margin-right: 5px;
}

.json-display {
  background-color: #fff;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  font-size: 12px;
  color: #303133;
  overflow-x: auto;
  white-space: pre-wrap;
}

.timestamp {
  margin-top: 10px;
  text-align: right;
  font-size: 0.8em;
  color: #909399;
}
</style> 