<template>
  <div class="input-unstyled-demo">
    <h1>Input Unstyled Components 演示</h1>

    <!-- Base Input (Unstyled) 演示 -->
    <section class="demo-section">
      <h2>1. Base Input (Unstyled) - 纯逻辑组件</h2>
      <p>只有功能逻辑，没有样式，完全可自定义</p>

      <div class="demo-group">
        <h3>基础功能测试</h3>
        <BaseInput
          v-model:value="baseValue1"
          placeholder="无样式输入框"
          @state-change="handleStateChange"
        />

        <BaseInput
          v-model:value="baseValue2"
          placeholder="禁用状态"
          disabled
        />

        <BaseInput
          v-model:value="baseValue3"
          placeholder="只读状态"
          readonly
        />
      </div>

      <div class="demo-group">
        <h3>自定义样式的 Base Input</h3>
        <BaseInput
          v-model:value="customValue1"
          placeholder="自定义样式 1"
          class="custom-input-1"
          clearable
          @state-change="handleStateChange"
        />

        <BaseInput
          v-model:value="customValue2"
          placeholder="自定义样式 2"
          class="custom-input-2"
          show-password
        />

        <BaseInput
          v-model:value="customValue3"
          placeholder="自定义样式 3"
          class="custom-input-3"
          :maxlength="20"
          show-word-limit
        />
      </div>

      <div class="demo-group">
        <h3>特殊模式</h3>
        <BaseInput
          v-model:value="textareaValue"
          placeholder="文本域模式"
          textarea
          :rows="3"
          class="custom-textarea"
        />

        <BaseInput
          v-model:value="tabelValue"
          placeholder="格子输入模式"
          texttabel
          :td="6"
          class="custom-tabel"
        />
      </div>
    </section>

    <!-- Styled Input 演示 -->
    <section class="demo-section">
      <h2>2. Styled Input - 基于 Base Input 的样式化组件</h2>
      <p>基于 Base Input，添加了预设的样式系统</p>

      <div class="demo-group">
        <h3>基础输入框</h3>
        <StyledInput
          v-model:value="styledValue1"
          placeholder="普通输入框"
        />

        <StyledInput
          v-model:value="styledValue2"
          placeholder="可清除输入框"
          clearable
        />

        <StyledInput
          v-model:value="styledValue3"
          placeholder="密码输入框"
          show-password
        />
      </div>

      <div class="demo-group">
        <h3>尺寸变体</h3>
        <StyledInput
          v-model:value="styledValue4"
          placeholder="小尺寸"
          size="small"
        />

        <StyledInput
          v-model:value="styledValue5"
          placeholder="中等尺寸"
          size="medium"
        />

        <StyledInput
          v-model:value="styledValue6"
          placeholder="大尺寸"
          size="large"
        />
      </div>

      <div class="demo-group">
        <h3>特殊模式</h3>
        <StyledInput
          v-model:value="styledTextarea"
          placeholder="文本域"
          textarea
          :rows="4"
          :maxlength="200"
          show-word-limit
        />

        <StyledInput
          v-model:value="styledTextline"
          placeholder="文本线模式"
          textline
        />

        <StyledInput
          v-model:value="styledTabel"
          placeholder="格子输入"
          texttabel
          :td="6"
        />
      </div>

      <div class="demo-group">
        <h3>Slip 动画标签</h3>
        <StyledInput
          v-model:value="slipValue"
          placeholder="Slip 动画标签"
          slip
        />
      </div>

      <div class="demo-group">
        <h3>验证状态</h3>
        <StyledInput
          v-model:value="errorValue"
          placeholder="错误状态"
          validate-state="error"
          validate-message="这是一个错误消息"
        />

        <StyledInput
          v-model:value="warningValue"
          placeholder="警告状态"
          validate-state="warning"
          validate-message="这是一个警告消息"
        />

        <StyledInput
          v-model:value="successValue"
          placeholder="成功状态"
          validate-state="success"
          validate-message="验证成功"
        />
      </div>
    </section>

    <!-- 状态监控 -->
    <section class="demo-section">
      <h2>3. 状态监控</h2>
      <div class="state-monitor">
        <h3>当前输入框状态:</h3>
        <pre>{{ JSON.stringify(currentState, null, 2) }}</pre>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import BaseInput from '../../../packages/ui/src/components/base-input/base-input.vue'
  import StyledInput from '../../../packages/ui/src/components/input/input.vue'
  import type { InputState } from '../../../packages/ui/src/components/base-input/index'

  // Base Input 值
  const baseValue1 = ref('')
  const baseValue2 = ref('')
  const baseValue3 = ref('只读内容')

  // 自定义样式值
  const customValue1 = ref('')
  const customValue2 = ref('')
  const customValue3 = ref('')

  // 特殊模式值
  const textareaValue = ref('')
  const tabelValue = ref('')

  // Styled Input 值
  const styledValue1 = ref('')
  const styledValue2 = ref('')
  const styledValue3 = ref('')
  const styledValue4 = ref('')
  const styledValue5 = ref('')
  const styledValue6 = ref('')

  // 特殊模式值
  const styledTextarea = ref('')
  const styledTextline = ref('')
  const styledTabel = ref('')
  const slipValue = ref('')

  // 验证状态值
  const errorValue = ref('')
  const warningValue = ref('')
  const successValue = ref('')

  // 状态监控
  const currentState = ref<InputState | null>(null)

  const handleStateChange = (state: InputState) => {
    currentState.value = state
    console.log('Input state changed:', state)
  }
</script>

<style scoped>
  .input-unstyled-demo {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .demo-section {
    margin-bottom: 3rem;
    padding: 2rem;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    background: #fafbfc;
  }

  .demo-section h2 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
    font-size: 1.5rem;
  }

  .demo-section p {
    margin: 0 0 1.5rem 0;
    color: #666;
  }

  .demo-group {
    margin-bottom: 2rem;
  }

  .demo-group h3 {
    margin: 0 0 1rem 0;
    color: #34495e;
    font-size: 1.2rem;
  }

  .demo-group > * + * {
    margin-top: 1rem;
  }

  .state-monitor {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 1rem;
  }

  .state-monitor h3 {
    margin: 0 0 0.5rem 0;
    color: #495057;
  }

  .state-monitor pre {
    margin: 0;
    font-size: 0.875rem;
    color: #6c757d;
    background: white;
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #e9ecef;
  }

  /* 自定义 Base Input 样式 */
  .custom-input-1 .base-input__wrapper {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
    border: none;
    border-radius: 25px;
    padding: 0 20px;
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
  }

  .custom-input-1 .base-input__inner {
    color: white;
  }

  .custom-input-1 .base-input__inner::placeholder {
    color: rgba(255, 255, 255, 0.7);
  }

  .custom-input-2 .base-input__wrapper {
    background: transparent;
    border: 2px solid #4ecdc4;
    border-radius: 0;
    color: #4ecdc4;
    transition: all 0.3s ease;
  }

  .custom-input-2 .base-input__wrapper:focus-within {
    background: #4ecdc4;
    color: white;
  }

  .custom-input-2 .base-input__inner:focus {
    color: white;
  }

  .custom-input-3 .base-input__wrapper {
    background: #2c3e50;
    border: none;
    border-radius: 0;
    color: white;
    position: relative;
    overflow: hidden;
  }

  .custom-input-3 .base-input__wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s ease;
  }

  .custom-input-3 .base-input__wrapper:focus-within::before {
    left: 100%;
  }

  .custom-textarea .base-input__textarea {
    background: #f8f9fa;
    border: 2px dashed #6c757d;
    border-radius: 8px;
    padding: 1rem;
    font-family: 'Courier New', monospace;
  }

  .custom-tabel .base-input__tabel-cell {
    width: 40px;
    height: 50px;
    border: 2px solid #7c3aed;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: bold;
    font-size: 18px;
  }

  .custom-tabel .base-input__tabel-cell:focus {
    border-color: #ffd700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  }
</style>
