/**
 * Speed UI 初始化配置
 * 提供可选的初始化方法，让用户自主选择需要的功能
 */

import { updateCSSVariables as updateSizeVariables } from './speedConfig'
import { applyTheme } from './speedThemeConfig'

export interface InitOptions {
  /** 是否初始化尺寸系统 */
  enableSizeSystem?: boolean
  /** 是否初始化默认主题 */
  enableDefaultTheme?: boolean
  /** 默认主题色 */
  defaultTheme?: string
}

/**
 * 初始化 Speed UI 配置
 * @param options 初始化选项
 */
export function initSpeedUI(options: InitOptions = {}) {
  const {
    enableSizeSystem = false,
    enableDefaultTheme = false,
    defaultTheme = 'blue'
  } = options

  if (typeof document === 'undefined') {
    console.warn('initSpeedUI should be called in browser environment')
    return
  }

  // 初始化尺寸系统
  if (enableSizeSystem) {
    updateSizeVariables()
    console.log('✅ Speed UI 尺寸系统已初始化')
  }

  // 初始化默认主题
  if (enableDefaultTheme) {
    applyTheme(defaultTheme)
    console.log(`✅ Speed UI 默认主题已设置: ${defaultTheme}`)
  }
}

/**
 * 快速初始化（启用所有功能）
 */
export function initSpeedUIFull() {
  initSpeedUI({
    enableSizeSystem: true,
    enableDefaultTheme: true,
  })
}

/**
 * 仅初始化主题系统
 */
export function initThemeOnly(theme: string = 'blue') {
  initSpeedUI({
    enableSizeSystem: false,
    enableDefaultTheme: true,
    defaultTheme: theme,
  })
}

/**
 * 仅初始化尺寸系统
 */
export function initSizeOnly() {
  initSpeedUI({
    enableSizeSystem: true,
    enableDefaultTheme: false,
  })
}
