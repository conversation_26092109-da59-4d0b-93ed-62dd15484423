<template>
  <div class="test-prefix-suffix">
    <h1>测试前缀后缀重构</h1>
    
    <div class="test-section">
      <h2>基础输入框（无前缀后缀）</h2>
      <SpInputField
        v-model:value="basicValue"
        label="基础输入"
        placeholder="请输入内容"
      />
      <p>值: {{ basicValue }}</p>
    </div>

    <div class="test-section">
      <h2>带前缀的输入框</h2>
      <SpInputField
        v-model:value="prefixValue"
        label="带前缀输入"
        placeholder="请输入内容"
        prefix="$"
      />
      <p>值: {{ prefixValue }}</p>
    </div>

    <div class="test-section">
      <h2>带后缀的输入框</h2>
      <SpInputField
        v-model:value="suffixValue"
        label="带后缀输入"
        placeholder="请输入内容"
        suffix="元"
      />
      <p>值: {{ suffixValue }}</p>
    </div>

    <div class="test-section">
      <h2>同时带前缀和后缀的输入框</h2>
      <SpInputField
        v-model:value="bothValue"
        label="前缀后缀输入"
        placeholder="请输入内容"
        prefix="$"
        suffix="USD"
      />
      <p>值: {{ bothValue }}</p>
    </div>

    <div class="test-section">
      <h2>自定义前缀插槽</h2>
      <SpInputField
        v-model:value="customPrefixValue"
        label="自定义前缀"
        placeholder="请输入内容"
      >
        <template #prefix>
          <span style="color: blue; font-weight: bold;">💰</span>
        </template>
      </SpInputField>
      <p>值: {{ customPrefixValue }}</p>
    </div>

    <div class="test-section">
      <h2>自定义后缀插槽</h2>
      <SpInputField
        v-model:value="customSuffixValue"
        label="自定义后缀"
        placeholder="请输入内容"
      >
        <template #suffix>
          <span style="color: green; font-weight: bold;">✓</span>
        </template>
      </SpInputField>
      <p>值: {{ customSuffixValue }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { SpInputField } from '@speed-ui/ui'

const basicValue = ref('')
const prefixValue = ref('')
const suffixValue = ref('')
const bothValue = ref('')
const customPrefixValue = ref('')
const customSuffixValue = ref('')
</script>

<style scoped>
.test-prefix-suffix {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.test-section h2 {
  margin-top: 0;
  color: #333;
}

.test-section p {
  margin-top: 10px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-family: monospace;
}
</style>
