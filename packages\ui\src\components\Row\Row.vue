<template>
  <component
    :is="tag"
    :class="rowClass"
    :style="rowStyle"
  >
    <slot></slot>
  </component>
</template>

<script setup lang="ts">
import { computed, provide } from 'vue'

export interface RowProps {
  /**
   * 栅格间隔
   */
  gutter?: number
  /**
   * flex 布局下的水平排列方式
   */
  justify?: 'start' | 'end' | 'center' | 'space-around' | 'space-between' | 'space-evenly'
  /**
   * flex 布局下的垂直排列方式
   */
  align?: 'top' | 'middle' | 'bottom'
  /**
   * 自定义元素标签
   */
  tag?: string
}

const props = withDefaults(defineProps<RowProps>(), {
  gutter: 0,
  justify: 'start',
  align: 'top',
  tag: 'div',
})

// 提供给子组件使用
provide('spRow', {
  gutter: computed(() => props.gutter),
})

const rowClass = computed(() => [
  'sp-row',
  props.justify !== 'start' ? `sp-row--justify-${props.justify}` : '',
  props.align !== 'top' ? `sp-row--align-${props.align}` : '',
])

const rowStyle = computed(() => {
  const styles: Record<string, string> = {}
  
  if (props.gutter) {
    const gutterValue = props.gutter / 2
    styles.marginLeft = `-${gutterValue}px`
    styles.marginRight = `-${gutterValue}px`
  }
  
  return styles
})
</script>

<script lang="ts">
export default {
  name: 'SpRow',
}
</script>

<style lang="scss" scoped>
.sp-row {
  display: flex;
  flex-wrap: wrap;
  position: relative;
  box-sizing: border-box;

  // 水平对齐方式
  &--justify-center {
    justify-content: center;
  }

  &--justify-end {
    justify-content: flex-end;
  }

  &--justify-space-between {
    justify-content: space-between;
  }

  &--justify-space-around {
    justify-content: space-around;
  }

  &--justify-space-evenly {
    justify-content: space-evenly;
  }

  // 垂直对齐方式
  &--align-middle {
    align-items: center;
  }

  &--align-bottom {
    align-items: flex-end;
  }
}
</style> 