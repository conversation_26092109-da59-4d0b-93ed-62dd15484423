<template>
  <div class="list-demo">
    <h1>📝 List 列表组件演示</h1>
    <p>可复用的基础列表组件，为 Select、Dropdown 等高级组件提供基础设施</p>

    <!-- 基础列表 -->
    <section class="demo-section">
      <h2>基础列表 (Basic List)</h2>
      <div class="demo-container">
        <sp-list style="width: 300px">
          <sp-list-item
            item-key="item1"
            title="第一项"
            description="这是第一项的描述信息"
          />
          <sp-list-item
            item-key="item2"
            title="第二项"
            description="这是第二项的描述信息"
          />
          <sp-list-item
            item-key="item3"
            title="第三项"
          />
          <sp-list-item
            item-key="item4"
            title="禁用项"
            description="这是一个禁用的项目"
            :disabled="true"
          />
        </sp-list>
      </div>
    </section>

    <!-- 可选择列表 -->
    <section class="demo-section">
      <h2>可选择列表 (Selectable List)</h2>
      <div class="demo-container">
        <sp-list
          v-model:selected-keys="selectedKeys1"
          :selectable="true"
          style="width: 300px"
          @select="onSelect"
        >
          <sp-list-item
            item-key="select1"
            title="选项 A222"
            description="单选模式下的第一个选项"
          />
          <sp-list-item
            item-key="select2"
            title="选项 B"
            description="单选模式下的第二个选项"
          />
          <sp-list-item
            item-key="select3"
            title="选项 C"
            description="单选模式下的第三个选项"
          />
        </sp-list>
        <div class="demo-info">
          <h4>选中状态:</h4>
          <code>{{ JSON.stringify(selectedKeys1) }}</code>
        </div>
      </div>
    </section>

    <!-- 多选列表 -->
    <section class="demo-section">
      <h2>多选列表 (Multiple Selection)</h2>
      <div class="demo-container">
        <sp-list
          v-model:selected-keys="selectedKeys2"
          :selectable="true"
          :multiple="true"
          style="width: 300px"
        >
          <sp-list-item
            item-key="multi1"
            title="多选项 1"
            description="支持多选的第一个选项"
          />
          <sp-list-item
            item-key="multi2"
            title="多选项 2"
            description="支持多选的第二个选项"
          />
          <sp-list-item
            item-key="multi3"
            title="多选项 3"
            description="支持多选的第三个选项"
          />
          <sp-list-item
            item-key="multi4"
            title="多选项 4"
            description="支持多选的第四个选项"
          />
        </sp-list>
        <div class="demo-info">
          <h4>选中状态:</h4>
          <code>{{ JSON.stringify(selectedKeys2) }}</code>
        </div>
      </div>
    </section>

    <!-- 尺寸变体 -->
    <section class="demo-section">
      <h2>尺寸变体 (Size Variants)</h2>
      <div class="size-demo">
        <div class="size-item">
          <h3>Small</h3>
          <sp-list
            size="small"
            bordered
            style="width: 200px"
          >
            <sp-list-item
              item-key="s1"
              title="小尺寸项目"
            />
            <sp-list-item
              item-key="s2"
              title="紧凑布局"
            />
          </sp-list>
        </div>
        <div class="size-item">
          <h3>Medium (默认)</h3>
          <sp-list
            size="medium"
            bordered
            style="width: 200px"
          >
            <sp-list-item
              item-key="m1"
              title="中等尺寸项目"
            />
            <sp-list-item
              item-key="m2"
              title="标准布局"
            />
          </sp-list>
        </div>
        <div class="size-item">
          <h3>Large</h3>
          <sp-list
            size="large"
            bordered
            style="width: 200px"
          >
            <sp-list-item
              item-key="l1"
              title="大尺寸项目"
            />
            <sp-list-item
              item-key="l2"
              title="宽松布局"
            />
          </sp-list>
        </div>
      </div>
    </section>

    <!-- 自定义内容 -->
    <section class="demo-section">
      <h2>自定义内容 (Custom Content)</h2>
      <div class="demo-container">
        <sp-list
          bordered
          style="width: 400px"
        >
          <sp-list-item item-key="custom1">
            <template #suffix>
              <span style="color: #999; font-size: 12px">2023-12-25</span>
            </template>
            <div>
              <div style="font-weight: 500; margin-bottom: 4px">📧 新邮件</div>
              <div style="color: #666; font-size: 13px">
                来自 John Doe 的重要邮件
              </div>
            </div>
          </sp-list-item>
          <sp-list-item item-key="custom2">
            <template #suffix>
              <span style="color: #52c41a; font-size: 12px">✓ 已完成</span>
            </template>
            <div>
              <div style="font-weight: 500; margin-bottom: 4px">
                📋 任务更新
              </div>
              <div style="color: #666; font-size: 13px">
                项目 Alpha 已完成第一阶段
              </div>
            </div>
          </sp-list-item>
          <sp-list-item item-key="custom3">
            <template #suffix>
              <span style="color: #ff4d4f; font-size: 12px">⚠ 紧急</span>
            </template>
            <div>
              <div style="font-weight: 500; margin-bottom: 4px">
                🚨 系统警告
              </div>
              <div style="color: #666; font-size: 13px">
                服务器 CPU 使用率超过 80%
              </div>
            </div>
          </sp-list-item>
        </sp-list>
      </div>
    </section>

    <!-- 带边框和无分割线 -->
    <section class="demo-section">
      <h2>样式选项 (Style Options)</h2>
      <div class="demo-container">
        <div style="display: flex; gap: 20px">
          <div>
            <h3>带边框</h3>
            <sp-list
              bordered
              style="width: 200px"
            >
              <sp-list-item
                item-key="b1"
                title="边框样式"
              />
              <sp-list-item
                item-key="b2"
                title="项目 2"
              />
              <sp-list-item
                item-key="b3"
                title="项目 3"
              />
            </sp-list>
          </div>
          <div>
            <h3>无分割线</h3>
            <sp-list
              :split="false"
              style="width: 200px"
            >
              <sp-list-item
                item-key="ns1"
                title="无分割线"
              />
              <sp-list-item
                item-key="ns2"
                title="项目 2"
              />
              <sp-list-item
                item-key="ns3"
                title="项目 3"
              />
            </sp-list>
          </div>
          <div>
            <h3>无 hover 效果</h3>
            <sp-list
              :hoverable="false"
              style="width: 200px"
            >
              <sp-list-item
                key="nh1"
                title="无 hover"
              />
              <sp-list-item
                key="nh2"
                title="项目 2"
              />
              <sp-list-item
                key="nh3"
                title="项目 3"
              />
            </sp-list>
          </div>
        </div>
      </div>
    </section>

    <!-- 主题演示 -->
    <section class="demo-section">
      <h2>🎨 主题切换演示</h2>
      <div class="theme-demo">
        <div class="theme-controls">
          <button
            @click="changeTheme('#667eea')"
            class="theme-btn"
            style="background: #667eea"
          >
            默认紫色
          </button>
          <button
            @click="changeTheme('#52c41a')"
            class="theme-btn"
            style="background: #52c41a"
          >
            成功绿色
          </button>
          <button
            @click="changeTheme('#ff4d4f')"
            class="theme-btn"
            style="background: #ff4d4f"
          >
            危险红色
          </button>
          <button
            @click="changeTheme('#1890ff')"
            class="theme-btn"
            style="background: #1890ff"
          >
            信息蓝色
          </button>
          <button
            @click="changeTheme('#faad14')"
            class="theme-btn"
            style="background: #faad14"
          >
            警告橙色
          </button>
        </div>
        <sp-list
          v-model:selected-keys="selectedKeys3"
          :selectable="true"
          :multiple="true"
          bordered
          style="width: 300px; margin-top: 16px"
        >
          <sp-list-item
            item-key="theme1"
            title="主题选项 1"
            description="选中时会显示主题色"
          />
          <sp-list-item
            item-key="theme2"
            title="主题选项 2"
            description="支持动态主题切换"
          />
          <sp-list-item
            item-key="theme3"
            title="主题选项 3"
            description="CSS 变量驱动的主题系统"
          />
        </sp-list>
      </div>
    </section>

    <!-- 复用说明 -->
    <section class="demo-section">
      <h2>🚀 未来复用场景</h2>
      <div class="reuse-info">
        <div class="reuse-item">
          <strong>Select 组件:</strong>
          List 作为下拉选项容器，ListItem 作为选项
        </div>
        <div class="reuse-item">
          <strong>Dropdown 组件:</strong>
          List 作为下拉菜单容器，支持自定义内容
        </div>
        <div class="reuse-item">
          <strong>Transfer 组件:</strong>
          多个 List 组件组合，实现穿梭框功能
        </div>
        <div class="reuse-item">
          <strong>AutoComplete 组件:</strong>
          List 作为搜索建议列表
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  // 选中状态管理
  const selectedKeys1 = ref<string[]>(['select2'])
  const selectedKeys2 = ref<string[]>(['multi1', 'multi2', 'multi3'])
  const selectedKeys3 = ref<string[]>(['theme1'])

  // 选择事件处理
  const onSelect = (key: string, selected: boolean, selectedKeys: string[]) => {
    debugger
    console.log('选择事件:', { key, selected, selectedKeys })
    console.log('当前 selectedKeys1:', selectedKeys1.value)
  }

  // 主题切换
  const changeTheme = (color: string) => {
    // 计算辅助颜色
    const root = document.documentElement

    // 设置主色
    root.style.setProperty('--sp-color-primary', color)

    // 计算 hover 颜色 (稍微亮一些)
    const hoverColor = lightenColor(color, 10)
    root.style.setProperty('--sp-color-primary-hover', hoverColor)

    // 计算 active 颜色 (稍微暗一些)
    const activeColor = darkenColor(color, 10)
    root.style.setProperty('--sp-color-primary-active', activeColor)

    // 计算浅色背景
    const lightestColor = lightenColor(color, 45)
    root.style.setProperty('--sp-color-primary-lightest', lightestColor)

    const lightColor = lightenColor(color, 30)
    root.style.setProperty('--sp-color-primary-light', lightColor)

    // 计算禁用颜色
    const disabledColor = lightenColor(color, 35)
    root.style.setProperty('--sp-color-primary-disabled', disabledColor)
  }

  // 颜色工具函数
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null
  }

  const rgbToHex = (r: number, g: number, b: number) => {
    return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
  }

  const lightenColor = (hex: string, percent: number) => {
    const rgb = hexToRgb(hex)
    if (!rgb) return hex

    const factor = percent / 100
    const r = Math.min(255, Math.round(rgb.r + (255 - rgb.r) * factor))
    const g = Math.min(255, Math.round(rgb.g + (255 - rgb.g) * factor))
    const b = Math.min(255, Math.round(rgb.b + (255 - rgb.b) * factor))

    return rgbToHex(r, g, b)
  }

  const darkenColor = (hex: string, percent: number) => {
    const rgb = hexToRgb(hex)
    if (!rgb) return hex

    const factor = 1 - percent / 100
    const r = Math.max(0, Math.round(rgb.r * factor))
    const g = Math.max(0, Math.round(rgb.g * factor))
    const b = Math.max(0, Math.round(rgb.b * factor))

    return rgbToHex(r, g, b)
  }
</script>

<style scoped>
  .list-demo {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .list-demo h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 10px;
  }

  .list-demo > p {
    text-align: center;
    color: #7f8c8d;
    margin-bottom: 40px;
  }

  .demo-section {
    margin-bottom: 50px;
    padding: 30px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background: #fafafa;
  }

  .demo-section h2 {
    color: #34495e;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #ecf0f1;
  }

  .demo-container {
    display: flex;
    gap: 30px;
    align-items: flex-start;
  }

  .demo-info {
    flex: 1;
    min-width: 200px;
  }

  .demo-info h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
  }

  .demo-info code {
    display: block;
    padding: 10px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    color: #495057;
    word-break: break-all;
  }

  .size-demo {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
  }

  .size-item h3 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 14px;
  }

  .theme-demo {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .theme-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .theme-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    color: white;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: opacity 0.2s;
  }

  .theme-btn:hover {
    opacity: 0.8;
  }

  .reuse-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
  }

  .reuse-item {
    padding: 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #ddd;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .reuse-item strong {
    color: #2c3e50;
  }
</style>
