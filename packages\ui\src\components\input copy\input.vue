<!--
  Input.vue - 接口层
  职责：对外暴露API，接收props和events，处理业务逻辑
-->

<template>
  <InputCore
    ref="inputCoreRef"
    mode="input"
    :value="currentValue"
    :variant="props.variant"
    :effect="props.effect"
    :size="props.size"
    :placeholder="props.placeholder"
    :disabled="props.disabled"
    :readonly="props.readonly"
    :clearable="props.clearable"
    :prefix-icon="props.prefixIcon"
    :suffix-icon="props.suffixIcon"
    :loading="props.loading"
    :focused="focused"
    :has-value="hasValueComputed"
    :error="props.error || computedValidateState === 'error'"
    :validate-state="computedValidateState"
    :validate-message="computedValidateMessage"
    v-bind="inputCoreProps"
    @click="handleWrapperClick"
    @focus="handleFocus"
    @blur="handleBlur"
    @clear="handleClear"
  >
    <!-- 前缀插槽 -->
    <template #prefix>
      <slot name="prefix" />
    </template>

    <!-- 后缀插槽 -->
    <template #suffix>
      <!-- 密码显示切换按钮 -->
      <sp-icon
        v-if="showPasswordIcon"
        :name="passwordIconName"
        :size="iconSize"
        :class="passwordIconClasses"
        @click.stop.prevent="togglePassword"
      />

      <!-- 字数统计 -->
      <span
        v-if="props.showWordLimit && props.maxlength"
        :class="wordCountClasses"
      >
        {{ wordCount }}/{{ props.maxlength }}
      </span>

      <slot name="suffix" />
    </template>

    <!-- 内容区域 - 实际的 input 元素 -->
    <template #default>
      <input
        ref="inputRef"
        :type="actualType"
        :value="currentValue"
        :placeholder="props.placeholder"
        :disabled="props.disabled"
        :readonly="props.readonly"
        :maxlength="props.maxlength"
        class="sp-input__inner"
        @input="handleInput"
        @change="handleChange"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
      />
    </template>
  </InputCore>
</template>

<script setup lang="ts">
  import { ref, computed, inject } from 'vue'
  import { useVModel } from '@speed-ui/hooks'
  import InputCore from './inputcore'
  import SpIcon from '../icon/Icon.vue'
  import { useInputStyles } from '../../composables/useInputStyles'
  import type { InputProps, InputEmits } from './types'
  import { inputPropsDefaults } from './types'

  /**
   * Input 组件 - 接口层
   *
   * 简洁的两层架构设计：
   * input.vue (接口层) → inputcore.tsx (基础层)
   */

  const props = withDefaults(defineProps<InputProps>(), inputPropsDefaults)
  const emit = defineEmits<InputEmits>()

  // 表单字段注入（用于表单验证）
  const formItemField = inject<any>('spFormItemField', null)

  // 组件引用
  const inputCoreRef = ref<InstanceType<typeof InputCore> | null>(null)
  const inputRef = ref<HTMLInputElement | null>(null)

  // 状态管理
  const focused = ref(false)
  const passwordVisible = ref(false)

  // 使用 useVModel 进行双向绑定
  const {
    value: currentValue,
    updateValue,
    hasValue: hasValueComputed,
  } = useVModel<string | number | undefined>(props, emit, {
    prop: 'value',
    event: 'update:value',
    defaultValue: props.value,
    formField: formItemField,
    onAfterUpdate: newValue => {
      emit('change', newValue)
    },
    debug: false,
  })

  // 验证状态 - 优先使用表单字段
  const computedValidateState = computed(() => {
    if (formItemField && formItemField.validateState) {
      return formItemField.validateState.value
    }
    return props.validateState
  })

  const computedValidateMessage = computed(() => {
    if (formItemField && formItemField.errors.value.length > 0) {
      return formItemField.errors.value[0]
    }
    return props.validateMessage
  })

  // 计算属性
  const actualType = computed(() => {
    if (props.type === 'password' && props.showPassword) {
      return passwordVisible.value ? 'text' : 'password'
    }
    return props.type
  })

  const showPasswordIcon = computed(() => {
    return (
      props.type === 'password' && props.showPassword && hasValueComputed.value
    )
  })

  const passwordIconName = computed(() => {
    return passwordVisible.value ? 'EyeOff' : 'Eye'
  })

  const wordCount = computed(() => {
    const value = currentValue.value
    return typeof value === 'string' ? value.length : 0
  })

  // 计算显示逻辑
  const computedDisabled = computed(() => props.disabled)
  const showPrefix = computed(() => !!props.prefixIcon)
  const showSuffix = computed(() => {
    return (
      !!props.suffixIcon ||
      !!props.clearable ||
      showPasswordIcon.value ||
      !!(props.showWordLimit && props.maxlength)
    )
  })

  // 使用样式 composable
  const {
    inputCoreProps,
    iconSize,
    clearIconClasses,
    passwordIconClasses,
    wordCountClasses,
  } = useInputStyles(props, {
    computedDisabled,
    isFocused: focused,
    hasValue: hasValueComputed,
    showPrefix,
    showSuffix,
  })

  // 事件处理
  const handleWrapperClick = () => {
    if (props.disabled || props.readonly) return
    inputRef.value?.focus()
  }

  const handleInput = (event: Event) => {
    const target = event.target as HTMLInputElement
    updateValue(target.value)
    emit('input', event)
  }

  const handleChange = (event: Event) => {
    // change 事件已经通过 useVModel 的 onAfterUpdate 处理
    emit('change', currentValue.value)
  }

  const handleFocus = (event: FocusEvent) => {
    focused.value = true
    emit('focus', event)
  }

  const handleBlur = (event: FocusEvent) => {
    focused.value = false
    emit('blur', event)

    // 触发表单验证
    if (formItemField) {
      formItemField.handleBlur()
    }
  }

  const handleKeydown = (event: KeyboardEvent) => {
    emit('keydown', event)
  }

  const handleClear = () => {
    updateValue('')
    emit('clear')
    inputRef.value?.focus()
  }

  const togglePassword = () => {
    passwordVisible.value = !passwordVisible.value
  }

  // 暴露的方法
  const focus = () => {
    inputRef.value?.focus()
  }

  const blur = () => {
    inputRef.value?.blur()
  }

  const select = () => {
    inputRef.value?.select()
  }

  const clear = () => {
    handleClear()
  }

  // 暴露给外部使用
  defineExpose({
    /** 使输入框获得焦点 */
    focus,
    /** 使输入框失去焦点 */
    blur,
    /** 选中输入框中的文字 */
    select,
    /** 清空输入框 */
    clear,
    /** 输入框元素引用 */
    get input() {
      return inputRef.value || null
    },
    /** 包装器元素引用 */
    get wrapper() {
      return inputCoreRef.value?.$refs?.wrapperRef || null
    },
  })
</script>

<script lang="ts">
  export default {
    name: 'SpInputNew',
  }
</script>
