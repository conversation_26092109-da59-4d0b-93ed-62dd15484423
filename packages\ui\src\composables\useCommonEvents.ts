/**
 * 通用事件处理 Composables
 * 用于在双层组件结构中管理事件的分发和合并
 */

import { provide, inject, computed, type ComputedRef } from 'vue'

// 通用事件类型
const COMMON_EVENTS = ['click', 'mouseenter', 'mouseleave', 'mousedown', 'mouseup', 'focus', 'blur'] as const

type CommonEvent = typeof COMMON_EVENTS[number]
type EventHandler = (event: Event) => void
type EventHandlers = Partial<Record<CommonEvent, EventHandler>>

/**
 * 提供者函数 - 在外层组件中使用
 * 从 attrs 中提取通用事件处理器并提供给子组件
 */
export function provideCommonEvents(attrs: Record<string, any>): EventHandlers {
  // 从 attrs 中提取通用事件处理器
  const eventHandlers = COMMON_EVENTS.reduce((handlers, event) => {
    const handlerName = `on${event.charAt(0).toUpperCase() + event.slice(1)}`
    const handler = attrs[handlerName]
    if (handler && typeof handler === 'function') {
      handlers[event] = handler
    }
    return handlers
  }, {} as EventHandlers)

  // 提供事件处理器给子组件
  provide('commonEventHandlers', eventHandlers)

  return eventHandlers
}

/**
 * 消费者函数 - 在子组件中使用
 * 注入上层提供的事件处理器，并与本地处理器合并
 */
export function useCommonEvents(localHandlers: EventHandlers = {}): ComputedRef<EventHandlers> {
  // 注入上层提供的事件处理器
  const injectedHandlers = inject<EventHandlers>('commonEventHandlers', {})

  console.log('🔍 useCommonEvents - injected handlers:', injectedHandlers)
  console.log('🔍 useCommonEvents - local handlers:', localHandlers)

  // 合并事件处理器（本地优先，链式调用）
  return computed(() => {
    const merged: EventHandlers = { ...injectedHandlers }

    // 本地处理器与注入的处理器合并
    Object.keys(localHandlers).forEach(eventKey => {
      const event = eventKey as CommonEvent
      const existingHandler = merged[event]
      const localHandler = localHandlers[event]

      if (existingHandler && localHandler) {
        // 链式调用：先执行注入的处理器，再执行本地处理器
        merged[event] = (e: Event) => {
          console.log(`🔗 Chaining ${event}: injected + local`)
          existingHandler(e)
          localHandler(e)
        }
      } else if (localHandler) {
        // 只有本地处理器
        console.log(`📝 Using local handler for ${event}`)
        merged[event] = localHandler
      }
      // 如果只有注入的处理器，保持原样
    })

    console.log('🎯 useCommonEvents - merged handlers:', merged)
    return merged
  })
}

/**
 * 事件过滤器 - 用于在不同层级间分配事件
 * @param handlers 原始事件处理器
 * @param excludeEvents 要排除的事件
 * @returns 过滤后的事件处理器
 */
export function filterEvents(
  handlers: EventHandlers, 
  excludeEvents: CommonEvent[]
): ComputedRef<EventHandlers> {
  return computed(() => {
    const filtered: EventHandlers = {}
    
    Object.keys(handlers).forEach(eventKey => {
      const event = eventKey as CommonEvent
      if (!excludeEvents.includes(event)) {
        filtered[event] = handlers[event]
      }
    })
    
    return filtered
  })
}

/**
 * 事件选择器 - 只选择指定的事件
 * @param handlers 原始事件处理器
 * @param includeEvents 要包含的事件
 * @returns 选择后的事件处理器
 */
export function selectEvents(
  handlers: EventHandlers, 
  includeEvents: CommonEvent[]
): ComputedRef<EventHandlers> {
  return computed(() => {
    const selected: EventHandlers = {}
    
    includeEvents.forEach(event => {
      if (handlers[event]) {
        selected[event] = handlers[event]
      }
    })
    
    return selected
  })
}
