<template>
  <div style="padding: 20px">
    <h2>Select 组件测试 - 基于 sp-input</h2>

    <div style="margin: 20px 0">
      <h3>1. 基础单选</h3>
      <Select
        v-model:value="singleValue"
        :options="options"
        placeholder="请选择一个选项"
        clearable
        @change="handleSingleChange"
      />
      <p>选中值: {{ singleValue }}</p>
    </div>

    <div style="margin: 20px 0">
      <h3>2. 多选模式</h3>
      <Select
        v-model:value="multipleValue"
        :options="options"
        placeholder="请选择多个选项"
        multiple
        clearable
        @change="handleMultipleChange"
      />
      <p>选中值: {{ multipleValue }}</p>
    </div>

    <div style="margin: 20px 0">
      <h3>3. 禁用状态</h3>
      <Select
        v-model:value="disabledValue"
        :options="options"
        placeholder="禁用状态"
        disabled
      />
    </div>

    <div style="margin: 20px 0">
      <h3>4. 不同尺寸</h3>
      <div style="margin: 10px 0">
        <label>小尺寸:</label>
        <Select
          v-model:value="smallValue"
          :options="options"
          placeholder="小尺寸"
          size="small"
        />
      </div>
      <div style="margin: 10px 0">
        <label>默认尺寸:</label>
        <Select
          v-model:value="defaultValue"
          :options="options"
          placeholder="默认尺寸"
          size="default"
        />
      </div>
      <div style="margin: 10px 0">
        <label>大尺寸:</label>
        <Select
          v-model:value="largeValue"
          :options="options"
          placeholder="大尺寸"
          size="large"
        />
      </div>
    </div>

    <div style="margin: 20px 0">
      <h3>5. 错误状态</h3>
      <Select
        v-model:value="errorValue"
        :options="options"
        placeholder="错误状态"
        validate-state="error"
        validate-message="这是一个错误信息"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import Select from './packages/ui/src/components/select/select.vue'

  // 测试数据
  const options = [
    { label: '选项1', value: '1' },
    { label: '选项2', value: '2' },
    { label: '选项3', value: '3' },
    { label: '禁用选项', value: '4', disabled: true },
    { label: '选项5', value: '5' },
  ]

  // 响应式数据
  const singleValue = ref('')
  const multipleValue = ref([])
  const disabledValue = ref('')
  const smallValue = ref('')
  const defaultValue = ref('')
  const largeValue = ref('')
  const errorValue = ref('')

  // 事件处理
  const handleSingleChange = (value: any) => {
    console.log('单选值变化:', value)
  }

  const handleMultipleChange = (value: any) => {
    console.log('多选值变化:', value)
  }
</script>
