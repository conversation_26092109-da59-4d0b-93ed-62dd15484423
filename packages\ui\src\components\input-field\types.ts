/**
 * InputField 组件类型定义
 * 继承 Input 组件所有功能，并添加表单集成和浮动标签
 */

import type { InputProps, InputEmits } from '../input/types'

/** InputField 专有属性 */
export interface InputFieldOwnProps {
  /** 标签文本 - 支持浮动动画 */
  label?: string
  /** 表单字段名 - 用于表单验证 */
  name: string
  /** 验证规则 */
  rules?: any
  /** 是否必填 */
  required?: boolean
  /** 是否显示验证消息 */
  showMessage?: boolean
  /** 帮助文本 */
  helperText?: string
  /** 标签是否始终浮动（不回落） */
  persistentLabel?: boolean
  /** 前缀文本或图标（位于输入框左侧，prepend之后） */
  prefix?: string
  /** 后缀文本或图标（位于输入框右侧，append之前） */
  suffix?: string

  // ===== 短信验证码相关属性 =====
  /** 短信验证码倒计时时长（秒），默认60秒 */
  smsCountdown?: number
  /** 获取验证码按钮文字，默认"获取验证码" */
  smsSendText?: string
  /** 重新获取验证码按钮文字，默认"重新获取" */
  smsResendText?: string
}

/** InputField 完整属性，继承 Input 的所有属性 */
export interface InputFieldProps extends InputProps, InputFieldOwnProps {
  /** 表单字段名 - 用于表单验证（覆盖 InputFieldOwnProps 中的定义） */
  name: string
}

/** InputField 事件 */
export interface InputFieldEmits extends InputEmits {
  /** 验证事件 */
  (e: 'validate', name: string, isValid: boolean, message: string): void
  /** 外部前置图标点击事件 */
  (e: 'click:prepend', event: MouseEvent): void
  /** 内部前置图标点击事件 */
  (e: 'click:prepend-inner', event: MouseEvent): void
  /** 外部后置图标点击事件 */
  (e: 'click:append', event: MouseEvent): void
  /** 内部后置图标点击事件 */
  (e: 'click:append-inner', event: MouseEvent): void
  /** 国家选择事件 */
  (e: 'country-select', country: any): void

  // ===== 短信验证码相关事件 =====
  /** 短信验证码发送事件 */
  (e: 'sms-send'): void
  /** 短信验证码倒计时开始事件 */
  (e: 'sms-start', countdown: number): void
  /** 短信验证码倒计时进行中事件 */
  (e: 'sms-tick', remaining: number): void
  /** 短信验证码倒计时结束事件 */
  (e: 'sms-end'): void
}

/** InputField 实例方法 */
export interface InputFieldInstance {
  /** 使输入框获得焦点 */
  focus: () => void
  /** 使输入框失去焦点 */
  blur: () => void
  /** 选中输入框中的文字 */
  select: () => void
  /** 清空输入框 */
  clear: () => void
  /** 验证字段 */
  validate: () => Promise<boolean>
  /** 重置字段 */
  resetField: () => void
  /** 清除验证 */
  clearValidate: () => void
  /** 输入框元素引用 */
  input: HTMLInputElement | null
  /** 包装器元素引用 */
  wrapper: HTMLDivElement | null
}

/** InputField 默认属性 */
export const inputFieldPropsDefaults: Partial<InputFieldProps> = {
  value: '',
  type: 'text',
  variant: 'default',
  effect: 'none',
  size: 'medium',
  disabled: false,
  readonly: false,
  clearable: false,
  showPassword: false,
  showWordLimit: false,
  error: false,
  warning: false,
  success: false,
  loading: false,
  required: false,
  showMessage: true,
  persistentLabel: false,
  prefix: undefined,
  suffix: undefined,
  smsCountdown: 60,
  smsSendText: '获取验证码',
  smsResendText: '重新获取',
}
