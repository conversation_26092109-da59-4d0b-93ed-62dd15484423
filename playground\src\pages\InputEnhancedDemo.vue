<template>
  <div class="input-enhanced-demo">
    <h1>Input 增强功能演示</h1>

    <div class="demo-section">
      <h2>🚀 格子输入模式 (Texttabel)</h2>
      <div class="demo-item">
        <label>6位验证码:</label>
        <sp-input
          v-model:value="tabelValue6"
          texttabel
          :td="6"
          placeholder="验证码"
        />
        <p>值: {{ tabelValue6 }}</p>
      </div>

      <div class="demo-item">
        <label>4位PIN码:</label>
        <sp-input
          v-model:value="tabelValue4"
          texttabel
          :td="4"
          placeholder="PIN"
        />
        <p>值: {{ tabelValue4 }}</p>
      </div>
    </div>

    <div class="demo-section">
      <h2>📄 文本域模式 (Textarea)</h2>
      <div class="demo-item">
        <label>普通文本域:</label>
        <sp-input
          v-model:value="textareaValue"
          placeholder="请输入多行文本..."
          :rows="4"
        />
        <p>值: {{ textareaValue }}</p>
      </div>

      <div class="demo-item">
        <label>带字数统计的文本域:</label>
        <sp-input
          v-model:value="limitedTextareaValue"
          textarea
          placeholder="最多200个字符..."
          :rows="3"
          :maxlength="200"
          show-word-limit
        />
        <p>值: {{ limitedTextareaValue }}</p>
      </div>
    </div>

    <div class="demo-section">
      <h2>✨ Slip 动画标签</h2>
      <div class="demo-item">
        <label>用户名:</label>
        <sp-input
          v-model:value="slipValue1"
          slip
          placeholder="用户名"
        />
        <p>值: {{ slipValue1 }}</p>
      </div>

      <div class="demo-item">
        <label>邮箱:</label>
        <sp-input
          v-model:value="slipValue2"
          slip
          placeholder="邮箱地址"
          type="email"
        />
        <p>值: {{ slipValue2 }}</p>
      </div>
    </div>

    <div class="demo-section">
      <h2>🎯 前缀后缀图标</h2>
      <div class="demo-item">
        <label>前缀图标:</label>
        <sp-input
          v-model:value="prefixValue"
          prefix-icon="icon-user"
          placeholder="用户名"
        />
        <p>值: {{ prefixValue }}</p>
      </div>

      <div class="demo-item">
        <label>后缀图标:</label>
        <sp-input
          v-model:value="suffixValue"
          suffix-icon="Search"
          placeholder="搜索内容"
        />
        <p>值: {{ suffixValue }}</p>
      </div>
    </div>

    <div class="demo-section">
      <h2>🔢 字数统计功能</h2>
      <div class="demo-item">
        <label>限制50字符:</label>
        <sp-input
          v-model:value="countValue"
          :maxlength="50"
          show-word-limit
          placeholder="最多50个字符"
        />
        <p>值: {{ countValue }}</p>
        <p>字符长度: {{ countValue.length }}</p>
      </div>

      <div class="demo-item">
        <label>原生 input 测试 (maxlength=50):</label>
        <input
          v-model="nativeTestValue"
          maxlength="50"
          placeholder="原生input测试"
          style="
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            width: 300px;
          "
        />
        <p>值: {{ nativeTestValue }}</p>
        <p>字符长度: {{ nativeTestValue.length }}</p>
      </div>
    </div>

    <div class="demo-section">
      <h2>🔐 增强密码功能</h2>
      <div class="demo-item">
        <label>密码:</label>
        <sp-input
          v-model:value="passwordValue"
          type="password"
          show-password
          clearable
          placeholder="密码"
        />
        <p>值: {{ passwordValue }}</p>
      </div>
    </div>

    <div class="demo-section">
      <h2>🏷️ Tag 图标组件演示</h2>

      <!-- 直接图标测试 -->
      <div
        class="demo-item"
        style="border: 2px solid #409eff; padding: 15px; margin-bottom: 20px"
      >
        <label>直接图标测试：</label>
        <div
          style="display: flex; align-items: center; gap: 15px; margin: 10px 0"
        >
          <Search style="width: 24px; height: 24px; color: #409eff" />
          <LocationOutline style="width: 24px; height: 24px; color: #67c23a" />
          <RefreshOutline style="width: 24px; height: 24px; color: #e6a23c" />
          <span>← 如果您能看到这些图标，说明 @vicons/ionicons5 工作正常</span>
        </div>
      </div>

      <div class="demo-item">
        <label>搜索框 (后缀图标):</label>
        <sp-input
          v-model:value="searchValue"
          suffix-icon="Search"
          placeholder="搜索内容"
          @suffix-click="handleSearchClick"
        />
        <p>值: {{ searchValue }}</p>
        <p>点击搜索图标会触发搜索事件</p>
      </div>

      <div class="demo-item">
        <label>位置输入 (后缀图标):</label>
        <sp-input
          v-model:value="locationValue"
          suffix-icon="LocationOutline"
          placeholder="输入位置"
          @suffix-click="handleLocationClick"
        />
        <p>值: {{ locationValue }}</p>
        <p>点击位置图标会触发定位事件</p>
      </div>
    </div>

    <div class="demo-section">
      <h2>🎨 组合演示</h2>
      <div class="demo-item">
        <label>完整功能组合:</label>
        <sp-input
          v-model:value="fullFeatureValue"
          slip
          clearable
          show-word-limit
          :maxlength="100"
          prefix-icon="icon-user"
          placeholder="用户信息"
          validate-state="success"
          validate-message="输入格式正确"
        />
        <p>值: {{ fullFeatureValue }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { RefreshOutline, Search, LocationOutline } from '@vicons/ionicons5'
  // 响应式数据
  const tabelValue6 = ref('')
  const tabelValue4 = ref('')
  const textareaValue = ref('')
  const limitedTextareaValue = ref('')
  const slipValue1 = ref('')
  const slipValue2 = ref('')
  const prefixValue = ref('')
  const suffixValue = ref('')
  const countValue = ref('')
  const nativeTestValue = ref('')
  const passwordValue = ref('')
  const searchValue = ref('')
  const locationValue = ref('')
  const fullFeatureValue = ref('')

  // 事件处理函数
  const handleSearchClick = (event: MouseEvent) => {
    console.log('搜索图标被点击:', event)
    alert(`搜索: ${searchValue.value}`)
  }

  const handleLocationClick = (event: MouseEvent) => {
    console.log('位置图标被点击:', event)
    alert('获取当前位置...')
  }
</script>

<style scoped>
  .input-enhanced-demo {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
  }

  .demo-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;
  }

  .demo-section h2 {
    margin-top: 0;
    color: #409eff;
    border-bottom: 2px solid #409eff;
    padding-bottom: 10px;
  }

  .demo-item {
    margin-bottom: 20px;
  }

  .demo-item label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
  }

  .demo-item p {
    margin: 8px 0 0 0;
    padding: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #666;
    word-break: break-all;
  }

  /* 图标样式 */
  :deep(.icon-user::before) {
    content: '👤';
    font-style: normal;
  }

  :deep(.icon-search::before) {
    content: '🔍';
    font-style: normal;
  }
</style>
