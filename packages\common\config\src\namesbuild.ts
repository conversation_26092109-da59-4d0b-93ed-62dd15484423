// 导入 Vue 3 的响应式 API 和依赖注入相关函数
import { computed, inject, provide, unref } from 'vue'

// 导入 BEM 命名辅助工具
import { useBEM } from '../../../common/bem-helper/src/index'

// 导入 Vue 相关类型定义
import type { App, ComputedRef, MaybeRef } from 'vue'

/**
 * 命名空间类型定义
 * Speed UI 组件库的默认命名空间前缀
 */
export type Namespace = 'sp'

/**
 * 依赖注入的键名常量
 * 用于在 Vue 组件树中传递命名空间配置
 */
export const PROVIDED_NAMESPACE = '__sp-provided-namespace'

/**
 * 全局默认命名空间
 * 使用 computed 包装以保持响应式特性，默认值为 'sp'
 */
export const globalNamespace = computed(() => 'sp' as Namespace)

/**
 * 配置命名空间函数
 * 用于在应用初始化时或组件中设置命名空间配置
 *
 * 使用场景：
 * 1. 应用初始化时全局配置命名空间
 * 2. 在特定组件树中覆盖命名空间
 *
 * @param sourceNamespace 命名空间配置，可以是字符串或响应式引用
 * @param app Vue 应用实例，如果提供则使用 app.provide 进行全局配置
 */
export function configNamespace<N extends string = Namespace>(
  sourceNamespace: MaybeRef<N>,
  app?: App
) {
  if (app) {
    // 应用级别的命名空间配置
    // 创建一个计算属性来处理命名空间的响应式更新
    const namespace = computed(() => {
      const namespace = unref(sourceNamespace) // 解包响应式引用

      // 如果提供了命名空间则使用，否则使用全局默认值
      return namespace || globalNamespace.value
    })

    // 在应用级别提供命名空间配置
    app.provide(PROVIDED_NAMESPACE, namespace)
  } else {
    // 组件级别的命名空间配置
    // 尝试从上级组件注入已有的命名空间配置
    const upstreamNamespace = inject<ComputedRef<string> | null>(
      PROVIDED_NAMESPACE,
      null
    )

    // 创建新的命名空间计算属性，优先级：当前配置 > 上级配置 > 全局默认
    const namespace = computed(() => {
      return (
        unref(sourceNamespace) ||
        upstreamNamespace?.value ||
        globalNamespace.value
      )
    })

    // 在当前组件级别提供命名空间配置
    provide(PROVIDED_NAMESPACE, namespace)
  }
}

/**
 * 获取当前命名空间的 Hook 函数
 * 从依赖注入系统中获取当前生效的命名空间配置
 *
 * @returns 返回当前命名空间的计算属性引用
 */
export function useNamespace<N extends string = Namespace>() {
  return inject(PROVIDED_NAMESPACE, globalNamespace) as ComputedRef<N>
}

/**
 * 创建 BEM 命名辅助器
 * 结合命名空间和 BEM 规范生成 CSS 类名
 *
 * 特殊处理：
 * - 对于 CSS 变量名，命名空间固定为 'sp'（不响应命名空间变化）
 * - 对于普通类名，使用当前生效的命名空间
 *
 * @param block BEM 中的块名（Block）
 * @param namespace 命名空间配置，默认使用当前注入的命名空间
 * @returns 返回 BEM 辅助器实例，包含生成各种 CSS 类名的方法
 */
export function bemHelper<B extends string, N extends string = Namespace>(
  block: B,
  namespace: MaybeRef<N> = useNamespace()
) {
  const currentNamespace = unref(namespace)
  return useBEM(block)
}

/**
 * 命名辅助器类型定义
 * 表示 usebemHelper 函数的返回值类型
 */
export type bemHelper = ReturnType<typeof bemHelper>
