<template>
  <div
    :class="['sp-menu-item', itemClasses]"
    role="menuitem"
    :tabindex="disabled ? -1 : 0"
    @click="handleClick"
    @keydown="handleKeydown"
  >
    <!-- 图标 -->
    <div
      v-if="icon"
      class="sp-menu-item__icon"
    >
      <Icon :name="icon" />
    </div>

    <!-- 内容 -->
    <div class="sp-menu-item__content">
      <slot>{{ title }}</slot>
    </div>

    <!-- 后缀 -->
    <div
      v-if="$slots.suffix"
      class="sp-menu-item__suffix"
    >
      <slot name="suffix" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, inject } from 'vue'
  import { bemHelper } from '@speed-ui/config'
  import type { MenuItemProps, MenuContext } from './types'
  import Icon from '../icon/Icon.vue'

  const props = withDefaults(defineProps<MenuItemProps>(), {
    level: 0,
  })

  // BEM helper
  const bem = bemHelper('menu-item')

  // 注入 Menu 上下文
  const menuContext = inject<MenuContext>('menuContext')

  console.log('📡 MenuItem 注入的 context:', {
    itemKey: props.itemKey,
    menuContext存在: !!menuContext,
    context对象: menuContext,
  })

  // 计算是否选中
  const isSelected = computed(() => {
    if (!menuContext || !props.itemKey) return false
    return menuContext.selectedKeys.value.includes(props.itemKey)
  })

  // 计算样式类
  const itemClasses = computed(() => {
    const classes = [
      {
        'sp-menu-item--selected': isSelected.value,
        'sp-menu-item--disabled': props.disabled,
        'sp-menu-item--danger': props.danger,
        'sp-menu-item--group': props.group,
      },
    ]

    // 调试双向绑定问题
    console.log('🔍 MenuItem 调试:', {
      itemKey: props.itemKey,
      menuContext存在: !!menuContext,
      selectedKeys来自context: menuContext?.selectedKeys.value,
      selectedKeys类型: typeof menuContext?.selectedKeys.value,
      isSelected计算结果: isSelected.value,
      includes检查: props.itemKey
        ? menuContext?.selectedKeys.value?.includes?.(props.itemKey)
        : false,
      原始数组: JSON.stringify(menuContext?.selectedKeys.value),
      itemKey类型: typeof props.itemKey,
    })

    return classes
  })

  // 点击处理
  const handleClick = (event: Event) => {
    if (props.disabled || !props.itemKey) return

    event.stopPropagation()

    if (menuContext) {
      menuContext.handleSelect(props.itemKey)
    }
  }

  // 键盘导航
  const handleKeydown = (event: KeyboardEvent) => {
    if (props.disabled) return

    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault()
        handleClick(event)
        break
      case 'ArrowUp':
        event.preventDefault()
        focusPreviousItem()
        break
      case 'ArrowDown':
        event.preventDefault()
        focusNextItem()
        break
      case 'Home':
        event.preventDefault()
        focusFirstItem()
        break
      case 'End':
        event.preventDefault()
        focusLastItem()
        break
      case 'Escape':
        event.preventDefault()
        // 如果有子菜单，关闭子菜单
        if (menuContext) {
          // 这里可以添加关闭子菜单的逻辑
        }
        break
    }
  }

  // 焦点导航辅助函数
  const focusPreviousItem = () => {
    const currentElement = event?.target as HTMLElement
    const menuItems = currentElement
      .closest('[role="menu"]')
      ?.querySelectorAll('[role="menuitem"]:not([disabled])')
    if (!menuItems) return

    const currentIndex = Array.from(menuItems).indexOf(currentElement)
    const previousIndex =
      currentIndex > 0 ? currentIndex - 1 : menuItems.length - 1
    ;(menuItems[previousIndex] as HTMLElement)?.focus()
  }

  const focusNextItem = () => {
    const currentElement = event?.target as HTMLElement
    const menuItems = currentElement
      .closest('[role="menu"]')
      ?.querySelectorAll('[role="menuitem"]:not([disabled])')
    if (!menuItems) return

    const currentIndex = Array.from(menuItems).indexOf(currentElement)
    const nextIndex = currentIndex < menuItems.length - 1 ? currentIndex + 1 : 0
    ;(menuItems[nextIndex] as HTMLElement)?.focus()
  }

  const focusFirstItem = () => {
    const currentElement = event?.target as HTMLElement
    const menuItems = currentElement
      .closest('[role="menu"]')
      ?.querySelectorAll('[role="menuitem"]:not([disabled])')
    if (menuItems && menuItems.length > 0) {
      ;(menuItems[0] as HTMLElement)?.focus()
    }
  }

  const focusLastItem = () => {
    const currentElement = event?.target as HTMLElement
    const menuItems = currentElement
      .closest('[role="menu"]')
      ?.querySelectorAll('[role="menuitem"]:not([disabled])')
    if (menuItems && menuItems.length > 0) {
      ;(menuItems[menuItems.length - 1] as HTMLElement)?.focus()
    }
  }
</script>
