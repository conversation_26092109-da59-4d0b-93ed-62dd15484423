<!--
  WordLimitControl.vue - 字数统计控件
  显示当前字数和最大字数限制
-->

<template>
  <span class="sp-input-field__count">
    {{ wordCount }}/{{ maxlength }}
  </span>
</template>

<script setup lang="ts">
  interface WordLimitControlProps {
    /** 当前字数 */
    wordCount: number
    /** 最大字数限制 */
    maxlength: number
  }

  defineProps<WordLimitControlProps>()
</script>

<script lang="ts">
  export default {
    name: 'WordLimitControl',
  }
</script>
