import { defineComponent, ref, computed, type PropType } from 'vue'

// JSX 类型声明
declare global {
  namespace JSX {
    interface IntrinsicElements {
      input: any
    }
  }
}

/** BaseInput 支持的输入类型 */
export type BaseInputType = 
  | 'text'
  | 'password'
  | 'email'
  | 'number'
  | 'tel'
  | 'url'
  | 'search'
  | 'range'
  | 'date'
  | 'time'
  | 'datetime-local'
  | 'month'
  | 'week'
  | 'color'
  | 'file'

/** BaseInput 属性接口 */
export interface BaseInputProps {
  /** 输入值 */
  value?: string | number
  /** 输入类型 */
  type?: BaseInputType
  /** 占位符 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 最大长度 */
  maxlength?: number
  /** 最小长度 */
  minlength?: number
  /** 最小值（用于 number、range 等类型） */
  min?: number
  /** 最大值（用于 number、range 等类型） */
  max?: number
  /** 步长（用于 number、range 等类型） */
  step?: number
  /** 自动完成 */
  autocomplete?: string
  /** 自动聚焦 */
  autofocus?: boolean
  /** 输入模式 */
  inputmode?: 'none' | 'text' | 'tel' | 'url' | 'email' | 'numeric' | 'decimal' | 'search'
  /** 表单名称 */
  name?: string
  /** 输入框 ID */
  id?: string
  /** 是否必填 */
  required?: boolean
  /** 自定义类名 */
  class?: string | string[] | Record<string, boolean>
  /** 自定义样式 */
  style?: string | Record<string, any>
  /** 接受的文件类型（用于 file 类型） */
  accept?: string
  /** 是否允许多选（用于 file 类型） */
  multiple?: boolean
}

/** BaseInput 事件接口 */
export interface BaseInputEmits {
  /** 值更新事件 */
  (e: 'update:value', value: string | number): void
  /** 输入事件 */
  (e: 'input', event: Event): void
  /** 值改变事件 */
  (e: 'change', event: Event): void
  /** 获得焦点事件 */
  (e: 'focus', event: FocusEvent): void
  /** 失去焦点事件 */
  (e: 'blur', event: FocusEvent): void
  /** 键盘按下事件 */
  (e: 'keydown', event: KeyboardEvent): void
  /** 键盘抬起事件 */
  (e: 'keyup', event: KeyboardEvent): void
  /** 键盘按键事件 */
  (e: 'keypress', event: KeyboardEvent): void
  /** 点击事件 */
  (e: 'click', event: MouseEvent): void
}

/** BaseInput 实例方法接口 */
export interface BaseInputInstance {
  /** 输入框元素引用 */
  inputRef: HTMLInputElement | null
  /** 聚焦到输入框 */
  focus: () => void
  /** 失去焦点 */
  blur: () => void
  /** 选中输入框内容 */
  select: () => void
  /** 清空输入框 */
  clear: () => void
}

/**
 * BaseInput 组件 - 基础输入框组件
 * 
 * 提供最基础的 input 元素封装，支持所有原生 input 类型
 * 专注于输入逻辑，不包含样式和布局
 */
export default defineComponent({
  name: 'BaseInput',
  
  props: {
    value: {
      type: [String, Number] as PropType<string | number>,
      default: undefined,
    },
    type: {
      type: String as PropType<BaseInputType>,
      default: 'text',
    },
    placeholder: {
      type: String,
      default: undefined,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    maxlength: {
      type: Number,
      default: undefined,
    },
    minlength: {
      type: Number,
      default: undefined,
    },
    min: {
      type: Number,
      default: undefined,
    },
    max: {
      type: Number,
      default: undefined,
    },
    step: {
      type: Number,
      default: undefined,
    },
    autocomplete: {
      type: String,
      default: undefined,
    },
    autofocus: {
      type: Boolean,
      default: false,
    },
    inputmode: {
      type: String as PropType<BaseInputProps['inputmode']>,
      default: undefined,
    },
    name: {
      type: String,
      default: undefined,
    },
    id: {
      type: String,
      default: undefined,
    },
    required: {
      type: Boolean,
      default: false,
    },
    class: {
      type: [String, Array, Object] as PropType<string | string[] | Record<string, boolean>>,
      default: undefined,
    },
    style: {
      type: [String, Object] as PropType<string | Record<string, any>>,
      default: undefined,
    },
    accept: {
      type: String,
      default: undefined,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },

  emits: [
    'update:value',
    'input',
    'change',
    'focus',
    'blur',
    'keydown',
    'keyup',
    'keypress',
    'click',
  ],

  setup(props, { emit, expose }) {
    // 输入框引用
    const inputRef = ref<HTMLInputElement>()

    // 计算当前值
    const currentValue = computed(() => {
      return props.value ?? ''
    })

    // ===== 事件处理 =====
    const handleInput = (event: Event) => {
      const target = event.target as HTMLInputElement
      let value: string | number = target.value

      // 对于 number 和 range 类型，转换为数字
      if (props.type === 'number' || props.type === 'range') {
        const numValue = Number(value)
        if (!isNaN(numValue)) {
          value = numValue
        }
      }

      emit('update:value', value)
      emit('input', event)
    }

    const handleChange = (event: Event) => {
      emit('change', event)
    }

    const handleFocus = (event: FocusEvent) => {
      emit('focus', event)
    }

    const handleBlur = (event: FocusEvent) => {
      emit('blur', event)
    }

    const handleKeydown = (event: KeyboardEvent) => {
      emit('keydown', event)
    }

    const handleKeyup = (event: KeyboardEvent) => {
      emit('keyup', event)
    }

    const handleKeypress = (event: KeyboardEvent) => {
      emit('keypress', event)
    }

    const handleClick = (event: MouseEvent) => {
      emit('click', event)
    }

    // ===== 实例方法 =====
    const focus = () => {
      inputRef.value?.focus()
    }

    const blur = () => {
      inputRef.value?.blur()
    }

    const select = () => {
      inputRef.value?.select()
    }

    const clear = () => {
      if (inputRef.value) {
        inputRef.value.value = ''
        const event = new Event('input', { bubbles: true })
        inputRef.value.dispatchEvent(event)
      }
    }

    // 暴露实例方法和属性
    expose({
      inputRef: computed(() => inputRef.value || null),
      focus,
      blur,
      select,
      clear,
    })

    // ===== 渲染函数 =====
    return () => (
      <input
        ref={inputRef}
        type={props.type}
        value={currentValue.value}
        placeholder={props.placeholder}
        disabled={props.disabled}
        readonly={props.readonly}
        maxlength={props.maxlength}
        minlength={props.minlength}
        min={props.min}
        max={props.max}
        step={props.step}
        autocomplete={props.autocomplete}
        autofocus={props.autofocus}
        inputmode={props.inputmode}
        name={props.name}
        id={props.id}
        required={props.required}
        class={props.class}
        style={props.style}
        accept={props.accept}
        multiple={props.multiple}
        onInput={handleInput}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeydown={handleKeydown}
        onKeyup={handleKeyup}
        onKeypress={handleKeypress}
        onClick={handleClick}
      />
    )
  },
})
