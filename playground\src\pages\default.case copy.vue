<text>
# Button2 组件

Button 是一个功能丰富的按钮组件，支持多种类型、尺寸和状态。

**功能特点：**
- 支持多种按钮类型
- 可以添加点击事件
- 响应式交互效果
</text>

<template>
    <sp-button type="primary" @click="handlePrimaryClick">主要按钮2 (点击试试)</sp-button>
    <sp-button type="secondary" @click="handleSecondaryClick">次要按钮23</sp-button>
    <sp-button type="success" @click="showMessage('成功操作！', 'success')">成功按钮</sp-button>
    <sp-button type="warning" @click="showMessage('警告提示！', 'warning')">警告按钮</sp-button>
    <sp-button type="danger" @click="confirmDelete">危险按钮</sp-button>
    <sp-button type="outline" @click="toggleCounter">轮廓按钮 ({{ counter }})</sp-button>
</template>

<script lang="ts">
    import { ref } from 'vue'

    const counter = ref(0)

    // 主要按钮点击事件
    const handlePrimaryClick = () => {
        alert('🎉 主要按钮被点击了！\n这证明了 case 文件中的逻辑是可以正常工作的！')
    }

    // 次要按钮点击事件
    const handleSecondaryClick = () => {
        const userInput = prompt('请输入你的名字：')
        if (userInput) {
            alert(`你好，${userInput}！欢迎使用 Speed UI！`)
        }
    }

    // 通用消息提示方法
    const showMessage = (message: string, type: string) => {
        const icon = type === 'success' ? '✅' : '⚠️'
        alert(`${icon} ${message}`)
    }

    // 危险操作确认
    const confirmDelete = () => {
        const confirmed = confirm('⚠️ 确定要执行危险操作吗？\n这个操作无法撤销！')
        if (confirmed) {
            alert('💥 危险操作已执行！（这只是演示）')
        } else {
            alert('✋ 操作已取消')
        }
    }

    // 计数器切换
    const toggleCounter = () => {
        counter.value++
        if (counter.value >= 5) {
            alert(`🎯 你已经点击了 ${counter.value} 次！\n计数器重置为 0`)
            counter.value = 0
        }
    }

    console.log("🚀 Button Demo 组件已加载！")
</script>
