<template>
  <ListItem
    :item-key="String(optionValue)"
    :title="label"
    :disabled="disabled"
    :selected="selected"
    :selectable="false"
    :class="optionClassName"
    @click="handleClick"
  >
    <template #suffix>
      <i
        v-if="selected"
        :class="`${prefixCls}__option-check`"
      >
        <Checkmark />
      </i>
    </template>
  </ListItem>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { Checkmark } from '@vicons/ionicons5'
  import { classNames } from '../../../utils'
  import { ListItem } from '../../list'
  import type { SelectOption } from '../select'
  import { getOptionValue, getOptionLabel } from '../select'

  interface Props {
    option: SelectOption
    prefixCls: string
    selected: boolean
    disabled: boolean
    valueKey?: string
    labelKey?: string
  }

  interface Emits {
    click: [option: SelectOption]
  }

  const props = withDefaults(defineProps<Props>(), {
    valueKey: 'value',
    labelKey: 'label',
  })
  const emit = defineEmits<Emits>()

  // 计算选项值
  const optionValue = computed(() => {
    return getOptionValue(props.option, props.valueKey)
  })

  // 计算选项标签
  const label = computed(() => {
    return getOptionLabel(props.option, props.labelKey)
  })

  // 计算选项类名
  const optionClassName = computed(() =>
    classNames(`${props.prefixCls}__option`, {
      [`${props.prefixCls}__option--selected`]: props.selected,
      [`${props.prefixCls}__option--disabled`]: props.disabled,
    })
  )

  // 处理点击事件
  const handleClick = () => {
    if (props.disabled) return
    emit('click', props.option)
  }
</script>

<script lang="ts">
  export default {
    name: 'SelectOption',
  }
</script>
