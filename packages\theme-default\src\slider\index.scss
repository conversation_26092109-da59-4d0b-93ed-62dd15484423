// ================================
// Slider 滑块组件样式
// ================================

@use '../common/var.scss' as *;

// ========== Slider 容器 ==========
.sp-slider {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;

  // 禁用状态
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  // 尺寸变体
  &--small {
    height: 16px;
  }

  &--medium {
    height: 20px;
  }

  &--large {
    height: 24px;
  }
}

// ========== 原生 input[type="range"] 样式 ==========
.sp-slider__input {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 4px;
  border-radius: 2px;
  // background 由 JavaScript 动态设置以显示进度条
  outline: none;
  cursor: pointer;
  transition: all $transition-duration-base $transition-timing-base;

  // 禁用状态
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  // ========== 默认变体样式 ==========
  .sp-slider--default & {
    // background 由 JavaScript 动态设置以显示进度条

    // WebKit 滑块样式
    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background: #ffffff;
      border: 2px solid var(--sp-color-primary);
      cursor: grab;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      transition: all $transition-duration-base $transition-timing-base;

      &:hover {
        transform: scale(1.1);
        box-shadow: 0 0 0 4px var(--sp-color-primary-lightest),
          0 2px 6px rgba(0, 0, 0, 0.3);
      }

      &:active {
        cursor: grabbing;
        transform: scale(1.2);
        box-shadow: 0 0 0 6px var(--sp-color-primary-lightest),
          0 3px 8px rgba(0, 0, 0, 0.4);
      }
    }

    // Firefox 滑块样式
    &::-moz-range-thumb {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background: #ffffff;
      border: 2px solid var(--sp-color-primary);
      cursor: grab;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      transition: all $transition-duration-base $transition-timing-base;

      &:hover {
        transform: scale(1.1);
        box-shadow: 0 0 0 4px var(--sp-color-primary-lightest),
          0 2px 6px rgba(0, 0, 0, 0.3);
      }

      &:active {
        cursor: grabbing;
        transform: scale(1.2);
        box-shadow: 0 0 0 6px var(--sp-color-primary-lightest),
          0 3px 8px rgba(0, 0, 0, 0.4);
      }
    }

    // Firefox 轨道样式
    &::-moz-range-track {
      height: 4px;
      background: $border-color-base;
      border-radius: 2px;
      border: none;
    }

    // Firefox 进度样式
    &::-moz-range-progress {
      height: 4px;
      background: var(--sp-color-primary);
      border-radius: 2px;
    }
  }

  // ========== 填充变体样式 ==========
  .sp-slider--filled & {
    height: 100%;
    background: $background-color-base;
    border: 1px solid $border-color-base;
    border-radius: 10px;
    position: relative;
    overflow: hidden;

    // 进度条伪元素 - 带圆角
    &::before {
      content: '';
      position: absolute;
      top: 1px;
      left: 1px;
      height: calc(100% - 2px);
      background: var(--sp-color-primary);
      border-radius: 8px; // 给进度条添加圆角
      width: var(--progress-width, 0%);
      z-index: 1;
      // transition: none; // 确保没有延迟
    }

    // 鼠标移入效果 - 显示外边框
    &:hover {
      border-color: var(--sp-color-primary);
      box-shadow: 0 0 0 1px var(--sp-color-primary);
    }

    // 聚焦效果
    &:focus-visible {
      border-color: var(--sp-color-primary);
      box-shadow: 0 0 0 2px var(--sp-color-primary-lightest);
      outline: none;
    }

    // WebKit 滑块样式
    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 28px;
      height: 20px;
      border-radius: 10px; // 椭圆形圆角
      background: var(--sp-color-primary);
      border: 2px solid #ffffff;
      cursor: grab;
      box-shadow: $box-shadow-light;
      transition: all $transition-duration-base $transition-timing-base;
      position: relative;
      z-index: 2; // 确保滑块按钮在进度条之上

      &:hover {
        transform: scale(1.1);
        box-shadow: $box-shadow-base;
      }

      &:active {
        cursor: grabbing;
        transform: scale(1.2);
        box-shadow: $box-shadow-dark;
      }
    }

    // Firefox 滑块样式
    &::-moz-range-thumb {
      width: 28px;
      height: 20px;
      border-radius: 10px; // 椭圆形圆角
      background: var(--sp-color-primary);
      border: 2px solid #ffffff;
      cursor: grab;
      box-shadow: $box-shadow-light;
      transition: all $transition-duration-base $transition-timing-base;
      position: relative;
      z-index: 2; // 确保滑块按钮在进度条之上

      &:hover {
        transform: scale(1.1);
        box-shadow: $box-shadow-base;
      }

      &:active {
        cursor: grabbing;
        transform: scale(1.2);
        box-shadow: $box-shadow-dark;
      }
    }

    // Firefox 轨道样式
    &::-moz-range-track {
      height: 100%;
      background: $background-color-base;
      border: 1px solid $border-color-base;
      border-radius: 10px;
    }

    // Firefox 进度样式
    &::-moz-range-progress {
      height: 100%;
      background: var(--sp-color-primary);
      border-radius: 9px;
    }
  }

  // ========== 填充正方形变体样式 ==========
  .sp-slider--filled-square & {
    height: 100%;
    background: $background-color-base;
    border: 1px solid $border-color-base;
    border-radius: 0; // 正方形，无圆角
    position: relative;
    overflow: hidden;

    // 进度条伪元素 - 无圆角
    &::before {
      content: '';
      position: absolute;
      top: 1px;
      left: 1px;
      height: calc(100% - 2px);
      background: var(--sp-color-primary);
      border-radius: 0; // 正方形进度条，无圆角
      width: var(--progress-width, 0%);
      z-index: 1;
    }

    // 鼠标移入效果 - 显示外边框
    &:hover {
      border-color: var(--sp-color-primary);
      box-shadow: 0 0 0 1px var(--sp-color-primary);
    }

    // 聚焦效果
    &:focus-visible {
      border-color: var(--sp-color-primary);
      box-shadow: 0 0 0 2px var(--sp-color-primary-lightest);
      outline: none;
    }

    // WebKit 滑块样式 - 正方形
    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 24px;
      height: 20px;
      border-radius: 0; // 正方形滑块按钮，无圆角
      background: var(--sp-color-primary);
      border: 2px solid #ffffff;
      cursor: grab;
      box-shadow: $box-shadow-light;
      transition: all $transition-duration-base $transition-timing-base;
      position: relative;
      z-index: 2; // 确保滑块按钮在进度条之上

      &:hover {
        transform: scale(1.1);
        box-shadow: $box-shadow-base;
      }

      &:active {
        cursor: grabbing;
        transform: scale(1.2);
        box-shadow: $box-shadow-dark;
      }
    }

    // Firefox 滑块样式 - 正方形
    &::-moz-range-thumb {
      width: 24px;
      height: 20px;
      border-radius: 0; // 正方形滑块按钮，无圆角
      background: var(--sp-color-primary);
      border: 2px solid #ffffff;
      cursor: grab;
      box-shadow: $box-shadow-light;
      transition: all $transition-duration-base $transition-timing-base;
      position: relative;
      z-index: 2; // 确保滑块按钮在进度条之上

      &:hover {
        transform: scale(1.1);
        box-shadow: $box-shadow-base;
      }

      &:active {
        cursor: grabbing;
        transform: scale(1.2);
        box-shadow: $box-shadow-dark;
      }
    }

    // Firefox 轨道样式 - 正方形
    &::-moz-range-track {
      height: 100%;
      background: $background-color-base;
      border: 1px solid $border-color-base;
      border-radius: 0; // 正方形轨道，无圆角
    }

    // Firefox 进度样式 - 正方形
    &::-moz-range-progress {
      height: 100%;
      background: var(--sp-color-primary);
      border-radius: 0; // 正方形进度，无圆角
    }
  }

  // ========== 尺寸变体 ==========
  .sp-slider--small & {
    &::-webkit-slider-thumb {
      width: 12px;
      height: 12px;
    }

    &::-moz-range-thumb {
      width: 12px;
      height: 12px;
    }
  }

  .sp-slider--large & {
    &::-webkit-slider-thumb {
      width: 20px;
      height: 20px;
    }

    &::-moz-range-thumb {
      width: 20px;
      height: 20px;
    }
  }
}

// ========== 标记点 ==========
.sp-slider__marks {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  height: 20px;
  pointer-events: none;
  margin-top: 8px;
}

.sp-slider__mark {
  position: absolute;
  top: 0;
  transform: translateX(-50%);
  font-size: $font-size-sm;
  color: $color-text-secondary;
  white-space: nowrap;

  &::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 4px;
    background: $border-color-base;
  }

  // 激活状态的标记
  &--active {
    color: var(--sp-color-primary);

    &::before {
      background: var(--sp-color-primary);
    }
  }
}

// ========== 无障碍支持 ==========
.sp-slider__input:focus-visible {
  outline: 2px solid var(--sp-color-primary);
  outline-offset: 2px;
}

// 高对比度模式
@media (prefers-contrast: high) {
  .sp-slider__input {
    &::-webkit-slider-thumb {
      border-width: 3px;
    }

    &::-moz-range-thumb {
      border-width: 3px;
    }
  }
}

// 减少动画模式
@media (prefers-reduced-motion: reduce) {
  .sp-slider__input {
    transition: none;

    &::-webkit-slider-thumb {
      transition: none;
    }

    &::-moz-range-thumb {
      transition: none;
    }
  }
}
