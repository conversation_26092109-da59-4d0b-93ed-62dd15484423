/**
 * Speed UI 组件尺寸 Composables
 */

import { computed, type ComputedRef } from 'vue'
import type { ComponentSize } from './types'
import {
  mapSizeToScrollbar,
  getSizeValue,
  getSizeClass,
  normalizeSizeValue,
} from './utils'
import { DEFAULT_SIZE } from './constants'

export interface UseSizeOptions {
  /**
   * 默认尺寸
   */
  defaultSize?: ComponentSize
  /**
   * 是否启用响应式尺寸计算
   */
  responsive?: boolean
}

export interface UseSizeReturn {
  /**
   * 当前尺寸
   */
  currentSize: ComputedRef<ComponentSize>
  /**
   * 尺寸对应的数值
   */
  sizeValue: ComputedRef<number>
  /**
   * 尺寸对应的 CSS 类名
   */
  sizeClass: ComputedRef<string>
  /**
   * 映射到滚动条尺寸
   */
  scrollbarSize: ComputedRef<string>
  /**
   * 检查是否为指定尺寸
   */
  isSize: (size: ComponentSize) => ComputedRef<boolean>
  /**
   * 检查是否为小尺寸
   */
  isSmall: ComputedRef<boolean>
  /**
   * 检查是否为默认尺寸
   */
  isDefault: ComputedRef<boolean>
  /**
   * 检查是否为大尺寸
   */
  isLarge: ComputedRef<boolean>
}

/**
 * 尺寸相关的 composable
 * @param size 尺寸值或获取尺寸的函数
 * @param options 配置选项
 * @returns 尺寸相关的响应式数据和方法
 */
export function useSize(
  size: ComponentSize | (() => ComponentSize) | undefined,
  options: UseSizeOptions = {}
): UseSizeReturn {
  const { defaultSize = DEFAULT_SIZE } = options

  const currentSize = computed(() => {
    let sizeValue: ComponentSize | undefined

    if (typeof size === 'function') {
      sizeValue = size()
    } else {
      sizeValue = size
    }

    return normalizeSizeValue(sizeValue, defaultSize)
  })

  const sizeValue = computed(() => getSizeValue(currentSize.value))
  const sizeClass = computed(() => getSizeClass(currentSize.value))
  const scrollbarSize = computed(() => mapSizeToScrollbar(currentSize.value))

  const isSize = (targetSize: ComponentSize) => {
    return computed(() => currentSize.value === targetSize)
  }

  const isSmall = computed(() => currentSize.value === 'small')
  const isDefault = computed(() => currentSize.value === 'default')
  const isLarge = computed(() => currentSize.value === 'large')

  return {
    currentSize,
    sizeValue,
    sizeClass,
    scrollbarSize,
    isSize,
    isSmall,
    isDefault,
    isLarge,
  }
}

/**
 * 尺寸比较 composable
 * @param size1 尺寸1
 * @param size2 尺寸2
 * @returns 比较结果的响应式引用
 */
export function useSizeComparison(
  size1: ComponentSize | (() => ComponentSize),
  size2: ComponentSize | (() => ComponentSize)
) {
  const currentSize1 = computed(() => {
    return typeof size1 === 'function' ? size1() : size1
  })

  const currentSize2 = computed(() => {
    return typeof size2 === 'function' ? size2() : size2
  })

  const isLarger = computed(
    () => getSizeValue(currentSize1.value) > getSizeValue(currentSize2.value)
  )
  const isSmaller = computed(
    () => getSizeValue(currentSize1.value) < getSizeValue(currentSize2.value)
  )
  const isEqual = computed(() => currentSize1.value === currentSize2.value)

  return {
    isLarger,
    isSmaller,
    isEqual,
  }
}
