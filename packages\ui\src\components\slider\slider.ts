/**
 * Slider 组件类型定义
 */

/** 滑块尺寸 */
export type SliderSize = 'small' | 'medium' | 'large'

/** 滑块变体 */
export type SliderVariant = 'default' | 'filled' | 'filled-square'

/** 滑块属性接口 */
export interface SliderProps {
  /** 当前值 */
  modelValue?: number
  /** 最小值 */
  min?: number
  /** 最大值 */
  max?: number
  /** 步长 */
  step?: number
  /** 是否禁用 */
  disabled?: boolean
  /** 变体类型 */
  variant?: SliderVariant
  /** 尺寸 */
  size?: SliderSize
  /** 标记点配置 */
  marks?: Record<number, string>
}

/** 滑块事件接口 */
export interface SliderEmits {
  /** 值更新事件 */
  'update:modelValue': [value: number]
  /** 值改变事件 */
  change: [value: number]
}

/** 滑块实例接口 */
export interface SliderInstance {
  /** 输入框引用 */
  inputRef: HTMLInputElement | undefined
  /** 设置焦点 */
  focus: () => void
  /** 失去焦点 */
  blur: () => void
}
