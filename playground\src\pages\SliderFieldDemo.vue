<template>
  <div class="slider-field-demo">
    <h1>🎚️ SliderField 滑块字段组件演示</h1>
    <p>演示 SliderField 组件的不同变体和功能，集成了浮动标签和表单验证</p>

    <!-- 基础用法 -->
    <section class="demo-section">
      <h2>基础用法（无标签）</h2>
      <div class="demo-container">
        <div class="slider-wrapper">
          <h3>纯滑块</h3>
          <sp-slider-field
            v-model:value="value1"
            name="volume"
            prepend-icon="Search"
            prepend-icon-inner="Search"
            variant="filled"
            prefix="1"
            :min="0"
            :max="100"
            :step="1"
            show-value
          />
          <p>当前值: {{ value1 }}</p>
        </div>

        <div class="slider-wrapper">
          <h3>带前缀后缀</h3>
          <sp-slider-field
            v-model:value="value2"
            name="brightness"
            prefix="1"
            suffix="2"
            :min="0"
            :max="100"
            :step="5"
            show-value
          />
          <p>当前值: {{ value2 }}</p>
        </div>
      </div>
    </section>

    <!-- 带标签的用法（可选） -->
    <section class="demo-section">
      <h2>带标签的用法（可选）</h2>
      <div class="demo-container">
        <div class="slider-wrapper">
          <h3>如果需要标签</h3>
          <sp-slider-field
            v-model:value="value15"
            label="音量控制"
            name="volume-with-label"
            :min="0"
            :max="100"
            show-value
          />
          <p>当前值: {{ value15 }}</p>
        </div>
      </div>
    </section>

    <!-- 变体样式 -->
    <section class="demo-section">
      <h2>变体样式</h2>
      <div class="demo-container">
        <div class="slider-wrapper">
          <h3>默认变体</h3>
          <sp-slider-field
            v-model:value="value3"
            label="默认样式"
            name="default"
            variant="default"
            :min="0"
            :max="100"
            show-value
          />
          <p>当前值: {{ value3 }}</p>
        </div>

        <div class="slider-wrapper">
          <h3>填充变体</h3>
          <sp-slider-field
            v-model:value="value4"
            label="填充样式"
            name="filled"
            variant="filled"
            :min="0"
            :max="100"
            show-value
          />
          <p>当前值: {{ value4 }}</p>
        </div>

        <div class="slider-wrapper">
          <h3>方形变体</h3>
          <sp-slider-field
            v-model:value="value16"
            label="方形样式"
            name="square"
            variant="filled-square"
            :min="0"
            :max="100"
            show-value
          />
          <p>当前值: {{ value16 }}</p>
        </div>

        <div class="slider-wrapper">
          <h3>下划线变体</h3>
          <sp-slider-field
            v-model:value="value5"
            label="下划线样式"
            name="underlined"
            variant="underlined"
            :min="0"
            :max="100"
            show-value
          />
          <p>当前值: {{ value5 }}</p>
        </div>
      </div>
    </section>

    <!-- 尺寸变体 -->
    <section class="demo-section">
      <h2>尺寸变体</h2>
      <div class="demo-container">
        <div class="slider-wrapper">
          <h3>小尺寸</h3>
          <sp-slider-field
            v-model:value="value6"
            label="小尺寸滑块"
            name="small"
            size="small"
            :min="0"
            :max="100"
            show-value
          />
          <p>当前值: {{ value6 }}</p>
        </div>

        <div class="slider-wrapper">
          <h3>中等尺寸</h3>
          <sp-slider-field
            v-model:value="value7"
            label="中等尺寸滑块"
            name="medium"
            size="medium"
            :min="0"
            :max="100"
            show-value
          />
          <p>当前值: {{ value7 }}</p>
        </div>

        <div class="slider-wrapper">
          <h3>大尺寸</h3>
          <sp-slider-field
            v-model:value="value8"
            label="大尺寸滑块"
            name="large"
            size="large"
            :min="0"
            :max="100"
            show-value
          />
          <p>当前值: {{ value8 }}</p>
        </div>
      </div>
    </section>

    <!-- 带标记的滑块 -->
    <section class="demo-section">
      <h2>带标记的滑块</h2>
      <div class="demo-container">
        <div class="slider-wrapper">
          <h3>温度控制</h3>
          <sp-slider-field
            v-model:value="value9"
            label="温度设置"
            name="temperature"
            :min="16"
            :max="30"
            :step="1"
            :marks="temperatureMarks"
            show-value
            :value-formatter="formatTemperature"
          />
          <p>当前值: {{ value9 }}°C</p>
        </div>

        <div class="slider-wrapper">
          <h3>评分滑块</h3>
          <sp-slider-field
            v-model:value="value10"
            label="评分"
            name="rating"
            :min="0"
            :max="10"
            :step="1"
            :marks="ratingMarks"
            show-value
            :value-formatter="formatRating"
          />
          <p>当前值: {{ value10 }}/10</p>
        </div>
      </div>
    </section>

    <!-- 表单验证 -->
    <section class="demo-section">
      <h2>表单验证</h2>
      <div class="demo-container">
        <div class="slider-wrapper">
          <h3>必填字段</h3>
          <sp-slider-field
            v-model:value="value11"
            label="必填滑块"
            name="required"
            required
            :min="1"
            :max="100"
            show-value
            helper-text="这是一个必填的滑块字段"
          />
          <p>当前值: {{ value11 }}</p>
        </div>

        <div class="slider-wrapper">
          <h3>帮助文本</h3>
          <sp-slider-field
            v-model:value="value12"
            label="带帮助文本"
            name="helper"
            :min="0"
            :max="100"
            show-value
            helper-text="这是帮助文本，用于说明字段用途"
          />
          <p>当前值: {{ value12 }}</p>
        </div>
      </div>
    </section>

    <!-- 禁用状态 -->
    <section class="demo-section">
      <h2>禁用状态</h2>
      <div class="demo-container">
        <div class="slider-wrapper">
          <h3>禁用的滑块</h3>
          <sp-slider-field
            v-model:value="value13"
            label="禁用状态"
            name="disabled"
            :disabled="true"
            :min="0"
            :max="100"
            show-value
          />
          <p>当前值: {{ value13 }}</p>
        </div>
      </div>
    </section>

    <!-- 图标和功能 -->
    <section class="demo-section">
      <h2>图标和功能</h2>
      <div class="demo-container">
        <div class="slider-wrapper">
          <h3>带图标（无标签）</h3>
          <sp-slider-field
            v-model:value="value14"
            name="volume-control"
            prepend-icon="VolumeDown"
            append-icon="VolumeUp"
            :min="0"
            :max="100"
            show-value
          />
          <p>当前值: {{ value14 }}</p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  // 响应式数据
  const value1 = ref(30)
  const value2 = ref(60)
  const value3 = ref(25)
  const value4 = ref(50)
  const value5 = ref(75)
  const value6 = ref(25)
  const value7 = ref(50)
  const value8 = ref(75)
  const value9 = ref(22)
  const value10 = ref(7)
  const value11 = ref(0)
  const value12 = ref(50)
  const value13 = ref(30)
  const value14 = ref(60)
  const value15 = ref(45)
  const value16 = ref(35)

  // 标记配置
  const temperatureMarks = {
    16: '16°',
    20: '20°',
    24: '24°',
    28: '28°',
    30: '30°',
  }

  const ratingMarks = {
    0: '差',
    2: '较差',
    4: '一般',
    6: '良好',
    8: '很好',
    10: '优秀',
  }

  // 值格式化函数，修复 TypeScript 类型错误
  const formatTemperature = (val: number): string => `${val}°C`
  const formatRating = (val: number): string => `${val}/10`
</script>

<style scoped>
  .slider-field-demo {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .demo-section {
    margin-bottom: 40px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
  }

  .demo-section h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
  }

  .demo-container {
    display: flex;
    flex-direction: column;
    gap: 30px;
  }

  .slider-wrapper {
    padding: 20px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .slider-wrapper h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #555;
    font-size: 16px;
  }

  .slider-wrapper p {
    margin-top: 15px;
    margin-bottom: 0;
    color: #666;
    font-size: 14px;
  }
</style>
