<template>
  <Form
    :class="[
      'sp-form',
      `sp-form--${labelPosition}`,
      {
        'sp-form--inline': inline,
        'sp-form--disabled': disabled,
      },
    ]"
    :validation-schema="validationSchema"
    :initial-values="initialValues"
    @submit="onSubmit"
    @invalid-submit="onInvalidSubmit"
  >
    <slot></slot>
  </Form>
</template>

<script setup lang="ts">
import { provide, computed, ref } from 'vue'
import { Form, useForm } from 'vee-validate'
import './style'

export interface FormProps {
  labelPosition?: 'right' | 'left' | 'top'
  labelWidth?: string | number
  labelSuffix?: string
  hideLabel?: boolean
  inline?: boolean
  disabled?: boolean
  size?: 'small' | 'default' | 'large'
  showMessage?: boolean
  inlineMessage?: boolean
  statusIcon?: boolean
  hideRequiredAsterisk?: boolean
  scrollToError?: boolean
  validationSchema?: any
  initialValues?: Record<string, any>
}

const props = withDefaults(defineProps<FormProps>(), {
  labelPosition: 'right',
  labelWidth: '',
  labelSuffix: '',
  hideLabel: false,
  inline: false,
  disabled: false,
  size: 'default',
  showMessage: true,
  inlineMessage: false,
  statusIcon: false,
  hideRequiredAsterisk: false,
  scrollToError: false,
})

const emit = defineEmits<{
  submit: [values: Record<string, any>]
  invalid: [errors: Record<string, string>]
}>()

// VeeValidate 表单实例
const veeForm = useForm({
  validationSchema: props.validationSchema,
  initialValues: props.initialValues,
})

// 存储 FormItem 引用（用于滚动等操作）
const formItemRefs = ref<Map<string, any>>(new Map())

// 注册 FormItem
const addFormItem = (item: any) => {
  if (item.name) {
    formItemRefs.value.set(item.name, item)
  }
}

// 注销 FormItem
const removeFormItem = (name: string) => {
  formItemRefs.value.delete(name)
}

// 计算标签宽度
const labelWidthComputed = computed(() => {
  if (typeof props.labelWidth === 'number') {
    return `${props.labelWidth}px`
  }
  return props.labelWidth
})

// VeeValidate 提交成功处理
const onSubmit = (values: Record<string, any>) => {
  console.log('VeeValidate form submitted:', values)
  emit('submit', values)
}

// VeeValidate 提交失败处理
const onInvalidSubmit = (ctx: any) => {
  const errors = ctx.errors || {}
  console.log('VeeValidate form invalid:', errors)
  emit('invalid', errors)
  
  if (props.scrollToError) {
    // 滚动到第一个错误字段
    const firstErrorField = Object.keys(errors)[0]
    if (firstErrorField) {
      scrollToField(firstErrorField)
    }
  }
}

// 滚动到指定字段
const scrollToField = (name: string) => {
  const item = formItemRefs.value.get(name)
  if (item?.scrollIntoView) {
    item.scrollIntoView()
  }
}

// 验证表单
const validate = async (): Promise<boolean> => {
  const result = await veeForm.validate()
  return result.valid
}

// 验证指定字段
const validateField = async (name: string): Promise<boolean> => {
  const result = await veeForm.validateField(name)
  return result.valid
}

// 重置表单
const resetFields = () => {
  veeForm.resetForm()
}

// 清除验证结果
const clearValidate = (names?: string | string[]) => {
  if (!names) {
    // 清除所有字段的验证状态
    const fieldNames = Object.keys(veeForm.values)
    fieldNames.forEach(name => veeForm.setFieldError(name, undefined))
  } else {
    const nameArray = Array.isArray(names) ? names : [names]
    nameArray.forEach(name => veeForm.setFieldError(name, undefined))
  }
}

// 提供给子组件的上下文
provide('spForm', {
  labelPosition: props.labelPosition,
  labelWidth: labelWidthComputed.value,
  labelSuffix: props.labelSuffix,
  hideLabel: props.hideLabel,
  inline: props.inline,
  disabled: props.disabled,
  size: props.size,
  showMessage: props.showMessage,
  inlineMessage: props.inlineMessage,
  statusIcon: props.statusIcon,
  hideRequiredAsterisk: props.hideRequiredAsterisk,
  scrollToError: props.scrollToError,
  addFormItem,
  removeFormItem,
  emit,
})

// 暴露方法给外部使用
defineExpose({
  validate,
  validateField,
  resetFields,
  clearValidate,
  scrollToField,
  veeForm, // 暴露 VeeValidate 表单实例
})
</script>

<script lang="ts">
export default {
  name: 'SpForm',
}
</script> 