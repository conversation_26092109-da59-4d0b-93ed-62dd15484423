// ================================
// List 组件样式
// ================================

@use 'sass:color';
@use 'common/var.scss' as *;

// ========== List 容器 ==========
.sp-list {
  position: relative;
  background: $background-color-base;
  border-radius: $border-radius-base;
  overflow: hidden;

  // 边框样式
  &--bordered {
    border: 1px solid $border-color-base;
  }

  // 加载状态
  &--loading {
    opacity: 0.6;
    pointer-events: none;
  }

  // 尺寸变体
  &--small {
    .sp-list-item {
      padding: #{$spacing-xs} #{$spacing-sm};
      min-height: 32px;

      .sp-list-item__title {
        font-size: $font-size-sm;
      }
    }
  }

  &--medium {
    .sp-list-item {
      padding: #{$spacing-sm} #{$spacing-md};
      min-height: 40px;
    }
  }

  &--large {
    .sp-list-item {
      padding: #{$spacing-md} #{$spacing-lg};
      min-height: 48px;

      .sp-list-item__title {
        font-size: $font-size-lg;
      }
    }
  }

  // 简洁样式变体 - 移除所有分割线
  &--simple {
    .sp-list-item {
      border-bottom: none !important;
    }

    // 即使设置了 split 属性也不显示分割线
    &.sp-list--split .sp-list-item {
      border-bottom: none !important;
    }
  }
}

// ========== List Item ==========
.sp-list-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: #{$spacing-sm} #{$spacing-md};
  min-height: 40px;
  transition: all $transition-duration-base $transition-timing-base;
  cursor: default;
  border-bottom: 1px solid transparent;

  // 分割线
  .sp-list--split & {
    border-bottom-color: $border-color-base;

    &:last-child {
      border-bottom-color: transparent;
    }
  }

  // hover 效果
  .sp-list--hoverable &:not(&--disabled):hover {
    background: $background-color-hover;
  }

  // 可选择状态
  &--selectable {
    cursor: pointer;
  }

  // 选中状态
  &--selected {
    background: var(--sp-color-primary-lightest);
    border-left: 3px solid var(--sp-color-primary);

    .sp-list-item__title {
      color: var(--sp-color-primary);
    }
  }

  // 简洁样式变体的选中状态 - 只有背景色
  .sp-list--simple & {
    // 移除分割线
    border-bottom: none;

    &--selected {
      background: #e6f4ff; // 淡蓝色背景
      border-left: none;
      border-radius: 0;

      .sp-list-item__title {
        color: inherit;
      }
    }

    // 简洁模式下的 hover 效果也很轻微
    &:not(&--disabled):hover {
      background: #f5f5f5;
    }

    // 简洁模式下隐藏复选框
    .sp-list-item__checkbox {
      display: none;
    }
  }

  // 禁用状态
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;

    .sp-list-item__title,
    .sp-list-item__description {
      color: $color-text-disabled;
    }
  }
}

// ========== 选择框 ==========
.sp-list-item__checkbox {
  margin-right: #{$spacing-sm};
  flex-shrink: 0;
}

.sp-list-item__checkbox-input {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border: 1px solid $border-color-base;
  border-radius: 2px;
  background: $background-color-base;
  transition: all $transition-duration-base $transition-timing-base;
  cursor: pointer;
  outline: none;

  &:hover {
    border-color: var(--sp-color-primary);
  }

  &:focus {
    border-color: var(--sp-color-primary);
    box-shadow: 0 0 0 2px var(--sp-color-primary-lightest);
  }

  &--checked {
    background: var(--sp-color-primary);
    border-color: var(--sp-color-primary);

    .sp-list-item__checkbox-icon {
      color: #fff;
      font-size: 10px;
      font-weight: bold;
      line-height: 1;
    }
  }

  &--disabled {
    background: $background-color-disabled;
    border-color: $border-color-disabled;
    cursor: not-allowed;

    &.sp-list-item__checkbox-input--checked {
      background: $color-text-disabled;
      border-color: $color-text-disabled;
    }
  }
}

// ========== 内容区域 ==========
.sp-list-item__content {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0; // 防止文本溢出
}

.sp-list-item__main {
  flex: 1;
  min-width: 0;
}

.sp-list-item__title {
  font-size: $font-size-base;
  font-weight: $font-weight-base;
  color: $color-text-primary;
  line-height: $line-height-base;
  margin: 0;
  word-break: break-word;

  // 单行省略
  &--ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.sp-list-item__description {
  font-size: $font-size-sm;
  color: $color-text-secondary;
  line-height: 1.4;
  margin: 2px 0 0 0;
  word-break: break-word;

  // 单行省略
  &--ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // 多行省略
  &--clamp {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.sp-list-item__suffix {
  margin-left: #{$spacing-sm};
  flex-shrink: 0;
  color: $color-text-secondary;
  font-size: $font-size-sm;
}

// ========== 响应式设计 ==========
@media (max-width: 768px) {
  .sp-list-item {
    padding: #{$spacing-xs} #{$spacing-sm};

    .sp-list-item__title {
      font-size: $font-size-sm;
    }

    .sp-list-item__description {
      font-size: 12px;
    }
  }

  .sp-list--large .sp-list-item {
    padding: #{$spacing-sm} #{$spacing-md};
  }
}

// ========== 暗色主题支持 ==========
@media (prefers-color-scheme: dark) {
  .sp-list {
    background: $background-color-dark;
    border-color: $border-color-dark;

    .sp-list-item {
      border-bottom-color: $border-color-dark;

      &:hover {
        background: color.adjust($background-color-dark, $lightness: 5%);
      }

      .sp-list-item__title {
        color: $color-text-dark;
      }

      .sp-list-item__description {
        color: $color-text-placeholder-dark;
      }
    }

    .sp-list-item__checkbox-input {
      background: $background-color-dark;
      border-color: $border-color-dark;

      &:hover {
        border-color: $color-primary-dark;
      }

      &--checked {
        background: $color-primary-dark;
        border-color: $color-primary-dark;
      }
    }
  }
}

// ========== 动画效果 ==========
.sp-list-item__checkbox-input {
  .sp-list-item__checkbox-icon {
    transform: scale(0);
    transition: transform 0.2s $transition-timing-base;
  }

  &--checked .sp-list-item__checkbox-icon {
    transform: scale(1);
  }
}

// ========== 无障碍支持 ==========
.sp-list-item:focus-visible {
  outline: 2px solid var(--sp-color-primary);
  outline-offset: -2px;
}

// 高对比度模式
@media (prefers-contrast: high) {
  .sp-list-item {
    border-bottom-width: 2px;

    &--selected {
      border-left-width: 4px;
    }
  }

  .sp-list-item__checkbox-input {
    border-width: 2px;
  }
}
