<text>
# Select 组件

**功能特点：**
- 支持多种选择选项
- 可以绑定数据模型
- 响应式交互效果
</text>

<template>
    <sp-select
        v-model="basicValue"
        :options="basicOptions"
        placeholder="请选择一个选项"
        style="width: 200px;"
    />
</template>

<script setup lang="ts">
import { ref } from 'vue'

const basicValue = ref('')

const basicOptions = [
  { label: '选项一', value: 1 },
  { label: '选项二', value: 'option2' },
  { label: '选项三', value: 'option3' },
  { label: '选项四', value: 'option4' },
  { label: '选项五', value: 'option5' },
]
</script>
