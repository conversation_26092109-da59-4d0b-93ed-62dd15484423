<template>
  <div style="padding: 2rem; max-width: 600px; margin: 0 auto;">
    <h1>SPInputField 测试页面</h1>
    
    <div style="margin-bottom: 2rem;">
      <h2>基础测试</h2>
      <SPInputField
        v-model="testValue"
        label="测试输入框"
        placeholder="请输入内容"
        style="margin-bottom: 1rem;"
      />
      <p>当前值: {{ testValue }}</p>
    </div>

    <div style="margin-bottom: 2rem;">
      <h2>带前缀后缀</h2>
      <SPInputField
        v-model="prefixValue"
        label="金额"
        prefix="¥"
        suffix="元"
        placeholder="0.00"
        style="margin-bottom: 1rem;"
      />
      <p>当前值: {{ prefixValue }}</p>
    </div>

    <div style="margin-bottom: 2rem;">
      <h2>不同变体</h2>
      
      <div style="margin-bottom: 1rem;">
        <h3>Filled (默认)</h3>
        <SPInputField
          v-model="filledValue"
          label="Filled 输入框"
          variant="filled"
          placeholder="Filled 变体"
        />
      </div>

      <div style="margin-bottom: 1rem;">
        <h3>Outlined</h3>
        <SPInputField
          v-model="outlinedValue"
          label="Outlined"
          variant="outlined"
          placeholder="Outlined 变体"
        />
      </div>

      <div style="margin-bottom: 1rem;">
        <h3>Underlined</h3>
        <SPInputField
          v-model="underlinedValue"
          label="Underlined 输入框"
          variant="underlined"
          placeholder="Underlined 变体"
        />
      </div>

      <div style="margin-bottom: 1rem;">
        <h3>Plain</h3>
        <SPInputField
          v-model="plainValue"
          label="Plain 输入框"
          variant="plain"
          placeholder="Plain 变体"
        />
      </div>
    </div>

    <div style="margin-bottom: 2rem;">
      <h2>功能测试</h2>
      
      <div style="margin-bottom: 1rem;">
        <h3>可清除</h3>
        <SPInputField
          v-model="clearableValue"
          label="可清除输入框"
          placeholder="输入内容后显示清除按钮"
          clearable
        />
      </div>

      <div style="margin-bottom: 1rem;">
        <h3>计数器</h3>
        <SPInputField
          v-model="counterValue"
          label="限制50字符"
          placeholder="输入内容..."
          :maxlength="50"
          counter
        />
      </div>

      <div style="margin-bottom: 1rem;">
        <h3>错误状态</h3>
        <SPInputField
          v-model="errorValue"
          label="邮箱地址"
          placeholder="请输入邮箱"
          :error="!isValidEmail(errorValue) && errorValue.length > 0"
          :error-messages="!isValidEmail(errorValue) && errorValue.length > 0 ? ['请输入有效的邮箱地址'] : []"
        />
      </div>
    </div>

    <div style="margin-top: 2rem; padding: 1rem; background: #f5f5f5; border-radius: 8px;">
      <h3>调试信息</h3>
      <pre>{{ {
        testValue,
        prefixValue,
        filledValue,
        outlinedValue,
        underlinedValue,
        plainValue,
        clearableValue,
        counterValue,
        errorValue
      } }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SPInputField from '../../../packages/ui/src/components/SPInputField/SPInputField.vue'

// 响应式数据
const testValue = ref('')
const prefixValue = ref('')
const filledValue = ref('')
const outlinedValue = ref('')
const underlinedValue = ref('')
const plainValue = ref('')
const clearableValue = ref('')
const counterValue = ref('')
const errorValue = ref('')

// 邮箱验证函数
const isValidEmail = (email: string): boolean => {
  if (!email) return true // 空值不显示错误
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}
</script>

<style scoped>
h1, h2, h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

h2 {
  border-bottom: 2px solid #ecf0f1;
  padding-bottom: 0.5rem;
}

h3 {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

pre {
  background: #fff;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-size: 12px;
  overflow-x: auto;
}
</style>
