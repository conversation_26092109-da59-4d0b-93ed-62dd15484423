/**
 * 输入框控件配置
 * 定义所有内置控件的配置信息
 */

import type { ControlConfig } from './types'
import ClearControl from './ClearControl.vue'
import PasswordControl from './PasswordControl.vue'
import IconControl from './IconControl.vue'
import WordLimitControl from './WordLimitControl.vue'
import NumberControls from './number.vue'
import SearchControls from './search.vue'
import TelControls from './tel.vue'
import SmsCodeControls from './smscode.vue'

/** 清除控件配置 */
export const clearControlConfig: ControlConfig = {
  type: 'clear',
  component: ClearControl,
  position: 'append',
  order: 100,
  condition: context => {
    return context.inputLogic.showClearIcon.value
  },
  propsFactory: context => ({
    iconSize: context.slotProps.iconSize || 16,
    disabled: context.computed.computedDisabled?.value ?? false,
  }),
  eventsFactory: context => ({
    clear: () => {
      context.inputLogic.clear()
      context.emit('update:value', undefined)
      context.emit('clear')
      // 清除后重新聚焦输入框
      context.inputLogic.focus()
    },
  }),
}

/** 密码控件配置 */
export const passwordControlConfig: ControlConfig = {
  type: 'password',
  component: PasswordControl,
  position: 'append',
  order: 90,
  condition: context => {
    return context.inputLogic.showPasswordIcon.value
  },
  propsFactory: context => ({
    iconSize: context.slotProps.iconSize || 16,
    disabled: context.computed.computedDisabled.value,
    iconName: context.inputLogic.passwordIconName.value,
  }),
  eventsFactory: context => ({
    toggle: () => {
      context.inputLogic.togglePassword()
      // 切换密码后重新聚焦输入框
      context.inputLogic.focus()
    },
  }),
}

/** 数字控件配置 */
export const numberControlConfig: ControlConfig = {
  type: 'number',
  component: NumberControls,
  position: 'append',
  order: 80,
  condition: context => {
    return context.inputLogic.showNumberControls.value
  },
  propsFactory: context => ({
    iconSize: context.slotProps.iconSize,
    canIncrement: context.inputLogic.canIncrement.value,
    canDecrement: context.inputLogic.canDecrement.value,
  }),
  eventsFactory: context => ({
    increment: () => {
      context.inputLogic.incrementNumber()
      // 操作后重新聚焦输入框
      context.inputLogic.focus()
    },
    decrement: () => {
      context.inputLogic.decrementNumber()
      // 操作后重新聚焦输入框
      context.inputLogic.focus()
    },
    focus: () => {
      if (!context.computed.computedDisabled.value && !context.props.readonly) {
        context.inputLogic.focus()
      }
    },
  }),
}

/** 搜索控件配置 */
export const searchControlConfig: ControlConfig = {
  type: 'search',
  component: SearchControls,
  position: 'append',
  order: 70,
  condition: context => {
    return context.inputLogic.showSearchControls.value
  },
  propsFactory: context => ({
    iconSize: context.slotProps.iconSize || 16,
    canSearch:
      !context.computed.computedDisabled.value &&
      !context.inputLogic.isSearchLoading.value,
    canClear:
      !context.computed.computedDisabled.value &&
      !context.inputLogic.isSearchLoading.value,
    showClear: true,
    hasValue: context.inputLogic.hasValue.value,
  }),
  eventsFactory: context => ({
    search: () => {
      context.inputLogic.executeSearch()
      // 搜索后重新聚焦输入框
      context.inputLogic.focus()
    },
    clear: () => {
      context.inputLogic.clearSearch()
      // 清除后重新聚焦输入框
      context.inputLogic.focus()
    },
    focus: () => {
      if (!context.computed.computedDisabled.value && !context.props.readonly) {
        context.inputLogic.focus()
      }
    },
  }),
}

/** 电话控件配置 */
export const telControlConfig: ControlConfig = {
  type: 'tel',
  component: TelControls,
  position: 'prepend',
  order: 10,
  condition: context => {
    return context.inputLogic.showTelControls.value
  },
  propsFactory: context => ({
    iconSize: context.slotProps.iconSize || 16,
    disabled: context.computed.computedDisabled.value,
    selectedCountryCode: context.inputLogic.selectedCountryCode.value,
  }),
  eventsFactory: context => ({
    'country-select': context.inputLogic.handleCountrySelect,
    'update:selectedCountryCode': (code: string) => {
      context.inputLogic.selectedCountryCode.value = code
    },
    focus: () => {
      if (!context.computed.computedDisabled.value && !context.props.readonly) {
        context.inputLogic.focus()
      }
    },
  }),
}

/** 短信验证码控件配置 */
export const smsCodeControlConfig: ControlConfig = {
  type: 'smscode',
  component: SmsCodeControls,
  position: 'append',
  order: 60,
  condition: context => {
    return context.inputLogic.showSmsCodeControls.value
  },
  propsFactory: context => ({
    countdown: context.props.smsCountdown,
    sendText: context.props.smsSendText,
    resendText: context.props.smsResendText,
    canSend: !context.computed.computedDisabled.value,
  }),
  eventsFactory: context => ({
    'sms-send': () => {
      context.emit('sms-send')
      // 发送短信后聚焦输入框
      context.inputLogic.focus()
    },
    'sms-start': (countdown: number) => context.emit('sms-start', countdown),
    'sms-tick': (remaining: number) => context.emit('sms-tick', remaining),
    'sms-end': () => context.emit('sms-end'),
    focus: () => {
      if (!context.computed.computedDisabled.value && !context.props.readonly) {
        context.inputLogic.focus()
      }
    },
  }),
}

/** 字数统计控件配置 */
export const wordLimitControlConfig: ControlConfig = {
  type: 'wordLimit',
  component: WordLimitControl,
  position: 'append',
  order: 50,
  condition: context => {
    return context.props.showWordLimit && context.props.maxlength
  },
  propsFactory: context => ({
    wordCount: context.inputLogic.wordCount.value,
    maxlength: context.props.maxlength,
  }),
  eventsFactory: () => ({
    // 字数统计控件不需要事件处理
  }),
}

/** 前置图标控件配置 */
export const prependIconControlConfig: ControlConfig = {
  type: 'prependIcon',
  component: IconControl,
  position: 'prepend',
  order: 20,
  condition: context => {
    return (
      !!context.props.prependIconInner &&
      !context.inputLogic.showTelControls?.value
    )
  },
  propsFactory: context => ({
    iconSize: context.slotProps.iconSize || 16,
    disabled: context.computed.computedDisabled.value,
    iconName: context.props.prependIconInner,
    clickable: true,
    iconClass: 'sp-input-field__prepend-icon',
  }),
  eventsFactory: context => ({
    click: (event: MouseEvent) => {
      context.emit('click:prepend-inner', event)
    },
  }),
}

/** 后置图标控件配置 */
export const appendIconControlConfig: ControlConfig = {
  type: 'appendIcon',
  component: IconControl,
  position: 'append',
  order: 40,
  condition: context => {
    return !!context.props.appendIconInner
  },
  propsFactory: context => ({
    iconSize: context.slotProps.iconSize || 16,
    disabled: context.computed.computedDisabled.value,
    iconName: context.props.appendIconInner,
    clickable: true,
    iconClass: 'sp-input-field__append-icon',
  }),
  eventsFactory: context => ({
    click: (event: MouseEvent) => {
      context.emit('click:append-inner', event)
    },
  }),
}

/** 外部前置图标控件配置 */
export const prependIconExternalControlConfig: ControlConfig = {
  type: 'prependIconExternal',
  component: IconControl,
  position: 'prependOuter',
  order: 5,
  condition: context => {
    return !!context.props.prependIcon
  },
  propsFactory: context => ({
    iconSize: context.slotProps.iconSize || 16,
    disabled: context.computed.computedDisabled?.value ?? false,
    iconName: context.props.prependIcon,
    clickable: true,
    iconClass: 'sp-input-field__prepend-icon-external',
  }),
  eventsFactory: context => ({
    click: (event: MouseEvent) => {
      context.emit('click:prepend', event)
    },
  }),
}

/** 外部后置图标控件配置 */
export const appendIconExternalControlConfig: ControlConfig = {
  type: 'appendIconExternal',
  component: IconControl,
  position: 'appendOuter',
  order: 5,
  condition: context => {
    return !!context.props.appendIcon
  },
  propsFactory: context => ({
    iconSize: context.slotProps.iconSize || 16,
    disabled: context.computed.computedDisabled?.value ?? false,
    iconName: context.props.appendIcon,
    clickable: true,
    iconClass: 'sp-input-field__append-icon-external',
  }),
  eventsFactory: context => ({
    click: (event: MouseEvent) => {
      context.emit('click:append', event)
    },
  }),
}

/** 所有内置控件配置 */
export const builtinControlConfigs: ControlConfig[] = [
  clearControlConfig,
  passwordControlConfig,
  numberControlConfig,
  searchControlConfig,
  telControlConfig,
  smsCodeControlConfig,
  wordLimitControlConfig,
  prependIconExternalControlConfig,
  appendIconExternalControlConfig,
  prependIconControlConfig,
  appendIconControlConfig,
]
