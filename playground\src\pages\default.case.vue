<text>
# Button2 组件

Button 是一个功能丰富的按钮组件，支持多种类型、尺寸和状态。

**功能特点：**
- 支持多种按钮类型
- 可以添加点击事件
- 响应式交互效果
</text>

<template>
    <sp-button type="primary">主要按钮</sp-button>
    <sp-button type="secondary">次要按钮</sp-button>
    <sp-button type="success">成功按钮</sp-button>
    <sp-button type="warning">警告按钮</sp-button>
    <sp-button type="danger">危险按钮</sp-button>
    <sp-button type="outline">轮廓按钮</sp-button>
</template>