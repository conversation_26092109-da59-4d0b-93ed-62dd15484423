import { defineConfig } from 'vitepress'
import { caseFilePlugin } from './vite-plugin-case.js'
import { markdownCasePlugin } from './markdown-case-plugin.js'
import { resolve, dirname } from 'path'
import { fileURLToPath } from 'url'

const __dirname = dirname(fileURLToPath(import.meta.url))

export default defineConfig({
  title: 'Speed UI',
  description: 'Vue 3 组件库',
  
  markdown: {
    config: (md) => {
      markdownCasePlugin(md)
    }
  },
  
  vite: {
    plugins: [
      caseFilePlugin(),
      {
        name: 'docs-path-resolver',
        resolveId(id) {
          if (id.startsWith('/docs/')) {
            const relativePath = id.replace('/docs/', '')
            return resolve(__dirname, '..', relativePath)
          }
          return null
        }
      }
    ],
    server: {
      fs: {
        allow: ['..', '../../packages', '../..', '../cases']
      }
    },
    resolve: {
      alias: {
        // '@button-cases': resolve(__dirname, '../../packages/ui/src/components/Button/cases'),
        '@/packages': resolve(__dirname, '../../packages'),
        '@/cases': resolve(__dirname, '../cases'),
        '/docs/': resolve(__dirname, '../') + '/'
      }
    },
    optimizeDeps: {
      exclude: ['@button-cases']
    }
  },
  
  themeConfig: {
    // 禁用默认导航栏，使用自定义导航栏
    // nav: [],
    
    // 禁用默认的右侧目录
    outline: false,
    
    // 恢复侧边栏配置
    sidebar: {
      '/guide/': [
        {
          text: '指南',
          items: [
            { text: '快速开始', link: '/guide/getting-started' },
            { text: '安装', link: '/guide/installation' },
            { text: '主题定制', link: '/guide/theming' }
          ]
        }
      ],
      '/components/': [
        {
          text: '基础组件',
          items: [
            { text: 'Button 按钮', link: '/components/button' },
            { text: 'Icon 图标', link: '/components/icon' },
            { text: 'Link 链接', link: '/components/link' }
          ]
        },
        {
          text: '表单组件',
          items: [
            { text: 'Input 输入框', link: '/components/input' },
            { text: 'Select 选择器', link: '/components/select' },
            { text: 'Checkbox 多选框', link: '/components/checkbox' }
          ]
        }
      ]
    },
    
    socialLinks: [
      { icon: 'github', link: 'https://github.com/your-username/speed-ui' }
    ]
  }
})