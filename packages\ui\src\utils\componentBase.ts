import { computed, inject, ref, type ComputedRef } from 'vue'
import { classNames } from './classNames'

/**
 * 组件基础属性接口
 * 定义所有组件共享的基础属性
 */
export interface BaseComponentProps {
  /** 组件尺寸 */
  size?: 'small' | 'medium' | 'large' | 'huge'
  /** 是否禁用 */
  disabled?: boolean
  /** 验证状态 */
  validateState?: 'success' | 'warning' | 'error'
  /** 验证消息 */
  validateMessage?: string
  /** 自定义样式 */
  attrStyle?: import('vue').CSSProperties
  /** 自定义类名 */
  className?: string
}

/**
 * 组件状态管理接口
 * 定义组件常用的状态属性
 */
export interface ComponentState {
  /** 是否聚焦 */
  isFocused: boolean
  /** 是否加载中 */
  isLoading: boolean
  /** 是否激活 */
  isActive: boolean
  /** 是否悬停 */
  isHovered: boolean
}

/**
 * 表单字段接口
 * 定义表单集成相关的属性和方法
 */
export interface FormItemField {
  value: ComputedRef<any>
  setValue: (value: any) => void
  handleBlur: () => void
  validateState?: ComputedRef<string | undefined>
  errors: ComputedRef<string[]>
  meta: {
    pending: boolean
    touched: boolean
    valid: boolean
  }
}

/**
 * 使用表单字段集成
 * 提供与表单组件的集成功能
 * @param props 组件属性
 * @param emit 事件发射器
 * @returns 表单字段相关的计算属性和方法
 */
export function useFormField(props: any, emit: any) {
  // 注入表单字段上下文
  const formItemField = inject<FormItemField | undefined>('spFormItemField', undefined)

  /**
   * 计算验证状态
   * 优先使用表单字段的验证状态，其次使用组件自身的验证状态
   */
  const computedValidateState = computed(() => {
    if (formItemField) {
      if (formItemField.validateState) {
        const state = formItemField.validateState.value
        if (state === 'validating') return 'warning'
        return state || undefined
      }
      const meta = formItemField.meta
      if (meta.pending) return 'warning'
      if (meta.touched && !meta.valid) return 'error'
      if (meta.touched && meta.valid) return 'success'
      return undefined
    }
    return props.validateState
  })

  /**
   * 计算验证消息
   * 优先使用表单字段的错误消息，其次使用组件自身的验证消息
   */
  const computedValidateMessage = computed(() => {
    if (formItemField && formItemField.errors.value[0]) {
      return formItemField.errors.value[0]
    }
    return props.validateMessage
  })

  /**
   * 处理值更新
   * 同时更新表单字段和触发组件事件
   */
  const handleValueUpdate = (value: any) => {
    if (formItemField) {
      formItemField.setValue(value)
    }
    emit('update:value', value)
  }

  /**
   * 处理失焦事件
   * 触发表单字段的失焦处理和组件事件
   */
  const handleBlur = (event: FocusEvent) => {
    if (formItemField) {
      formItemField.handleBlur()
    }
    emit('blur', event)
  }

  return {
    formItemField,
    computedValidateState,
    computedValidateMessage,
    handleValueUpdate,
    handleBlur
  }
}

/**
 * 使用组件状态管理
 * 提供组件常用状态的响应式管理
 * @returns 状态管理相关的响应式引用和方法
 */
export function useComponentState() {
  const isFocused = ref(false)
  const isLoading = ref(false)
  const isActive = ref(false)
  const isHovered = ref(false)

  /**
   * 设置聚焦状态
   */
  const setFocused = (focused: boolean) => {
    isFocused.value = focused
  }

  /**
   * 设置加载状态
   */
  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  /**
   * 设置激活状态
   */
  const setActive = (active: boolean) => {
    isActive.value = active
  }

  /**
   * 设置悬停状态
   */
  const setHovered = (hovered: boolean) => {
    isHovered.value = hovered
  }

  /**
   * 重置所有状态
   */
  const resetStates = () => {
    isFocused.value = false
    isLoading.value = false
    isActive.value = false
    isHovered.value = false
  }

  return {
    // 状态
    isFocused,
    isLoading,
    isActive,
    isHovered,
    // 方法
    setFocused,
    setLoading,
    setActive,
    setHovered,
    resetStates
  }
}

/**
 * 生成组件类名
 * 根据组件属性生成标准的CSS类名
 * @param baseClass 基础类名
 * @param props 组件属性
 * @param state 组件状态
 * @returns 完整的类名字符串
 */
export function generateComponentClasses(
  baseClass: string,
  props: BaseComponentProps,
  state?: Partial<ComponentState>
): string {
  return classNames(
    baseClass,
    {
      // 尺寸类名
      [`${baseClass}--${props.size}`]: props.size,
      // 状态类名
      [`${baseClass}--disabled`]: props.disabled,
      [`${baseClass}--${props.validateState}`]: props.validateState,
      // 组件状态类名
      [`${baseClass}--focused`]: state?.isFocused,
      [`${baseClass}--loading`]: state?.isLoading,
      [`${baseClass}--active`]: state?.isActive,
      [`${baseClass}--hovered`]: state?.isHovered,
    },
    // 自定义类名
    props.className
  )
}

/**
 * 默认组件属性
 * 提供组件的默认属性值
 */
export const defaultComponentProps: Partial<BaseComponentProps> = {
  size: 'medium',
  disabled: false,
  validateState: undefined,
  validateMessage: undefined,
  attrStyle: undefined,
  className: undefined
}