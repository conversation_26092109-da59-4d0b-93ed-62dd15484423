const { readFileSync, writeFileSync, existsSync, mkdirSync, readdirSync, statSync } = require('fs')
const { resolve, join, dirname } = require('path')

/**
 * 递归查找 case 文件
 */
function findCaseFiles(dir, files = []) {
  const items = readdirSync(dir)
  
  for (const item of items) {
    const fullPath = join(dir, item)
    const stat = statSync(fullPath)
    
    if (stat.isDirectory() && item === 'cases') {
      // 如果是 cases 目录，查找其中的 .case.vue 文件
      const caseDir = fullPath
      const caseItems = readdirSync(caseDir)
      
      for (const caseItem of caseItems) {
        if (caseItem.endsWith('.case.vue')) {
          files.push(join(caseDir, caseItem))
        }
      }
    } else if (stat.isDirectory()) {
      // 递归查找其他目录
      findCaseFiles(fullPath, files)
    }
  }
  
  return files
}

/**
 * 同步 case 文件从源头到 docs 目录
 */
function syncCases() {
  console.log('🚀 开始同步 case 文件...')
  
  // 查找所有组件的 case 文件
  const componentsDir = resolve('packages/ui/src/components')
  const caseFiles = findCaseFiles(componentsDir)
  
  console.log(`📁 找到 ${caseFiles.length} 个 case 文件:`)
  caseFiles.forEach(file => console.log(`  - ${file}`))
  
  for (const sourceFile of caseFiles) {
    try {
      // 提取文件名
      const fileName = sourceFile.split(/[\/\\]/).pop()
      
      // 目标路径
      const targetFile = resolve('docs/components', fileName)
      
      // 确保目标目录存在
      const targetDir = dirname(targetFile)
      if (!existsSync(targetDir)) {
        mkdirSync(targetDir, { recursive: true })
      }
      
      // 读取源文件内容
      const content = readFileSync(sourceFile, 'utf-8')
      
      // 写入目标文件
      writeFileSync(targetFile, content, 'utf-8')
      
      console.log(`✅ 同步: ${sourceFile} -> ${targetFile}`)
      
    } catch (error) {
      console.error(`❌ 同步失败: ${sourceFile}`, error)
    }
  }
  
  console.log('🎉 case 文件同步完成!')
}

// 如果直接运行这个脚本
if (require.main === module) {
  syncCases()
}

module.exports = { syncCases } 