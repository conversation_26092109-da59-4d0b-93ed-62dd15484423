<template>
  <div class="page-container">
    <h1>清除功能测试</h1>
    
    <div class="demo-section">
      <h2>Input 清除测试</h2>
      <div class="demo-item">
        <label>只有 clearable（自动清空）：</label>
        <sp-input
          v-model:value="inputValue1"
          placeholder="只设置 clearable，点击清除应该自动清空"
          clearable
        />
        <span class="value-display">值：{{ inputValue1 || '(空)' }}</span>
      </div>

      <div class="demo-item">
        <label>clearable + @clear 事件：</label>
        <sp-input
          v-model:value="inputValue2"
          placeholder="设置 clearable + @clear，应该自动清空并触发事件"
          clearable
          @clear="onInputClear"
        />
        <span class="value-display">值：{{ inputValue2 || '(空)' }}</span>
      </div>

      <div class="demo-item">
        <label>密码输入框：</label>
        <sp-input
          v-model:value="passwordValue"
          type="password"
          placeholder="输入密码后点击清除按钮"
          clearable
          show-password
          @clear="onPasswordClear"
        />
        <span class="value-display">值：{{ passwordValue || '(空)' }}</span>
      </div>
    </div>

    <div class="demo-section">
      <h2>Select 清除测试</h2>
      <div class="demo-item">
        <label>只有 clearable（自动清空）：</label>
        <Select
          v-model:value="selectValue1"
          :options="options"
          placeholder="只设置 clearable，点击清除应该自动清空"
          clearable
        />
        <span class="value-display">值：{{ selectValue1 || '(空)' }}</span>
      </div>

      <div class="demo-item">
        <label>clearable + @clear 事件：</label>
        <Select
          v-model:value="selectValue2"
          :options="options"
          placeholder="设置 clearable + @clear，应该自动清空并触发事件"
          clearable
          @clear="onSelectClear"
        />
        <span class="value-display">值：{{ selectValue2 || '(空)' }}</span>
      </div>
    </div>

    <div class="demo-section">
      <h2>事件日志</h2>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          {{ log }}
        </div>
      </div>
      <button @click="clearLogs">清空日志</button>
    </div>

    <div class="demo-section">
      <h2>测试说明</h2>
      <div class="test-info">
        <h3>✅ 期望行为：</h3>
        <ul>
          <li><strong>只设置 clearable</strong>：点击清除按钮应该自动清空内容，无需用户写代码</li>
          <li><strong>设置 clearable + @clear</strong>：自动清空内容 + 触发用户的自定义事件</li>
          <li><strong>焦点保持</strong>：清除后输入框应该保持焦点，可以立即继续输入</li>
        </ul>

        <h3>🧪 测试步骤：</h3>
        <ol>
          <li>观察初始值是否正确显示</li>
          <li>点击清除按钮（X 图标）</li>
          <li>检查：内容是否被自动清空？</li>
          <li>检查：输入框是否仍然有焦点？</li>
          <li>检查：是否可以立即继续输入？</li>
          <li>检查：有 @clear 事件的组件是否在日志中显示事件触发？</li>
        </ol>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import Select from '../../../packages/ui/src/components/select/select.vue'

  const inputValue1 = ref('测试内容1')
  const inputValue2 = ref('测试内容2')
  const passwordValue = ref('password123')
  const selectValue1 = ref('1')
  const selectValue2 = ref('2')
  const logs = ref<string[]>([])

  const options = [
    { label: '选项1', value: '1' },
    { label: '选项2', value: '2' },
    { label: '选项3', value: '3' },
  ]

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    logs.value.unshift(`[${timestamp}] ${message}`)
    if (logs.value.length > 10) {
      logs.value = logs.value.slice(0, 10)
    }
  }

  const onInputClear = () => {
    addLog('Input 清除事件触发')
  }

  const onPasswordClear = () => {
    addLog('Password 清除事件触发')
  }

  const onSelectClear = () => {
    addLog('Select 清除事件触发')
  }

  const clearLogs = () => {
    logs.value = []
  }
</script>

<style scoped>
  .page-container {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
  }

  .demo-section {
    margin: 30px 0;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
  }

  .demo-section h2 {
    margin-top: 0;
    color: #303133;
  }

  .demo-item {
    margin: 15px 0;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .demo-item label {
    min-width: 120px;
    font-weight: 500;
  }

  .value-display {
    margin-left: 10px;
    color: #909399;
    font-size: 12px;
  }

  .log-container {
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    background: #f9f9f9;
    font-family: monospace;
    font-size: 12px;
  }

  .log-item {
    margin: 2px 0;
    padding: 2px 0;
    border-bottom: 1px solid #eee;
  }

  button {
    margin-top: 10px;
    padding: 5px 10px;
    background: #409eff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  button:hover {
    background: #66b1ff;
  }

  ol {
    padding-left: 20px;
  }

  li {
    margin: 5px 0;
  }

  .test-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #409eff;
  }

  .test-info h3 {
    margin: 0 0 10px 0;
    color: #303133;
  }

  .test-info ul, .test-info ol {
    margin: 10px 0;
  }
</style>
