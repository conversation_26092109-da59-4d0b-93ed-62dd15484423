<!--
  Textarea.vue - 接口层
  使用 InputCore + useTextareaStyles 的统一架构
-->

<template>
  <InputCore
    ref="inputCoreRef"
    mode="custom"
    :value="currentValue"
    :variant="props.variant"
    :effect="props.effect"
    :size="props.size"
    :placeholder="props.placeholder"
    :disabled="props.disabled"
    :readonly="props.readonly"
    :clearable="props.clearable"
    :prefix-icon="props.prefixIcon"
    :suffix-icon="props.suffixIcon"
    :loading="props.loading"
    :focused="focused"
    :has-value="hasValueComputed"
    :error="props.error || computedValidateState === 'error'"
    :validate-state="computedValidateState"
    :validate-message="computedValidateMessage"
    v-bind="inputCoreProps"
    @click="handleWrapperClick"
    @focus="handleFocus"
    @blur="handleBlur"
    @clear="handleClear"
  >
    <!-- 前缀插槽 -->
    <template #prefix>
      <slot name="prefix" />
    </template>

    <!-- 后缀插槽 -->
    <template #suffix>
      <!-- 字数统计 -->
      <span
        v-if="props.showWordLimit && props.maxlength"
        :class="wordCountClasses"
      >
        {{ wordCount }}/{{ props.maxlength }}
      </span>

      <slot name="suffix" />
    </template>

    <!-- 内容区域 - 实际的 textarea 元素 -->
    <template #inner>
      <textarea
        ref="textareaRef"
        :value="currentValue"
        :placeholder="props.placeholder"
        :disabled="props.disabled"
        :readonly="props.readonly"
        :maxlength="props.maxlength"
        :rows="props.rows || 3"
        :cols="props.cols"
        :class="innerClasses"
        :style="{
          overflow: props.autosize ? 'hidden' : 'auto',
          resize: props.resize || 'vertical',
        }"
        @input="handleInput"
        @change="handleChange"
        @focus="handleFocus"
        @blur="handleBlur"
      />
    </template>
  </InputCore>
</template>

<script setup lang="ts">
  import { ref, computed, inject, nextTick, watch } from 'vue'
  import { useVModel } from '@speed-ui/hooks'
  import InputCore from '../input/inputcore'
  import { useTextareaStyles } from '../../composables/useTextareaStyles'
  import type { TextareaProps, TextareaEmits } from './types'

  /**
   * Textarea 组件 - 接口层
   *
   * 使用 InputCore + useTextareaStyles 的统一架构：
   * textarea.vue (接口层) → InputCore (通用基础层) + useTextareaStyles (样式层)
   */

  const props = withDefaults(defineProps<TextareaProps>(), {
    rows: 3,
    resize: 'vertical',
    autosize: false,
  })
  const emit = defineEmits<TextareaEmits>()

  // 表单字段注入（用于表单验证）
  const formItemField = inject<any>('spFormItemField', null)

  // 组件引用
  const inputCoreRef = ref<InstanceType<typeof InputCore> | null>(null)
  const textareaRef = ref<HTMLTextAreaElement | null>(null)

  // 状态管理
  const focused = ref(false)

  // 使用 useVModel 进行双向绑定
  const {
    value: currentValue,
    updateValue,
    hasValue: hasValueComputed,
  } = useVModel<string | undefined>(props, emit, {
    prop: 'value',
    event: 'update:value',
    defaultValue: props.value,
    formField: formItemField,
    onAfterUpdate: newValue => {
      emit('change', newValue)
    },
    debug: false,
  })

  // 验证状态 - 优先使用表单字段
  const computedValidateState = computed(() => {
    if (formItemField && formItemField.validateState) {
      return formItemField.validateState.value
    }
    return props.validateState
  })

  const computedValidateMessage = computed(() => {
    if (formItemField && formItemField.errors.value.length > 0) {
      return formItemField.errors.value[0]
    }
    return props.validateMessage
  })

  // 计算属性
  const wordCount = computed(() => {
    const value = currentValue.value
    return typeof value === 'string' ? value.length : 0
  })

  // 计算显示逻辑（确保类型正确）
  const computedDisabled = computed(() => !!props.disabled)

  // 使用样式 composable
  const { inputCoreProps, innerClasses, wordCountClasses } = useTextareaStyles(
    props,
    {
      computedDisabled,
      isFocused: focused,
      hasValue: hasValueComputed,
    }
  )

  // 事件处理
  const handleWrapperClick = () => {
    if (props.disabled || props.readonly) return
    textareaRef.value?.focus()
  }

  const handleInput = (event: Event) => {
    const target = event.target as HTMLTextAreaElement
    updateValue(target.value)
    emit('input', event)

    // 触发高度调整
    if (props.autosize) {
      nextTick(() => {
        adjustHeight()
      })
    }
  }

  const handleChange = (event: Event) => {
    // change 事件已经通过 useVModel 的 onAfterUpdate 处理
    emit('change', currentValue.value)
  }

  const handleFocus = (event: FocusEvent) => {
    focused.value = true
    emit('focus', event)
  }

  const handleBlur = (event: FocusEvent) => {
    focused.value = false
    emit('blur', event)

    // 触发表单验证
    if (formItemField) {
      formItemField.handleBlur()
    }
  }

  const handleClear = () => {
    updateValue('')
    emit('clear')
    textareaRef.value?.focus()
  }

  // Textarea 特有的自动调整高度功能
  const adjustHeight = () => {
    if (!props.autosize || !textareaRef.value) return

    const textarea = textareaRef.value

    // 重置高度以获取 scrollHeight
    textarea.style.height = 'auto'

    let height = textarea.scrollHeight

    // 如果 autosize 是对象，处理最小/最大行数
    if (typeof props.autosize === 'object') {
      const { minRows, maxRows } = props.autosize
      const lineHeight =
        parseInt(getComputedStyle(textarea).lineHeight, 10) || 20

      if (minRows) {
        height = Math.max(height, minRows * lineHeight)
      }
      if (maxRows) {
        height = Math.min(height, maxRows * lineHeight)
      }
    }

    textarea.style.height = `${height}px`
  }

  // 监听值变化，触发高度调整
  watch(
    () => currentValue.value,
    () => {
      if (props.autosize) {
        nextTick(() => {
          adjustHeight()
        })
      }
    }
  )

  // 暴露的方法
  const focus = () => {
    textareaRef.value?.focus()
  }

  const blur = () => {
    textareaRef.value?.blur()
  }

  const select = () => {
    textareaRef.value?.select()
  }

  const clear = () => {
    handleClear()
  }

  // 暴露给外部使用
  defineExpose({
    /** 使文本域获得焦点 */
    focus,
    /** 使文本域失去焦点 */
    blur,
    /** 选中文本域中的文字 */
    select,
    /** 清空文本域 */
    clear,
    /** 调整文本域高度（autosize 模式下） */
    adjustHeight,
    /** 文本域元素引用 */
    get textarea() {
      return textareaRef.value || null
    },
    /** 包装器元素引用 */
    get wrapper() {
      return inputCoreRef.value?.$refs?.wrapperRef || null
    },
  })
</script>

<script lang="ts">
  export default {
    name: 'SpTextarea',
  }
</script>
