{"name": "@speed-ui/size-helper", "version": "1.0.0", "description": "Speed UI 组件尺寸助手工具 - 用于统一管理组件尺寸相关的类型和工具函数", "license": "MIT", "author": "Speed UI Team", "keywords": ["speed-ui", "size", "component", "utility", "typescript", "helper"], "type": "module", "main": "./dist/index.cjs.js", "module": "./dist/index.es.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.es.js", "require": "./dist/index.cjs.js"}}, "files": ["dist", "src", "README.md"], "scripts": {"build": "vite build", "dev": "vite build --watch", "prepublishOnly": "npm run build"}, "devDependencies": {"vue": "^3.3.0"}, "peerDependencies": {"vue": "^3.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/speed-ui.git", "directory": "packages/common/size"}, "homepage": "https://speed-ui.dev", "bugs": {"url": "https://github.com/your-org/speed-ui/issues"}}