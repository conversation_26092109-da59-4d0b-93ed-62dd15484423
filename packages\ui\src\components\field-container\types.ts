/**
 * FieldContainer 纯样式容器类型定义
 * 仅包含样式、布局和基础插槽功能
 */

/** FieldContainer 简化属性 */
export interface FieldContainerProps {
  /** 标签文本 */
  label?: string
  /** 占位符文本 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 是否必填 */
  required?: boolean
  /** 是否错误状态 */
  error?: boolean
  /** 是否警告状态 */
  warning?: boolean
  /** 是否成功状态 */
  success?: boolean
  /** 是否加载中 */
  loading?: boolean
  /** 外观变体 */
  variant?: 'default' | 'underlined' | 'filled' | 'square' | 'unborder'
  /** 视觉效果 */
  effect?: 'none' | 'glow'
  /** 组件尺寸 */
  size?: 'small' | 'medium' | 'large'
  /** 帮助文本 */
  helperText?: string
  /** 标签是否始终浮动 */
  persistentLabel?: boolean
  /** 是否聚焦状态（外部控制） */
  focused?: boolean
  /** 是否有值（外部控制） */
  hasValue?: boolean
  /** 验证状态 */
  validateState?: 'success' | 'warning' | 'error' | ''
  /** 验证消息 */
  validateMessage?: string
  /** 前缀文本 */
  prefix?: string
  /** 后缀文本 */
  suffix?: string
}

/** FieldContainer 实例方法 */
export interface FieldContainerInstance {
  /** 包装器元素引用 */
  wrapper: HTMLDivElement | null
  /** 是否聚焦状态 */
  isFocused: boolean
  /** 是否有值 */
  hasValue: boolean
  /** 标签是否浮动 */
  isLabelFloating: boolean
  /** 验证状态 */
  validateState: string
}

/** 默认插槽 Props 类型 */
export interface FieldContainerSlotProps {
  /** 字段 ID */
  fieldId: string
  /** 输入元素类名 */
  inputClasses: string[]
  /** 输入元素样式 */
  inputStyle: Record<string, any>
  /** 占位符文本 */
  placeholder?: string
  /** 是否禁用 */
  disabled: boolean
  /** 是否只读 */
  readonly: boolean
  /** 图标尺寸 */
  iconSize: number
}

/** 前置区域插槽 Props 类型 */
export interface FieldContainerPrependSlotProps {
  /** 前置区域类名 */
  prependClasses: string[]
  /** 图标尺寸 */
  iconSize: number
}

/** 后置区域插槽 Props 类型 */
export interface FieldContainerAppendSlotProps {
  /** 后置区域类名 */
  appendClasses: string[]
  /** 图标尺寸 */
  iconSize: number
}

/** 功能区域插槽 Props 类型 */
export interface FieldContainerFunctionsSlotProps {
  /** 功能区域类名 */
  functionsClasses: string[]
  /** 图标尺寸 */
  iconSize: number
}
