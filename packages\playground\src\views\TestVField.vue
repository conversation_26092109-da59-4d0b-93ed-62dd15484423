<template>
  <div class="test-vfield">
    <h1>VField 组件测试</h1>
    
    <div class="demo-section">
      <h2>VField 不同变体</h2>
      
      <div class="demo-item">
        <h3>基础变体</h3>
        <div class="vfield-demo">
          <VField
            label="Filled 变体"
            variant="filled"
            class="demo-vfield"
            :dirty="!!vfieldValue1"
          >
            <input 
              type="text" 
              placeholder="请输入内容"
              v-model="vfieldValue1"
            />
          </VField>
          
          <VField
            label="Outlined 变体"
            variant="outlined"
            class="demo-vfield"
            :dirty="!!vfieldValue2"
          >
            <input 
              type="text" 
              placeholder="请输入内容"
              v-model="vfieldValue2"
            />
          </VField>
          
          <VField
            label="Underlined 变体"
            variant="underlined"
            class="demo-vfield"
            :dirty="!!vfieldValue3"
          >
            <input 
              type="text" 
              placeholder="请输入内容"
              v-model="vfieldValue3"
            />
          </VField>
          
          <VField
            label="Solo 变体"
            variant="solo"
            class="demo-vfield"
            :dirty="!!vfieldValue4"
          >
            <input 
              type="text" 
              placeholder="请输入内容"
              v-model="vfieldValue4"
            />
          </VField>
        </div>
      </div>

      <div class="demo-item">
        <h3>带图标的 VField</h3>
        <div class="vfield-demo">
          <VField
            label="带前置图标"
            variant="filled"
            prepend-inner-icon="👤"
            class="demo-vfield"
            :dirty="!!vfieldValue5"
          >
            <input 
              type="text" 
              placeholder="用户名"
              v-model="vfieldValue5"
            />
          </VField>
          
          <VField
            label="带后置图标"
            variant="outlined"
            append-inner-icon="📧"
            class="demo-vfield"
            :dirty="!!vfieldValue6"
          >
            <input 
              type="email" 
              placeholder="邮箱"
              v-model="vfieldValue6"
            />
          </VField>
          
          <VField
            label="可清除"
            variant="filled"
            :clearable="true"
            :dirty="!!vfieldValue7"
            class="demo-vfield"
            @click:clear="vfieldValue7 = ''"
          >
            <input 
              type="text" 
              placeholder="可清除的输入"
              v-model="vfieldValue7"
            />
          </VField>
        </div>
      </div>

      <div class="demo-item">
        <h3>状态演示</h3>
        <div class="vfield-demo">
          <VField
            label="错误状态"
            variant="outlined"
            :error="true"
            class="demo-vfield"
            :dirty="!!vfieldValue8"
          >
            <input 
              type="text" 
              placeholder="错误状态"
              v-model="vfieldValue8"
            />
          </VField>
          
          <VField
            label="禁用状态"
            variant="filled"
            :disabled="true"
            class="demo-vfield"
          >
            <input 
              type="text" 
              placeholder="禁用状态"
              disabled
            />
          </VField>
          
          <VField
            label="加载状态"
            variant="outlined"
            :loading="true"
            class="demo-vfield"
            :dirty="!!vfieldValue9"
          >
            <input 
              type="text" 
              placeholder="加载状态"
              v-model="vfieldValue9"
            />
          </VField>
        </div>
      </div>

      <div class="demo-item">
        <h3>自定义插槽</h3>
        <div class="vfield-demo">
          <VField
            label="自定义前置"
            variant="outlined"
            class="demo-vfield"
            :dirty="!!vfieldValue10"
          >
            <template #prepend-inner>
              <div style="color: #2196f3; margin-right: 8px;">🔍</div>
            </template>
            <input 
              type="text" 
              placeholder="搜索..."
              v-model="vfieldValue10"
            />
          </VField>
          
          <VField
            label="自定义后置"
            variant="filled"
            class="demo-vfield"
            :dirty="!!vfieldValue11"
          >
            <input 
              type="password" 
              placeholder="密码"
              v-model="vfieldValue11"
            />
            <template #append-inner>
              <div style="color: #666; cursor: pointer;">👁️</div>
            </template>
          </VField>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import VField from '@speed-ui/ui/src/components/field-container/VField.vue'

  // 响应式数据
  const vfieldValue1 = ref('')
  const vfieldValue2 = ref('')
  const vfieldValue3 = ref('')
  const vfieldValue4 = ref('')
  const vfieldValue5 = ref('')
  const vfieldValue6 = ref('')
  const vfieldValue7 = ref('')
  const vfieldValue8 = ref('')
  const vfieldValue9 = ref('')
  const vfieldValue10 = ref('')
  const vfieldValue11 = ref('')
</script>

<style scoped>
.test-vfield {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 40px;
}

.demo-item {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.vfield-demo {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.demo-vfield {
  margin-bottom: 16px;
}

.demo-vfield input {
  width: 100%;
  outline: none;
  border: none;
  background: transparent;
  font-size: 16px;
  color: inherit;
}

h1 {
  color: #333;
  margin-bottom: 30px;
}

h2 {
  color: #555;
  margin-bottom: 20px;
  border-bottom: 2px solid #2196f3;
  padding-bottom: 8px;
}

h3 {
  color: #666;
  margin-bottom: 15px;
  font-size: 16px;
}
</style>

<style>
/* 导入 VField 样式 */
@import '@speed-ui/ui/src/components/field-container/VField.scss';
</style>
