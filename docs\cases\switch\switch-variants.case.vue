<text>
> 通过添加 `square` 或 `vertical` 属性可以切换开关的样式
</text>

<template>
    <div class="button-demo-row">
        <span>默认样式</span>
        <sp-switch v-model:value="value1" />
        <span>方形样式</span>
        <sp-switch v-model:value="value2" square />
        <span>垂直样式</span>
        <sp-switch v-model:value="value3" vertical />
        <span>方形垂直</span>
        <sp-switch v-model:value="value4" square vertical />
    </div>
</template>

<script setup>
import { ref } from 'vue'

const value1 = ref(false)
const value2 = ref(true)
const value3 = ref(false)
const value4 = ref(true)
</script>