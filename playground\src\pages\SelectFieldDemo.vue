<template>
  <div class="select-field-demo">
    <h1>🎯 SelectField 组件演示</h1>
    <p class="description">
      SelectField 巧妙复用了 InputField 的所有功能，通过插槽替换输入框为选择器，
      享受浮动标签、表单验证等所有特性！
    </p>

    <!-- 基础用法 -->
    <section class="demo-section">
      <h2>🏷️ 基础浮动标签选择器</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h3>基础选择器</h3>
          <SelectField
            v-model:value="basicValue"
            label="选择城市"
            name="city"
            placeholder="请选择城市"
            :options="cityOptions"
clearable
          />
          <p class="value-display">选中值: {{ basicValue || '未选择' }}</p>
        </div>

        <div class="demo-item">
          <h3>必填字段</h3>
          <SelectField
            v-model:value="requiredValue"
            label="选择语言"
            name="language"
            placeholder="请选择编程语言"
            :options="languageOptions"
            :required="true"
            helper-text="这是必填字段"
          />
          <p class="value-display">选中值: {{ requiredValue || '未选择' }}</p>
        </div>

        <div class="demo-item">
          <h3>持久浮动标签</h3>
          <SelectField
            v-model:value="persistentValue"
            label="选择框架"
            name="framework"
            placeholder="请选择前端框架"
            :options="frameworkOptions"
            :persistent-label="true"
          />
          <p class="value-display">选中值: {{ persistentValue || '未选择' }}</p>
        </div>
      </div>
    </section>

    <!-- 多选模式 -->
    <section class="demo-section">
      <h2>🏷️ 多选模式</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h3>多选标签</h3>
          <SelectField
            v-model:value="multipleValues"
            label="选择技能"
            name="skills"
            placeholder="请选择你的技能"
            :options="skillOptions"
            :multiple="true"
            :clearable="true"
          />
          <p class="value-display">选中值: {{ multipleValues }}</p>
        </div>

        <div class="demo-item">
          <h3>多选必填</h3>
          <SelectField
            v-model:value="multipleRequiredValues"
            label="选择爱好"
            name="hobbies"
            placeholder="至少选择一个爱好"
            :options="hobbyOptions"
            :multiple="true"
            :required="true"
            helper-text="请至少选择一个爱好"
          />
          <p class="value-display">选中值: {{ multipleRequiredValues }}</p>
        </div>
      </div>
    </section>

    <!-- 尺寸变体 -->
    <section class="demo-section">
      <h2>📏 尺寸变体</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h3>小尺寸</h3>
          <SelectField
            v-model:value="smallValue"
            label="小尺寸选择器"
            name="small"
            size="small"
            :options="sizeOptions"
          />
        </div>

        <div class="demo-item">
          <h3>默认尺寸</h3>
          <SelectField
            v-model:value="defaultValue"
            label="默认尺寸选择器"
            name="default"
            size="default"
            :options="sizeOptions"
          />
        </div>

        <div class="demo-item">
          <h3>大尺寸</h3>
          <SelectField
            v-model:value="largeValue"
            label="大尺寸选择器"
            name="large"
            size="large"
            :options="sizeOptions"
          />
        </div>
      </div>
    </section>

    <!-- 状态演示 -->
    <section class="demo-section">
      <h2>🔧 状态演示</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h3>禁用状态</h3>
          <SelectField
            v-model:value="disabledValue"
            label="禁用选择器"
            name="disabled"
            :options="statusOptions"
            :disabled="true"
          />
        </div>

        <div class="demo-item">
          <h3>错误状态</h3>
          <SelectField
            v-model:value="errorValue"
            label="错误状态"
            name="error"
            :options="statusOptions"
            :error="true"
            helper-text="这里有个错误"
          />
        </div>

        <div class="demo-item">
          <h3>带验证</h3>
          <SelectField
            v-model:value="validateValue"
            label="验证字段"
            name="validate"
            :options="statusOptions"
            :rules="validationRules"
            @validate="handleValidate"
          />
          <p class="validation-info">{{ validationMessage }}</p>
        </div>
      </div>
    </section>

    <!-- 焦点行为测试 -->
    <section class="demo-section focus-test-section">
      <h2>🎯 焦点行为测试</h2>
      <p class="test-description">
        测试选择选项后输入框是否保持焦点状态（浮动标签应保持上浮，输入框边框保持激活颜色）
      </p>
      <div class="demo-grid">
        <div class="demo-item">
          <h3>单选焦点测试</h3>
          <SelectField
            ref="focusTestRef"
            v-model:value="focusTestValue"
            label="测试选择后焦点保持"
            name="focusTest"
            placeholder="点击选择一个选项"
            :options="focusTestOptions"
            @focus="onFocusTest"
            @blur="onBlurTest"
          />
          <div class="focus-status">
            <div
              class="status-indicator"
              :class="{ active: isFocused }"
            >
              焦点状态: {{ isFocused ? '已聚焦 ✅' : '未聚焦 ❌' }}
            </div>
            <p class="value-display">
              选中值: {{ focusTestValue || '未选择' }}
            </p>
            <div class="test-instructions">
              <p>
                💡
                <strong>测试步骤：</strong>
              </p>
              <ol>
                <li>点击输入框，观察浮动标签上浮，边框变色</li>
                <li>选择一个选项</li>
                <li>
                  观察选择后浮动标签是否保持上浮状态，边框是否保持激活颜色
                </li>
              </ol>
            </div>
          </div>
          <div class="test-buttons">
            <button
              @click="testFocus"
              class="test-btn primary"
            >
              手动聚焦
            </button>
            <button
              @click="testBlur"
              class="test-btn"
            >
              手动失焦
            </button>
            <button
              @click="resetFocusTest"
              class="test-btn"
            >
              重置测试
            </button>
          </div>
        </div>

        <div class="demo-item">
          <h3>多选焦点测试</h3>
          <SelectField
            v-model:value="multipleFocusTestValue"
            label="多选焦点测试"
            name="multipleFocusTest"
            placeholder="选择多个选项"
            :options="focusTestOptions"
            :multiple="true"
            @focus="onMultipleFocusTest"
            @blur="onMultipleBlurTest"
          />
          <div class="focus-status">
            <div
              class="status-indicator"
              :class="{ active: isMultipleFocused }"
            >
              多选焦点状态: {{ isMultipleFocused ? '已聚焦 ✅' : '未聚焦 ❌' }}
            </div>
            <p class="value-display">
              选中值: {{ multipleFocusTestValue.join(', ') || '未选择' }}
            </p>
            <p class="test-note">
              💡 多选模式下，选择选项后下拉框应保持打开状态
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 自定义配置 -->
    <section class="demo-section">
      <h2>⚙️ 自定义配置</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h3>自定义键名</h3>
          <SelectField
            v-model:value="customKeyValue"
            label="自定义数据结构"
            name="customKey"
            :options="customKeyOptions"
            value-key="id"
            label-key="name"
            placeholder="选择用户"
          />
          <p class="value-display">选中ID: {{ customKeyValue }}</p>
        </div>

        <div class="demo-item">
          <h3>空数据提示</h3>
          <SelectField
            v-model:value="emptyValue"
            label="空数据演示"
            name="empty"
            :options="[]"
            empty-text="没有可选项"
            placeholder="暂无数据"
          />
        </div>

        <div class="demo-item">
          <h3>禁用选项</h3>
          <SelectField
            v-model:value="disabledOptionValue"
            label="部分选项禁用"
            name="disabledOption"
            :options="disabledOptions"
            placeholder="部分选项不可选"
          />
        </div>
      </div>
    </section>

    <!-- 表单集成演示 -->
    <section class="demo-section">
      <h2>📝 表单集成演示</h2>
      <div class="form-demo">
        <h3>完整表单示例</h3>
        <form
          @submit.prevent="handleSubmit"
          class="demo-form"
        >
          <SelectField
            v-model:value="form.category"
            label="项目分类"
            name="category"
            :options="categoryOptions"
            :required="true"
            :rules="categoryRules"
          />

          <SelectField
            v-model:value="form.tags"
            label="项目标签"
            name="tags"
            :options="tagOptions"
            :multiple="true"
            :required="true"
            helper-text="可选择多个标签"
          />

          <SelectField
            v-model:value="form.priority"
            label="优先级"
            name="priority"
            :options="priorityOptions"
            :clearable="true"
            helper-text="可选择项目优先级"
          />

          <div class="form-actions">
            <button
              type="submit"
              class="submit-btn"
            >
              提交表单
            </button>
            <button
              type="button"
              @click="resetForm"
              class="reset-btn"
            >
              重置
            </button>
          </div>
        </form>

        <div class="form-result">
          <h4>表单数据：</h4>
          <pre>{{ JSON.stringify(form, null, 2) }}</pre>
        </div>
      </div>
    </section>

    <!-- InputField 变体演示 -->
    <section class="demo-section">
      <h2>🎨 InputField 变体演示</h2>
      <p class="description">
        SelectField 完美继承了 InputField 的所有变体样式，享受一致的视觉体验！
      </p>

      <div class="demo-grid">
        <div class="demo-item">
          <h3>默认变体 (default)</h3>
          <SelectField
            v-model:value="variantDefaultValue"
            label="默认样式"
            name="variantDefault"
            variant="default"
            :options="variantOptions"
            placeholder="选择默认样式"
          />
        </div>

        <div class="demo-item">
          <h3>填充变体 (filled)</h3>
          <SelectField
            v-model:value="variantFilledValue"
            label="填充样式"
            name="variantFilled"
            variant="filled"
            :options="variantOptions"
            placeholder="选择填充样式"
          />
        </div>

        <div class="demo-item">
          <h3>下划线变体 (underlined)</h3>
          <SelectField
            v-model:value="variantUnderlinedValue"
            label="下划线样式"
            name="variantUnderlined"
            variant="underlined"
            :options="variantOptions"
            placeholder="选择下划线样式"
          />
        </div>

        <div class="demo-item">
          <h3>圆角变体 (pill)</h3>
          <SelectField
            v-model:value="variantPillValue"
            label="圆角样式"
            name="variantPill"
            variant="pill"
            :options="variantOptions"
            placeholder="选择圆角样式"
          />
        </div>
      </div>

      <div class="demo-grid">
        <div class="demo-item">
          <h3>方形变体 (square)</h3>
          <SelectField
            v-model:value="variantSquareValue"
            label="方形样式"
            name="variantSquare"
            variant="square"
            :options="variantOptions"
            placeholder="选择方形样式"
          />
        </div>

        <div class="demo-item">
          <h3>无边框变体 (unborder)</h3>
          <SelectField
            v-model:value="variantUnborderValue"
            label="无边框样式"
            name="variantUnborder"
            variant="unborder"
            :options="variantOptions"
            placeholder="选择无边框样式"
          />
        </div>

        <div class="demo-item">
          <h3>组合演示</h3>
          <SelectField
            v-model:value="variantComboValue"
            label="变体组合"
            name="variantCombo"
            variant="filled"
            size="large"
            :clearable="true"
            :options="variantOptions"
            placeholder="填充+大尺寸+可清除"
          />
        </div>

        <div class="demo-item">
          <h3>多选变体</h3>
          <SelectField
            v-model:value="variantMultipleValue"
            label="多选变体"
            name="variantMultiple"
            variant="underlined"
            :multiple="true"
            :options="variantOptions"
            placeholder="下划线+多选"
          />
        </div>
      </div>
    </section>

    <!-- 变体组合演示 -->
    <section class="demo-section">
      <h2>🎭 变体组合演示</h2>
      <p class="description">不同变体与尺寸、状态的组合效果</p>

      <div class="demo-grid">
        <div class="demo-item">
          <h3>填充 + 大尺寸 + 必填</h3>
          <SelectField
            v-model:value="comboFilledValue"
            label="填充大尺寸必填"
            name="comboFilled"
            variant="filled"
            size="large"
            :required="true"
            :options="comboOptions"
            helper-text="这是一个必填字段"
          />
        </div>

        <div class="demo-item">
          <h3>圆角 + 小尺寸 + 清除</h3>
          <SelectField
            v-model:value="comboOutlinedValue"
            label="圆角小尺寸可清除"
            name="comboOutlined"
            variant="pill"
            size="small"
            :clearable="true"
            :options="comboOptions"
            placeholder="可清除的选择器"
          />
        </div>

        <div class="demo-item">
          <h3>下划线 + 多选 + 错误</h3>
          <SelectField
            v-model:value="comboUnderlinedValue"
            label="下划线多选错误"
            name="comboUnderlined"
            variant="underlined"
            :multiple="true"
            :error="true"
            :options="comboOptions"
            helper-text="这是一个错误状态"
          />
        </div>

        <div class="demo-item">
          <h3>无边框 + 禁用</h3>
          <SelectField
            v-model:value="comboModernValue"
            label="无边框样式禁用"
            name="comboModern"
            variant="unborder"
            :disabled="true"
            :options="comboOptions"
            placeholder="禁用状态"
          />
        </div>
      </div>
    </section>

    <!-- 技术亮点说明 -->
    <section class="demo-section">
      <h2>✨ 技术亮点</h2>
      <div class="highlight-grid">
        <div class="highlight-item">
          <h3>🔄 完美复用</h3>
          <p>
            直接复用 InputField 的所有功能，包括浮动标签、表单验证、尺寸变体等
          </p>
        </div>
        <div class="highlight-item">
          <h3>🎯 插槽替换</h3>
          <p>通过 #inner 插槽巧妙替换输入框为选择器，保留所有原有特性</p>
        </div>
        <div class="highlight-item">
          <h3>📦 功能完整</h3>
          <p>支持单选、多选、验证、清除、禁用等所有常见功能</p>
        </div>
        <div class="highlight-item">
          <h3>🎨 样式一致</h3>
          <p>与 InputField 保持完全一致的视觉体验和交互逻辑</p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { SelectField } from '@speed-ui/ui'

  // 基础选择器值
  const basicValue = ref('')
  const requiredValue = ref('')
  const persistentValue = ref('')

  // 多选值
  const multipleValues = ref([])
  const multipleRequiredValues = ref([])

  // 尺寸值
  const smallValue = ref('')
  const defaultValue = ref('')
  const largeValue = ref('')

  // 状态值
  const disabledValue = ref('disabled-option')
  const errorValue = ref('')
  const validateValue = ref('')

  // 自定义配置值
  const customKeyValue = ref('')
  const emptyValue = ref('')
  const disabledOptionValue = ref('')

  // 表单数据
  const form = ref({
    category: '',
    tags: [],
    priority: '',
  })

  // 验证信息
  const validationMessage = ref('')

  // 焦点测试相关
  const focusTestRef = ref()
  const focusTestValue = ref('')
  const multipleFocusTestValue = ref([])
  const isFocused = ref(false)
  const isMultipleFocused = ref(false)

  // 焦点测试选项
  const focusTestOptions = [
    { label: '选项 A', value: 'option-a' },
    { label: '选项 B', value: 'option-b' },
    { label: '选项 C', value: 'option-c' },
    { label: '选项 D', value: 'option-d' },
  ]

  // 焦点测试事件处理
  const onFocusTest = () => {
    isFocused.value = true
    console.log('单选焦点测试 - 获得焦点')
  }

  const onBlurTest = () => {
    isFocused.value = false
    console.log('单选焦点测试 - 失去焦点')
  }

  const onMultipleFocusTest = () => {
    isMultipleFocused.value = true
    console.log('多选焦点测试 - 获得焦点')
  }

  const onMultipleBlurTest = () => {
    isMultipleFocused.value = false
    console.log('多选焦点测试 - 失去焦点')
  }

  // 焦点测试方法
  const testFocus = () => {
    if (focusTestRef.value) {
      focusTestRef.value.focus?.()
    }
  }

  const testBlur = () => {
    if (focusTestRef.value) {
      focusTestRef.value.blur?.()
    }
  }

  const resetFocusTest = () => {
    focusTestValue.value = ''
    multipleFocusTestValue.value = []
    isFocused.value = false
    isMultipleFocused.value = false
  }

  // 选项数据
  const cityOptions = [
    { label: '北京', value: 'beijing' },
    { label: '上海', value: 'shanghai' },
    { label: '广州', value: 'guangzhou' },
    { label: '深圳', value: 'shenzhen' },
    { label: '杭州', value: 'hangzhou' },
  ]

  const languageOptions = [
    { label: 'JavaScript', value: 'js' },
    { label: 'TypeScript', value: 'ts' },
    { label: 'Python', value: 'python' },
    { label: 'Java', value: 'java' },
    { label: 'Go', value: 'go' },
  ]

  const frameworkOptions = [
    { label: 'Vue.js', value: 'vue' },
    { label: 'React', value: 'react' },
    { label: 'Angular', value: 'angular' },
    { label: 'Svelte', value: 'svelte' },
  ]

  const skillOptions = [
    { label: '前端开发', value: 'frontend' },
    { label: '后端开发', value: 'backend' },
    { label: '移动开发', value: 'mobile' },
    { label: 'UI设计', value: 'ui' },
    { label: '产品设计', value: 'product' },
    { label: '数据分析', value: 'data' },
    { label: '数据分析', value: 'data1' },
    { label: '数据分析', value: 'data23' },
    { label: '数据分析', value: '3' },
  ]

  const hobbyOptions = [
    { label: '阅读', value: 'reading' },
    { label: '运动', value: 'sports' },
    { label: '音乐', value: 'music' },
    { label: '电影', value: 'movies' },
    { label: '游戏', value: 'gaming' },
    { label: '旅行', value: 'travel' },
  ]

  const sizeOptions = [
    { label: '选项一', value: 'option1' },
    { label: '选项二', value: 'option2' },
    { label: '选项三', value: 'option3' },
  ]

  const statusOptions = [
    { label: '正常', value: 'normal' },
    { label: '警告', value: 'warning' },
    { label: '错误', value: 'error' },
  ]

  const customKeyOptions = [
    { id: 1, name: '张三', role: '开发者', label: '张三', value: 1 },
    { id: 2, name: '李四', role: '设计师', label: '李四', value: 2 },
    { id: 3, name: '王五', role: '产品经理', label: '王五', value: 3 },
  ]

  const disabledOptions = [
    { label: '可选择', value: 'enabled' },
    { label: '已禁用', value: 'disabled1', disabled: true },
    { label: '正常选项', value: 'normal' },
    { label: '另一个禁用', value: 'disabled2', disabled: true },
  ]

  const categoryOptions = [
    { label: '前端项目', value: 'frontend' },
    { label: '后端项目', value: 'backend' },
    { label: '全栈项目', value: 'fullstack' },
    { label: '移动应用', value: 'mobile' },
  ]

  const tagOptions = [
    { label: 'Vue', value: 'vue' },
    { label: 'React', value: 'react' },
    { label: 'Node.js', value: 'nodejs' },
    { label: 'TypeScript', value: 'typescript' },
    { label: 'UI/UX', value: 'ui' },
  ]

  const priorityOptions = [
    { label: '低优先级', value: 'low' },
    { label: '中优先级', value: 'medium' },
    { label: '高优先级', value: 'high' },
    { label: '紧急', value: 'urgent' },
  ]

  // 验证规则
  const validationRules = (value: string) => {
    if (!value) return '请选择一个选项'
    if (value === 'error') return '不能选择错误选项'
    return true
  }

  const categoryRules = (value: string) => {
    if (!value) return '请选择项目分类'
    return true
  }

  // 事件处理
  const handleValidate = (name: string, isValid: boolean, message: string) => {
    validationMessage.value = isValid ? '验证通过' : `验证失败: ${message}`
  }

  const handleSubmit = () => {
    alert('表单提交成功！\n' + JSON.stringify(form.value, null, 2))
  }

  const resetForm = () => {
    form.value = {
      category: '',
      tags: [],
      priority: '',
    }
  }

  // 变体演示相关变量
  const variantDefaultValue = ref('')
  const variantFilledValue = ref('')
  const variantUnderlinedValue = ref('')
  const variantPillValue = ref('')
  const variantSquareValue = ref('')
  const variantUnborderValue = ref('')
  const variantComboValue = ref('')
  const variantMultipleValue = ref([])

  // 变体组合演示变量
  const comboFilledValue = ref('')
  const comboOutlinedValue = ref('')
  const comboUnderlinedValue = ref([])
  const comboModernValue = ref('')

  // 变体演示选项
  const variantOptions = [
    { label: '默认选项', value: 'default' },
    { label: '填充选项', value: 'filled' },
    { label: '下划线选项', value: 'underlined' },
    { label: '圆角选项', value: 'pill' },
    { label: '方形选项', value: 'square' },
    { label: '无边框选项', value: 'unborder' },
    { label: '组合选项', value: 'combo' },
    { label: '多选选项', value: 'multiple' },
  ]

  const comboOptions = [
    { label: '组合选项 A', value: 'combo-a' },
    { label: '组合选项 B', value: 'combo-b' },
    { label: '组合选项 C', value: 'combo-c' },
    { label: '组合选项 D', value: 'combo-d' },
  ]
</script>

<style scoped>
  .select-field-demo {
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;
    background: #fafafa;
    min-height: 100vh;
  }

  h1 {
    color: #1f2937;
    text-align: center;
    margin-bottom: 16px;
    font-size: 2.5rem;
  }

  .description {
    text-align: center;
    color: #6b7280;
    font-size: 1.1rem;
    margin-bottom: 40px;
    line-height: 1.6;
  }

  .demo-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 32px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .demo-section h2 {
    color: #1f2937;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 12px;
    margin-bottom: 24px;
    font-size: 1.5rem;
  }

  .demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
  }

  /* 焦点测试样式 */
  .focus-test-section {
    border: 2px solid #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  }

  .test-description {
    color: #1e40af;
    font-weight: 500;
    margin-bottom: 20px;
    padding: 12px;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 8px;
    border-left: 4px solid #3b82f6;
  }

  .focus-status {
    margin-top: 16px;
    padding: 12px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
  }

  .status-indicator {
    font-weight: bold;
    padding: 8px 12px;
    border-radius: 6px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
    background: #fee2e2;
    color: #dc2626;
  }

  .status-indicator.active {
    background: #d1fae5;
    color: #059669;
  }

  .test-instructions {
    margin-top: 12px;
    font-size: 14px;
    color: #64748b;
  }

  .test-instructions ol {
    margin: 8px 0;
    padding-left: 20px;
  }

  .test-instructions li {
    margin-bottom: 4px;
    line-height: 1.4;
  }

  .test-buttons {
    margin-top: 16px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .test-btn {
    padding: 8px 16px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    color: #374151;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
  }

  .test-btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
  }

  .test-btn.primary {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
  }

  .test-btn.primary:hover {
    background: #2563eb;
    border-color: #2563eb;
  }

  .test-note {
    color: #6366f1;
    font-size: 14px;
    font-style: italic;
    margin-top: 8px;
  }

  .demo-item {
    padding: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: #f9fafb;
  }

  .demo-item h3 {
    color: #374151;
    margin-bottom: 16px;
    font-size: 1.1rem;
  }

  .value-display {
    margin-top: 12px;
    padding: 8px 12px;
    background: #f3f4f6;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #374151;
  }

  .validation-info {
    margin-top: 8px;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
  }

  .form-demo {
    background: #f8fafc;
    padding: 24px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
  }

  .demo-form {
    display: grid;
    gap: 20px;
    margin-bottom: 24px;
  }

  .form-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
  }

  .submit-btn,
  .reset-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }

  .submit-btn {
    background: #3b82f6;
    color: white;
  }

  .submit-btn:hover {
    background: #2563eb;
  }

  .reset-btn {
    background: #e5e7eb;
    color: #374151;
  }

  .reset-btn:hover {
    background: #d1d5db;
  }

  .form-result {
    margin-top: 20px;
    padding: 16px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
  }

  .form-result h4 {
    color: #374151;
    margin-bottom: 12px;
  }

  .form-result pre {
    color: #6b7280;
    font-size: 0.9rem;
    white-space: pre-wrap;
    margin: 0;
  }

  .highlight-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .highlight-item {
    text-align: center;
    padding: 20px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  }

  .highlight-item h3 {
    color: #1e40af;
    margin-bottom: 12px;
    font-size: 1.1rem;
  }

  .highlight-item p {
    color: #64748b;
    line-height: 1.5;
    margin: 0;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .select-field-demo {
      padding: 16px;
    }

    .demo-grid {
      grid-template-columns: 1fr;
    }

    .highlight-grid {
      grid-template-columns: 1fr;
    }

    h1 {
      font-size: 2rem;
    }
  }
</style>
