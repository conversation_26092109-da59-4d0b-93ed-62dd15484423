/**
 * Speed UI 组件尺寸相关常量
 */

import type {
  ComponentSize,
  ExtendedComponentSize,
  SizeMapping,
  SizeValueMap,
  SizePixelMap,
} from './types'

// 默认尺寸值
export const DEFAULT_SIZE: ComponentSize = 'default'

// 所有可用尺寸列表
export const COMPONENT_SIZES: readonly ComponentSize[] = [
  'small',
  'default',
  'large',
] as const

// 扩展尺寸列表
export const EXTENDED_COMPONENT_SIZES: readonly ExtendedComponentSize[] = [
  'mini',
  'small',
  'default',
  'large',
  'huge',
] as const

// 尺寸映射配置
export const SIZE_MAPPING: SizeMapping = {
  // Select size -> Scrollbar size
  selectToScrollbar: {
    small: 'small',
    default: 'medium',
    large: 'large',
  },
  // 可以添加更多组件间的映射
  // buttonToInput: { ... },
  // inputToSelect: { ... }
} as const

// 尺寸对应的数值映射（用于计算）
export const SIZE_VALUE_MAP: SizeValueMap = {
  small: 12,
  default: 16,
  large: 20,
} as const

// 尺寸对应的像素值映射
export const SIZE_PIXEL_MAP: SizePixelMap = {
  small: '12px',
  default: '16px',
  large: '20px',
} as const
