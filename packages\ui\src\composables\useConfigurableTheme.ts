import { computed, type Ref } from 'vue'
import { getThemeColors, type ThemeColors } from '../config/speedThemeConfig'

/**
 * 可配置的主题系统
 * 支持预设主题（blue/green/red/orange/purple/pink/cyan/gray）和自定义颜色值
 */
export function useConfigurableTheme(theme: Ref<string>) {
  // 获取主题色配置
  const themeColors = computed((): ThemeColors => {
    return getThemeColors(theme.value)
  })

  // 生成 CSS 变量（用于自定义颜色或动态主题）
  const dynamicThemeVars = computed(() => {
    // 如果是预设主题，使用全局 CSS 变量，不生成内联样式
    if (isPresetTheme(theme.value)) {
      return {}
    }

    // 自定义颜色值才生成内联样式
    if (theme.value.startsWith('#')) {
      const colors = themeColors.value
      return {
        '--sp-color-primary': colors.primary,
        '--sp-color-primary-hover': colors.primaryHover,
        '--sp-color-primary-active': colors.primaryActive,
        '--sp-color-primary-disabled': colors.primaryDisabled,
        '--sp-color-primary-lightest': colors.primaryLightest,
        '--sp-color-primary-light': colors.primaryLight,
        '--sp-color-primary-dark': colors.primaryDark,
      }
    }

    return {}
  })

  // CSS 类名（用于预设主题）
  const themeClass = computed(() => {
    if (isPresetTheme(theme.value)) {
      return `sp-theme--${theme.value}`
    }
    return ''
  })

  // 判断是否为预设主题
  function isPresetTheme(themeName: string): boolean {
    const presetThemes = [
      'blue',
      'green',
      'red',
      'orange',
      'purple',
      'pink',
      'cyan',
      'gray',
    ]
    return presetThemes.includes(themeName)
  }

  return {
    themeColors,
    dynamicThemeVars,
    themeClass,
  }
}
