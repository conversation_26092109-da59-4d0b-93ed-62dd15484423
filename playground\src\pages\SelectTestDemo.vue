<template>
  <div class="select-test-demo">
    <h1>Select 组件测试</h1>

    <div class="demo-container">
      <section class="demo-section">
        <h3>📋 基础用法</h3>
        <div class="demo-row">
          <div class="demo-item">
            <label>基础单选：</label>
            <sp-select
              v-model:value="basicValue"
              :options="basicOptions"
              placeholder="请选择一个选项"
              style="width: 200px"
            />
            <span class="demo-value">选中值: {{ basicValue || '无' }}</span>
          </div>
        </div>
      </section>

      <section class="demo-section">
        <h3>🔍 可清除</h3>
        <div class="demo-row">
          <div class="demo-item">
            <label>可清除选择器：</label>
            <sp-select
              v-model:value="clearableValue"
              :options="basicOptions"
              placeholder="请选择"
              allow-clear
              style="width: 200px"
            />
            <span class="demo-value">选中值: {{ clearableValue || '无' }}</span>
          </div>
        </div>
      </section>

      <section class="demo-section">
        <h3>🏷️ 多选模式</h3>
        <div class="demo-row">
          <div class="demo-item">
            <label>多选选择器：</label>
            <sp-select
              v-model:value="multipleValue"
              :options="basicOptions"
              placeholder="请选择多个选项"
              mode="multiple"
              allow-clear
              style="width: 300px"
            />
            <span class="demo-value">
              选中值:
              {{ multipleValue.length ? multipleValue.join(', ') : '无' }}
            </span>
          </div>
        </div>
      </section>

      <section class="demo-section">
        <h3>📏 尺寸变体</h3>
        <div class="demo-col">
          <div class="demo-item">
            <label>小尺寸：</label>
            <sp-select
              v-model:value="sizeValue"
              :options="basicOptions"
              placeholder="小尺寸"
              size="small"
              style="width: 150px"
            />
          </div>
          <div class="demo-item">
            <label>默认尺寸：</label>
            <sp-select
              v-model:value="sizeValue"
              :options="basicOptions"
              placeholder="默认尺寸"
              size="medium"
              style="width: 180px"
            />
          </div>
          <div class="demo-item">
            <label>大尺寸：</label>
            <sp-select
              v-model:value="sizeValue"
              :options="basicOptions"
              placeholder="大尺寸"
              size="large"
              style="width: 220px"
            />
          </div>
        </div>
      </section>

      <section class="demo-section">
        <h3>🚫 禁用状态</h3>
        <div class="demo-row">
          <div class="demo-item">
            <label>禁用选择器：</label>
            <sp-select
              v-model:value="disabledValue"
              :options="basicOptions"
              placeholder="禁用状态"
              disabled
              style="width: 200px"
            />
          </div>
          <div class="demo-item">
            <label>部分选项禁用：</label>
            <sp-select
              v-model:value="partialDisabledValue"
              :options="partialDisabledOptions"
              placeholder="部分禁用"
              style="width: 200px"
            />
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select as SpSelect } from '../../../packages/ui/src/components/select'

  // 演示数据
  const basicValue = ref('')
  const clearableValue = ref('')
  const multipleValue = ref([])
  const sizeValue = ref('')
  const disabledValue = ref('option1')
  const partialDisabledValue = ref('')

  // 基础选项
  const basicOptions = [
    { label: '选项一', value: 1 },
    { label: '选项二', value: 'option2' },
    { label: '选项三', value: 'option3' },
    { label: '选项四', value: 'option4' },
    { label: '选项五', value: 'option5' },
  ]

  // 部分禁用选项
  const partialDisabledOptions = [
    { label: '可选择', value: 'enabled1' },
    { label: '已禁用', value: 'disabled1', disabled: true },
    { label: '可选择', value: 'enabled2' },
    { label: '已禁用', value: 'disabled2', disabled: true },
    { label: '可选择', value: 'enabled3' },
  ]
</script>

<style scoped>
  .select-test-demo {
    padding: 20px;
    max-width: 900px;
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .select-test-demo h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 30px;
  }

  .demo-container {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e4e7ed;
  }

  .demo-section {
    margin-bottom: 40px;
  }

  .demo-section:last-child {
    margin-bottom: 0;
  }

  .demo-section h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #606266;
    font-size: 18px;
  }

  .demo-row {
    display: flex;
    gap: 30px;
    margin-bottom: 20px;
    flex-wrap: wrap;
  }

  .demo-col {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 20px;
  }

  .demo-item {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
  }

  .demo-item label {
    font-weight: 600;
    color: #606266;
    min-width: 120px;
    font-size: 14px;
  }

  .demo-value {
    color: #909399;
    font-size: 12px;
    background: #f5f7fa;
    padding: 4px 8px;
    border-radius: 4px;
    min-width: 100px;
  }
</style>
