import { ref, computed, watch, type Ref, type ComputedRef } from 'vue'
import type { UseVModelOptions, UseVModelReturn } from './types'

/**
 * 通用 v-model 双向绑定 composable
 *
 * @example
 * ```typescript
 * // 基础用法
 * const { value, updateValue } = useVModel(props, emit, {
 *   prop: 'value',
 *   event: 'update:value',
 *   defaultValue: props.value
 * })
 *
 * // 表单集成
 * const { value, updateValue } = useVModel(props, emit, {
 *   prop: 'value',
 *   event: 'update:value',
 *   defaultValue: props.value,
 *   formField: formItemField,
 *   onAfterUpdate: (newValue) => {
 *     emit('change', newValue)
 *   }
 * })
 *
 * // 自定义 prop 和事件
 * const { value, updateValue } = useVModel(props, emit, {
 *   prop: 'checked',
 *   event: 'update:checked',
 *   defaultValue: false
 * })
 * ```
 */
export function useVModel<T>(
  props: any,
  emit: any,
  options: UseVModelOptions<T> = {}
): UseVModelReturn<T> {
  const {
    prop = 'value',
    event = 'update:value',
    defaultValue,
    formField,
    onBeforeUpdate,
    onAfterUpdate,
    validator,
    parser,
    formatter,
    debug = false,
  } = options

  // 调试日志函数
  const log = (...args: any[]) => {
    if (debug) {
      console.log(`[useVModel:${prop}]`, ...args)
    }
  }

  // 内部状态
  const internalValue = ref<T>(defaultValue as T)

  // 初始化值
  const initializeValue = (): T => {
    // 优先级：表单字段 > props > 默认值
    if (formField?.value?.value !== undefined) {
      log('从表单字段初始化:', formField.value.value)
      return formField.value.value
    }
    if (props[prop] !== undefined) {
      log('从 props 初始化:', props[prop])
      return props[prop]
    }
    log('使用默认值初始化:', defaultValue)
    return defaultValue as T
  }

  // 设置初始值
  internalValue.value = initializeValue()

  // 监听外部 prop 变化
  watch(
    () => props[prop],
    newValue => {
      log('外部 prop 变化:', { newValue, currentValue: internalValue.value })

      // 解析值
      const parsedValue = parser ? parser(newValue) : newValue

      if (parsedValue !== internalValue.value) {
        log('更新内部值:', parsedValue)
        internalValue.value = parsedValue
      }
    },
    { immediate: true }
  )

  // 监听表单字段变化
  if (formField) {
    watch(
      () => formField.value?.value,
      newValue => {
        log('表单字段变化:', { newValue, currentValue: internalValue.value })

        if (newValue !== undefined && newValue !== internalValue.value) {
          log('从表单字段更新内部值:', newValue)
          internalValue.value = newValue
        }
      },
      { immediate: true }
    )
  }

  // 更新值的方法
  const updateValue = (newValue: T): void => {
    log('updateValue 调用:', { newValue, oldValue: internalValue.value })

    // 验证
    if (validator && !validator(newValue)) {
      console.warn(`[useVModel:${prop}] 值验证失败:`, newValue)
      return
    }

    // 更新前处理
    let processedValue = newValue
    if (onBeforeUpdate) {
      const result = onBeforeUpdate(newValue, internalValue.value)
      if (result === false) {
        log('更新被 onBeforeUpdate 阻止')
        return // 阻止更新
      }
      processedValue = result
      log('onBeforeUpdate 处理后的值:', processedValue)
    }

    // 更新内部状态
    const oldValue = internalValue.value
    internalValue.value = processedValue

    // 更新表单字段
    if (formField && typeof formField.setValue === 'function') {
      log('更新表单字段:', processedValue)
      formField.setValue(processedValue)
    }

    // 发出事件
    const emitValue = formatter ? formatter(processedValue) : processedValue
    log('发出事件:', { event, emitValue })
    emit(event, emitValue)

    // 更新后处理
    if (onAfterUpdate) {
      log('执行 onAfterUpdate')
      onAfterUpdate(processedValue, oldValue)
    }
  }

  // 重置值
  const resetValue = (): void => {
    log('resetValue 调用')
    updateValue(defaultValue as T)
  }

  // 计算属性：是否有值
  const hasValue = computed((): boolean => {
    const val = internalValue.value
    if (Array.isArray(val)) return val.length > 0
    return val !== undefined && val !== null && val !== ''
  })

  // 计算属性：是否为空
  const isEmpty = computed((): boolean => !hasValue.value)

  log('useVModel 初始化完成:', {
    prop,
    event,
    initialValue: internalValue.value,
    hasValue: hasValue.value,
    isEmpty: isEmpty.value,
  })

  return {
    value: internalValue as Ref<T>,
    updateValue,
    resetValue,
    hasValue,
    isEmpty,
  }
}

// 默认导出
export default useVModel
