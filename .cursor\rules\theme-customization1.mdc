---
description: 
globs: packages/theme-default/src/**/*.scss
alwaysApply: false
---
# 主题定制指南

## 主题包结构

主题包位于 `packages/theme-default` 目录下，结构如下：

```
theme-default/
├── src/
│   ├── index.scss        # 主题入口文件
│   ├── variables.scss    # 全局变量
│   ├── mixins.scss      # 全局混入
│   ├── button.scss      # 按钮组件样式
│   ├── input.scss       # 输入框组件样式
│   └── ...              # 其他组件样式
└── package.json
```

## 样式变量系统

### 1. 颜色系统

```scss
// variables.scss
:root {
  // 品牌色
  --sp-primary-color: #1890ff;
  --sp-success-color: #52c41a;
  --sp-warning-color: #faad14;
  --sp-danger-color: #f5222d;
  
  // 中性色
  --sp-text-color: rgba(0, 0, 0, 0.85);
  --sp-text-color-secondary: rgba(0, 0, 0, 0.45);
  --sp-border-color: #d9d9d9;
  --sp-background-color: #f5f5f5;
}
```

### 2. 尺寸系统

```scss
// variables.scss
:root {
  // 字体大小
  --sp-font-size-xs: 12px;
  --sp-font-size-sm: 14px;
  --sp-font-size-md: 16px;
  --sp-font-size-lg: 18px;
  
  // 间距
  --sp-spacing-xs: 4px;
  --sp-spacing-sm: 8px;
  --sp-spacing-md: 16px;
  --sp-spacing-lg: 24px;
  
  // 圆角
  --sp-border-radius-sm: 2px;
  --sp-border-radius-md: 4px;
  --sp-border-radius-lg: 8px;
}
```

## 组件样式开发

### 1. 创建组件样式文件

在 `packages/theme-default/src` 下创建对应的组件样式文件：

```scss
// button.scss
.sp-button {
  // 基础样式
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--sp-spacing-sm) var(--sp-spacing-md);
  font-size: var(--sp-font-size-sm);
  border-radius: var(--sp-border-radius-md);
  
  // 变体样式
  &--primary {
    background-color: var(--sp-primary-color);
    color: #fff;
  }
  
  &--secondary {
    background-color: var(--sp-background-color);
    color: var(--sp-text-color);
  }
}
```

### 2. 在入口文件中引入

```scss
// index.scss
@import './variables.scss';
@import './mixins.scss';
@import './button.scss';
@import './input.scss';
// ... 其他组件样式
```

## 自定义主题

### 1. 创建新主题包

```bash
packages/
└── theme-custom/
    ├── package.json
    ├── src/
    │   ├── index.scss
    │   └── variables.scss
    └── README.md
```

### 2. 覆盖默认变量

```scss
// theme-custom/src/variables.scss
:root {
  // 覆盖默认变量
  --sp-primary-color: #722ed1;
  --sp-success-color: #13c2c2;
  --sp-warning-color: #fa8c16;
  --sp-danger-color: #eb2f96;
  
  // 自定义变量
  --sp-custom-color: #f6ffed;
}
```

## 使用主题

### 1. 安装主题包

```bash
pnpm add @theme-default
# 或
pnpm add @theme-custom
```

### 2. 在应用入口引入

```typescript
// main.ts
import '@theme-default/src/index.scss'
// 或
import '@theme-custom/src/index.scss'
```

## 最佳实践

1. 使用 CSS 变量实现主题切换
2. 保持变量命名规范统一（--sp-*）
3. 组件样式文件与组件名保持一致
4. 使用 BEM 命名规范
5. 提供主题预览和文档
6. 支持暗色主题
7. 考虑无障碍设计
8. 保持样式模块化
9. 避免样式冲突
10. 提供主题切换机制
