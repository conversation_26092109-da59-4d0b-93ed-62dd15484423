import { computed } from 'vue'
import { useBEM } from '@speed-ui/bem-helper'
import type { InputProps } from '../components/input/types'

interface LogicState {
  computedDisabled: { value: boolean | undefined }
  isFocused: { value: boolean }
  hasValue?: { value: boolean }
  showPrefix?: { value: boolean }
  showSuffix?: { value: boolean }
}

export function useInputStyles(props: InputProps, logicState: LogicState) {
  const bem = useBEM('input')

  // ===== 图标尺寸计算 =====
  const iconSize = computed(() => {
    switch (props.size) {
      case 'small':
        return 14
      case 'large':
        return 18
      case 'medium':
      default:
        return 16
    }
  })

  // ===== 根容器类名 =====
  const rootClasses = computed(() => [
    bem.b(),
    bem.m(props.size || 'medium'),
    bem.m(props.variant || 'default'),
    bem.m(`effect-${props.effect || 'none'}`),
    {
      [bem.m('disabled')]: logicState.computedDisabled.value || false,
      [bem.m('readonly')]: props.readonly,
      [bem.m('focused')]: logicState.isFocused?.value || false,
      [bem.m('error')]: props.error || props.validateState === 'error',
      [bem.m('warning')]: props.validateState === 'warning',
      [bem.m('success')]: props.validateState === 'success',
      [bem.m('loading')]: props.loading,
      [bem.m('has-value')]: logicState.hasValue?.value || false,
      [bem.m('has-prefix')]: logicState.showPrefix?.value || !!props.prefixIcon,
      [bem.m('has-suffix')]:
        logicState.showSuffix?.value || !!props.suffixIcon || props.clearable,
    },
  ])

  // ===== 各部分类名 =====
  const wrapperClasses = computed(() => [bem.e('wrapper')])

  const innerClasses = computed(() => [bem.e('inner')])

  const prefixClasses = computed(() => [bem.e('prefix')])

  const suffixClasses = computed(() => [bem.e('suffix')])

  const prefixIconClasses = computed(() => [bem.e('prefix-icon')])

  const suffixIconClasses = computed(() => [bem.e('suffix-icon')])

  const clearIconClasses = computed(() => [bem.e('clear')])

  const passwordIconClasses = computed(() => [bem.e('password')])

  const wordCountClasses = computed(() => [bem.e('count')])

  const loadingBarClasses = computed(() => [bem.e('loading-bar')])

  const loadingProgressClasses = computed(() => [bem.e('loading-progress')])

  // ===== 为 InputCore 准备的属性 =====
  const inputCoreProps = computed(() => ({
    rootClasses: rootClasses.value,
    wrapperClasses: wrapperClasses.value,
    innerClasses: innerClasses.value,
    prefixClasses: prefixClasses.value,
    suffixClasses: suffixClasses.value,
    iconSize: iconSize.value,
  }))

  return {
    // 类名
    rootClasses,
    wrapperClasses,
    innerClasses,
    prefixClasses,
    suffixClasses,
    prefixIconClasses,
    suffixIconClasses,
    clearIconClasses,
    passwordIconClasses,
    wordCountClasses,
    loadingBarClasses,
    loadingProgressClasses,

    // 其他计算属性
    iconSize,

    // 为 InputCore 准备的属性包
    inputCoreProps,
  }
}
