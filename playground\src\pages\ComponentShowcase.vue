<template>
  <div class="component-showcase">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>{{ pageTitle }}</h1>
      <p class="page-description">{{ pageDescription }}</p>
    </div>

    <!-- 演示区域容器 -->
    <div class="showcase-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  pageTitle: string
  pageDescription?: string
}

defineProps<Props>()
</script>

<style scoped>
.component-showcase {
  min-height: 100vh;
  background: #f9fafb;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 32px 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 36px;
  font-weight: 700;
  color: #111827;
  letter-spacing: -0.025em;
}

.page-description {
  margin: 0;
  font-size: 18px;
  color: #6b7280;
  line-height: 1.6;
}

.showcase-content {
  padding: 32px 24px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 24px 16px;
  }
  
  .page-header h1 {
    font-size: 28px;
  }
  
  .page-description {
    font-size: 16px;
  }
  
  .showcase-content {
    padding: 24px 16px;
  }
}
</style> 