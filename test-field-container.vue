<template>
  <div class="test-container">
    <h2>FieldContainer 前缀条件渲染测试</h2>

    <!-- 测试1: 没有前缀图标和插槽 - 前缀区域不应该渲染 -->
    <div class="test-case">
      <h3>测试1: 无前缀 - 前缀区域不渲染</h3>
      <sp-field-container
        label="无前缀输入框"
        placeholder="这里不应该有前缀区域"
      >
        <input type="text" />
      </sp-field-container>
    </div>

    <!-- 测试2: 有前缀图标 - 前缀区域应该渲染 -->
    <div class="test-case">
      <h3>测试2: 有前缀图标 - 前缀区域渲染</h3>
      <sp-field-container
        label="有前缀图标"
        placeholder="这里应该有前缀图标"
        prefix-icon="Search"
      >
        <template #prefix="{ prefixClasses, iconSize, prefixIcon }">
          <div :class="prefixClasses">
            <sp-icon v-if="prefixIcon" :name="prefixIcon" :size="iconSize" />
          </div>
        </template>
        <input type="text" />
      </sp-field-container>
    </div>

    <!-- 测试3: 有前缀插槽 - 前缀区域应该渲染 -->
    <div class="test-case">
      <h3>测试3: 有前缀插槽 - 前缀区域渲染</h3>
      <sp-field-container
        label="有前缀插槽"
        placeholder="这里应该有前缀插槽内容"
      >
        <template #prefix="{ prefixClasses }">
          <div :class="prefixClasses">
            <span>🔍</span>
          </div>
        </template>
        <input type="text" />
      </sp-field-container>
    </div>

    <!-- 测试4: 同时有前缀图标和插槽 - 前缀区域应该渲染 -->
    <div class="test-case">
      <h3>测试4: 前缀图标 + 插槽 - 前缀区域渲染</h3>
      <sp-field-container
        label="前缀图标和插槽"
        placeholder="这里应该有前缀图标和插槽内容"
        prefix-icon="User"
      >
        <template #prefix>
          <span>👤</span>
        </template>
        <input type="text" />
      </sp-field-container>
    </div>

    <!-- 测试5: 后缀测试 - 没有后缀图标和插槽 -->
    <div class="test-case">
      <h3>测试5: 无后缀 - 后缀区域不渲染</h3>
      <sp-field-container
        label="无后缀输入框"
        placeholder="这里不应该有后缀区域"
      >
        <input type="text" />
      </sp-field-container>
    </div>

    <!-- 测试6: 有后缀图标 - 后缀区域应该渲染 -->
    <div class="test-case">
      <h3>测试6: 有后缀图标 - 后缀区域渲染</h3>
      <sp-field-container
        label="有后缀图标"
        placeholder="这里应该有后缀图标"
        suffix-icon="Send"
      >
        <input type="text" />
      </sp-field-container>
    </div>
  </div>
</template>

<script setup lang="ts">
// 这个文件用于测试 FieldContainer 的前缀后缀条件渲染功能
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.test-case {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.test-case h3 {
  margin-top: 0;
  color: #333;
  font-size: 16px;
}

input {
  width: 100%;
  border: none;
  outline: none;
  background: transparent;
  padding: 8px 0;
}
</style>
