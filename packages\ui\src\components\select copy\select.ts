// Select 组件的类型定义和工具函数

export interface SelectOption {
  label: string
  value: string | number
  disabled?: boolean
  [key: string]: any
}

// 联动选择器的数据格式（支持多级嵌套）
export interface LinkedSelectData {
  value: string | number
  label: string
  disabled?: boolean
  children?: LinkedSelectData[] // 支持多级嵌套
  [key: string]: any
}

export interface SelectProps {
  value?: string | number | Array<string | number>
  options?: SelectOption[]
  placeholder?: string
  clearable?: boolean
  disabled?: boolean
  multiple?: boolean
  size?: 'small' | 'default' | 'large'
  variant?: 'default' | 'simple' // 样式变体
  emptyText?: string
  valueKey?: string
  labelKey?: string
  validateState?: 'success' | 'warning' | 'error'
  validateMessage?: string
  showValidateMessage?: boolean
  prefixCls?: string // 类名前缀
  // 联动选择器相关属性（重新设计支持多级）
  linkedGroup?: string
  linkedLevel?: number // 使用级别：1, 2, 3... 代替 parent/child
  linkedData?: LinkedSelectData[] // 只有第一级（level=1）需要传入完整数据
}

export interface SelectEmits {
  'update:value': [value: string | number | Array<string | number> | undefined]
  change: [value: string | number | Array<string | number> | undefined]
  focus: []
  blur: []
  clear: []
}

// Select 组件的默认属性
export const selectPropsDefaults = {
  placeholder: '请选择',
  clearable: false,
  disabled: false,
  multiple: false,
  size: 'default' as const,
  variant: 'default' as const,
  emptyText: '暂无数据',
  valueKey: 'value',
  labelKey: 'label',
  showValidateMessage: true,
  prefixCls: 'sp-select',
  options: () => [] as SelectOption[],
}

// 工具函数：获取选项值
export const getOptionValue = (
  option: SelectOption,
  valueKey: string = 'value'
) => {
  return option[valueKey]
}

// 工具函数：获取选项标签
export const getOptionLabel = (
  option: SelectOption,
  labelKey: string = 'label'
) => {
  return option[labelKey]
}

// 工具函数：判断选项是否禁用
export const isOptionDisabled = (option: SelectOption) => {
  return option.disabled || false
}

// 工具函数：判断是否有值
export const hasValue = (
  value: string | number | Array<string | number> | undefined,
  multiple: boolean
) => {
  if (multiple) {
    return Array.isArray(value) && value.length > 0
  }
  return value !== undefined && value !== null && value !== ''
}

// 工具函数：获取选中的选项（单选）
export const getSelectedOption = (
  options: SelectOption[],
  value: string | number | Array<string | number> | undefined,
  multiple: boolean,
  valueKey: string = 'value'
) => {
  if (multiple) return null
  return (
    options.find(option => getOptionValue(option, valueKey) === value) || null
  )
}

// 工具函数：获取选中的选项（多选）
export const getSelectedOptions = (
  options: SelectOption[],
  value: string | number | Array<string | number> | undefined,
  multiple: boolean,
  valueKey: string = 'value'
) => {
  if (!multiple) return []
  const values = Array.isArray(value) ? value : []
  return options.filter(option =>
    values.includes(getOptionValue(option, valueKey))
  )
}

// 工具函数：判断选项是否被选中
export const isSelected = (
  option: SelectOption,
  value: string | number | Array<string | number> | undefined,
  multiple: boolean,
  valueKey: string = 'value'
) => {
  if (multiple) {
    const values = Array.isArray(value) ? value : []
    return values.includes(getOptionValue(option, valueKey))
  }
  return getOptionValue(option, valueKey) === value
}

// 工具函数：更新下拉框位置
export const updateDropdownPosition = (selectElement: HTMLElement) => {
  if (!selectElement) return {}

  const rect = selectElement.getBoundingClientRect()
  const { innerHeight } = window

  // 检查下方空间是否足够
  const spaceBelow = innerHeight - rect.bottom
  const dropdownHeight = 200 // 预估下拉框高度

  if (spaceBelow < dropdownHeight && rect.top > dropdownHeight) {
    // 向上显示
    return {
      top: 'auto',
      bottom: '100%',
      marginBottom: '4px',
    }
  } else {
    // 向下显示
    return {
      top: '100%',
      bottom: 'auto',
      marginTop: '4px',
    }
  }
}

// 联动选择器工具函数：根据路径获取嵌套数据
export const getNestedData = (
  linkedData: LinkedSelectData[],
  valuePath: (string | number)[],
  valueKey: string = 'value'
): LinkedSelectData[] => {
  if (!linkedData || valuePath.length === 0) return linkedData || []

  let currentData = linkedData

  for (const value of valuePath) {
    const found = currentData.find(
      item => getOptionValue(item as SelectOption, valueKey) === value
    )

    if (!found || !found.children) return []
    currentData = found.children
  }

  return currentData
}

// 联动选择器工具函数：将嵌套数据转换为选项
export const convertToOptions = (
  linkedData: LinkedSelectData[],
  valueKey: string = 'value',
  labelKey: string = 'label'
): SelectOption[] => {
  if (!linkedData) return []

  return linkedData.map(item => ({
    value: getOptionValue(item as SelectOption, valueKey),
    label: getOptionLabel(item as SelectOption, labelKey),
    disabled: item.disabled || false,
  }))
}

// 多级联动管理器
interface LinkedComponent {
  level: number
  updateOptions: (options: SelectOption[]) => void
  clearValue: () => void
  currentValue?: string | number
}

interface LinkedGroup {
  rootData: LinkedSelectData[]
  components: Map<number, LinkedComponent>
  values: Map<number, string | number | undefined>
}

class MultiLevelLinkedSelectManager {
  private groups = new Map<string, LinkedGroup>()

  // 注册组件
  registerComponent(
    groupId: string,
    level: number,
    updateOptions: (options: SelectOption[]) => void,
    clearValue: () => void,
    rootData?: LinkedSelectData[]
  ) {
    if (!this.groups.has(groupId)) {
      this.groups.set(groupId, {
        rootData: rootData || [],
        components: new Map(),
        values: new Map(),
      })
    }

    const group = this.groups.get(groupId)!

    // 如果提供了根数据，更新组的根数据
    if (rootData && level === 1) {
      group.rootData = rootData
    }

    group.components.set(level, {
      level,
      updateOptions,
      clearValue,
    })

    // 初始化选项
    this.updateComponentOptions(groupId, level)
  }

  // 值变化处理
  onValueChange(
    groupId: string,
    level: number,
    value: string | number | undefined,
    valueKey: string = 'value'
  ) {
    const group = this.groups.get(groupId)
    if (!group) return

    // 更新当前级别的值
    group.values.set(level, value)

    // 清空后续级别的值
    const maxLevel = Math.max(...Array.from(group.components.keys()))
    for (let i = level + 1; i <= maxLevel; i++) {
      group.values.delete(i)
      const component = group.components.get(i)
      if (component) {
        component.clearValue()
      }
    }

    // 更新所有后续级别的选项
    for (let i = level + 1; i <= maxLevel; i++) {
      this.updateComponentOptions(groupId, i, valueKey)
    }
  }

  // 更新组件选项
  private updateComponentOptions(
    groupId: string,
    level: number,
    valueKey: string = 'value'
  ) {
    const group = this.groups.get(groupId)
    if (!group) return

    const component = group.components.get(level)
    if (!component) return

    if (level === 1) {
      // 第一级直接使用根数据
      const options = convertToOptions(group.rootData, valueKey)
      component.updateOptions(options)
    } else {
      // 后续级别根据前面级别的值计算
      const valuePath: (string | number)[] = []
      for (let i = 1; i < level; i++) {
        const value = group.values.get(i)
        if (value === undefined) {
          // 如果前面级别没有值，清空当前级别选项
          component.updateOptions([])
          return
        }
        valuePath.push(value)
      }

      const nestedData = getNestedData(group.rootData, valuePath, valueKey)
      const options = convertToOptions(nestedData, valueKey)
      component.updateOptions(options)
    }
  }

  // 销毁组件注册
  unregister(groupId: string, level: number) {
    const group = this.groups.get(groupId)
    if (!group) return

    group.components.delete(level)
    group.values.delete(level)

    // 如果组为空，则删除整个组
    if (group.components.size === 0) {
      this.groups.delete(groupId)
    }
  }
}

// 全局单例
export const multiLevelLinkedSelectManager = new MultiLevelLinkedSelectManager()
