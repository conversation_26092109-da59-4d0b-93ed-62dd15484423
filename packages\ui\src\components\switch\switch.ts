export interface SwitchProps {
  /**
   * 开关的值/状态 (用于 v-model:value)
   */
  value?: boolean
  /**
   * 开关的值/状态 (用于 v-model)
   */
  modelValue?: boolean
  /**
   * 是否禁用
   */
  disabled?: boolean
  /**
   * 开关尺寸
   */
  size?: 'small' | 'medium' | 'large' | 'huge'
  /**
   * 打开时的背景色
   */
  activeColor?: string
  /**
   * 关闭时的背景色
   */
  inactiveColor?: string
  /**
   * 开关打开时显示的文本
   */
  onText?: string
  /**
   * 开关关闭时显示的文本
   */
  offText?: string
  /**
   * 开关打开时在外部显示的文本
   */
  onOutText?: string
  /**
   * 开关关闭时在外部显示的文本
   */
  offOutText?: string
  /**
   * 是否为方形样式
   */
  square?: boolean
  /**
   * 是否为垂直样式
   */
  vertical?: boolean
  /**
   * 倒计时时间（秒），设置后按钮中间会显示倒计时数字
   */
  time?: number
  /**
   * 自定义开关背景颜色
   */
  switchColor?: string
  /**
   * 自定义按钮（小圆圈）颜色
   */
  buttonColor?: string
  /**
   * 开启状态下的开关背景颜色
   */
  switchOnColor?: string
  /**
   * 关闭状态下的开关背景颜色
   */
  switchOffColor?: string
  /**
   * 开启状态下的按钮（小圆圈）颜色
   */
  buttonOnColor?: string
  /**
   * 关闭状态下的按钮（小圆圈）颜色
   */
  buttonOffColor?: string
}

export interface SwitchEmits {
  /**
   * 更新开关值的事件 (用于 v-model:value)
   */
  (e: 'update:value', value: boolean): void
  /**
   * 更新开关值的事件 (用于 v-model)
   */
  (e: 'update:modelValue', value: boolean): void
  /**
   * 开关状态改变时的事件
   */
  (e: 'change', value: boolean): void
}