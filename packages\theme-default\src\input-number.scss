/**
 * InputNumber 组件样式
 * 数字输入框的控制按钮样式 - 作为后缀内容实现
 */

@use './common/var.scss' as *;

// InputNumber 变量
$input-number-control-width: 24px !default;
$input-number-control-height: 16px !default;
$input-number-control-border: 1px solid $border-color-base !default;
$input-number-control-bg: $background-color-base !default;
$input-number-control-hover-bg: $background-color-hover !default;
$input-number-control-active-bg: $background-color-active !default;

// 输入框后缀区域的数字控制按钮
.sp-input {
  // 数字控制按钮容器 - 作为后缀内容
  &__number-controls {
    display: inline-flex;
    flex-direction: column;
    width: $input-number-control-width;
    height: 100%;
    margin-left: 4px;
    border-left: $input-number-control-border;
    background-color: $input-number-control-bg;
    border-radius: 4px;
    overflow: hidden;
    vertical-align: middle;

    &--small {
      width: 20px;
    }

    &--medium {
      width: 24px;
    }

    &--large {
      width: 28px;
    }

    &--right {
      // 右侧位置（默认）
    }
  }

  // 数字控制按钮基础样式
  &__number-up,
  &__number-down {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    border: none;
    background: transparent;
    color: $color-text-secondary;
    cursor: pointer;
    transition: all $transition-duration-base $transition-timing-base;
    outline: none;
    padding: 0;
    margin: 0;

    &:hover {
      background-color: $input-number-control-hover-bg;
      color: $color-text-primary;
    }

    &:active {
      background-color: $input-number-control-active-bg;
    }

    &:focus {
      background-color: $input-number-control-hover-bg;
      color: $color-primary;
    }

    // 禁用状态
    &--disabled,
    &:disabled {
      color: $color-text-disabled;
      cursor: not-allowed;
      background-color: transparent;

      &:hover,
      &:active,
      &:focus {
        background-color: transparent;
        color: $color-text-disabled;
      }
    }
  }

  // 上箭头按钮
  &__number-up {
    border-bottom: 1px solid $border-color-base;
    border-radius: 4px 4px 0 0;

    &--small {
      border-radius: 3px 3px 0 0;
    }

    &--large {
      border-radius: 5px 5px 0 0;
    }
  }

  // 下箭头按钮
  &__number-down {
    border-radius: 0 0 4px 4px;

    &--small {
      border-radius: 0 0 3px 3px;
    }

    &--large {
      border-radius: 0 0 5px 5px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .sp-input {
    &__number-controls {
      width: 28px; // 移动端增大触摸区域
    }

    &__number-up,
    &__number-down {
      font-size: 14px;
    }
  }
}
