import InputField from './input-field.vue'
import type {
  InputFieldProps,
  InputFieldEmits,
  InputFieldInstance,
} from './types'

export type { InputFieldProps, InputFieldEmits, InputFieldInstance }
export { InputField }
export default InputField
export { default as SpInputField } from './input-field.vue'
export { default as SpNumberControls } from './controls/number.vue'
export { default as SpSmsCodeControls } from './controls/smscode.vue'
export type * from './types'
