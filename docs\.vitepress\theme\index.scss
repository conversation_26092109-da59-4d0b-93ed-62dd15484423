/* 隐藏 VitePress 默认导航栏 */
.VPNavBar {
  display: none !important;
}

/* 隐藏默认的搜索框 */
.VPNavBarSearch {
  display: none !important;
}

/* 全局样式重置和优化 */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #303133;
  background-color: #ffffff;
  transition: background-color 0.3s, color 0.3s;
}

/* 暗色主题全局样式 */
.dark body {
  background-color: #1a1a1a;
  color: #e5e7eb;
}

/* 内容区域样式优化 */
.VPContent {
  padding-top: 0 !important;
  min-width: none !important;
  max-width: none !important;
  width: 100% !important;
}

/* 移除默认的页面边距 */
.VPDoc {
  padding-top: 32px !important;
}
/* 链接样式优化 */
a {
  color: #409eff;
  text-decoration: none;
  transition: color 0.3s;
}

a:hover {
  color: #66b1ff;
}

/* 按钮样式重置 */
button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 暗色主题滚动条 */
.dark ::-webkit-scrollbar-track {
  background: #2d2d2d;
}

.dark ::-webkit-scrollbar-thumb {
  background: #4a4a4a;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #5a5a5a;
}
/* 内容区域样式优化 */
.VPContent {
  padding-top: 0 !important;
  min-width: none !important;
  max-width: none !important;
  width: 100% !important;
}

/* 隐藏默认的 VitePress 侧边栏 - 使用自定义侧边栏 */
.VPSidebar {
  display: none !important;
}

/* 调整内容区域宽度 - 恢复正常布局 */
.VPDoc {
  /* 恢复默认样式以配合侧边栏 */
}

/* 调整内容容器宽度 */
.content-container {
  width: 100% !important;
  max-width: none !important;
  margin: 0 auto !important;
}

/* 标题样式优化 */
h1, h2, h3, h4, h5, h6 {
  margin-top: 2em;
  margin-bottom: 1em;
  font-weight: 600;
  line-height: 1.25;
  color: #303133;
}

.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
  color: #e5e7eb;
}

h1 {
  font-size: 2.2em;
}

h2 {
  font-size: 1.65em;
  padding-bottom: 0.3em;
  border-bottom: 1px solid #eaecef;
}

.dark h2 {
  border-bottom-color: #3c3c3c;
}

h3 {
  font-size: 1.35em;
}

h4 {
  font-size: 1.15em;
}

/* 代码块样式优化 */
pre {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
  font-size: 14px;
  line-height: 1.45;
}

.dark pre {
  background: #161b22;
  border-color: #30363d;
}

code {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 3px;
  padding: 2px 4px;
  font-size: 0.9em;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

.dark code {
  background: #161b22;
  border-color: #30363d;
  color: #e6edf3;
}

pre code {
  background: none;
  border: none;
  padding: 0;
}

/* 表格样式优化 */
table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

th, td {
  border: 1px solid #e1e4e8;
  padding: 8px 12px;
  text-align: left;
}

th {
  background: #f6f8fa;
  font-weight: 600;
}

.dark th {
  background: #161b22;
  border-color: #30363d;
}

.dark td {
  border-color: #30363d;
}

/* 演示样式 */
.button-demo-row {
  display: flex;
  gap: 20px;
  align-items: center;
  margin: 16px 0;
  flex-wrap: wrap;
}

/* 响应式设计已移除 - 取消媒体查询限制 */

/* 强制覆盖VitePress默认的媒体查询限制 */
@media (min-width: 1440px) {
  .VPContent {
    padding-right: 0 !important;
    padding-left: 0 !important;
    margin: 0 !important;
    max-width: none !important;
    width: 100% !important;
  }
}

@media (min-width: 960px) {
  .VPContent {
    padding-right: 0 !important;
    padding-left: 0 !important;
    margin: 0 !important;
    max-width: none !important;
    width: 100% !important;
  }
}

/* 全局强制覆盖所有VPContent样式 */

@media (min-width: 1440px) {
  .VPContent {
    margin: 0 !important;
    max-width: none !important;
    width: 100% !important;
  }
}

@media (min-width: 960px) {
  .VPContent {
    margin: 0 !important;
    max-width: none !important;
    width: 100% !important;
  }
}

/* 全局强制覆盖所有VPContent样式 */
.VPContent {
  padding-right: 0 !important;
  padding-left: 0 !important;
  margin-left: 270px !important; /* 为侧边栏留出空间 */
  max-width: none !important;
  width: calc(100% - 270px) !important; /* 减去侧边栏宽度 */
}

/* 没有侧边栏时的内容区域 */
.Layout.no-sidebar .VPContent {
  margin-left: 0 !important;
  width: 100% !important;
}
