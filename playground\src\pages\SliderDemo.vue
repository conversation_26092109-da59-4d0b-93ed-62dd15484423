<template>
  <div class="slider-demo">
    <h1>🎚️ Slider 滑块组件演示</h1>
    <p>演示 Slider 组件的不同变体和功能</p>

    <!-- 基础用法 -->
    <section class="demo-section">
      <h2>基础用法</h2>
      <div class="demo-container">
        <div class="slider-wrapper">
          <h3>默认变体 (Default)</h3>
          <sp-slider
            v-model="value1"
            :min="0"
            :max="100"
            :step="1"
          />
          <p>当前值: {{ value1 }}</p>
        </div>
        
        <div class="slider-wrapper">
          <h3>填充变体 (Filled)</h3>
          <sp-slider
            v-model="value2"
            variant="filled"
            :min="0"
            :max="100"
            :step="1"
          />
          <p>当前值: {{ value2 }}</p>
        </div>
      </div>
    </section>

    <!-- 尺寸变体 -->
    <section class="demo-section">
      <h2>尺寸变体</h2>
      <div class="demo-container">
        <div class="slider-wrapper">
          <h3>小尺寸 (Small)</h3>
          <sp-slider
            v-model="value3"
            size="small"
            :min="0"
            :max="100"
          />
          <p>当前值: {{ value3 }}</p>
        </div>
        
        <div class="slider-wrapper">
          <h3>中等尺寸 (Medium)</h3>
          <sp-slider
            v-model="value4"
            size="medium"
            :min="0"
            :max="100"
          />
          <p>当前值: {{ value4 }}</p>
        </div>
        
        <div class="slider-wrapper">
          <h3>大尺寸 (Large)</h3>
          <sp-slider
            v-model="value5"
            size="large"
            :min="0"
            :max="100"
          />
          <p>当前值: {{ value5 }}</p>
        </div>
      </div>
    </section>

    <!-- 带标记的滑块 -->
    <section class="demo-section">
      <h2>带标记的滑块</h2>
      <div class="demo-container">
        <div class="slider-wrapper">
          <h3>默认变体 + 标记</h3>
          <sp-slider
            v-model="value6"
            :min="0"
            :max="100"
            :step="10"
            :marks="marks1"
          />
          <p>当前值: {{ value6 }}</p>
        </div>
        
        <div class="slider-wrapper">
          <h3>填充变体 + 标记</h3>
          <sp-slider
            v-model="value7"
            variant="filled"
            :min="0"
            :max="100"
            :step="10"
            :marks="marks1"
          />
          <p>当前值: {{ value7 }}</p>
        </div>
      </div>
    </section>

    <!-- 禁用状态 -->
    <section class="demo-section">
      <h2>禁用状态</h2>
      <div class="demo-container">
        <div class="slider-wrapper">
          <h3>禁用的默认变体</h3>
          <sp-slider
            v-model="value8"
            :disabled="true"
            :min="0"
            :max="100"
          />
          <p>当前值: {{ value8 }}</p>
        </div>
        
        <div class="slider-wrapper">
          <h3>禁用的填充变体</h3>
          <sp-slider
            v-model="value9"
            variant="filled"
            :disabled="true"
            :min="0"
            :max="100"
          />
          <p>当前值: {{ value9 }}</p>
        </div>
      </div>
    </section>

    <!-- 对比展示 -->
    <section class="demo-section">
      <h2>变体对比</h2>
      <div class="demo-container">
        <div class="comparison-wrapper">
          <div class="comparison-item">
            <h3>默认变体</h3>
            <p>传统滑块样式</p>
            <sp-slider
              v-model="value10"
              :min="0"
              :max="100"
            />
            <p>值: {{ value10 }}</p>
          </div>
          
          <div class="comparison-item">
            <h3>填充变体</h3>
            <p>轨道包裹滑块，现代化设计</p>
            <sp-slider
              v-model="value11"
              variant="filled"
              :min="0"
              :max="100"
            />
            <p>值: {{ value11 }}</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 响应式数据
const value1 = ref(30)
const value2 = ref(60)
const value3 = ref(25)
const value4 = ref(50)
const value5 = ref(75)
const value6 = ref(40)
const value7 = ref(70)
const value8 = ref(30)
const value9 = ref(60)
const value10 = ref(40)
const value11 = ref(60)

// 标记配置
const marks1 = {
  0: '0',
  25: '25',
  50: '50',
  75: '75',
  100: '100'
}
</script>

<style scoped>
.slider-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 40px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.demo-section h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 2px solid #667eea;
  padding-bottom: 10px;
}

.demo-container {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.slider-wrapper {
  padding: 20px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.slider-wrapper h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #555;
  font-size: 16px;
}

.slider-wrapper p {
  margin-top: 15px;
  margin-bottom: 0;
  color: #666;
  font-size: 14px;
}

.comparison-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.comparison-item {
  padding: 20px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.comparison-item h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #555;
}

.comparison-item p {
  margin-bottom: 15px;
  color: #666;
  font-size: 14px;
}

@media (max-width: 768px) {
  .comparison-wrapper {
    grid-template-columns: 1fr;
  }
  
  .demo-container {
    gap: 20px;
  }
}
</style>
