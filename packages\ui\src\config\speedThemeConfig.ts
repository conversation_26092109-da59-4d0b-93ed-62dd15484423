/**
 * Speed UI 主题配置系统
 * 类似于尺寸系统，提供主题色的统一管理
 */

// 主题色配置
export interface ThemeColors {
  /** 主色调 */
  primary: string
  /** 悬停色 */
  primaryHover: string
  /** 激活色 */
  primaryActive: string
  /** 禁用色 */
  primaryDisabled: string
  /** 最浅色 */
  primaryLightest: string
  /** 浅色 */
  primaryLight: string
  /** 深色 */
  primaryDark: string
}

// 主题配置
export interface SpeedUIThemeConfig {
  /** 默认蓝色主题 */
  blue: ThemeColors
  /** 绿色主题 */
  green: ThemeColors
  /** 红色主题 */
  red: ThemeColors
  /** 橙色主题 */
  orange: ThemeColors
  /** 紫色主题 */
  purple: ThemeColors
  /** 粉色主题 */
  pink: ThemeColors
  /** 青色主题 */
  cyan: ThemeColors
  /** 灰色主题 */
  gray: ThemeColors
  /** 自定义主题映射函数 */
  customThemeMapper?: (primaryColor: string) => ThemeColors
}

// 颜色处理工具函数
function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null
}

function rgbToHex(r: number, g: number, b: number): string {
  return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
}

function lightenColor(hex: string, percent: number): string {
  const rgb = hexToRgb(hex)
  if (!rgb) return hex

  const factor = percent / 100
  const r = Math.min(255, Math.round(rgb.r + (255 - rgb.r) * factor))
  const g = Math.min(255, Math.round(rgb.g + (255 - rgb.g) * factor))
  const b = Math.min(255, Math.round(rgb.b + (255 - rgb.b) * factor))

  return rgbToHex(r, g, b)
}

function darkenColor(hex: string, percent: number): string {
  const rgb = hexToRgb(hex)
  if (!rgb) return hex

  const factor = percent / 100
  const r = Math.max(0, Math.round(rgb.r * (1 - factor)))
  const g = Math.max(0, Math.round(rgb.g * (1 - factor)))
  const b = Math.max(0, Math.round(rgb.b * (1 - factor)))

  return rgbToHex(r, g, b)
}

// 生成主题色系
function generateThemeColors(primary: string): ThemeColors {
  return {
    primary,
    primaryHover: lightenColor(primary, 10),
    primaryActive: darkenColor(primary, 10),
    primaryDisabled: lightenColor(primary, 40),
    primaryLightest: lightenColor(primary, 45),
    primaryLight: lightenColor(primary, 30),
    primaryDark: darkenColor(primary, 20),
  }
}

// 默认主题配置
export const defaultSpeedUIThemeConfig: SpeedUIThemeConfig = {
  blue: generateThemeColors('#1890ff'),
  green: generateThemeColors('#52c41a'),
  red: generateThemeColors('#ff4d4f'),
  orange: generateThemeColors('#fa8c16'),
  purple: generateThemeColors('#722ed1'),
  pink: generateThemeColors('#eb2f96'),
  cyan: generateThemeColors('#13c2c2'),
  gray: generateThemeColors('#595959'),

  // 自定义主题映射函数
  customThemeMapper: (primaryColor: string): ThemeColors => {
    return generateThemeColors(primaryColor)
  },
}

// 全局主题配置实例
let globalThemeConfig: SpeedUIThemeConfig = { ...defaultSpeedUIThemeConfig }

/**
 * 设置全局主题配置
 */
export function setSpeedUIThemeConfig(config: Partial<SpeedUIThemeConfig>) {
  globalThemeConfig = {
    ...globalThemeConfig,
    ...config,
  }
}

/**
 * 获取全局主题配置
 */
export function getSpeedUIThemeConfig(): SpeedUIThemeConfig {
  return globalThemeConfig
}

/**
 * 根据主题名称获取主题色配置
 */
export function getThemeColors(theme: string): ThemeColors {
  if (globalThemeConfig[theme as keyof SpeedUIThemeConfig]) {
    return globalThemeConfig[theme as keyof SpeedUIThemeConfig] as ThemeColors
  }

  // 如果是颜色值，则动态生成主题色
  if (theme.startsWith('#')) {
    return (
      globalThemeConfig.customThemeMapper?.(theme) || generateThemeColors(theme)
    )
  }

  // 默认返回蓝色主题
  return globalThemeConfig.blue
}

/**
 * 应用主题到页面
 */
export function applyTheme(theme: string | ThemeColors) {
  let colors: ThemeColors

  if (typeof theme === 'string') {
    colors = getThemeColors(theme)
  } else {
    colors = theme
  }

  updateCSSVariables(colors)
}

/**
 * 更新全局 CSS 变量
 */
function updateCSSVariables(colors: ThemeColors) {
  if (typeof document === 'undefined') return

  const root = document.documentElement

  root.style.setProperty('--sp-color-primary', colors.primary)
  root.style.setProperty('--sp-color-primary-hover', colors.primaryHover)
  root.style.setProperty('--sp-color-primary-active', colors.primaryActive)
  root.style.setProperty('--sp-color-primary-disabled', colors.primaryDisabled)
  root.style.setProperty('--sp-color-primary-lightest', colors.primaryLightest)
  root.style.setProperty('--sp-color-primary-light', colors.primaryLight)
  root.style.setProperty('--sp-color-primary-dark', colors.primaryDark)
}

// 初始化时应用默认主题
if (typeof document !== 'undefined') {
  applyTheme('blue')
}
