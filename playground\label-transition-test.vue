<template>
  <div class="label-transition-test">
    <h2>Label 过渡动画测试</h2>
    <p>测试从中间向上移动的label过渡效果，label应该在快到顶部时才开始透明，同时placeholder从下方出现</p>
    
    <div class="test-section">
      <h3>Default 变体 - 新的过渡效果</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>基础测试</h4>
          <SPInputField
            variant="default"
            size="medium"
            label="用户名"
            placeholder="请输入用户名"
            required
          />
          <p class="note">观察label向上移动时的过渡效果</p>
        </div>
        
        <div class="test-item">
          <h4>长标签测试</h4>
          <SPInputField
            variant="default"
            size="medium"
            label="这是一个很长的标签文本用来测试动画效果"
            placeholder="请输入内容"
            required
          />
          <p class="note">长标签的过渡效果</p>
        </div>
        
        <div class="test-item">
          <h4>短标签测试</h4>
          <SPInputField
            variant="default"
            size="medium"
            label="名"
            placeholder="请输入姓名"
            required
          />
          <p class="note">短标签的过渡效果</p>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>对比其他变体（原有效果）</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>Underlined 变体</h4>
          <SPInputField
            variant="underlined"
            size="medium"
            label="用户名"
            placeholder="请输入用户名"
            required
          />
          <p class="note">下划线变体保持原有动画</p>
        </div>
        
        <div class="test-item">
          <h4>Filled 变体</h4>
          <SPInputField
            variant="filled"
            size="medium"
            label="用户名"
            placeholder="请输入用户名"
            required
          />
          <p class="note">填充变体保持原有动画</p>
        </div>
        
        <div class="test-item">
          <h4>Unborder 变体</h4>
          <SPInputField
            variant="unborder"
            size="medium"
            label="用户名"
            placeholder="请输入用户名"
            required
          />
          <p class="note">无边框变体保持原有动画</p>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>不同尺寸的Default变体</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>Small 尺寸</h4>
          <SPInputField
            variant="default"
            size="small"
            label="测试标签"
            placeholder="小尺寸测试"
            required
          />
        </div>
        
        <div class="test-item">
          <h4>Large 尺寸</h4>
          <SPInputField
            variant="default"
            size="large"
            label="测试标签"
            placeholder="大尺寸测试"
            required
          />
        </div>
        
        <div class="test-item">
          <h4>自定义尺寸 (60px)</h4>
          <SPInputField
            variant="default"
            :size="60"
            label="测试标签"
            placeholder="自定义尺寸测试"
            required
          />
        </div>
      </div>
    </div>

    <div class="animation-tips">
      <h3>🎬 动画测试说明</h3>
      <ul>
        <li><strong>点击输入框</strong>：观察label向上浮动的动画</li>
        <li><strong>新效果特点</strong>：label在移动过程中保持可见，快到顶部时才开始透明</li>
        <li><strong>placeholder出现</strong>：在label变透明的同时，placeholder从下方淡入</li>
        <li><strong>连贯感</strong>：整个过程应该给人一种从中间向上移动的连贯感觉</li>
        <li><strong>对比测试</strong>：可以对比其他变体的原有动画效果</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import SPInputField from '../packages/ui/src/components/input-field/input-field.vue'
</script>

<style scoped>
.label-transition-test {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.test-item {
  padding: 15px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafafa;
}

.test-item h4 {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 14px;
  font-weight: 600;
}

.note {
  margin: 10px 0 0 0;
  font-size: 12px;
  color: #888;
  font-style: italic;
}

.animation-tips {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.animation-tips h3 {
  margin-top: 0;
  color: #007bff;
}

.animation-tips ul {
  margin: 0;
  padding-left: 20px;
}

.animation-tips li {
  margin-bottom: 8px;
  color: #555;
}
</style>
