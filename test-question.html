<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0"
    />
    <title>问号图标测试</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .test-container {
        max-width: 600px;
        margin: 0 auto;
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      .test-item {
        margin-bottom: 20px;
      }
      .test-label {
        display: block;
        margin-bottom: 8px;
        font-weight: bold;
        color: #333;
      }
      .debug-info {
        margin-top: 10px;
        padding: 10px;
        background-color: #f0f0f0;
        border-radius: 4px;
        font-size: 12px;
        color: #666;
      }
    </style>
  </head>
  <body>
    <div class="test-container">
      <h1>问号图标测试页面</h1>

      <div class="test-item">
        <label class="test-label">基础输入框（无问号）:</label>
        <sp-input placeholder="普通输入框" />
      </div>

      <div class="test-item">
        <label class="test-label">带问号的输入框:</label>
        <sp-input
          placeholder="带问号的输入框"
          question="这是一个帮助提示信息"
          @question-click="handleQuestionClick"
        />
        <div class="debug-info">question属性: "这是一个帮助提示信息"</div>
      </div>

      <div class="test-item">
        <label class="test-label">带清除按钮和问号的输入框:</label>
        <sp-input
          v-model="testValue"
          placeholder="带清除和问号的输入框"
          clearable
          question="这是另一个帮助提示"
          @question-click="handleQuestionClick"
          @clear="handleClear"
        />
        <div class="debug-info">
          当前值: {{ testValue }}
          <br />
          clearable: true
          <br />
          question: "这是另一个帮助提示"
        </div>
      </div>

      <div class="test-item">
        <label class="test-label">禁用状态（问号应该不显示）:</label>
        <sp-input
          placeholder="禁用的输入框"
          question="禁用状态的帮助"
          disabled
          @question-click="handleQuestionClick"
        />
        <div class="debug-info">
          disabled: true
          <br />
          question: "禁用状态的帮助" (应该不显示)
        </div>
      </div>

      <div class="test-item">
        <label class="test-label">只读状态（问号应该不显示）:</label>
        <sp-input
          placeholder="只读的输入框"
          question="只读状态的帮助"
          readonly
          @question-click="handleQuestionClick"
        />
        <div class="debug-info">
          readonly: true
          <br />
          question: "只读状态的帮助" (应该不显示)
        </div>
      </div>
    </div>

    <script type="module">
      import { createApp } from 'vue'

      const app = createApp({
        data() {
          return {
            testValue: '测试值',
          }
        },
        methods: {
          handleQuestionClick(question) {
            console.log('问号被点击:', question)
            alert('问号被点击: ' + question)
          },
          handleClear() {
            console.log('清除按钮被点击')
            this.testValue = ''
          },
        },
      })

      app.mount('body')
    </script>
  </body>
</html>
