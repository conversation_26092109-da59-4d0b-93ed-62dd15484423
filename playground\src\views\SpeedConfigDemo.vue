<template>
  <div class="speed-config-demo">
    <h1>Speed UI 配置系统演示</h1>
    
    <!-- 配置控制面板 -->
    <div class="config-panel">
      <h2>配置控制面板</h2>
      <div class="config-controls">
        <div class="config-group">
          <h3>Medium 尺寸配置</h3>
          <label>
            基础高度: 
            <input 
              v-model.number="mediumConfig.base" 
              type="number" 
              @input="updateConfig"
            />
          </label>
          <label>
            字体大小: 
            <input 
              v-model.number="mediumConfig.fontSize" 
              type="number" 
              @input="updateConfig"
            />
          </label>
          <label>
            上边距: 
            <input 
              v-model.number="mediumConfig.paddingTop" 
              type="number" 
              @input="updateConfig"
            />
          </label>
          <label>
            前缀左边距: 
            <input 
              v-model.number="mediumConfig.prefixPaddingLeft" 
              type="number" 
              @input="updateConfig"
            />
          </label>
        </div>
      </div>
    </div>

    <!-- 组件演示区域 -->
    <div class="demo-section">
      <h2>组件演示</h2>
      
      <div class="demo-group">
        <h3>预设尺寸（无内联样式）</h3>
        <div class="demo-row">
          <sp-input-field 
            size="small" 
            label="小尺寸" 
            placeholder="使用预设小尺寸"
            prefix-icon="User"
          />
          <sp-input-field 
            size="medium" 
            label="中等尺寸" 
            placeholder="使用预设中等尺寸"
            prefix-icon="Search"
          />
          <sp-input-field 
            size="large" 
            label="大尺寸" 
            placeholder="使用预设大尺寸"
            prefix-icon="Mail"
          />
        </div>
      </div>

      <div class="demo-group">
        <h3>数字尺寸（有内联样式）</h3>
        <div class="demo-row">
          <sp-input-field 
            :size="40" 
            label="40px 高度" 
            placeholder="自定义 40px 高度"
            prefix-icon="User"
          />
          <sp-input-field 
            :size="60" 
            label="60px 高度" 
            placeholder="自定义 60px 高度"
            prefix-icon="Search"
          />
          <sp-input-field 
            :size="80" 
            label="80px 高度" 
            placeholder="自定义 80px 高度"
            prefix-icon="Mail"
          />
        </div>
      </div>

      <div class="demo-group">
        <h3>不同变体</h3>
        <div class="demo-row">
          <sp-input-field 
            size="medium" 
            variant="default"
            label="默认变体" 
            placeholder="默认边框样式"
            prefix-icon="User"
          />
          <sp-input-field 
            size="medium" 
            variant="filled"
            label="填充变体" 
            placeholder="填充背景样式"
            prefix-icon="Search"
          />
          <sp-input-field 
            size="medium" 
            variant="underlined"
            label="下划线变体" 
            placeholder="下划线样式"
            prefix-icon="Mail"
          />
        </div>
      </div>
    </div>

    <!-- 代码示例 -->
    <div class="code-section">
      <h2>使用方法</h2>
      <pre><code>{{ codeExample }}</code></pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { setSpeedUIConfig } from '../../../packages/ui/src/config/speedConfig'

// 配置状态
const mediumConfig = reactive({
  base: 48,
  fontSize: 16,
  paddingTop: 24,
  paddingBottom: 4,
  labelFloatingTop: 8,
  labelFontSize: 14,
  prefixPadding: 4,
  prefixPaddingLeft: 16,
  prefixPaddingDefault: 20,
  prefixPaddingSquare: 4,
  gap: 12,
})

// 更新配置
const updateConfig = () => {
  setSpeedUIConfig({
    medium: { ...mediumConfig }
  })
}

// 代码示例
const codeExample = `// 1. 在应用启动时设置全局配置
import { setSpeedUIConfig } from '@speed-ui/ui/config/speedConfig'

setSpeedUIConfig({
  medium: {
    base: 52,           // 修改基础高度
    fontSize: 17,       // 修改字体大小
    paddingTop: 26,     // 修改上边距
    prefixPaddingLeft: 18, // 修改前缀左边距
    // ... 其他配置
  }
})

// 2. 使用预设尺寸（推荐，无内联样式）
<sp-input-field size="medium" label="中等尺寸" />
<sp-input-field size="large" label="大尺寸" />

// 3. 使用数字尺寸（会生成内联样式）
<sp-input-field :size="60" label="60px 高度" />

// 4. 不同变体都支持配置
<sp-input-field size="medium" variant="filled" />
<sp-input-field size="medium" variant="underlined" />`
</script>

<style scoped>
.speed-config-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.config-panel {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.config-controls {
  display: flex;
  gap: 30px;
}

.config-group {
  flex: 1;
}

.config-group h3 {
  margin-top: 0;
  color: #333;
}

.config-group label {
  display: block;
  margin-bottom: 10px;
  font-weight: 500;
}

.config-group input {
  margin-left: 10px;
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 80px;
}

.demo-section {
  margin-bottom: 30px;
}

.demo-group {
  margin-bottom: 30px;
}

.demo-group h3 {
  color: #666;
  margin-bottom: 15px;
}

.demo-row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.demo-row > * {
  flex: 1;
  min-width: 250px;
}

.code-section {
  background: #f8f8f8;
  padding: 20px;
  border-radius: 8px;
}

.code-section pre {
  background: #2d3748;
  color: #e2e8f0;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'Fira Code', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.5;
}
</style>
