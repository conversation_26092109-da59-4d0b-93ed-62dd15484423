<template>
  <div class="dropdown-demo">
    <h1>📋 Dropdown 下拉菜单组件演示</h1>
    <p>
      基础浮层容器组件，为 Select、DatePicker、Menu 等高级组件提供定位基础设施
    </p>

    <!-- 基础下拉菜单 -->
    <section class="demo-section">
      <h2>基础下拉菜单 (Basic Dropdown)</h2>
      <div class="demo-container">
        <sp-dropdown>
          <template #trigger>
            <button class="demo-button">点击显示下拉菜单</button>
          </template>
          <sp-list style="width: 200px">
            <sp-list-item
              item-key="basic1"
              title="选项 1"
              description="第一个选项"
            />
            <sp-list-item
              item-key="basic2"
              title="选项 2"
              description="第二个选项"
            />
            <sp-list-item
              item-key="basic3"
              title="选项 3"
              description="第三个选项"
            />
          </sp-list>
        </sp-dropdown>
      </div>
    </section>

    <!-- 触发方式 -->
    <section class="demo-section">
      <h2>触发方式 (Trigger Types)</h2>
      <div class="demo-container">
        <div class="trigger-group">
          <div class="trigger-item">
            <h3>点击触发</h3>
            <sp-dropdown trigger="click">
              <template #trigger>
                <button class="demo-button">Click</button>
              </template>
              <sp-menu style="width: 150px">
                <sp-menu-item key="click1">点击选项 1</sp-menu-item>
                <sp-menu-item key="click2">点击选项 2</sp-menu-item>
                <sp-menu-item key="click3">点击选项 3</sp-menu-item>
              </sp-menu>
            </sp-dropdown>
          </div>

          <div class="trigger-item">
            <h3>悬停触发</h3>
            <sp-dropdown trigger="hover">
              <template #trigger>
                <button class="demo-button">Hover</button>
              </template>
              <sp-menu style="width: 150px">
                <sp-menu-item key="hover1">悬停选项 1</sp-menu-item>
                <sp-menu-item key="hover2">悬停选项 2</sp-menu-item>
                <sp-menu-item key="hover3">悬停选项 3</sp-menu-item>
              </sp-menu>
            </sp-dropdown>
          </div>

          <div class="trigger-item">
            <h3>右键触发</h3>
            <sp-dropdown trigger="contextmenu">
              <template #trigger>
                <button class="demo-button">Right Click</button>
              </template>
              <sp-menu style="width: 150px">
                <sp-menu-item key="context1">右键选项 1</sp-menu-item>
                <sp-menu-item key="context2">右键选项 2</sp-menu-item>
                <sp-menu-item key="context3">右键选项 3</sp-menu-item>
              </sp-menu>
            </sp-dropdown>
          </div>
        </div>
      </div>
    </section>

    <!-- 弹出位置 -->
    <section class="demo-section">
      <h2>弹出位置 (Placement)</h2>
      <div class="demo-container">
        <div class="placement-grid">
          <div class="placement-item">
            <h4>top-start</h4>
            <sp-dropdown
              placement="top-start"
              :arrow="true"
            >
              <template #trigger>
                <button class="demo-button small">↖</button>
              </template>
              <sp-list style="width: 120px">
                <sp-list-item
                  key="ts1"
                  title="上方开始"
                />
                <sp-list-item
                  key="ts2"
                  title="选项 2"
                />
              </sp-list>
            </sp-dropdown>
          </div>

          <div class="placement-item">
            <h4>top</h4>
            <sp-dropdown
              placement="top"
              :arrow="true"
            >
              <template #trigger>
                <button class="demo-button small">↑</button>
              </template>
              <sp-list style="width: 120px">
                <sp-list-item
                  key="t1"
                  title="正上方"
                />
                <sp-list-item
                  key="t2"
                  title="选项 2"
                />
              </sp-list>
            </sp-dropdown>
          </div>

          <div class="placement-item">
            <h4>top-end</h4>
            <sp-dropdown
              placement="top-end"
              :arrow="true"
            >
              <template #trigger>
                <button class="demo-button small">↗</button>
              </template>
              <sp-list style="width: 120px">
                <sp-list-item
                  key="te1"
                  title="上方结束"
                />
                <sp-list-item
                  key="te2"
                  title="选项 2"
                />
              </sp-list>
            </sp-dropdown>
          </div>

          <div class="placement-item">
            <h4>left-start</h4>
            <sp-dropdown
              placement="left-start"
              :arrow="true"
            >
              <template #trigger>
                <button class="demo-button small">↖</button>
              </template>
              <sp-list style="width: 120px">
                <sp-list-item
                  key="ls1"
                  title="左侧开始"
                />
                <sp-list-item
                  key="ls2"
                  title="选项 2"
                />
              </sp-list>
            </sp-dropdown>
          </div>

          <div class="placement-item">
            <h4>center</h4>
            <div class="center-placeholder">Dropdown</div>
          </div>

          <div class="placement-item">
            <h4>right-start</h4>
            <sp-dropdown
              placement="right-start"
              :arrow="true"
            >
              <template #trigger>
                <button class="demo-button small">↗</button>
              </template>
              <sp-list style="width: 120px">
                <sp-list-item
                  key="rs1"
                  title="右侧开始"
                />
                <sp-list-item
                  key="rs2"
                  title="选项 2"
                />
              </sp-list>
            </sp-dropdown>
          </div>

          <div class="placement-item">
            <h4>bottom-start</h4>
            <sp-dropdown
              placement="bottom-start"
              :arrow="true"
            >
              <template #trigger>
                <button class="demo-button small">↙</button>
              </template>
              <sp-list style="width: 120px">
                <sp-list-item
                  key="bs1"
                  title="下方开始"
                />
                <sp-list-item
                  key="bs2"
                  title="选项 2"
                />
              </sp-list>
            </sp-dropdown>
          </div>

          <div class="placement-item">
            <h4>bottom</h4>
            <sp-dropdown
              placement="bottom"
              :arrow="true"
            >
              <template #trigger>
                <button class="demo-button small">↓</button>
              </template>
              <sp-list style="width: 120px">
                <sp-list-item
                  key="b1"
                  title="正下方"
                />
                <sp-list-item
                  key="b2"
                  title="选项 2"
                />
              </sp-list>
            </sp-dropdown>
          </div>

          <div class="placement-item">
            <h4>bottom-end</h4>
            <sp-dropdown
              placement="bottom-end"
              :arrow="true"
            >
              <template #trigger>
                <button class="demo-button small">↘</button>
              </template>
              <sp-list style="width: 120px">
                <sp-list-item
                  key="be1"
                  title="下方结束"
                />
                <sp-list-item
                  key="be2"
                  title="选项 2"
                />
              </sp-list>
            </sp-dropdown>
          </div>
        </div>
      </div>
    </section>

    <!-- 箭头指示器 -->
    <section class="demo-section">
      <h2>箭头指示器 (Arrow)</h2>
      <div class="demo-container">
        <div class="arrow-demo">
          <div class="arrow-item">
            <h3>无箭头</h3>
            <sp-dropdown :arrow="false">
              <template #trigger>
                <button class="demo-button">无箭头</button>
              </template>
              <sp-list style="width: 150px">
                <sp-list-item
                  key="no1"
                  title="选项 1"
                />
                <sp-list-item
                  key="no2"
                  title="选项 2"
                />
              </sp-list>
            </sp-dropdown>
          </div>

          <div class="arrow-item">
            <h3>有箭头</h3>
            <sp-dropdown :arrow="true">
              <template #trigger>
                <button class="demo-button">有箭头</button>
              </template>
              <sp-list style="width: 150px">
                <sp-list-item
                  key="arrow1"
                  title="选项 1"
                />
                <sp-list-item
                  key="arrow2"
                  title="选项 2"
                />
              </sp-list>
            </sp-dropdown>
          </div>
        </div>
      </div>
    </section>

    <!-- 状态控制 -->
    <section class="demo-section">
      <h2>状态控制 (State Control)</h2>
      <div class="demo-container">
        <div class="state-demo">
          <div class="state-item">
            <h3>禁用状态</h3>
            <sp-dropdown :disabled="true">
              <template #trigger>
                <button
                  class="demo-button"
                  disabled
                >
                  禁用按钮
                </button>
              </template>
              <sp-list style="width: 150px">
                <sp-list-item
                  key="dis1"
                  title="选项 1"
                />
                <sp-list-item
                  key="dis2"
                  title="选项 2"
                />
              </sp-list>
            </sp-dropdown>
          </div>

          <div class="state-item">
            <h3>受控显示</h3>
            <sp-dropdown v-model:visible="controlledVisible">
              <template #trigger>
                <button class="demo-button">受控显示</button>
              </template>
              <sp-list style="width: 150px">
                <sp-list-item
                  key="ctrl1"
                  title="选项 1"
                />
                <sp-list-item
                  key="ctrl2"
                  title="选项 2"
                />
              </sp-list>
            </sp-dropdown>
            <div class="state-controls">
              <button
                @click="controlledVisible = true"
                class="control-btn"
              >
                显示
              </button>
              <button
                @click="controlledVisible = false"
                class="control-btn"
              >
                隐藏
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 内容组合 -->
    <section class="demo-section">
      <h2>内容组合 (Content Composition)</h2>
      <div class="demo-container">
        <div class="content-demo">
          <div class="content-item">
            <h3>List + ListItem</h3>
            <sp-dropdown>
              <template #trigger>
                <button class="demo-button">列表选项</button>
              </template>
              <sp-list style="width: 200px">
                <sp-list-item
                  key="list1"
                  title="用户设置"
                  description="管理个人信息"
                />
                <sp-list-item
                  key="list2"
                  title="系统设置"
                  description="配置系统参数"
                />
                <sp-list-item
                  key="list3"
                  title="退出登录"
                  description="安全退出系统"
                />
              </sp-list>
            </sp-dropdown>
          </div>

          <div class="content-item">
            <h3>Menu + MenuItem</h3>
            <sp-dropdown>
              <template #trigger>
                <button class="demo-button">菜单操作</button>
              </template>
              <sp-menu style="width: 180px">
                <sp-menu-item key="menu1">📝 新建文档</sp-menu-item>
                <sp-menu-item key="menu2">📁 打开文件</sp-menu-item>
                <sp-menu-item key="menu3">💾 保存</sp-menu-item>
                <sp-menu-item
                  key="menu4"
                  disabled
                >
                  ❌ 删除
                </sp-menu-item>
              </sp-menu>
            </sp-dropdown>
          </div>

          <div class="content-item">
            <h3>自定义内容</h3>
            <sp-dropdown>
              <template #trigger>
                <button class="demo-button">自定义面板</button>
              </template>
              <div class="custom-content">
                <div class="custom-header">
                  <h4>用户信息</h4>
                </div>
                <div class="custom-body">
                  <div class="user-info">
                    <div class="avatar">👤</div>
                    <div class="info">
                      <div class="name">张三</div>
                      <div class="email"><EMAIL></div>
                    </div>
                  </div>
                  <div class="actions">
                    <button class="action-btn">编辑资料</button>
                    <button class="action-btn">账号设置</button>
                  </div>
                </div>
              </div>
            </sp-dropdown>
          </div>
        </div>
      </div>
    </section>

    <!-- 事件演示 -->
    <section class="demo-section">
      <h2>事件演示 (Events)</h2>
      <div class="demo-container">
        <sp-dropdown
          @visible-change="onVisibleChange"
          @select="onSelect"
          @click-outside="onClickOutside"
        >
          <template #trigger>
            <button class="demo-button">事件监听</button>
          </template>
          <sp-list style="width: 180px">
            <sp-list-item
              key="event1"
              title="事件选项 1"
            />
            <sp-list-item
              key="event2"
              title="事件选项 2"
            />
            <sp-list-item
              key="event3"
              title="事件选项 3"
            />
          </sp-list>
        </sp-dropdown>

        <div class="event-log">
          <h4>事件日志:</h4>
          <div class="log-content">
            <div
              v-for="(log, index) in eventLogs"
              :key="index"
              class="log-item"
            >
              {{ log }}
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 复用说明 -->
    <section class="demo-section">
      <h2>🚀 组件复用场景</h2>
      <div class="reuse-info">
        <div class="reuse-item">
          <strong>Select 组件:</strong>
          Dropdown 作为浮层容器，List + ListItem 作为选项列表
        </div>
        <div class="reuse-item">
          <strong>DatePicker 组件:</strong>
          Dropdown 作为日历面板容器，自定义日历内容
        </div>
        <div class="reuse-item">
          <strong>Tooltip 组件:</strong>
          Dropdown 的简化版本，提供基础定位能力
        </div>
        <div class="reuse-item">
          <strong>Popover 组件:</strong>
          基于 Dropdown 扩展，支持更丰富的内容
        </div>
        <div class="reuse-item">
          <strong>Cascader 组件:</strong>
          多个 Dropdown 级联，实现多级选择
        </div>
        <div class="reuse-item">
          <strong>AutoComplete 组件:</strong>
          Dropdown + List 实现搜索建议面板
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  // 状态管理
  const controlledVisible = ref(false)
  const eventLogs = ref<string[]>([])

  // 事件处理
  const onVisibleChange = (visible: boolean) => {
    const log = `🔄 visibleChange: ${visible ? '显示' : '隐藏'}`
    eventLogs.value.unshift(log)
    if (eventLogs.value.length > 5) {
      eventLogs.value.pop()
    }
  }

  const onSelect = () => {
    const log = `✅ select: 选择了一个选项`
    eventLogs.value.unshift(log)
    if (eventLogs.value.length > 5) {
      eventLogs.value.pop()
    }
  }

  const onClickOutside = () => {
    const log = `👆 clickOutside: 点击了外部区域`
    eventLogs.value.unshift(log)
    if (eventLogs.value.length > 5) {
      eventLogs.value.pop()
    }
  }
</script>

<style scoped>
  .dropdown-demo {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .dropdown-demo h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 10px;
  }

  .dropdown-demo > p {
    text-align: center;
    color: #7f8c8d;
    margin-bottom: 40px;
  }

  .demo-section {
    margin-bottom: 50px;
    padding: 30px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background: #fafafa;
  }

  .demo-section h2 {
    color: #34495e;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #ecf0f1;
  }

  .demo-container {
    display: flex;
    gap: 30px;
    align-items: flex-start;
    flex-wrap: wrap;
  }

  .demo-button {
    padding: 8px 16px;
    border: 1px solid #d0d7de;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
  }

  .demo-button:hover:not(:disabled) {
    border-color: #0969da;
    background: #f6f8fa;
  }

  .demo-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .demo-button.small {
    padding: 6px 12px;
    font-size: 12px;
  }

  .trigger-group {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
  }

  .trigger-item h3 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 14px;
  }

  .placement-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    max-width: 400px;
    margin: 0 auto;
  }

  .placement-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }

  .placement-item h4 {
    margin: 0;
    font-size: 12px;
    color: #666;
  }

  .center-placeholder {
    padding: 6px 12px;
    background: #f0f0f0;
    border-radius: 6px;
    font-size: 12px;
    color: #999;
    pointer-events: none;
  }

  .arrow-demo {
    display: flex;
    gap: 30px;
  }

  .arrow-item h3 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 14px;
  }

  .state-demo {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
  }

  .state-item h3 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 14px;
  }

  .state-controls {
    margin-top: 10px;
    display: flex;
    gap: 8px;
  }

  .control-btn {
    padding: 4px 8px;
    border: 1px solid #d0d7de;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    font-size: 12px;
  }

  .content-demo {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
  }

  .content-item h3 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 14px;
  }

  .custom-content {
    width: 250px;
    background: white;
    border-radius: 6px;
    overflow: hidden;
  }

  .custom-header {
    padding: 12px 16px;
    background: #f6f8fa;
    border-bottom: 1px solid #e1e4e8;
  }

  .custom-header h4 {
    margin: 0;
    font-size: 14px;
    color: #24292f;
  }

  .custom-body {
    padding: 16px;
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
  }

  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
  }

  .info .name {
    font-weight: 500;
    margin-bottom: 4px;
  }

  .info .email {
    font-size: 12px;
    color: #656d76;
  }

  .actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .action-btn {
    padding: 6px 12px;
    border: 1px solid #d0d7de;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    font-size: 12px;
    text-align: left;
  }

  .event-log {
    flex: 1;
    min-width: 250px;
  }

  .event-log h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
  }

  .log-content {
    max-height: 150px;
    overflow-y: auto;
    background: #f8f9fa;
    border: 1px solid #e1e4e8;
    border-radius: 4px;
    padding: 10px;
  }

  .log-item {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #495057;
    margin-bottom: 4px;
    padding: 4px;
    background: white;
    border-radius: 2px;
  }

  .reuse-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
  }

  .reuse-item {
    padding: 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #ddd;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .reuse-item strong {
    color: #2c3e50;
  }
</style>
