<template>
  <div class="input-wrapper-test">
    <h1>🚀 Input 组件重构测试</h1>
    <p>测试基于 BaseInputWrapper 重构后的 Password、Email、Search 组件</p>

    <!-- Password 组件测试 -->
    <div class="demo-section">
      <h2>🔐 Password 组件测试</h2>

      <div class="demo-item">
        <label>基础密码输入框:</label>
        <sp-input-password
          v-model:value="passwordValue1"
          placeholder="请输入密码"
          clearable
        />
        <p>当前值: "{{ passwordValue1 }}"</p>
      </div>

      <div class="demo-item">
        <label>带帮助的密码输入框:</label>
        <sp-input-password
          v-model:value="passwordValue2"
          placeholder="请输入密码"
          clearable
          :maxlength="20"
          show-count-out
        />
        <p>当前值: "{{ passwordValue2 }}"</p>
      </div>
    </div>

    <!-- Email 组件测试 -->
    <div class="demo-section">
      <h2>📧 Email 组件测试</h2>

      <div class="demo-item">
        <label>基础邮箱输入框:</label>
        <sp-input-email
          v-model:value="emailValue1"
          placeholder="请输入邮箱地址"
          clearable
          @validate="handleEmailValidate"
        />
        <p>当前值: "{{ emailValue1 }}"</p>
        <p
          v-if="emailValidation.message"
          :style="{ color: emailValidation.valid ? 'green' : 'red' }"
        >
          {{ emailValidation.message }}
        </p>
      </div>

      <div class="demo-item">
        <label>带帮助的邮箱输入框:</label>
        <sp-input-email
          v-model:value="emailValue2"
          placeholder="请输入邮箱地址"
          help="请输入有效的邮箱地址"
          clearable
          :maxlength="50"
          show-count-out
        />
        <p>当前值: "{{ emailValue2 }}"</p>
      </div>
    </div>

    <!-- Search 组件测试 -->
    <div class="demo-section">
      <h2>🔍 Search 组件测试</h2>

      <div class="demo-item">
        <label>基础搜索输入框:</label>
        <sp-input-search
          v-model:value="searchValue1"
          placeholder="请输入搜索内容"
          clearable
          @search="handleSearch"
        />
        <p>当前值: "{{ searchValue1 }}"</p>
        <p
          v-if="searchResult"
          style="color: blue"
        >
          {{ searchResult }}
        </p>
      </div>

      <div class="demo-item">
        <label>加载状态搜索框:</label>
        <sp-input-search
          v-model:value="searchValue2"
          placeholder="请输入搜索内容"
          :loading="searchLoading"
          clearable
          @search="handleSearchWithLoading"
        />
        <p>当前值: "{{ searchValue2 }}"</p>
        <button @click="toggleSearchLoading">
          {{ searchLoading ? '停止加载' : '开始加载' }}
        </button>
      </div>
    </div>

    <!-- 对比测试 -->
    <div class="demo-section">
      <h2>📊 重构前后对比</h2>
      <div class="comparison">
        <div class="before">
          <h3>重构前 (代码行数)</h3>
          <ul>
            <li>Password.vue: ~209 行</li>
            <li>Email.vue: ~262 行</li>
            <li>Search.vue: ~212 行</li>
            <li><strong>总计: ~683 行</strong></li>
          </ul>
        </div>
        <div class="after">
          <h3>重构后 (代码行数)</h3>
          <ul>
            <li>BaseInputWrapper.vue: ~200 行</li>
            <li>Password.vue: ~140 行</li>
            <li>Email.vue: ~160 行</li>
            <li>Search.vue: ~130 行</li>
            <li><strong>总计: ~630 行</strong></li>
          </ul>
        </div>
      </div>
      <div class="benefits">
        <h3>✅ 重构收益</h3>
        <ul>
          <li>
            <strong>代码减少</strong>
            : 减少了约 53 行重复代码
          </li>
          <li>
            <strong>维护性提升</strong>
            : 共同逻辑集中在 BaseInputWrapper
          </li>
          <li>
            <strong>一致性保证</strong>
            : 所有包装组件行为一致
          </li>
          <li>
            <strong>扩展性增强</strong>
            : 新增包装组件更容易
          </li>
          <li>
            <strong>类型安全</strong>
            : 统一的类型定义
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  // Password 测试数据
  const passwordValue1 = ref('')
  const passwordValue2 = ref('')

  // Email 测试数据
  const emailValue1 = ref('')
  const emailValue2 = ref('')
  const emailValidation = ref({ valid: false, message: '' })

  // Search 测试数据
  const searchValue1 = ref('')
  const searchValue2 = ref('')
  const searchResult = ref('')
  const searchLoading = ref(false)

  // 事件处理
  const handleEmailValidate = (result: {
    valid: boolean
    message?: string
  }) => {
    emailValidation.value = {
      valid: result.valid,
      message: result.message || '',
    }
  }

  const handleSearch = (value: string | number) => {
    searchResult.value = `搜索了: "${value}"`
    setTimeout(() => {
      searchResult.value = ''
    }, 3000)
  }

  const handleSearchWithLoading = (value: string | number) => {
    searchLoading.value = true
    setTimeout(() => {
      searchLoading.value = false
      searchResult.value = `加载搜索完成: "${value}"`
      setTimeout(() => {
        searchResult.value = ''
      }, 3000)
    }, 2000)
  }

  const toggleSearchLoading = () => {
    searchLoading.value = !searchLoading.value
  }
</script>

<style scoped>
  .input-wrapper-test {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .demo-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;
  }

  .demo-section h2 {
    margin-top: 0;
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
  }

  .demo-item {
    margin-bottom: 20px;
    padding: 15px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .demo-item label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
  }

  .demo-item p {
    margin: 8px 0 0 0;
    font-size: 14px;
    color: #666;
  }

  .comparison {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
  }

  .before,
  .after {
    padding: 15px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .before h3 {
    color: #dc3545;
  }

  .after h3 {
    color: #28a745;
  }

  .benefits {
    padding: 15px;
    background: #e8f5e8;
    border-radius: 6px;
    border-left: 4px solid #28a745;
  }

  .benefits h3 {
    margin-top: 0;
    color: #155724;
  }

  .benefits ul {
    margin: 0;
  }

  .benefits li {
    margin-bottom: 5px;
  }

  button {
    padding: 8px 16px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
  }

  button:hover {
    background: #0056b3;
  }
</style>
