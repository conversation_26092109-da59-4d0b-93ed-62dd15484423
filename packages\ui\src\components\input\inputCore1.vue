<!--
  InputCore.vue - 使用 Composition API 的核心组件
  用 composables 实现关注点分离，但保持简洁实用
-->

<template>
  <div :class="rootClasses">
    <div
      ref="wrapperRef"
      :class="wrapperClasses"
      @click="handleWrapperClick"
    >
      <!-- 前缀区域 -->
      <div
        v-if="slots.prefix || prefixIcon"
        :class="prefixClasses"
        @click.stop
        @mousedown.prevent
      >
        <sp-icon
          v-if="prefixIcon"
          :name="prefixIcon"
          :size="iconSize"
          :class="prefixIconClasses"
        />
        <slot name="prefix"></slot>
      </div>

      <!-- 输入框 -->
      <input
        ref="inputRef"
        :type="actualType"
        :value="value"
        :placeholder="placeholder"
        :disabled="computedDisabled"
        :readonly="readonly"
        :maxlength="maxlength"
        :class="inputClasses"
        @input="handleInput"
        @change="handleChange"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
      />

      <!-- 后缀区域 -->
      <div
        v-if="showSuffix"
        :class="suffixClasses"
        @click.stop
        @mousedown.prevent
      >
        <!-- 清除按钮 -->
        <sp-icon
          v-if="showClearIcon"
          name="CloseCircle"
          :size="iconSize"
          clickable
          :class="clearIconClasses"
          @click.stop.prevent="handleClear"
          @mousedown.stop.prevent
        />

        <!-- 密码显示切换 -->
        <sp-icon
          v-if="showPasswordIcon"
          :name="passwordIconName"
          :size="iconSize"
          clickable
          :class="passwordIconClasses"
          @click.stop.prevent="togglePassword"
          @mousedown.stop.prevent
        />

        <!-- 字数统计 -->
        <span
          v-if="showWordLimit"
          :class="wordCountClasses"
        >
          {{ wordCount }}/{{ maxlength }}
        </span>

        <!-- 自定义后缀图标 -->
        <sp-icon
          v-if="suffixIcon"
          :name="suffixIcon"
          :size="iconSize"
          :class="suffixIconClasses"
        />

        <!-- 后缀插槽 -->
        <slot name="suffix"></slot>
      </div>
    </div>

    <!-- 加载动画条 -->
    <div
      v-if="loading"
      :class="loadingBarClasses"
    >
      <div :class="loadingProgressClasses"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useSlots } from 'vue'
  import SpIcon from '../icon/Icon.vue'
  import { useInputLogic, useInputStyles } from '../../composables'
  import type { InputProps, InputEmits } from './types'

  // ===== Props 和 Emits =====
  const props = withDefaults(defineProps<InputProps>(), {
    value: '',
    type: 'text',
    variant: 'default',
    effect: 'none',
    size: 'medium',
    disabled: false,
    readonly: false,
    clearable: false,
    showPassword: false,
    showWordLimit: false,
    error: false,
    loading: false,
  })

  const emit = defineEmits<InputEmits>()
  const slots = useSlots()

  // ===== 使用逻辑 Composable =====
  const {
    // 引用
    wrapperRef,
    inputRef,

    // 状态
    passwordVisible,
    isFocused,

    // 计算属性
    actualType,
    iconSize,
    hasValue,
    wordCount,
    computedDisabled,

    // 显示逻辑
    showClearIcon,
    showPasswordIcon,
    passwordIconName,
    showSuffix,

    // 事件处理
    handleInput,
    handleChange,
    handleFocus,
    handleBlur,
    handleKeydown,
    handleWrapperClick,
    handleClear,
    togglePassword,

    // 方法
    focus,
    blur,
    select,
    clear,
  } = useInputLogic(props, emit)

  // ===== 使用样式 Composable =====
  const {
    rootClasses,
    wrapperClasses,
    inputClasses,
    prefixClasses,
    suffixClasses,
    prefixIconClasses,
    suffixIconClasses,
    clearIconClasses,
    passwordIconClasses,
    wordCountClasses,
    loadingBarClasses,
    loadingProgressClasses,
  } = useInputStyles(props, {
    computedDisabled,
    isFocused,
  })

  // ===== 暴露方法 =====
  defineExpose({
    focus,
    blur,
    select,
    clear,
    get input() {
      return inputRef.value || null
    },
    get wrapper() {
      return wrapperRef.value || null
    },
  })
</script>

<script lang="ts">
  export default {
    name: 'InputCore',
  }
</script>
