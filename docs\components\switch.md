# switch 开关

## 基础用法

:::switch/basic

## 不同尺寸

:::switch/sizes

## 禁用状态

:::switch/states

## 文本显示

:::switch/text

## loading状态

:::switch/loading

## icon显示

:::switch/icon

## 颜色自定义

:::switch/colors

## 样式变体

:::switch/variants

## 倒计时功能

:::switch/time

## 自定义样式

:::switch/custom

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| v-model:value | 开关状态 | `boolean` | `false` |
| disabled | 是否禁用 | `boolean` | `false` |
| size | 开关尺寸 | `'small' \| 'medium' \| 'large' \| 'huge'` | `'medium'` |
| activeColor | 打开时的背景色 | `string` | - |
| inactiveColor | 关闭时的背景色 | `string` | - |
| onText | 开关打开时显示的文本 | `string` | - |
| offText | 开关关闭时显示的文本 | `string` | - |
| onOutText | 开关打开时在外部显示的文本 | `string` | - |
| offOutText | 开关关闭时在外部显示的文本 | `string` | - |
| square | 是否为方形样式 | `boolean` | `false` |
| vertical | 是否为垂直样式 | `boolean` | `false` |
| time | 倒计时时间（秒） | `number` | - |
| switchColor | 自定义开关背景颜色 | `string` | - |
| buttonColor | 自定义按钮（小圆圈）颜色 | `string` | - |
| switchOnColor | 开启状态下的开关背景颜色 | `string` | - |
| switchOffColor | 关闭状态下的开关背景颜色 | `string` | - |
| buttonOnColor | 开启状态下的按钮颜色 | `string` | - |
| buttonOffColor | 关闭状态下的按钮颜色 | `string` | - |

### Events

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| update:value | 更新开关值（用于 v-model:value） | `(value: boolean) => void` |
| change | 开关状态改变时触发 | `(value: boolean) => void` |