<template>
  <div class="slider-field-simple">
    <h1>🎚️ SliderField 简洁版演示</h1>
    <p>无边框、无标签的纯滑块组件</p>

    <!-- 纯滑块 -->
    <section class="demo-section">
      <h2>纯滑块</h2>
      <div class="demo-container">
        <div class="slider-wrapper">
          <h3>基础滑块</h3>
          <sp-slider-field
            v-model:value="value1"
            name="basic"
            :min="0"
            :max="100"
            show-value
          />
          <p>当前值: {{ value1 }}</p>
        </div>
      </div>
    </section>

    <!-- 带前缀后缀 -->
    <section class="demo-section">
      <h2>带前缀后缀</h2>
      <div class="demo-container">
        <div class="slider-wrapper">
          <h3>音量控制</h3>
          <sp-slider-field
            v-model:value="value2"
            name="volume"
            prefix="🔇"
            suffix="🔊"
            :min="0"
            :max="100"
            show-value
          />
          <p>当前值: {{ value2 }}</p>
        </div>

        <div class="slider-wrapper">
          <h3>亮度控制</h3>
          <sp-slider-field
            v-model:value="value3"
            name="brightness"
            prefix="🔅"
            suffix="🔆"
            :min="0"
            :max="100"
            show-value
          />
          <p>当前值: {{ value3 }}</p>
        </div>
      </div>
    </section>

    <!-- 带图标 -->
    <section class="demo-section">
      <h2>带图标</h2>
      <div class="demo-container">
        <div class="slider-wrapper">
          <h3>音量滑块</h3>
          <sp-slider-field
            v-model:value="value4"
            name="volume-icon"
            prepend-icon-inner="VolumeDown"
            append-icon-inner="VolumeUp"
            :min="0"
            :max="100"
            show-value
          />
          <p>当前值: {{ value4 }}</p>
        </div>

        <div class="slider-wrapper">
          <h3>温度控制</h3>
          <sp-slider-field
            v-model:value="value5"
            name="temperature"
            prepend-icon-inner="Thermometer"
            :min="16"
            :max="30"
            :marks="temperatureMarks"
            show-value
            :value-formatter="(val) => `${val}°C`"
          />
          <p>当前值: {{ value5 }}°C</p>
        </div>
      </div>
    </section>

    <!-- 不同尺寸 -->
    <section class="demo-section">
      <h2>不同尺寸</h2>
      <div class="demo-container">
        <div class="slider-wrapper">
          <h3>小尺寸</h3>
          <sp-slider-field
            v-model:value="value6"
            name="small"
            size="small"
            :min="0"
            :max="100"
            show-value
          />
          <p>当前值: {{ value6 }}</p>
        </div>

        <div class="slider-wrapper">
          <h3>中等尺寸</h3>
          <sp-slider-field
            v-model:value="value7"
            name="medium"
            size="medium"
            :min="0"
            :max="100"
            show-value
          />
          <p>当前值: {{ value7 }}</p>
        </div>

        <div class="slider-wrapper">
          <h3>大尺寸</h3>
          <sp-slider-field
            v-model:value="value8"
            name="large"
            size="large"
            :min="0"
            :max="100"
            show-value
          />
          <p>当前值: {{ value8 }}</p>
        </div>
      </div>
    </section>

    <!-- 滑块变体 -->
    <section class="demo-section">
      <h2>滑块变体</h2>
      <div class="demo-container">
        <div class="slider-wrapper">
          <h3>默认变体</h3>
          <sp-slider-field
            v-model:value="value9"
            name="default-variant"
            variant="default"
            :min="0"
            :max="100"
            show-value
          />
          <p>当前值: {{ value9 }}</p>
        </div>

        <div class="slider-wrapper">
          <h3>填充变体</h3>
          <sp-slider-field
            v-model:value="value10"
            name="filled-variant"
            variant="filled"
            :min="0"
            :max="100"
            show-value
          />
          <p>当前值: {{ value10 }}</p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 响应式数据
const value1 = ref(30)
const value2 = ref(60)
const value3 = ref(80)
const value4 = ref(45)
const value5 = ref(22)
const value6 = ref(25)
const value7 = ref(50)
const value8 = ref(75)
const value9 = ref(40)
const value10 = ref(65)

// 温度标记
const temperatureMarks = {
  16: '16°',
  20: '20°',
  24: '24°',
  28: '28°',
  30: '30°'
}
</script>

<style scoped>
.slider-field-simple {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 40px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.demo-section h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 2px solid #667eea;
  padding-bottom: 10px;
}

.demo-container {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.slider-wrapper {
  padding: 20px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.slider-wrapper h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #555;
  font-size: 16px;
}

.slider-wrapper p {
  margin-top: 15px;
  margin-bottom: 0;
  color: #666;
  font-size: 14px;
}
</style>
