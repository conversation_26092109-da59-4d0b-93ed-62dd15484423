.sp-form-item {
  margin-bottom: 6px;
  display: flex;
  align-items: flex-start;
  
  min-height: 58px;

  &:last-child {
    margin-bottom: 0;
  }

  // Label 位置布局
  &--label-top {
    flex-direction: column;
    align-items: stretch;
    margin-bottom: 18px;
    min-height: auto;
    
    .sp-form-item__label {
      padding: 0;
      line-height: 1.5;
      width: auto !important;
      text-align: left !important;
      justify-content: flex-start !important;
      display: block;
      flex-shrink: 0;
    }
    
    .sp-form-item__content {
      width: 100%;
      flex: 1;
    }
  }
  
  &--label-left,
  &--label-right {
    flex-direction: row;
    align-items: flex-start;
    
    .sp-form-item__label {
      flex-shrink: 0;
      box-sizing: border-box;
    }
    
    .sp-form-item__content {
      flex: 1;
      min-width: 0;
    }
  }
  
  &--label-left {
    .sp-form-item__label {
      text-align: left;
    }
  }
  
  &--label-right {
    .sp-form-item__label {
      text-align: right;
    }
  }

  &__label {
    display: inline-block;
    padding: 0 12px 0 0;
    font-size: 14px;
    color: #606266;
    line-height: 40px;
    font-weight: 500;
    white-space: nowrap;
    flex-shrink: 0;
    box-sizing: border-box;

    &-icon {
      margin-right: 4px;
      font-size: 12px;
      font-weight: bold;
      
      &--required {
        color: #f56c6c;
      }
      
      &--success {
        color: #67c23a;
      }
    }

    &-suffix {
      color: #909399;
    }
  }

  &__content {
    position: relative;
    flex: 1;
    min-width: 0;
  }

  &__error {
    position: absolute;
    top: calc(100% + 2px);
    left: 0;
    right: 0;
    color: #f56c6c;
    font-size: 12px;
    line-height: 16px;
    height: 16px;
    display: flex;
    align-items: center;

    &--success {
      color: #67c23a;
    }

    &--warning {
      color: #e6a23c;
    }

    &--inline {
      white-space: nowrap;
    }

    &-icon {
      margin-right: 4px;
      font-size: 12px;
      flex-shrink: 0;
    }
  }

  // 尺寸变体
  &--small {
    min-height: 48px;
    
    .sp-form-item__label {
      line-height: 32px;
      font-size: 12px;
    }
    
    .sp-form-item__error {
      font-size: 11px;
      line-height: 14px;
      height: 14px;
    }
  }

  &--large {
    min-height: 68px;
    
    .sp-form-item__label {
      line-height: 48px;
      font-size: 16px;
    }
    
    .sp-form-item__error {
      font-size: 13px;
      line-height: 18px;
      height: 18px;
    }
  }

  // 验证状态
  &--error {
    .sp-form-item__label {
      color: #f56c6c;
    }
  }

  &--success {
    .sp-form-item__label {
      color: #67c23a;
    }
  }

  &--warning {
    .sp-form-item__label {
      color: #e6a23c;
    }
  }

  &--validating {
    .sp-form-item__label {
      color: #409eff;
    }
  }

  // 无星号状态
  &--no-asterisk {
    .sp-form-item__label--required::before {
      display: none;
    }
  }
}

// 消息过渡动画 - 只透明度变化，不移动位置
.sp-form-item-message-enter-active,
.sp-form-item-message-leave-active {
  transition: opacity 0.2s ease-in-out;
}

.sp-form-item-message-enter-from,
.sp-form-item-message-leave-to {
  opacity: 0;
} 