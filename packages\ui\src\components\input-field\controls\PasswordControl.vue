<!--
  PasswordControl.vue - 密码显示切换控件
  提供密码显示/隐藏切换功能
-->

<template>
  <sp-icon
    :name="iconName"
    :size="iconSize"
    :clickable="!disabled"
    class="sp-input-field__password"
    @click="handleToggle"
    @mousedown.stop.prevent
  />
</template>

<script setup lang="ts">
  import SpIcon from '../../icon/Icon.vue'
  import type { BaseControlProps } from './types'

  interface PasswordControlProps extends BaseControlProps {
    /** 密码图标名称 */
    iconName: string
  }

  interface PasswordControlEmits {
    /** 切换密码显示状态 */
    (e: 'toggle'): void
  }

  const props = defineProps<PasswordControlProps>()
  const emit = defineEmits<PasswordControlEmits>()

  /** 处理切换点击 */
  const handleToggle = () => {
    if (!props.disabled) {
      emit('toggle')
    }
  }
</script>

<script lang="ts">
  export default {
    name: 'PasswordControl',
  }
</script>
