/**
 * 类名合并工具函数
 * 支持字符串、数组、对象等多种类型的类名合并
 */
export function classNames(...args: any[]): string {
  const classes: string[] = []
  
  for (let i = 0; i < args.length; i++) {
    const value = args[i]
    if (!value) continue
    
    if (typeof value === 'string') {
      classes.push(value)
    } else if (Array.isArray(value)) {
      const inner = classNames(...value)
      if (inner) classes.push(inner)
    } else if (typeof value === 'object') {
      for (const name in value) {
        if (value[name]) {
          classes.push(name)
        }
      }
    }
  }
  
  return classes.join(' ')
}

/**
 * 生成带前缀的类名
 * @param prefixCls 前缀类名
 * @param suffix 后缀
 * @returns 完整类名
 */
export function getPrefixCls(prefixCls: string, suffix?: string): string {
  return suffix ? `${prefixCls}-${suffix}` : prefixCls
}

/**
 * 生成BEM风格的类名
 * @param block 块名
 * @param element 元素名
 * @param modifier 修饰符
 * @returns BEM类名
 */
export function bemClass(block: string, element?: string, modifier?: string): string {
  let className = block
  
  if (element) {
    className += `__${element}`
  }
  
  if (modifier) {
    className += `--${modifier}`
  }
  
  return className
}