# Btn 按钮组件

一个现代化的按钮组件，采用双层架构设计，支持多种变体和交互效果。

## 🏗️ 架构设计

### 双层架构

- **逻辑层** (`btn.vue`): 负责状态管理、事件处理和业务逻辑
- **样式层** (`btn-style.vue`): 负责 DOM 渲染和样式应用

### 设计优势

- ✅ 职责分离，代码清晰
- ✅ 样式与逻辑解耦
- ✅ 易于维护和扩展
- ✅ 支持主题系统

## 📦 基础用法

```vue
<template>
  <!-- 默认按钮 -->
  <Btn>BUTTON</Btn>

  <!-- 边框按钮 -->
  <Btn variant="outlined">BUTTON</Btn>

  <!-- 文本按钮 -->
  <Btn variant="text">BUTTON</Btn>
</template>

<script setup>
  import { Btn } from '@speed-ui/ui'
</script>
```

## 🎨 变体类型

### variant 属性

- `default` - 默认实心按钮
- `outlined` - 边框按钮
- `text` - 文本按钮

### size 属性

- `small` - 小尺寸 (24px 高度)
- `medium` - 中等尺寸 (32px 高度，默认)
- `large` - 大尺寸 (40px 高度)

## 🎭 交互效果

### 鼠标悬停

- 轻微上移效果 (`translateY(-1px)`)
- 阴影增强
- 颜色变化

### 点击按下

- 按钮回到原位置
- 颜色进一步加深
- 阴影调整

### 禁用状态

- 透明度降低 (0.6)
- 禁用鼠标事件
- 光标变为 `not-allowed`

## 🔧 Props

| 属性     | 类型                                | 默认值      | 说明     |
| -------- | ----------------------------------- | ----------- | -------- |
| variant  | `'default' \| 'outlined' \| 'text'` | `'default'` | 按钮变体 |
| size     | `'small' \| 'medium' \| 'large'`    | `'medium'`  | 按钮尺寸 |
| disabled | `boolean`                           | `false`     | 是否禁用 |

## 📡 Events

| 事件名 | 参数                  | 说明     |
| ------ | --------------------- | -------- |
| click  | `(event: MouseEvent)` | 点击事件 |

## 🎨 主题支持

组件支持 Speed UI 主题系统，会自动响应以下 CSS 变量：

- `--sp-color-primary` - 主色调
- `--sp-color-primary-hover` - 悬停色
- `--sp-color-primary-active` - 激活色
- `--sp-color-primary-lightest` - 最浅色
- `--sp-color-primary-light` - 浅色

## 📱 响应式设计

- 在小屏幕设备上自动增加触摸目标大小
- 支持高对比度模式
- 支持减少动画偏好设置
- 支持深色模式

## 🔍 示例

查看完整的演示页面：`/zh-CN/btn-demo`

## 🚀 开发指南

### 添加新变体

1. 在 `BtnProps` 接口中添加新的 variant 类型
2. 在 `btn.vue` 中更新类名计算逻辑
3. 在 `btn-style.vue` 中添加对应的样式

### 自定义样式

可以通过 CSS 变量或直接覆盖类名来自定义样式：

```css
.sp-btn.my-custom-btn {
  --sp-color-primary: #ff6b6b;
  border-radius: 20px;
}
```
