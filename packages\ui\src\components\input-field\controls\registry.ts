/**
 * 输入框控件注册表
 * 管理所有控件的注册、配置和获取
 */

import type { 
  ControlConfig, 
  ControlRegistry, 
  ControlType, 
  ControlPosition 
} from './types'

/** 控件注册表实现 */
class InputControlRegistry implements ControlRegistry {
  private controls = new Map<ControlType, ControlConfig>()

  /** 注册控件 */
  register(config: ControlConfig): void {
    this.controls.set(config.type, config)
  }

  /** 注销控件 */
  unregister(type: ControlType): void {
    this.controls.delete(type)
  }

  /** 获取控件配置 */
  get(type: ControlType): ControlConfig | undefined {
    return this.controls.get(type)
  }

  /** 获取所有控件配置 */
  getAll(): ControlConfig[] {
    return Array.from(this.controls.values())
  }

  /** 根据位置获取控件 */
  getByPosition(position: ControlPosition): ControlConfig[] {
    return this.getAll()
      .filter(config => config.position === position)
      .sort((a, b) => a.order - b.order)
  }
}

/** 全局控件注册表实例 */
export const controlRegistry = new InputControlRegistry()

/** 便捷的注册函数 */
export function registerControl(config: ControlConfig): void {
  controlRegistry.register(config)
}

/** 便捷的获取函数 */
export function getControl(type: ControlType): ControlConfig | undefined {
  return controlRegistry.get(type)
}

/** 便捷的获取所有控件函数 */
export function getAllControls(): ControlConfig[] {
  return controlRegistry.getAll()
}

/** 便捷的根据位置获取控件函数 */
export function getControlsByPosition(position: ControlPosition): ControlConfig[] {
  return controlRegistry.getByPosition(position)
}
