import type { CSSProperties } from 'vue'

export interface ListInnerProps {
  /** HTML标签 */
  tag?: string
  /** CSS类名 */
  classes?: string[]
  /** 内联样式 */
  styles?: CSSProperties
  /** ARIA角色 */
  role?: string
  /** ARIA标签 */
  ariaLabel?: string
  /** 是否支持多选 */
  ariaMultiselectable?: boolean
}

export interface ListInnerEvents {
  click: [event: MouseEvent]
  keydown: [event: KeyboardEvent]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
}
