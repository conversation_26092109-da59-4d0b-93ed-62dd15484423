<!--
  ClearControl.vue - 清除按钮控件
  提供清除输入框内容的功能
-->

<template>
  <sp-icon
    name="CloseCircle"
    :size="iconSize"
    :clickable="!disabled"
    class="sp-input-field__clear"
    @click="handleClear"
    @mousedown.stop.prevent
  />
</template>

<script setup lang="ts">
  import SpIcon from '../../icon/Icon.vue'
  import type { BaseControlProps } from './types'

  interface ClearControlProps extends BaseControlProps {
    // 清除控件暂时不需要额外属性
  }

  interface ClearControlEmits {
    /** 清除事件 */
    (e: 'clear'): void
  }

  const props = defineProps<ClearControlProps>()
  const emit = defineEmits<ClearControlEmits>()

  /** 处理清除点击 */
  const handleClear = () => {
    if (!props.disabled) {
      emit('clear')
    }
  }
</script>

<script lang="ts">
  export default {
    name: 'ClearControl',
  }
</script>
