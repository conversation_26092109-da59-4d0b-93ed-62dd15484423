<template>
  <div
    class="sp-btn-wrapper"
    :class="wrapperClasses"
    :style="wrapperStyles"
  >
    <BtnBase
      ref="btnBaseRef"
      v-bind="coreProps"
      @click="handleClick"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @mousedown="handleMouseDown"
      @mouseup="handleMouseUp"
    >
      <slot />
    </BtnBase>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue'
  import { bemHelper } from '@speed-ui/config'
  import BtnBase from './btn-base.vue'
  import type { BtnStyleProps, BtnEmits } from './types'

  // 禁用属性继承，手动控制
  defineOptions({
    inheritAttrs: false
  })

  // 样式层组件，负责样式计算和类名生成
  const props = withDefaults(defineProps<BtnStyleProps>(), {
    variant: 'default',
    disabled: false,
    size: 'medium',
    isHovered: false,
    isPressed: false,
  })

  const emit = defineEmits<BtnEmits>()

  // 简单的事件处理器
  const handleClick = () => {
    console.log('� Button clicked in wrapper')
    emit('click')
  }

  const handleMouseEnter = () => {
    console.log('🐭 Mouse enter in wrapper')
    emit('mouseenter')
  }

  const handleMouseLeave = () => {
    console.log('🐭 Mouse leave in wrapper')
    emit('mouseleave')
  }

  const handleMouseDown = () => {
    console.log('🐭 Mouse down in wrapper')
    emit('mousedown')
  }

  const handleMouseUp = () => {
    console.log('🐭 Mouse up in wrapper')
    emit('mouseup')
  }

  // 获取逻辑层组件的引用
  const btnBaseRef = ref()

  // 创建 BEM 类名生成器
  const bem = bemHelper('btn')

  // 计算样式层包装器类名
  const wrapperClasses = computed(() => [
    bem.b(),
    bem.m(props.variant),
    props.size === 'medium' ? '' : bem.m(props.size),
    {
      [bem.m('disabled')]: props.disabled,
      [bem.m('hovered')]: props.isHovered || btnBaseRef.value?.isHovered,
      [bem.m('pressed')]: props.isPressed || btnBaseRef.value?.isPressed,
    },
  ])

  // 计算样式层包装器样式
  const wrapperStyles = computed(() => {
    const styles: Record<string, string> = {}
    const isHovered = props.isHovered || btnBaseRef.value?.isHovered
    const isPressed = props.isPressed || btnBaseRef.value?.isPressed

    // 根据状态添加样式变量
    if (isHovered && !props.disabled) {
      styles['--btn-state'] = 'hovered'
    }
    if (isPressed && !props.disabled) {
      styles['--btn-state'] = 'pressed'
    }

    return styles
  })

  // 传递给核心组件的 props
  const coreProps = computed(() => {
    const { variant, size, isHovered, isPressed, ...rest } = props
    return {
      ...rest,
      disabled: props.disabled,
    }
  })
</script>
