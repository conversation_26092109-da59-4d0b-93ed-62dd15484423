<template>
  <div
    class="sp-btn-wrapper"
    :class="wrapperClasses"
    :style="wrapperStyles"
    v-on="wrapperEventHandlers"
  >
    <BtnBase
      ref="btnBaseRef"
      v-bind="coreProps"
      v-on="coreEventHandlers"
    >
      <slot />
    </BtnBase>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, useAttrs } from 'vue'
  import { bemHelper } from '@speed-ui/config'
  import BtnBase from './btn-base.vue'
  import { provideCommonEvents, useCommonEvents, filterEvents } from '../../composables/useCommonEvents'
  import type { BtnStyleProps, BtnEmits } from './types'

  // 禁用属性继承，手动控制
  defineOptions({
    inheritAttrs: false
  })

  // 样式层组件，负责样式计算和类名生成
  const props = withDefaults(defineProps<BtnStyleProps>(), {
    variant: 'default',
    disabled: false,
    size: 'medium',
    isHovered: false,
    isPressed: false,
  })

  const emit = defineEmits<BtnEmits>()
  const attrs = useAttrs()

  // 提供通用事件给子组件
  const providedEvents = provideCommonEvents(attrs)

  // 样式层特定事件处理（处理样式相关的交互）
  const wrapperSpecificHandlers = {
    mouseenter: () => {
      // 样式层处理 hover 效果
      console.log('Button wrapper mouseenter - 处理样式层 hover')
    },
    mouseleave: () => {
      // 样式层处理 hover 效果
      console.log('Button wrapper mouseleave - 处理样式层 hover')
    }
  }

  // 合并样式层事件处理器
  const wrapperEventHandlers = useCommonEvents(wrapperSpecificHandlers)

  // 传递给核心组件的事件处理器（过滤掉已在wrapper处理的事件）
  const coreEventHandlers = filterEvents(providedEvents, ['mouseenter', 'mouseleave'])

  // 获取逻辑层组件的引用
  const btnBaseRef = ref()

  // 创建 BEM 类名生成器
  const bem = bemHelper('btn')

  // 计算样式层包装器类名
  const wrapperClasses = computed(() => [
    bem.b(),
    bem.m(props.variant),
    props.size === 'medium' ? '' : bem.m(props.size),
    {
      [bem.m('disabled')]: props.disabled,
      [bem.m('hovered')]: props.isHovered || btnBaseRef.value?.isHovered,
      [bem.m('pressed')]: props.isPressed || btnBaseRef.value?.isPressed,
    },
  ])

  // 计算样式层包装器样式
  const wrapperStyles = computed(() => {
    const styles: Record<string, string> = {}
    const isHovered = props.isHovered || btnBaseRef.value?.isHovered
    const isPressed = props.isPressed || btnBaseRef.value?.isPressed

    // 根据状态添加样式变量
    if (isHovered && !props.disabled) {
      styles['--btn-state'] = 'hovered'
    }
    if (isPressed && !props.disabled) {
      styles['--btn-state'] = 'pressed'
    }

    return styles
  })

  // 传递给核心组件的 props
  const coreProps = computed(() => {
    const { variant, size, isHovered, isPressed, ...rest } = props
    return {
      ...rest,
      disabled: props.disabled,
    }
  })
</script>
