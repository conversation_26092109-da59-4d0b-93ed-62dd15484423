<template>
  <BtnBase
    ref="btnBaseRef"
    :class="buttonClasses"
    :style="buttonStyles"
    :disabled="disabled"
    @click="(event) => emit('click', event)"
    @mouseenter="() => emit('mouseenter')"
    @mouseleave="() => emit('mouseleave')"
    @mousedown="() => emit('mousedown')"
    @mouseup="() => emit('mouseup')"
  >
    <slot />
  </BtnBase>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue'
  import { bemHelper } from '@speed-ui/config'
  import BtnBase from './btn-base.vue'
  import type { BtnStyleProps, BtnEmits } from './types'

  // 样式层组件，负责样式计算和类名生成
  const props = withDefaults(defineProps<BtnStyleProps>(), {
    variant: 'default',
    disabled: false,
    size: 'medium',
    isHovered: false,
    isPressed: false,
  })

  const emit = defineEmits<BtnEmits>()

  // 获取逻辑层组件的引用
  const btnBaseRef = ref()

  // 事件直接转发给父组件

  // 创建 BEM 类名生成器
  const bem = bemHelper('btn')

  // 计算类名
  const buttonClasses = computed(() => [
    bem.b(),
    bem.m(props.variant),
    props.size === 'medium' ? '' : bem.m(props.size),
    {
      [bem.m('disabled')]: props.disabled,
      [bem.m('hovered')]: props.isHovered || btnBaseRef.value?.isHovered,
      [bem.m('pressed')]: props.isPressed || btnBaseRef.value?.isPressed,
    },
  ])

  // 计算样式
  const buttonStyles = computed(() => {
    const styles: Record<string, string> = {}
    const isHovered = props.isHovered || btnBaseRef.value?.isHovered
    const isPressed = props.isPressed || btnBaseRef.value?.isPressed

    // 根据状态添加样式变量
    if (isHovered && !props.disabled) {
      styles['--btn-state'] = 'hovered'
    }
    if (isPressed && !props.disabled) {
      styles['--btn-state'] = 'pressed'
    }

    return styles
  })
</script>
