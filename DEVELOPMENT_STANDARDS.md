# Speed UI 组件开发规范

## 🎯 核心原则

1. **3 层架构标准**: Component.vue → ComponentLogic.vue → ComponentInner.vue
2. **BEM 类名规范**: 必须使用 `@speed-ui/config` 的 `bemHelper`
3. **TypeScript 支持**: 完整的类型定义和模块声明
4. **统一工具使用**: 保持与现有组件(如 Input)的一致性

## 🏗️ 架构规范

### 文件命名规范

```
packages/ui/src/components/[component]/
├── index.ts                 # 导出文件
├── [Component].vue         # 用户接口层 (首字母大写)
├── [Component]Logic.vue    # 逻辑处理层
├── [Component]Inner.vue    # DOM渲染层
├── types/
│   └── index.ts           # TypeScript类型定义
└── constants.ts           # 常量定义(可选)
```

### 导入顺序规范

```typescript
// 1. Vue 相关
import { ref, computed, provide, inject } from 'vue'

// 2. 项目工具包 (必须使用)
import { bemHelper } from '@speed-ui/config'

// 3. 子组件
import ComponentInner from './ComponentInner.vue'
import Icon from '../icon/Icon.vue'

// 4. 类型导入
import type { ComponentProps, ComponentContext } from './types'
```

## 🎨 BEM Helper 使用规范

### ❌ 错误用法 (禁止)

```typescript
// 不要手写类名字符串
const menuClasses = computed(() => [
  `sp-menu--${props.mode}`, // ❌ 错误
  `sp-menu--${props.theme}`, // ❌ 错误
  { 'sp-menu--disabled': props.disabled }, // ❌ 错误
])
```

### ✅ 正确用法 (必须)

```typescript
// 必须使用 bemHelper
import { bemHelper } from '@speed-ui/config'

const bem = bemHelper('menu')

const menuClasses = computed(() => [
  bem.b(), // sp-menu
  bem.m(props.mode), // sp-menu--vertical
  bem.m(props.theme), // sp-menu--light
  {
    [bem.m('disabled')]: props.disabled, // sp-menu--disabled
  },
])
```

### BEM Helper API

```typescript
const bem = bemHelper('component')

// 基础类名
bem.b() // sp-component

// 元素类名
bem.e('element') // sp-component__element

// 修饰符类名
bem.m('modifier') // sp-component--modifier

// 元素修饰符
bem.em('element', 'modifier') // sp-component__element--modifier

// 状态类名
bem.is('active') // is-active
```

## 📝 组件开发模板

### 1. 用户接口层 (Component.vue)

```vue
<template>
  <ComponentLogic
    v-bind="$attrs"
    :prop1="prop1"
    :prop2="prop2"
    @event1="$emit('event1', $event)"
    @event2="$emit('event2', $event)"
  >
    <slot />
  </ComponentLogic>
</template>

<script setup lang="ts">
  import type { ComponentProps } from './types'
  import ComponentLogic from './ComponentLogic.vue'

  // 用户接口层：定义对外API和属性默认值
  const props = withDefaults(defineProps<ComponentProps>(), {
    size: 'medium',
    disabled: false,
    // ...其他默认值
  })

  // 定义事件
  const emit = defineEmits<{
    event1: [value: any]
    event2: [info: any]
  }>()

  // 解构props便于传递
  const { prop1, prop2, prop3 } = props
</script>
```

### 2. 逻辑处理层 (ComponentLogic.vue)

```vue
<template>
  <ComponentInner
    :computed-classes="computedClasses"
    :computed-props="computedProps"
    :context="componentContext"
  >
    <slot />
  </ComponentInner>
</template>

<script setup lang="ts">
  import { computed, provide } from 'vue'
  import { bemHelper } from '@speed-ui/config'
  import type { ComponentProps, ComponentContext } from './types'
  import ComponentInner from './ComponentInner.vue'

  const props = defineProps<ComponentProps>()
  const emit = defineEmits<{...}>()

  // BEM helper (必须)
  const bem = bemHelper('component')

  // 业务逻辑处理
  const handleBusinessLogic = () => {
    // 核心业务逻辑
  }

  // 计算样式类名
  const computedClasses = computed(() => [
    bem.b(),
    bem.m(props.size),
    {
      [bem.m('disabled')]: props.disabled
    }
  ])

  // 上下文数据
  const componentContext: ComponentContext = {
    props,
    handleBusinessLogic,
    // ...其他数据
  }

  // 提供给子组件
  provide('componentContext', componentContext)
</script>
```

### 3. DOM 渲染层 (ComponentInner.vue)

```vue
<template>
  <div
    :class="computedClasses"
    role="component"
    :aria-disabled="context.props.disabled"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
  import type { ComponentContext } from './types'

  interface ComponentInnerProps {
    computedClasses: (string | Record<string, boolean>)[]
    computedProps: Record<string, any>
    context: ComponentContext
  }

  defineProps<ComponentInnerProps>()
</script>
```

## 🔧 TypeScript 规范

### 类型定义文件 (types/index.ts)

```typescript
import type { ComputedRef } from 'vue'

// 组件属性接口
export interface ComponentProps {
  size?: 'small' | 'medium' | 'large'
  disabled?: boolean
  // ...其他属性
}

// 组件上下文接口
export interface ComponentContext {
  props: ComponentProps
  handleMethod: () => void
  // ...其他方法和数据
}

// 事件信息接口
export interface ComponentEventInfo {
  type: string
  data: any
}
```

### 样式类名类型

```typescript
// 正确的类名数组类型
type ClassNames = (string | Record<string, boolean>)[]

interface InnerProps {
  computedClasses: ClassNames // ✅ 正确
  // menuClasses: string[]     // ❌ 错误，不支持对象形式
}
```

## 🎨 样式规范

### SCSS 文件结构

```scss
// packages/theme-default/src/component.scss
@use 'common/var.scss' as *; // 使用 @use 而不是 @import

.sp-component {
  // 基础样式
  display: block;

  // 修饰符样式
  &--small {
    font-size: var(--sp-font-size-sm);
  }
  &--medium {
    font-size: var(--sp-font-size-md);
  }
  &--large {
    font-size: var(--sp-font-size-lg);
  }

  // 状态样式
  &--disabled {
    opacity: var(--sp-opacity-disabled);
  }

  // 元素样式
  &__element {
    // 元素样式
  }
}
```

## 🚫 常见错误和修复

### 1. TypeScript 模块找不到

**错误**: `找不到模块"./ComponentInner.vue"或其相应的类型声明`

**原因**: TypeScript 配置问题或文件路径错误

**修复**:

- 确保文件存在且路径正确
- 检查 tsconfig.json 配置
- 重启 TypeScript 服务

### 2. BEM 类名手写

**错误**: 手写类名字符串

```typescript
// ❌ 错误
const classes = [`sp-menu--${props.mode}`]
```

**修复**: 使用 bemHelper

```typescript
// ✅ 正确
const bem = bemHelper('menu')
const classes = [bem.m(props.mode)]
```

### 3. 类名类型错误

**错误**: 类名数组类型定义不正确

```typescript
// ❌ 错误
menuClasses: string[]
```

**修复**: 支持对象形式的类名

```typescript
// ✅ 正确
menuClasses: (string | Record<string, boolean>)[]
```

## 📋 开发检查清单

### 代码质量检查

- [ ] 使用 `bemHelper` 生成所有 CSS 类名
- [ ] 导入 `@speed-ui/config` 工具包
- [ ] TypeScript 类型定义完整
- [ ] 3 层架构职责分离清晰
- [ ] 事件处理正确
- [ ] 插槽支持完整

### 测试验证

- [ ] 组件功能正常
- [ ] TypeScript 编译无错误
- [ ] 样式显示正确
- [ ] 事件触发正常
- [ ] 可访问性支持

### 文档要求

- [ ] 组件 API 文档
- [ ] 使用示例
- [ ] 类型声明
- [ ] 样式变量说明

## 📚 参考示例

最佳实践参考：

- **Input 组件**: `packages/ui/src/components/input/InputAffix.vue`
- **Menu 组件**: `packages/ui/src/components/menu/` (重构后)

按照这些规范开发，确保组件库的一致性和可维护性！
