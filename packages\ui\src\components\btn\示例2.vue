<!-- src/components/Button/ButtonCore.vue -->
<template>
  <button
    class="button-core"
    :disabled="disabled || loading"
    v-on="mergedEventHandlers"
  >
    <slot />
    <span v-if="loading" class="loading-indicator" />
    <span v-if="ripple" class="ripple-effect" />
  </button>
</template>

<script setup>
import { useCommonEvents } from '../../composables/useCommonEvents';

const props = defineProps({
  disabled: <PERSON><PERSON><PERSON>,
  loading: <PERSON>olean,
  ripple: Boolean
});

// 核心组件特定事件处理
const coreSpecificHandlers = {
  click: (e) => {
    console.log('Button core clicked');
    if (props.ripple) createRippleEffect(e.currentTarget);
  },
  focus: () => console.log('Button core focused')
};

// 合并事件处理器
const mergedEventHandlers = useCommonEvents(coreSpecificHandlers);
</script>