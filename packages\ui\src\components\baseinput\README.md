# BaseInput 组件

BaseInput 是一个基础的输入框组件，提供对原生 HTML input 元素的轻量级封装。它专注于输入逻辑的处理，不包含样式和布局，适合作为其他复杂输入组件的基础。

## 特性

- 🎯 **纯粹的输入逻辑** - 只处理输入相关的核心功能，不包含样式
- 🔧 **全类型支持** - 支持所有原生 HTML input 类型
- 📝 **TypeScript 友好** - 完整的类型定义和类型安全
- 🎪 **事件完整** - 支持所有原生 input 事件
- 🔄 **双向绑定** - 支持 v-model
- 🎛️ **实例方法** - 提供 focus、blur、select、clear 等方法

## 基本用法

```vue
<template>
  <!-- 基本文本输入 -->
  <BaseInput v-model:value="text" placeholder="请输入文本" />
  
  <!-- 数字输入 -->
  <BaseInput 
    v-model:value="number" 
    type="number" 
    :min="0" 
    :max="100" 
    :step="1" 
  />
  
  <!-- 滑块输入 -->
  <BaseInput 
    v-model:value="range" 
    type="range" 
    :min="0" 
    :max="100" 
    :step="5" 
  />
  
  <!-- 密码输入 -->
  <BaseInput 
    v-model:value="password" 
    type="password" 
    placeholder="请输入密码" 
  />
</template>

<script setup>
import { ref } from 'vue'
import BaseInput from '@/components/baseinput'

const text = ref('')
const number = ref(0)
const range = ref(50)
const password = ref('')
</script>
```

## 支持的输入类型

BaseInput 支持所有原生 HTML input 类型：

| 类型 | 说明 | 示例 |
|------|------|------|
| `text` | 文本输入（默认） | 普通文本 |
| `password` | 密码输入 | 密码字段 |
| `email` | 邮箱输入 | 邮箱地址 |
| `number` | 数字输入 | 数值 |
| `tel` | 电话输入 | 电话号码 |
| `url` | URL 输入 | 网址 |
| `search` | 搜索输入 | 搜索框 |
| `range` | 滑块输入 | 范围选择器 |
| `date` | 日期输入 | 日期选择 |
| `time` | 时间输入 | 时间选择 |
| `datetime-local` | 日期时间输入 | 本地日期时间 |
| `month` | 月份输入 | 月份选择 |
| `week` | 周输入 | 周选择 |
| `color` | 颜色输入 | 颜色选择器 |
| `file` | 文件输入 | 文件上传 |

## Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `value` | `string \| number` | `undefined` | 输入值，支持 v-model |
| `type` | `BaseInputType` | `'text'` | 输入类型 |
| `placeholder` | `string` | `undefined` | 占位符文本 |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `readonly` | `boolean` | `false` | 是否只读 |
| `maxlength` | `number` | `undefined` | 最大输入长度 |
| `minlength` | `number` | `undefined` | 最小输入长度 |
| `min` | `number` | `undefined` | 最小值（数字/范围类型） |
| `max` | `number` | `undefined` | 最大值（数字/范围类型） |
| `step` | `number` | `undefined` | 步长（数字/范围类型） |
| `autocomplete` | `string` | `undefined` | 自动完成 |
| `autofocus` | `boolean` | `false` | 自动聚焦 |
| `inputmode` | `string` | `undefined` | 输入模式 |
| `name` | `string` | `undefined` | 表单名称 |
| `id` | `string` | `undefined` | 元素 ID |
| `required` | `boolean` | `false` | 是否必填 |
| `class` | `string \| string[] \| object` | `undefined` | 自定义类名 |
| `style` | `string \| object` | `undefined` | 自定义样式 |
| `accept` | `string` | `undefined` | 接受的文件类型（file 类型） |
| `multiple` | `boolean` | `false` | 是否允许多选（file 类型） |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:value` | `(value: string \| number)` | 值更新事件，用于 v-model |
| `input` | `(event: Event)` | 输入事件 |
| `change` | `(event: Event)` | 值改变事件 |
| `focus` | `(event: FocusEvent)` | 获得焦点事件 |
| `blur` | `(event: FocusEvent)` | 失去焦点事件 |
| `keydown` | `(event: KeyboardEvent)` | 键盘按下事件 |
| `keyup` | `(event: KeyboardEvent)` | 键盘抬起事件 |
| `keypress` | `(event: KeyboardEvent)` | 键盘按键事件 |
| `click` | `(event: MouseEvent)` | 点击事件 |

## Methods

通过 ref 可以访问以下实例方法：

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `focus` | - | `void` | 聚焦到输入框 |
| `blur` | - | `void` | 失去焦点 |
| `select` | - | `void` | 选中输入框内容 |
| `clear` | - | `void` | 清空输入框 |

### 使用示例

```vue
<template>
  <BaseInput ref="inputRef" v-model:value="text" />
  <button @click="focusInput">聚焦</button>
  <button @click="clearInput">清空</button>
</template>

<script setup>
import { ref } from 'vue'
import BaseInput from '@/components/baseinput'

const inputRef = ref()
const text = ref('')

const focusInput = () => {
  inputRef.value?.focus()
}

const clearInput = () => {
  inputRef.value?.clear()
}
</script>
```

## 高级用法

### 数字输入处理

```vue
<template>
  <BaseInput 
    v-model:value="price" 
    type="number" 
    :min="0" 
    :step="0.01" 
    placeholder="请输入价格"
    @input="handlePriceInput"
  />
</template>

<script setup>
import { ref } from 'vue'

const price = ref(0)

const handlePriceInput = (event) => {
  console.log('价格输入:', event.target.value)
}
</script>
```

### 滑块输入处理

```vue
<template>
  <BaseInput 
    v-model:value="volume" 
    type="range" 
    :min="0" 
    :max="100" 
    :step="1" 
    @change="handleVolumeChange"
  />
  <span>音量: {{ volume }}%</span>
</template>

<script setup>
import { ref } from 'vue'

const volume = ref(50)

const handleVolumeChange = (event) => {
  console.log('音量改变:', event.target.value)
}
</script>
```

### 文件上传处理

```vue
<template>
  <BaseInput 
    type="file" 
    accept="image/*" 
    multiple 
    @change="handleFileChange"
  />
</template>

<script setup>
const handleFileChange = (event) => {
  const files = event.target.files
  console.log('选择的文件:', files)
}
</script>
```

## 设计理念

BaseInput 组件遵循以下设计原则：

1. **单一职责** - 只处理输入逻辑，不包含样式和布局
2. **最小化封装** - 尽可能保持原生 input 的行为和特性
3. **类型安全** - 提供完整的 TypeScript 类型支持
4. **可组合性** - 作为其他复杂组件的基础构建块
5. **事件透明** - 完整传递所有原生事件

## 与其他组件的关系

BaseInput 是其他输入组件的基础：

- **InputField** - 可以基于 BaseInput 构建，添加标签、验证等功能
- **Slider** - 可以使用 BaseInput 的 `type="range"` 作为核心
- **NumberInput** - 可以基于 BaseInput 的 `type="number"` 扩展
- **SearchInput** - 可以基于 BaseInput 的 `type="search"` 扩展

这种设计使得组件体系更加模块化和可维护。
