# 简化 3 层架构开发计划 (方案 C)

## 架构设计原则

### 3 层架构模式

```
Component.vue (用户接口层)
├── ComponentLogic.vue (逻辑处理层)
└── ComponentInner.vue (DOM渲染层)
```

### 各层职责划分

- **Component.vue**: 用户 API、属性定义、表单集成、外部事件
- **ComponentLogic.vue**: 状态管理、业务逻辑、数据转换、内部事件处理
- **ComponentInner.vue**: 纯 DOM 渲染、样式应用、原生事件绑定

### 架构优势

- ✅ 保持代码分离和可维护性
- ✅ 比 5 层架构简单，学习成本低
- ✅ 仍然保持良好的扩展性
- ✅ 开发效率高，风险可控

## 开发时间规划

### 总体时间安排: 16 天

#### 第一阶段: 现有组件架构调整 (4 天)

**目标**: 将 Menu 和 List 组件重构为 3 层架构

**Day 1-2: Menu 组件重构**

- [x] 分析现有 Menu.vue 功能
- [x] 创建 MenuLogic.vue (状态管理、主题切换逻辑)
- [x] 创建 MenuInner.vue (DOM 渲染、样式应用)
- [x] 重构 Menu.vue 为用户接口层
- [x] 测试功能完整性

**Day 3-4: List 组件重构**

- [x] 分析现有 List.vue 功能
- [ ] 创建 ListLogic.vue (选择逻辑、状态管理)
- [ ] 创建 ListInner.vue (列表渲染、事件绑定)
- [ ] 重构 List.vue 为用户接口层
- [ ] 测试功能完整性

#### 第二阶段: 新组件开发 (12 天)

**Day 5-6: Dropdown 组件**

- [ ] Dropdown.vue - 用户 API 层
- [ ] DropdownLogic.vue - 显示/隐藏逻辑、定位计算
- [ ] DropdownInner.vue - 弹出层渲染、动画效果
- [ ] 支持触发方式: click, hover, contextmenu
- [ ] 支持定位: top, bottom, left, right

**Day 7-8: VirtualScroll 组件**

- [ ] VirtualScroll.vue - 用户 API 层
- [ ] VirtualScrollLogic.vue - 滚动计算、可见区域管理
- [ ] VirtualScrollInner.vue - 虚拟列表渲染
- [ ] 支持大数据量渲染
- [ ] 支持动态高度

**Day 9-10: Select 组件**

- [ ] Select.vue - 用户 API 层
- [ ] SelectLogic.vue - 选项管理、搜索过滤
- [ ] SelectInner.vue - 下拉框渲染、选项显示
- [ ] 支持单选/多选
- [ ] 支持搜索功能

**Day 11-12: Navigation 组件**

- [ ] Navigation.vue - 用户 API 层
- [ ] NavigationLogic.vue - 路由管理、激活状态
- [ ] NavigationInner.vue - 导航项渲染、层级显示
- [ ] 支持多级导航
- [ ] 支持折叠展开

**Day 13-14: Transfer 组件**

- [ ] Transfer.vue - 用户 API 层
- [ ] TransferLogic.vue - 数据转移逻辑、搜索过滤
- [ ] TransferInner.vue - 穿梭框渲染、按钮操作
- [ ] 支持搜索功能
- [ ] 支持自定义渲染

**Day 15-16: ContextMenu 组件**

- [ ] ContextMenu.vue - 用户 API 层
- [ ] ContextMenuLogic.vue - 右键菜单逻辑、定位计算
- [ ] ContextMenuInner.vue - 菜单渲染、层级显示
- [ ] 支持右键触发
- [ ] 支持多级菜单

## 文件结构设计

### 每个组件的标准结构

```
packages/ui/src/components/[component]/
├── index.ts                 # 导出文件
├── [Component].vue         # 用户接口层
├── [Component]Logic.vue    # 逻辑处理层
├── [Component]Inner.vue    # DOM渲染层
├── types.ts                # TypeScript类型定义
└── constants.ts            # 常量定义
```

### 样式文件结构

```
packages/theme-default/src/
├── [component].scss        # 组件样式
└── index.scss             # 导入所有样式
```

## 技术规范

### 🚨 重要：必须遵循的开发规范

详细规范请查看: **[DEVELOPMENT_STANDARDS.md](./DEVELOPMENT_STANDARDS.md)**

### BEM Helper 使用 (必须)

```typescript
// ✅ 正确 - 必须使用 bemHelper
import { bemHelper } from '@speed-ui/config'

const bem = bemHelper('component')
const classes = computed(() => [
  bem.b(), // sp-component
  bem.m(props.size), // sp-component--small
  { [bem.m('disabled')]: props.disabled },
])

// ❌ 错误 - 禁止手写类名
const classes = [`sp-component--${props.size}`]
```

### 导入顺序标准

```typescript
// 1. Vue 相关
import { computed, ref, provide } from 'vue'

// 2. 项目工具包 (必须)
import { bemHelper } from '@speed-ui/config'

// 3. 子组件
import ComponentInner from './ComponentInner.vue'

// 4. 类型导入
import type { ComponentProps } from './types'
```

### TypeScript 类型规范

```typescript
// 类名数组类型 (支持对象形式)
type ClassNames = (string | Record<string, boolean>)[]

interface ComponentInnerProps {
  computedClasses: ClassNames // ✅ 正确
  // menuClasses: string[]     // ❌ 错误
}
```

### 样式规范

```scss
// 使用 @use 而不是 @import
@use 'common/var.scss' as *;

.sp-component {
  color: var(--sp-color-text);
  background: var(--sp-color-bg);

  &--small {
    font-size: var(--sp-font-size-sm);
  }
  &--disabled {
    opacity: var(--sp-opacity-disabled);
  }
}
```

## 质量保证

### 代码检查清单

- [ ] TypeScript 类型完整性
- [ ] Props 和 Emits 定义清晰
- [ ] 组件可访问性(a11y)支持
- [ ] 响应式设计适配
- [ ] 主题切换功能正常
- [ ] 事件处理正确
- [ ] 错误边界处理

### 测试验证

- [ ] 功能测试通过
- [ ] 在不同主题下显示正常
- [ ] 响应式布局正确
- [ ] TypeScript 编译无错误
- [ ] 没有控制台警告/错误

## 风险控制

### 技术风险

- **风险**: 重构可能破坏现有功能
- **应对**: 分阶段重构，每阶段充分测试

### 时间风险

- **风险**: 开发时间可能超出预期
- **应对**: 采用 MVP 方式，先实现核心功能

### 质量风险

- **风险**: 新架构可能引入复杂性
- **应对**: 制定明确的架构规范和代码标准

## 里程碑检查点

### 第一阶段完成 (Day 4)

- [x] Menu 和 List 组件架构重构完成
- [ ] 现有功能保持不变
- [ ] Demo 页面正常运行

### 第二阶段中期 (Day 10)

- [ ] 4 个新组件开发完成
- [ ] 基础功能测试通过
- [ ] 组件库结构清晰

### 项目完成 (Day 16)

- [ ] 8 个组件全部完成
- [ ] 文档和示例齐全
- [ ] 代码质量达标
- [ ] 可以发布使用

## 总结

方案 C 提供了在开发效率和架构清晰度之间的最佳平衡点:

**优势:**

- 开发时间合理 (16 天)
- 架构清晰且易于理解
- 风险可控，易于实施
- 保持代码分离原则

**交付成果:**

- 8 个高质量 Vue 组件
- 完整的 TypeScript 类型支持
- 主题切换功能
- 响应式设计
- 详细的文档和示例

这个计划确保我们能够在合理的时间内交付一个结构清晰、易于维护的组件库。
