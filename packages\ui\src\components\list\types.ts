import type { ComputedRef } from 'vue'

export type ListSize = 'small' | 'medium' | 'large'
export type ListVariant = 'default' | 'simple'

export interface ListProps {
  /** 列表尺寸 */
  size?: ListSize
  /** 样式变体 */
  variant?: ListVariant
  /** 是否显示边框 */
  bordered?: boolean
  /** 是否显示分割线 */
  split?: boolean
  /** 是否支持hover效果 */
  hoverable?: boolean
  /** 是否可选择 */
  selectable?: boolean
  /** 是否支持多选 */
  multiple?: boolean
  /** 当前选中的keys */
  selectedKeys?: string[]
  /** 最大高度，超出时滚动 */
  maxHeight?: number | string
  /** 是否加载中 */
  loading?: boolean
  /** 无障碍标签 */
  ariaLabel?: string
}

export interface ListItemProps {
  /** 唯一标识 */
  itemKey: string
  /** 标题 */
  title?: string
  /** 描述 */
  description?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否可选择（覆盖List的设置） */
  selectable?: boolean | undefined
  /** 额外的CSS类名 */
  className?: string
  /** 额外的样式 */
  style?: Record<string, any>
}

export interface ListContext {
  props: ListProps
  selectedKeys: ComputedRef<string[]>
  onItemSelect: (key: string, selected: boolean) => void
  onItemClick: (key: string, event: MouseEvent) => void
}

export interface ListEvents {
  'update:selectedKeys': [keys: string[]]
  select: [key: string, selected: boolean, selectedKeys: string[]]
  click: [key: string, event: MouseEvent]
}

export interface ListItemEvents {
  click: [key: string, event: MouseEvent]
  select: [key: string, selected: boolean]
}
