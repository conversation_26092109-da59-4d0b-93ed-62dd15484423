{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "declaration": true, "declarationMap": true, "emitDeclarationOnly": true, "outDir": "dist"}, "include": ["src/**/*.ts", "src/**/*.vue"], "exclude": ["node_modules", "dist", "**/*.spec.ts", "**/*.test.ts"]}