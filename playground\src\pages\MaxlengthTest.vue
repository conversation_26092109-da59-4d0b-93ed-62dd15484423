<template>
  <div class="maxlength-test">
    <h1>🧪 Maxlength 属性测试</h1>
    <p>测试 sp-input 的 maxlength 属性是否正常工作</p>

    <div class="demo-section">
      <h2>🎯 Select 组件测试</h2>

      <div class="demo-item">
        <label>基础单选 Select:</label>
        <sp-select
          v-model:value="selectValue1"
          :options="basicOptions"
          placeholder="请选择一个选项"
          clearable
        />
        <p>当前值: "{{ selectValue1 }}"</p>
      </div>

      <div class="demo-item">
        <label>多选 Select:</label>
        <sp-select
          v-model:value="selectValue2"
          :options="basicOptions"
          multiple
          placeholder="请选择多个选项"
          clearable
        />
        <p>当前值: {{ selectValue2 }}</p>
      </div>

      <div class="demo-item">
        <label>可搜索 Select:</label>
        <sp-select
          v-model:value="selectValue3"
          :options="basicOptions"
          filterable
          placeholder="搜索并选择"
          clearable
        />
        <p>当前值: "{{ selectValue3 }}"</p>
      </div>

      <div class="demo-item">
        <label>多选 + 搜索:</label>
        <sp-select
          v-model:value="selectValue4"
          :options="basicOptions"
          multiple
          filterable
          placeholder="搜索并多选"
          clearable
        />
        <p>当前值: {{ selectValue4 }}</p>
      </div>

      <div class="demo-item">
        <label>不同尺寸:</label>
        <div
          style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap"
        >
          <sp-select
            v-model:value="selectValue5"
            :options="basicOptions"
            size="small"
            placeholder="小尺寸"
            style="width: 150px"
          />
          <sp-select
            v-model:value="selectValue6"
            :options="basicOptions"
            size="medium"
            placeholder="中等尺寸"
            style="width: 150px"
          />
          <sp-select
            v-model:value="selectValue7"
            :options="basicOptions"
            size="large"
            placeholder="大尺寸"
            style="width: 150px"
          />
        </div>
        <p>
          小: "{{ selectValue5 }}", 中: "{{ selectValue6 }}", 大: "{{
            selectValue7
          }}"
        </p>
      </div>

      <div class="demo-item">
        <label>禁用状态:</label>
        <sp-select
          v-model:value="selectValue8"
          :options="basicOptions"
          disabled
          placeholder="禁用的选择框"
        />
        <p>当前值: "{{ selectValue8 }}"</p>
      </div>

      <div class="demo-item">
        <label>自定义选项显示:</label>
        <sp-select
          v-model:value="selectValue9"
          :options="customOptions"
          label-key="name"
          value-key="id"
          placeholder="选择用户"
          clearable
        />
        <p>当前值: "{{ selectValue9 }}"</p>
      </div>

      <div class="demo-item">
        <label>限制多选标签数量:</label>
        <sp-select
          v-model:value="selectValue10"
          :options="basicOptions"
          multiple
          :max-tag-count="2"
          placeholder="最多显示2个标签"
          clearable
        />
        <p>当前值: {{ selectValue10 }}</p>
      </div>
    </div>

    <div class="demo-section">
      <h2>基础 maxlength 测试</h2>

      <div class="demo-item">
        <label>maxlength="10" 的输入框:</label>
        <sp-input
          v-model:value="value1"
          :maxlength="10"
          :show-count-in="true"
          placeholder="最多输入10个字符"
        />
        <p>当前值: "{{ value1 }}" (长度: {{ value1.length }})</p>
      </div>

      <div class="demo-item">
        <label>maxlength="5" 的输入框:</label>
        <sp-input
          v-model:value="value2"
          :maxlength="5"
          placeholder="最多输入5个字符"
        />
        <p>当前值: "{{ value2 }}" (长度: {{ value2.length }})</p>
      </div>

      <div class="demo-item">
        <label>没有 maxlength 的输入框:</label>
        <sp-input
          v-model:value="value3"
          placeholder="无字符限制"
        />
        <p>当前值: "{{ value3 }}" (长度: {{ value3.length }})</p>
      </div>
    </div>

    <div class="demo-section">
      <h2>字符计数显示方式测试</h2>

      <div class="demo-item">
        <label>showCountOut - 外部显示字符计数:2222</label>
        <!-- 带问题提示的输入框 -->
        <sp-input
          prefixIcon="Copy"
          suffix="可以点击帮助"
          help="这是一个帮助提示"
        />
        <sp-input question="这是一个帮助提示" />
        <sp-input
          v-model:value="value4"
          :maxlength="200"
          suffix-icon="Search"
          prefixIcon="Copy"
          show-count-in
          disabled
          clearable
          :show-count-out="true"
          placeholder="外部显示计数"
        />
        <p>当前值: "{{ value4 }}" (长度: {{ value4.length }})</p>
      </div>

      <div class="demo-item">
        <label>showCountIn - 内部显示字符计数:</label>
        <sp-input-search
          v-model:value="value9"
          size="large"
          suffix-icon="Search"
          clearable
          show-count-in
          :maxlength="120"
          placeholder="内部显示计数2"
        />
        <p>当前值: "{{ value9 }}" (长度: {{ value9.length }})</p>
        <p style="color: blue">调试: 应该在输入框内部显示字符计数</p>
      </div>

      <div class="demo-item">
        <label>showWordLimit - 兼容旧属性 (默认外部显示):</label>
        <sp-input
          v-model:value="value10"
          :maxlength="18"
          show-word-limit
          placeholder="兼容旧属性"
        />
        <p>当前值: "{{ value10 }}" (长度: {{ value10.length }})</p>
      </div>

      <div class="demo-item">
        <label>同时启用内部和外部计数:</label>
        <sp-input
          v-model:value="value11"
          :maxlength="12"
          :show-count-in="true"
          :show-count-out="true"
          placeholder="内外都显示计数"
        />
        <p>当前值: "{{ value11 }}" (长度: {{ value11.length }})</p>
      </div>
    </div>

    <div class="demo-section">
      <h2>不同尺寸的 maxlength 测试</h2>

      <div class="demo-item">
        <label>小尺寸 maxlength="8":</label>
        <sp-input
          v-model:value="value5"
          :maxlength="8"
          size="small"
          placeholder="小尺寸，最多8字符"
        />
        <p>当前值: "{{ value5 }}" (长度: {{ value5.length }})</p>
      </div>

      <div class="demo-item">
        <label>大尺寸 maxlength="15":</label>
        <sp-input
          v-model:value="value6"
          :maxlength="15"
          size="large"
          placeholder="大尺寸，最多15字符"
        />
        <p>当前值: "{{ value6 }}" (长度: {{ value6.length }})</p>
      </div>
    </div>

    <div class="demo-section">
      <h2>🔒 密码输入框字符计数测试</h2>

      <div class="demo-item">
        <label>密码框 + showCountIn:</label>
        <sp-input-password
          v-model:value="value7"
          :maxlength="16"
          show-count-in
          placeholder="密码框内部计数"
        />
        <p>当前值: "{{ value7 }}" (长度: {{ value7.length }})</p>
      </div>

      <div class="demo-item">
        <label>密码框 + showCountOut:</label>
        <sp-input-password
          v-model:value="passwordValue2"
          :maxlength="20"
          show-count-out
          placeholder="密码框外部计数"
        />
        <p>
          当前值: "{{ passwordValue2 }}" (长度: {{ passwordValue2.length }})
        </p>
      </div>
    </div>

    <div class="demo-section">
      <h2>📧 邮箱输入框测试</h2>

      <div class="demo-item">
        <label>邮箱输入框 + 验证:</label>
        <sp-input-email
          clearable
          v-model:value="emailValue1"
          :maxlength="50"
          show-count-in
          help="请输入有效的邮箱地址"
          placeholder="输入邮箱地址"
          @validate="handleEmailValidate"
        />
        <p>当前值: "{{ emailValue1 }}" (长度: {{ emailValue1.length }})</p>
        <p
          v-if="emailValidation.message"
          :style="{ color: emailValidation.valid ? 'green' : 'red' }"
        >
          {{ emailValidation.message }}
        </p>
      </div>

      <div class="demo-item">
        <label>邮箱输入框 + 外部计数:</label>
        <sp-input-email
          v-model:value="emailValue2"
          :maxlength="40"
          show-count-out
          placeholder="邮箱地址（外部计数）"
        />
        <p>当前值: "{{ emailValue2 }}" (长度: {{ emailValue2.length }})</p>
      </div>
    </div>

    <div class="demo-section">
      <h2>🔍 搜索输入框字符计数测试</h2>

      <div class="demo-item">
        <label>搜索框 + showCountIn:</label>
        <sp-input-search
          v-model:value="searchValue1"
          :maxlength="30"
          show-count-in
          placeholder="搜索框内部计数"
        />
        <p>当前值: "{{ searchValue1 }}" (长度: {{ searchValue1.length }})</p>
      </div>

      <div class="demo-item">
        <label>搜索框 + showCountOut:</label>
        <sp-input-search
          v-model:value="searchValue2"
          :maxlength="25"
          show-count-out
          placeholder="搜索框外部计数"
        />
        <p>当前值: "{{ searchValue2 }}" (长度: {{ searchValue2.length }})</p>
      </div>
    </div>

    <div class="demo-section">
      <h2>🗑️ 清除功能测试</h2>

      <div class="demo-item">
        <label>基础清除功能 (clearable):</label>
        <sp-input
          v-model:value="clearValue1"
          :maxlength="20"
          clearable
          placeholder="输入内容后会显示清除按钮"
        />
        <p>当前值: "{{ clearValue1 }}" (长度: {{ clearValue1.length }})</p>
      </div>

      <div class="demo-item">
        <label>清除 + 字符计数 (内部):</label>
        <sp-input
          v-model:value="clearValue2"
          :maxlength="15"
          clearable
          show-count-in
          placeholder="清除按钮 + 内部计数"
        />
        <p>当前值: "{{ clearValue2 }}" (长度: {{ clearValue2.length }})</p>
      </div>

      <div class="demo-item">
        <label>清除 + 字符计数 (外部):</label>
        <sp-input
          v-model:value="clearValue3"
          :maxlength="25"
          clearable
          show-count-out
          placeholder="清除按钮 + 外部计数"
        />
        <p>当前值: "{{ clearValue3 }}" (长度: {{ clearValue3.length }})</p>
      </div>

      <div class="demo-item">
        <label>搜索框 + 清除功能:</label>
        <sp-input-search
          v-model:value="clearSearchValue"
          :maxlength="30"
          clearable
          show-count-in
          placeholder="搜索框 + 清除按钮"
          @search="handleSearch"
        />
        <p>
          当前值: "{{ clearSearchValue }}" (长度: {{ clearSearchValue.length }})
        </p>
        <p
          v-if="searchResult"
          style="color: green"
        >
          搜索结果: {{ searchResult }}
        </p>
      </div>

      <div class="demo-item">
        <label>密码框 + 清除功能:</label>
        <sp-input-password
          v-model:value="clearPasswordValue"
          :maxlength="16"
          clearable
          show-count-out
          placeholder="密码框 + 清除按钮"
        />
        <p>
          当前值: "{{ clearPasswordValue }}" (长度:
          {{ clearPasswordValue.length }})
        </p>
      </div>

      <div class="demo-item">
        <label>allowClear 属性 (备用方式):</label>
        <sp-input
          v-model:value="allowClearValue"
          :maxlength="18"
          allow-clear
          show-word-limit
          placeholder="使用 allow-clear 属性"
        />
        <p>
          当前值: "{{ allowClearValue }}" (长度: {{ allowClearValue.length }})
        </p>
      </div>
    </div>

    <div class="demo-section">
      <h2>🔢 数字输入框测试</h2>

      <div class="demo-item">
        <label>基础数字输入框2:</label>
        <sp-input-number
          v-model:value="numberValue1"
          :maxlength="16"
          show-count-in
          help="只能输入数字"
          placeholder="只能输入数字"
          clearable
        />
        <sp-input
          v-model:value="numberValue1"
          placeholder="只能输入数字"
          prefixIcon="Copy"
          suffixIcon="Copy"
          clearable
        />
        <p>当前值: {{ numberValue1 }} (类型: {{ typeof numberValue1 }})</p>
      </div>

      <div class="demo-item">
        <label>仅整数输入 (allowDecimal=false):</label>
        <sp-input-number
          v-model:value="numberValue2"
          :allow-decimal="false"
          placeholder="只能输入整数"
        />
        <p>当前值: {{ numberValue2 }} (类型: {{ typeof numberValue2 }})</p>
      </div>

      <div class="demo-item">
        <label>仅正数输入 (allowNegative=false):</label>
        <sp-input-number
          v-model:value="numberValue3"
          :allow-negative="false"
          placeholder="只能输入正数"
        />
        <p>当前值: {{ numberValue3 }} (类型: {{ typeof numberValue3 }})</p>
      </div>

      <div class="demo-item">
        <label>范围限制 (min=0, max=100):</label>
        <sp-input-number
          v-model:value="numberValue4"
          :min="0"
          :max="100"
          placeholder="0-100之间的数字"
        />
        <p>当前值: {{ numberValue4 }} (类型: {{ typeof numberValue4 }})</p>
      </div>

      <div class="demo-item">
        <label>精度控制 (precision=2):</label>
        <sp-input-number
          v-model:value="numberValue5"
          :precision="2"
          placeholder="保留2位小数"
        />
        <p>当前值: {{ numberValue5 }} (类型: {{ typeof numberValue5 }})</p>
      </div>

      <div class="demo-item">
        <label>禁用状态:</label>
        <sp-input-number
          v-model:value="numberValue6"
          disabled
          placeholder="禁用的数字输入框"
        />
        <p>当前值: {{ numberValue6 }} (类型: {{ typeof numberValue6 }})</p>
      </div>

      <div class="demo-item">
        <label>不同尺寸:</label>
        <div style="display: flex; gap: 10px; align-items: center">
          <sp-input-number
            v-model:value="numberValue7"
            size="small"
            placeholder="小尺寸"
          />
          <sp-input-number
            v-model:value="numberValue8"
            size="medium"
            placeholder="中等尺寸"
          />
          <sp-input-number
            v-model:value="numberValue9"
            size="large"
            placeholder="大尺寸"
          />
        </div>
        <p>
          小: {{ numberValue7 }}, 中: {{ numberValue8 }}, 大: {{ numberValue9 }}
        </p>
      </div>

      <div class="demo-item">
        <label>带清除功能:</label>
        <sp-input-number
          v-model:value="numberValue10"
          clearable
          placeholder="可清除的数字输入框"
        />
        <p>当前值: {{ numberValue10 }} (类型: {{ typeof numberValue10 }})</p>
      </div>
    </div>

    <div class="demo-section">
      <h2>原生 input 对比测试</h2>

      <div class="demo-item">
        <label>原生 input maxlength="10":</label>
        <input
          v-model="value8"
          maxlength="10"
          placeholder="原生input，最多10字符"
          style="padding: 8px; border: 1px solid #ccc; border-radius: 4px"
        />
        <p>当前值: "{{ value8 }}" (长度: {{ value8.length }})</p>
      </div>
    </div>

    <div class="demo-section">
      <h2>测试说明</h2>
      <ul>
        <li>如果 maxlength 正常工作，输入框应该阻止超过限制长度的字符输入</li>
        <li>可以尝试输入超过限制的字符，看是否被阻止</li>
        <li>可以尝试粘贴长文本，看是否被截断</li>
        <li>对比原生 input 的行为，确保一致性</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  // Select 组件测试变量
  const selectValue1 = ref('')
  const selectValue2 = ref([])
  const selectValue3 = ref('')
  const selectValue4 = ref([])
  const selectValue5 = ref('')
  const selectValue6 = ref('')
  const selectValue7 = ref('')
  const selectValue8 = ref('option2') // 预设值用于测试禁用状态
  const selectValue9 = ref('')
  const selectValue10 = ref([])

  // Select 选项数据
  const basicOptions = [
    { label: '选项一', value: 'option1' },
    { label: '选项二', value: 'option2' },
    { label: '选项三', value: 'option3' },
    { label: '选项四', value: 'option4' },
    { label: '选项五', value: 'option5' },
    { label: '选项六', value: 'option6' },
  ]

  const customOptions = [
    { id: 1, name: '张三', role: '开发者' },
    { id: 2, name: '李四', role: '设计师' },
    { id: 3, name: '王五', role: '产品经理' },
    { id: 4, name: '赵六', role: '测试工程师' },
  ]

  const value1 = ref('')
  const value2 = ref('')
  const value3 = ref('')
  const value4 = ref('')
  const value5 = ref('')
  const value6 = ref('')
  const value7 = ref('')
  const value8 = ref('')
  const value9 = ref('')
  const value10 = ref('')
  const value11 = ref('')

  // 新增的测试变量
  const passwordValue2 = ref('')
  const searchValue1 = ref('')
  const searchValue2 = ref('')

  // 清除功能测试变量
  const clearValue1 = ref('')
  const clearValue2 = ref('')
  const clearValue3 = ref('')
  const clearSearchValue = ref('')
  const clearPasswordValue = ref('')
  const allowClearValue = ref('')
  const searchResult = ref('')

  // 数字输入框测试变量
  const numberValue1 = ref<number | string>('')
  const numberValue2 = ref<number | string>('')
  const numberValue3 = ref<number | string>('')
  const numberValue4 = ref<number | string>('')
  const numberValue5 = ref<number | string>('')
  const numberValue6 = ref<number | string>(42)
  const numberValue7 = ref<number | string>('')
  const numberValue8 = ref<number | string>('')
  const numberValue9 = ref<number | string>('')
  const numberValue10 = ref<number | string>('')

  // 邮箱输入框测试变量
  const emailValue1 = ref('')
  const emailValue2 = ref('')
  const emailValidation = ref({ valid: false, message: '' })

  // 邮箱验证事件处理
  const handleEmailValidate = (result: {
    valid: boolean
    message?: string
  }) => {
    emailValidation.value = {
      valid: result.valid,
      message: result.message || '',
    }
  }

  // 搜索事件处理
  const handleSearch = (value: string | number) => {
    searchResult.value = `搜索了: "${value}"`
    setTimeout(() => {
      searchResult.value = ''
    }, 3000)
  }
</script>

<style scoped>
  .maxlength-test {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
  }

  .demo-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background: #fafafa;
  }

  .demo-section h2 {
    margin-top: 0;
    color: #333;
    border-bottom: 2px solid #1890ff;
    padding-bottom: 8px;
  }

  .demo-item {
    margin-bottom: 20px;
  }

  .demo-item label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
  }

  .demo-item p {
    margin-top: 8px;
    padding: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    font-family: monospace;
    font-size: 14px;
  }

  ul {
    background: #fff3cd;
    padding: 15px 20px;
    border-radius: 4px;
    border-left: 4px solid #ffc107;
  }

  ul li {
    margin-bottom: 8px;
  }
</style>
