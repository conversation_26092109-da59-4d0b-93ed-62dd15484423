<template>
  <div class="page-container">
    <h1>Input New 组件演示</h1>
    <p>基于新架构设计的简洁 Input 组件，使用 v-model:value 绑定数据</p>

    <div class="demo-section">
      <h2>基础用法</h2>
      <div class="demo-item">
        <label>基础输入框：</label>
        <sp-input
          v-model:value="basicValue"
          placeholder="请输入内容"
          @change="handleChange"
          @input="handleInput"
        />
        <span class="value-display">当前值：{{ basicValue || '(空)' }}</span>
      </div>
    </div>

    <div class="demo-section">
      <h2>变体样式</h2>
      <div class="demo-item">
        <label>默认样式：</label>
        <sp-input
          v-model:value="defaultVariantValue"
          variant="default"
          placeholder="默认边框样式"
        />
      </div>
      <div class="demo-item">
        <label>下划线样式：</label>
        <sp-input
          v-model:value="underlinedVariantValue"
          variant="underlined"
          placeholder="下划线样式输入框"
        />
      </div>
      <div class="demo-item">
        <label>下划线+图标：</label>
        <sp-input
          v-model:value="underlinedIconValue"
          variant="underlined"
          prefix-icon="Person"
          suffix-icon="Mail"
          placeholder="带图标的下划线样式"
        />
      </div>
      <div class="demo-item">
        <label>下划线+清除：</label>
        <sp-input
          v-model:value="underlinedClearValue"
          variant="underlined"
          clearable
          placeholder="可清除的下划线样式"
        />
      </div>
      <div class="demo-item">
        <label>下划线+错误：</label>
        <sp-input
          v-model:value="underlinedErrorValue"
          variant="underlined"
          error
          placeholder="错误状态的下划线样式"
        />
        <span class="value-display">点击输入框看中心扩展动画效果</span>
      </div>
      <div class="demo-item">
        <label>填充样式：</label>
        <sp-input
          v-model:value="filledValue"
          variant="filled"
          placeholder="带背景色的填充样式"
        />
      </div>
      <div class="demo-item">
        <label>填充+图标：</label>
        <sp-input
          v-model:value="filledIconValue"
          variant="filled"
          prefix-icon="Person"
          suffix-icon="Settings"
          placeholder="带图标的填充样式"
        />
      </div>
      <div class="demo-item">
        <label>填充+错误：</label>
        <sp-input
          v-model:value="filledErrorValue"
          variant="filled"
          error
          placeholder="错误状态的填充样式"
        />
        <span class="value-display">鼠标移入看背景色变化，聚焦看动画</span>
      </div>
      <div class="demo-item">
        <label>幽灵样式：</label>
        <sp-input
          v-model:value="ghostValue"
          variant="ghost"
          placeholder="极简透明的幽灵样式"
        />
        <span class="value-display">默认透明，hover淡入边框</span>
      </div>
      <div class="demo-item">
        <label>幽灵+图标：</label>
        <sp-input
          v-model:value="ghostIconValue"
          variant="ghost"
          prefix-icon="Search"
          suffix-icon="Filter"
          placeholder="带图标的幽灵样式"
        />
      </div>
      <div class="demo-item">
        <label>胶囊样式：</label>
        <sp-input
          v-model:value="pillValue"
          variant="pill"
          placeholder="现代感的胶囊样式"
        />
        <span class="value-display">完全圆角，现代设计</span>
      </div>
      <div class="demo-item">
        <label>胶囊+搜索：</label>
        <sp-input
          v-model:value="pillSearchValue"
          variant="pill"
          prefix-icon="Search"
          clearable
          placeholder="搜索框样式的胶囊输入框"
        />
      </div>
      <div class="demo-item">
        <label>胶囊+错误：</label>
        <sp-input
          v-model:value="pillErrorValue"
          variant="pill"
          error
          placeholder="错误状态的胶囊样式"
        />
        <span class="value-display">聚焦看发光效果</span>
      </div>
      <div class="demo-item">
        <label>方形样式：</label>
        <sp-input
          v-model:value="squareValue"
          variant="square"
          placeholder="方方正正的设计风格"
        />
        <span class="value-display">完全无圆角，方正设计</span>
      </div>
      <div class="demo-item">
        <label>方形+图标：</label>
        <sp-input
          v-model:value="squareIconValue"
          variant="square"
          prefix-icon="Settings"
          suffix-icon="ArrowForward"
          placeholder="带图标的方形样式"
        />
      </div>
      <div class="demo-item">
        <label>方形+发光：</label>
        <sp-input
          v-model:value="squareGlowValue"
          variant="square"
          effect="glow"
          placeholder="方形样式 + 发光效果"
        />
        <span class="value-display">方正边框 + 四周发光</span>
      </div>
      <div class="demo-item">
        <label>无边框样式：</label>
        <sp-input
          v-model:value="unborderValue"
          variant="unborder"
          placeholder="极简无边框设计"
        />
        <span class="value-display">完全无边框，hover淡入背景</span>
      </div>
      <div class="demo-item">
        <label>无边框+图标：</label>
        <sp-input
          v-model:value="unborderIconValue"
          variant="unborder"
          prefix-icon="Search"
          suffix-icon="Settings"
          placeholder="带图标的无边框样式"
        />
      </div>
      <div class="demo-item">
        <label>无边框+错误：</label>
        <sp-input
          v-model:value="unborderErrorValue"
          variant="unborder"
          error
          placeholder="错误状态的无边框样式"
        />
        <span class="value-display">聚焦显示淡色背景</span>
      </div>
      <div class="demo-item">
        <label>无边框+加载：</label>
        <sp-input
          v-model:value="unborderLoadingValue"
          variant="unborder"
          loading
          placeholder="无边框加载动画"
        />
        <span class="value-display">底部加载条，极简设计</span>
      </div>
    </div>

    <div class="demo-section">
      <h2>Effect 效果演示</h2>
      <div class="demo-item">
        <label>默认+发光：</label>
        <sp-input
          v-model:value="effectDefaultValue"
          variant="default"
          effect="glow"
          placeholder="默认样式 + 发光效果"
        />
        <span class="value-display">聚焦看双重阴影发光</span>
      </div>
      <div class="demo-item">
        <label>下划线+发光：</label>
        <sp-input
          v-model:value="effectUnderlinedValue"
          variant="underlined"
          effect="glow"
          placeholder="下划线 + 发光效果"
        />
      </div>
      <div class="demo-item">
        <label>填充+发光：</label>
        <sp-input
          v-model:value="effectFilledValue"
          variant="filled"
          effect="glow"
          placeholder="填充样式 + 发光效果"
        />
      </div>
      <div class="demo-item">
        <label>幽灵+发光：</label>
        <sp-input
          v-model:value="effectGhostValue"
          variant="ghost"
          effect="glow"
          placeholder="幽灵样式 + 发光效果"
        />
      </div>
      <div class="demo-item">
        <label>胶囊+发光：</label>
        <sp-input
          v-model:value="effectPillValue"
          variant="pill"
          effect="glow"
          size="large"
          prefix-icon="Person"
          suffix-icon="Settings"
          placeholder="胶囊样式 + 发光效果"
        />
        <span class="value-display">现在胶囊样式需要手动添加发光</span>
      </div>
      <div class="demo-item">
        <label>无边框+发光：</label>
        <sp-input
          v-model:value="effectUnborderValue"
          variant="unborder"
          effect="glow"
          placeholder="无边框样式 + 发光效果"
        />
        <span class="value-display">极简背景 + 发光边框</span>
      </div>
      <div class="demo-item">
        <label>错误+发光：</label>
        <sp-input
          v-model:value="effectErrorValue"
          variant="default"
          effect="glow"
          error
          placeholder="错误状态 + 发光效果"
        />
        <span class="value-display">红色发光效果</span>
      </div>
    </div>

    <div class="demo-section">
      <h2>不同尺寸</h2>
      <div class="demo-item">
        <label>大尺寸：</label>
        <sp-input
          v-model:value="largeValue"
          size="large"
          placeholder="大尺寸输入框"
        />
      </div>
      <div class="demo-item">
        <label>中等尺寸：</label>
        <sp-input
          v-model:value="mediumValue"
          size="medium"
          placeholder="中等尺寸输入框"
        />
      </div>
      <div class="demo-item">
        <label>小尺寸：</label>
        <sp-input
          v-model:value="smallValue"
          size="small"
          placeholder="小尺寸输入框"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>功能演示</h2>
      <div class="demo-item">
        <label>密码输入：</label>
        <sp-input
          v-model:value="passwordValue"
          type="password"
          show-password
          placeholder="请输入密码"
        />
      </div>
      <div class="demo-item">
        <label>密码输入：</label>
        <sp-input
          v-model:value="passwordValue"
          type="number"
          show-password
          placeholder="请输入密码"
        />
      </div>
      <div class="demo-item">
        <label>可清除：</label>
        <sp-input
          v-model:value="clearableValue"
          clearable
          placeholder="可清除的输入框"
        />
      </div>
      <div class="demo-item">
        <label>字数限制：</label>
        <sp-input
          v-model:value="limitValue"
          :maxlength="20"
          show-word-limit
          placeholder="最多输入20个字符"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>Loading 加载动画演示 🔄</h2>
      <div class="demo-item">
        <label>基础加载：</label>
        <sp-input
          v-model:value="loadingBasicValue"
          loading
          placeholder="基础加载动画"
        />
        <span class="value-display">
          实体加载条从左到右移动，loading状态下自动禁用
        </span>
      </div>
      <div class="demo-item">
        <label>下划线+加载：</label>
        <sp-input
          v-model:value="loadingUnderlinedValue"
          variant="underlined"
          loading
          placeholder="下划线样式加载"
        />
        <span class="value-display">加载条与下划线重合</span>
      </div>
      <div class="demo-item">
        <label>填充+加载：</label>
        <sp-input
          v-model:value="loadingFilledValue"
          variant="filled"
          loading
          placeholder="填充样式加载"
        />
        <span class="value-display">底部边框位置的加载条</span>
      </div>
      <div class="demo-item">
        <label>幽灵+加载：</label>
        <sp-input
          v-model:value="loadingGhostValue"
          variant="ghost"
          loading
          placeholder="幽灵样式加载"
        />
        <span class="value-display">稍微突出的加载条</span>
      </div>
      <div class="demo-item">
        <label>胶囊+加载：</label>
        <sp-input
          v-model:value="loadingPillValue"
          variant="pill"
          loading
          size="large"
          placeholder="胶囊样式加载"
        />
        <span class="value-display">圆角加载条</span>
      </div>
      <div class="demo-item">
        <label>方形+加载：</label>
        <sp-input
          v-model:value="loadingSquareValue"
          variant="square"
          loading
          placeholder="方形样式加载"
        />
        <span class="value-display">无圆角加载条</span>
      </div>
      <div class="demo-item">
        <label>错误+加载：</label>
        <sp-input
          v-model:value="loadingErrorValue"
          loading
          error
          placeholder="错误状态加载"
        />
        <span class="value-display">红色加载动画</span>
      </div>
      <div class="demo-item">
        <label>警告+加载：</label>
        <sp-input
          v-model:value="loadingWarningValue"
          loading
          validate-state="warning"
          placeholder="警告状态加载"
        />
        <span class="value-display">橙色加载动画</span>
      </div>
      <div class="demo-item">
        <label>成功+加载：</label>
        <sp-input
          v-model:value="loadingSuccessValue"
          loading
          validate-state="success"
          placeholder="成功状态加载"
        />
        <span class="value-display">绿色加载动画</span>
      </div>
      <div class="demo-item">
        <label>大尺寸+加载：</label>
        <sp-input
          v-model:value="loadingLargeValue"
          loading
          size="large"
          prefix-icon="Search"
          suffix-icon="Settings"
          placeholder="大尺寸加载动画"
        />
        <span class="value-display">配合图标的大尺寸加载</span>
      </div>
      <div class="demo-item">
        <label>无边框+加载：</label>
        <sp-input
          v-model:value="loadingUnborderValue"
          variant="unborder"
          loading
          placeholder="无边框样式加载"
        />
        <span class="value-display">极简加载条</span>
      </div>
      <div class="demo-item">
        <label>动态控制：</label>
        <sp-input
          v-model:value="loadingToggleValue"
          :loading="isLoadingToggled"
          clearable
          placeholder="可以动态控制的加载状态"
        />
        <div class="button-group">
          <button @click="toggleLoading">
            {{ isLoadingToggled ? '停止加载' : '开始加载' }}
          </button>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>图标演示 🎨</h2>
      <div class="demo-item">
        <label>搜索框：</label>
        <sp-input
          v-model:value="searchValue"
          prefix-icon="Search"
          placeholder="搜索功能"
          clearable
        />
      </div>
      <div class="demo-item">
        <label>邮箱输入：</label>
        <sp-input
          v-model:value="emailValue"
          suffix-icon="Mail"
          placeholder="请输入邮箱地址"
          type="email"
        />
      </div>
      <div class="demo-item">
        <label>用户设置：</label>
        <sp-input
          v-model:value="userValue"
          prefix-icon="Person"
          suffix-icon="Settings"
          placeholder="用户名设置"
        />
      </div>
      <div class="demo-item">
        <label>位置信息：</label>
        <sp-input
          v-model:value="locationValue"
          prefix-icon="Location"
          suffix-icon="ArrowForward"
          placeholder="输入位置"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>状态演示</h2>
      <div class="demo-item">
        <label>禁用状态：</label>
        <sp-input
          v-model:value="disabledValue"
          disabled
          placeholder="禁用状态"
        />
      </div>
      <div class="demo-item">
        <label>只读状态：</label>
        <sp-input
          v-model:value="readonlyValue"
          readonly
          placeholder="只读状态"
        />
      </div>
      <div class="demo-item">
        <label>错误状态：</label>
        <sp-input
          v-model:value="errorValue"
          error
          placeholder="错误状态"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>插槽演示</h2>
      <div class="demo-item">
        <label>前缀插槽：</label>
        <sp-input
          v-model:value="prefixValue"
          placeholder="带前缀的输入框"
        >
          <template #prefix>
            <sp-icon
              name="Search"
              :size="14"
              color="#409eff"
              clickable
            />
          </template>
        </sp-input>
      </div>
      <div class="demo-item">
        <label>后缀插槽：</label>
        <sp-input
          v-model:value="suffixValue"
          placeholder="带后缀的输入框"
        >
          <template #suffix>
            <sp-icon
              name="Mail"
              :size="14"
              color="#409eff"
              clickable
            />
          </template>
        </sp-input>
      </div>
    </div>

    <div class="demo-section">
      <h2>方法调用</h2>
      <div class="demo-item">
        <label>方法测试：</label>
        <sp-input
          ref="methodInputRef"
          v-model:value="methodValue"
          placeholder="测试方法调用"
        />
        <div class="button-group">
          <button @click="focusInput">聚焦</button>
          <button @click="blurInput">失焦</button>
          <button @click="selectInput">选中</button>
          <button @click="clearInput">清空</button>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>API 使用说明</h2>
      <div class="api-info">
        <h3>数据绑定</h3>
        <code>v-model:value="inputValue"</code>

        <h3>变体样式</h3>
        <ul>
          <li>
            <code>variant="default"</code>
            - 默认边框样式（默认值）
          </li>
          <li>
            <code>variant="underlined"</code>
            - 下划线样式，更简洁的视觉效果
          </li>
          <li>
            <code>variant="filled"</code>
            - 填充样式，带背景色和下边框，鼠标悬停背景色加深
          </li>
          <li>
            <code>variant="ghost"</code>
            - 幽灵样式，默认透明，hover淡入边框，聚焦显示完整样式
          </li>
          <li>
            <code>variant="pill"</code>
            - 胶囊样式，完全圆角设计，现代感强
          </li>
          <li>
            <code>variant="square"</code>
            - 方形样式，完全无圆角，方正设计风格
          </li>
          <li>
            <code>variant="unborder"</code>
            - 无边框样式，极简透明设计，hover显示淡色背景
          </li>
        </ul>

        <h3>视觉效果</h3>
        <ul>
          <li>
            <code>effect="none"</code>
            - 无特殊效果（默认值）
          </li>
          <li>
            <code>effect="glow"</code>
            - 发光效果，聚焦时显示双重阴影，可应用于所有变体
          </li>
        </ul>

        <h3>图标使用</h3>
        <ul>
          <li>
            <code>prefix-icon="Search"</code>
            - 前缀图标
          </li>
          <li>
            <code>suffix-icon="Mail"</code>
            - 后缀图标
          </li>
          <li>支持所有 Ionicons 图标名称</li>
          <li>图标会根据输入框尺寸自动调整大小</li>
        </ul>

        <h3>Loading 加载动画</h3>
        <ul>
          <li>
            <code>loading</code>
            - 开启底部加载动画
          </li>
          <li>实体加载条从左到右移动，循环播放</li>
          <li>支持所有变体样式，自动适配边框样式</li>
          <li>支持不同状态颜色（成功、警告、错误）</li>
          <li>loading状态下输入框自动禁用，隐藏清除和密码切换按钮</li>
          <li>可以与其他功能（图标、清除等）组合使用</li>
        </ul>

        <h3>事件监听</h3>
        <ul>
          <li>
            <code>@change="handleChange"</code>
            - 值改变事件
          </li>
          <li>
            <code>@input="handleInput"</code>
            - 输入事件
          </li>
          <li>
            <code>@focus="handleFocus"</code>
            - 聚焦事件
          </li>
          <li>
            <code>@blur="handleBlur"</code>
            - 失焦事件
          </li>
          <li>
            <code>@clear="handleClear"</code>
            - 清除事件
          </li>
        </ul>

        <h3>方法调用</h3>
        <ul>
          <li>
            <code>inputRef.focus()</code>
            - 聚焦输入框
          </li>
          <li>
            <code>inputRef.blur()</code>
            - 失焦输入框
          </li>
          <li>
            <code>inputRef.select()</code>
            - 选中输入框文本
          </li>
          <li>
            <code>inputRef.clear()</code>
            - 清空输入框
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  // 数据绑定
  const basicValue = ref('')

  // 变体样式
  const defaultVariantValue = ref('')
  const underlinedVariantValue = ref('')
  const underlinedIconValue = ref('')
  const underlinedClearValue = ref('可以清除这段文字')
  const underlinedErrorValue = ref('')
  const filledValue = ref('')
  const filledIconValue = ref('')
  const filledErrorValue = ref('')
  const ghostValue = ref('')
  const ghostIconValue = ref('')
  const pillValue = ref('')
  const pillSearchValue = ref('')
  const pillErrorValue = ref('')
  const squareValue = ref('')
  const squareIconValue = ref('')
  const squareGlowValue = ref('')
  const unborderValue = ref('')
  const unborderIconValue = ref('')
  const unborderErrorValue = ref('')
  const unborderLoadingValue = ref('')

  // Effect 效果
  const effectDefaultValue = ref('')
  const effectUnderlinedValue = ref('')
  const effectFilledValue = ref('')
  const effectGhostValue = ref('')
  const effectPillValue = ref('')
  const effectUnborderValue = ref('')
  const effectErrorValue = ref('')

  // 尺寸
  const largeValue = ref('大尺寸示例')
  const mediumValue = ref('中等尺寸示例')
  const smallValue = ref('小尺寸示例')
  const passwordValue = ref('')
  const clearableValue = ref('可以清除这段文字')
  const limitValue = ref('')
  const disabledValue = ref('禁用状态的值')
  const readonlyValue = ref('只读状态的值')
  const errorValue = ref('')
  const prefixValue = ref('')
  const suffixValue = ref('')
  const methodValue = ref('测试方法')

  // 图标相关
  const searchValue = ref('')
  const emailValue = ref('')
  const userValue = ref('')
  const locationValue = ref('')

  // Loading 相关
  const loadingBasicValue = ref('')
  const loadingUnderlinedValue = ref('')
  const loadingFilledValue = ref('')
  const loadingGhostValue = ref('')
  const loadingPillValue = ref('')
  const loadingSquareValue = ref('')
  const loadingErrorValue = ref('')
  const loadingWarningValue = ref('')
  const loadingSuccessValue = ref('')
  const loadingLargeValue = ref('')
  const loadingUnborderValue = ref('')
  const loadingToggleValue = ref('')
  const isLoadingToggled = ref(false)

  // 组件引用
  const methodInputRef = ref()

  // 事件处理
  const handleChange = (value: string | number | undefined) => {
    console.log('Change event:', value)
  }

  const handleInput = (event: Event) => {
    console.log('Input event:', event)
  }

  // 方法调用
  const focusInput = () => {
    methodInputRef.value?.focus()
  }

  const blurInput = () => {
    methodInputRef.value?.blur()
  }

  const selectInput = () => {
    methodInputRef.value?.select()
  }

  const clearInput = () => {
    methodInputRef.value?.clear()
  }

  // Loading 切换方法
  const toggleLoading = () => {
    isLoadingToggled.value = !isLoadingToggled.value
  }
</script>

<style scoped>
  .page-container {
    padding: 20px;
    max-width: 1000px;
    margin: 0 auto;
  }

  .page-container h1 {
    color: #303133;
    font-size: 28px;
    margin-bottom: 10px;
  }

  .page-container > p {
    color: #606266;
    font-size: 14px;
    margin-bottom: 30px;
  }

  .demo-section {
    margin-bottom: 40px;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    overflow: hidden;
  }

  .demo-section h2 {
    margin: 0;
    padding: 15px 20px;
    background-color: #fafafa;
    border-bottom: 1px solid #ebeef5;
    color: #303133;
    font-size: 18px;
    font-weight: 600;
  }

  .demo-item {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .demo-item:last-child {
    border-bottom: none;
  }

  .demo-item label {
    width: 120px;
    flex-shrink: 0;
    font-weight: 500;
    color: #606266;
    font-size: 14px;
  }

  .value-display {
    color: #909399;
    font-size: 12px;
    margin-left: 10px;
  }

  .button-group {
    display: flex;
    gap: 8px;
    margin-left: 10px;
  }

  .button-group button {
    padding: 6px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background: #fff;
    cursor: pointer;
    font-size: 12px;
    color: #606266;
    transition: all 0.2s;
  }

  .button-group button:hover {
    border-color: #409eff;
    color: #409eff;
  }

  .api-info {
    padding: 20px;
  }

  .api-info h3 {
    margin: 20px 0 10px 0;
    color: #303133;
    font-size: 16px;
  }

  .api-info h3:first-child {
    margin-top: 0;
  }

  .api-info code {
    background-color: #f5f7fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    color: #e74c3c;
  }

  .api-info ul {
    margin: 0;
    padding-left: 20px;
  }

  .api-info li {
    margin: 5px 0;
    font-size: 14px;
    color: #606266;
  }
</style>
