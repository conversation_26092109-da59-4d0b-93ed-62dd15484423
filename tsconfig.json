{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "declaration": true, "declarationMap": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@speed-ui/ui": ["packages/ui/src"], "@speed-ui/ui/*": ["packages/ui/src/*"], "@speed-ui/utils": ["packages/utils/src"], "@speed-ui/utils/*": ["packages/utils/src/*"]}}, "include": ["packages/*/src/**/*.ts", "packages/*/src/**/*.d.ts", "packages/*/src/**/*.tsx", "packages/*/src/**/*.vue", "playground/src/**/*.ts", "playground/src/**/*.d.ts", "playground/src/**/*.tsx", "playground/src/**/*.vue", "env.d.ts"]}