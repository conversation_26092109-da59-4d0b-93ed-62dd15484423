.sp-form {
  // 基础样式
  &--inline {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 16px;

    .sp-form-item {
      margin-bottom: 0;
      display: flex;
      align-items: center;

      .sp-form-item__label {
        margin-right: 8px;
        white-space: nowrap;
        flex-shrink: 0;
      }

      .sp-form-item__content {
        flex: 1;
        min-width: 0;
      }
    }
  }

  // 标签位置
  &--left {
    .sp-form-item__label {
      text-align: left;
    }
  }

  &--right {
    .sp-form-item__label {
      text-align: right;
    }
  }

  &--top {
    .sp-form-item {
      flex-direction: column;
      align-items: flex-start;

      .sp-form-item__label {
        margin-bottom: 8px;
        line-height: 1.5;
        padding: 0;
        display: block;
        text-align: left;
      }

      .sp-form-item__content {
        width: 100%;
        margin-left: 0 !important;
      }
    }
  }

  // 禁用状态
  &--disabled {
    .sp-form-item {
      pointer-events: none;
      opacity: 0.6;
    }
    
    .sp-form-item__label {
      color: #c0c4cc;
    }

    .sp-input,
    .sp-textarea,
    .sp-select {
      background-color: #f5f7fa;
      border-color: #e4e7ed;
      color: #c0c4cc;
      cursor: not-allowed;

      input,
      textarea {
        background-color: transparent;
        border: none;
        color: #c0c4cc;
        cursor: not-allowed;
      }
    }
  }

  // 尺寸变体
  &--large {
    .sp-form-item__label {
      line-height: 48px;
      font-size: 16px;
    }
  }

  &--small {
    .sp-form-item__label {
      line-height: 32px;
      font-size: 12px;
    }
  }
} 