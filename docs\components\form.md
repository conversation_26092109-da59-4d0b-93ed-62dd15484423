# Form 表单

由输入框、选择器、单选框、多选框等控件组成，用以收集、校验、提交数据。

## 基础用法

基础的表单数据域控件展示，包含布局、初始化、验证、提交。

<script setup>
import { ref, reactive } from 'vue'
import { Form, FormItem, Input, Button } from '@speed-ui/ui'
import '@speed-ui/ui/styles'

// 基础表单
const basicForm = reactive({
  name: '',
  region: '',
  delivery: false,
  type: [],
  resource: '',
  desc: ''
})

// 验证规则
const rules = reactive({
  name: [
    { required: true, message: '请输入活动名称', trigger: 'blur' },
    { min: 3, max: 15, message: '长度在 3 到 15 个字符', trigger: 'blur' }
  ],
  region: [
    { required: true, message: '请选择活动区域', trigger: 'change' }
  ],
  type: [
    { type: 'array', required: true, message: '请至少选择一个活动性质', trigger: 'change' }
  ],
  resource: [
    { required: true, message: '请选择活动资源', trigger: 'change' }
  ],
  desc: [
    { required: true, message: '请填写活动形式', trigger: 'blur' }
  ]
})

const basicFormRef = ref()

const onSubmit = () => {
  basicFormRef.value?.validate((valid) => {
    if (valid) {
      alert('提交成功!')
    } else {
      console.log('表单验证失败!')
      return false
    }
  })
}

const onReset = () => {
  basicFormRef.value?.resetFields()
}

// 行内表单
const inlineForm = reactive({
  user: '',
  region: ''
})

const onInlineSubmit = () => {
  console.log('提交！', inlineForm)
}

// 对齐方式表单
const alignForm = reactive({
  name: '',
  desc: ''
})
</script>

<template>
  <div class="demo-form">
    <!-- 基础表单 -->
    <h3>基础表单</h3>
    <Form 
      ref="basicFormRef"
      :model="basicForm" 
      :rules="rules" 
      label-width="100px"
      class="demo-form-basic"
    >
      <FormItem label="活动名称" prop="name">
        <Input v-model="basicForm.name" placeholder="请输入活动名称" />
      </FormItem>
      <FormItem label="活动区域" prop="region">
        <Input v-model="basicForm.region" placeholder="请选择活动区域" />
      </FormItem>
      <FormItem label="活动形式" prop="desc">
        <Input 
          v-model="basicForm.desc" 
          type="textarea" 
          placeholder="请输入活动形式"
          :rows="3"
        />
      </FormItem>
      <FormItem>
        <Button type="primary" @click="onSubmit">立即创建</Button>
        <Button @click="onReset" style="margin-left: 10px;">重置</Button>
      </FormItem>
    </Form>

    <!-- 行内表单 -->
    <h3>行内表单</h3>
    <Form :inline="true" :model="inlineForm" class="demo-form-inline">
      <FormItem label="审批人">
        <Input v-model="inlineForm.user" placeholder="审批人" />
      </FormItem>
      <FormItem label="活动区域">
        <Input v-model="inlineForm.region" placeholder="活动区域" />
      </FormItem>
      <FormItem>
        <Button type="primary" @click="onInlineSubmit">查询</Button>
      </FormItem>
    </Form>

    <!-- 对齐方式 -->
    <h3>标签对齐方式</h3>
    <div style="display: flex; gap: 40px;">
      <div>
        <h4>左对齐（默认）</h4>
        <Form :model="alignForm" label-width="80px" label-position="left">
          <FormItem label="名称">
            <Input v-model="alignForm.name" />
          </FormItem>
          <FormItem label="活动性质">
            <Input v-model="alignForm.desc" />
          </FormItem>
        </Form>
      </div>
      
      <div>
        <h4>右对齐</h4>
        <Form :model="alignForm" label-width="80px" label-position="right">
          <FormItem label="名称">
            <Input v-model="alignForm.name" />
          </FormItem>
          <FormItem label="活动性质">
            <Input v-model="alignForm.desc" />
          </FormItem>
        </Form>
      </div>
      
      <div>
        <h4>顶部对齐</h4>
        <Form :model="alignForm" label-position="top">
          <FormItem label="名称">
            <Input v-model="alignForm.name" />
          </FormItem>
          <FormItem label="活动性质">
            <Input v-model="alignForm.desc" />
          </FormItem>
        </Form>
      </div>
    </div>
  </div>
</template>

<style scoped>
.demo-form {
  max-width: 600px;
}

.demo-form h3 {
  margin: 30px 0 20px;
  font-size: 18px;
  color: #333;
}

.demo-form h4 {
  margin: 0 0 15px;
  font-size: 14px;
  color: #666;
}

.demo-form-basic {
  margin-bottom: 40px;
}

.demo-form-inline {
  margin-bottom: 40px;
}
</style>

## 表单验证

Form 组件提供了表单验证功能，只需为 Form 组件的 `rules` 属性传入约定的验证规则，并将 `FormItem` 的 `prop` 属性设置为需要验证的字段名即可。

### 验证规则

```javascript
const rules = {
  name: [
    { required: true, message: '请输入活动名称', trigger: 'blur' },
    { min: 3, max: 15, message: '长度在 3 到 15 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}
```

### 自定义验证规则

除了传入内置的验证规则，还可以传入自定义的验证函数。

```javascript
const validateAge = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('年龄不能为空'))
  }
  setTimeout(() => {
    if (!Number.isInteger(value)) {
      callback(new Error('请输入数字值'))
    } else {
      if (value < 18) {
        callback(new Error('必须年满18岁'))
      } else {
        callback()
      }
    }
  }, 1000)
}

const rules = {
  age: [
    { validator: validateAge, trigger: 'blur' }
  ]
}
```

## Form API

### Form Attributes

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
| --- | --- | --- | --- | --- |
| model | 表单数据对象 | object | — | — |
| rules | 表单验证规则 | object | — | — |
| inline | 行内表单模式 | boolean | — | false |
| label-position | 表单域标签的位置 | string | right/left/top | right |
| label-width | 表单域标签的宽度 | string | — | — |
| label-suffix | 表单域标签的后缀 | string | — | — |
| hide-required-asterisk | 是否隐藏必填字段的标签旁的红色星号 | boolean | — | false |
| show-message | 是否显示校验错误信息 | boolean | — | true |
| inline-message | 是否以行内形式展示校验信息 | boolean | — | false |
| status-icon | 是否在输入框中显示校验结果反馈图标 | boolean | — | false |
| validate-on-rule-change | 是否在 rules 属性改变后立即触发一次验证 | boolean | — | true |
| size | 用于控制该表单内组件的尺寸 | string | medium/small/mini | — |
| disabled | 是否禁用该表单内的所有组件 | boolean | — | false |

### Form Methods

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| validate | 对整个表单进行校验的方法 | Function(callback: Function(boolean, object)) |
| validateField | 对部分表单字段进行校验的方法 | Function(props: array \| string, callback: Function(errorMessage)) |
| resetFields | 对整个表单进行重置，将所有字段值重置为初始值并移除校验结果 | — |
| clearValidate | 移除表单项的校验结果 | Function(props: array \| string) |

### Form Events

| 事件名称 | 说明 | 回调参数 |
| --- | --- | --- |
| validate | 任一表单项被校验后触发 | 被校验的表单项 prop 值，校验是否通过，错误消息（如果存在） |

## FormItem API

### FormItem Attributes

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
| --- | --- | --- | --- | --- |
| prop | 表单域 model 字段 | string | 传入 Form 组件的 model 中的字段 | — |
| label | 标签文本 | string | — | — |
| label-for | 原生 for 属性 | string | — | — |
| label-width | 表单域标签的宽度 | string | — | — |
| required | 是否必填 | boolean | — | false |
| rules | 表单验证规则 | object | — | — |
| error | 表单域验证错误信息 | string | — | — |
| validate-status | 表单域验证状态 | string | success/warning/error/validating | — |
| show-message | 是否显示校验错误信息 | boolean | — | true |
| inline-message | 以行内形式展示校验信息 | boolean | — | false |
| size | 用于控制该表单域下组件的尺寸 | string | medium/small/mini | — |

### FormItem Methods

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| resetField | 对该表单项进行重置，将其值重置为初始值并移除校验结果 | — |
| clearValidate | 移除该表单项的校验结果 | — |
</rewritten_file> 