/**
 * Speed UI 预设尺寸样式
 * 通过 CSS 变量实现，避免大量内联样式
 */

// 小尺寸预设
.sp-size--small {
  --sp-field-size: var(--sp-size-small-base, 36px);
  --sp-field-font-size: var(--sp-size-small-font-size, 14px);
  --sp-field-padding-top: var(--sp-size-small-padding-top, 18px);
  --sp-field-padding-bottom: var(--sp-size-small-padding-bottom, 3px);
  --sp-field-label-floating-top: var(--sp-size-small-label-floating-top, 6px);
  --sp-field-label-font-size: var(--sp-size-small-label-font-size, 12px);
  --sp-field-prefix-padding: var(--sp-size-small-prefix-padding, 3px);
  --sp-field-prefix-padding-left: var(--sp-size-small-prefix-padding-left, 12px);
  --sp-field-prefix-padding-default: var(--sp-size-small-prefix-padding-default, 15px);
  --sp-field-prefix-padding-square: var(--sp-size-small-prefix-padding-square, 3px);
  --sp-field-gap: var(--sp-size-small-gap, 9px);
}

// 中等尺寸预设
.sp-size--medium {
  --sp-field-size: var(--sp-size-medium-base, 48px);
  --sp-field-font-size: var(--sp-size-medium-font-size, 16px);
  --sp-field-padding-top: var(--sp-size-medium-padding-top, 24px);
  --sp-field-padding-bottom: var(--sp-size-medium-padding-bottom, 4px);
  --sp-field-label-floating-top: var(--sp-size-medium-label-floating-top, 8px);
  --sp-field-label-font-size: var(--sp-size-medium-label-font-size, 14px);
  --sp-field-prefix-padding: var(--sp-size-medium-prefix-padding, 4px);
  --sp-field-prefix-padding-left: var(--sp-size-medium-prefix-padding-left, 16px);
  --sp-field-prefix-padding-default: var(--sp-size-medium-prefix-padding-default, 20px);
  --sp-field-prefix-padding-square: var(--sp-size-medium-prefix-padding-square, 4px);
  --sp-field-gap: var(--sp-size-medium-gap, 12px);
}

// 大尺寸预设
.sp-size--large {
  --sp-field-size: var(--sp-size-large-base, 56px);
  --sp-field-font-size: var(--sp-size-large-font-size, 18px);
  --sp-field-padding-top: var(--sp-size-large-padding-top, 28px);
  --sp-field-padding-bottom: var(--sp-size-large-padding-bottom, 4px);
  --sp-field-label-floating-top: var(--sp-size-large-label-floating-top, 10px);
  --sp-field-label-font-size: var(--sp-size-large-label-font-size, 16px);
  --sp-field-prefix-padding: var(--sp-size-large-prefix-padding, 6px);
  --sp-field-prefix-padding-left: var(--sp-size-large-prefix-padding-left, 20px);
  --sp-field-prefix-padding-default: var(--sp-size-large-prefix-padding-default, 25px);
  --sp-field-prefix-padding-square: var(--sp-size-large-prefix-padding-square, 6px);
  --sp-field-gap: var(--sp-size-large-gap, 14px);
}

// 超大尺寸预设
.sp-size--xlarge {
  --sp-field-size: var(--sp-size-xlarge-base, 64px);
  --sp-field-font-size: var(--sp-size-xlarge-font-size, 20px);
  --sp-field-padding-top: var(--sp-size-xlarge-padding-top, 32px);
  --sp-field-padding-bottom: var(--sp-size-xlarge-padding-bottom, 6px);
  --sp-field-label-floating-top: var(--sp-size-xlarge-label-floating-top, 12px);
  --sp-field-label-font-size: var(--sp-size-xlarge-label-font-size, 18px);
  --sp-field-prefix-padding: var(--sp-size-xlarge-prefix-padding, 8px);
  --sp-field-prefix-padding-left: var(--sp-size-xlarge-prefix-padding-left, 24px);
  --sp-field-prefix-padding-default: var(--sp-size-xlarge-prefix-padding-default, 30px);
  --sp-field-prefix-padding-square: var(--sp-size-xlarge-prefix-padding-square, 8px);
  --sp-field-gap: var(--sp-size-xlarge-gap, 16px);
}
