<template>
  <div class="sp-theme-switcher">
    <div
      v-if="showTitle"
      class="sp-theme-switcher__title"
    >
      {{ title }}
    </div>

    <!-- 预设主题 -->
    <div
      v-if="showPresets"
      class="sp-theme-switcher__presets"
    >
      <div
        v-if="showLabels"
        class="sp-theme-switcher__label"
      >
        {{ presetLabel }}
      </div>
      <div class="sp-theme-switcher__preset-buttons">
        <button
          v-for="(themeConfig, themeName) in presetThemes"
          :key="themeName"
          :class="[
            'sp-theme-switcher__preset-btn',
            { 'sp-theme-switcher__preset-btn--active': isCurrentPreset(themeName) }
          ]"
          :style="{ backgroundColor: themeConfig.primaryColor }"
          @click="handlePresetClick(themeName)"
          :title="getPresetName(themeName)"
        >
          {{ getPresetName(themeName) }}
        </button>
      </div>
    </div>

    <!-- 自定义颜色 -->
    <div
      v-if="showCustom"
      class="sp-theme-switcher__custom"
    >
      <div
        v-if="showLabels"
        class="sp-theme-switcher__label"
      >
        {{ customLabel }}
      </div>
      <div class="sp-theme-switcher__custom-controls">
        <input
          type="color"
          v-model="customColor"
          @change="handleCustomColorChange"
          class="sp-theme-switcher__color-picker"
        />
        <button
          v-if="showApplyButton"
          @click="handleApplyCustom"
          class="sp-theme-switcher__apply-btn"
        >
          {{ applyButtonText }}
        </button>
        <span
          v-if="showColorValue"
          class="sp-theme-switcher__color-value"
        >
          {{ customColor }}
        </span>
      </div>
    </div>

    <!-- 重置按钮 -->
    <div
      v-if="showReset"
      class="sp-theme-switcher__reset"
    >
      <button
        @click="handleReset"
        class="sp-theme-switcher__reset-btn"
      >
        {{ resetButtonText }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useTheme, type PresetThemeName, PRESET_THEMES } from '../../composables/use-theme'

interface Props {
  /** 是否显示标题 */
  showTitle?: boolean
  /** 标题文本 */
  title?: string
  /** 是否显示预设主题 */
  showPresets?: boolean
  /** 预设主题标签 */
  presetLabel?: string
  /** 是否显示自定义颜色 */
  showCustom?: boolean
  /** 自定义颜色标签 */
  customLabel?: string
  /** 是否显示应用按钮 */
  showApplyButton?: boolean
  /** 应用按钮文本 */
  applyButtonText?: string
  /** 是否显示颜色值 */
  showColorValue?: boolean
  /** 是否显示重置按钮 */
  showReset?: boolean
  /** 重置按钮文本 */
  resetButtonText?: string
  /** 是否显示标签 */
  showLabels?: boolean
  /** 是否自动应用自定义颜色 */
  autoApplyCustom?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showTitle: true,
  title: '🎨 主题切换器',
  showPresets: true,
  presetLabel: '预设主题:',
  showCustom: true,
  customLabel: '自定义颜色:',
  showApplyButton: false,
  applyButtonText: '应用',
  showColorValue: true,
  showReset: true,
  resetButtonText: '重置',
  showLabels: true,
  autoApplyCustom: true,
})

interface Emits {
  (e: 'theme-change', theme: string | object): void
  (e: 'preset-change', presetName: PresetThemeName): void
  (e: 'custom-change', color: string): void
  (e: 'reset'): void
}

const emit = defineEmits<Emits>()

// 使用主题
const { theme, setTheme, setPrimaryColor, resetTheme, presetThemes } = useTheme()

// 自定义颜色
const customColor = ref(theme.value.primaryColor || '#667eea')

// 预设主题名称映射
const presetNames: Record<PresetThemeName, string> = {
  default: '默认',
  blue: '蓝色',
  green: '绿色',
  red: '红色',
  orange: '橙色',
  purple: '紫色',
}

// 获取预设主题名称
const getPresetName = (themeName: string) => {
  return presetNames[themeName as PresetThemeName] || themeName
}

// 判断是否为当前预设主题
const isCurrentPreset = (themeName: string) => {
  const currentPrimary = theme.value.primaryColor
  const presetPrimary = PRESET_THEMES[themeName as PresetThemeName]?.primaryColor
  return currentPrimary === presetPrimary
}

// 处理预设主题点击
const handlePresetClick = (themeName: string) => {
  const presetName = themeName as PresetThemeName
  setTheme(presetName)
  customColor.value = PRESET_THEMES[presetName].primaryColor
  emit('theme-change', presetName)
  emit('preset-change', presetName)
}

// 处理自定义颜色变化
const handleCustomColorChange = () => {
  if (props.autoApplyCustom) {
    handleApplyCustom()
  }
}

// 应用自定义颜色
const handleApplyCustom = () => {
  setPrimaryColor(customColor.value)
  emit('theme-change', { primaryColor: customColor.value })
  emit('custom-change', customColor.value)
}

// 重置主题
const handleReset = () => {
  resetTheme()
  customColor.value = PRESET_THEMES.default.primaryColor
  emit('theme-change', 'default')
  emit('reset')
}

// 监听主题变化，同步自定义颜色
watch(
  () => theme.value.primaryColor,
  (newColor) => {
    if (newColor && newColor !== customColor.value) {
      customColor.value = newColor
    }
  }
)
</script>

<style scoped>
.sp-theme-switcher {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 1px solid #e1e8ed;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sp-theme-switcher__title {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.4rem;
  font-weight: 600;
  text-align: center;
}

.sp-theme-switcher__presets,
.sp-theme-switcher__custom,
.sp-theme-switcher__reset {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.sp-theme-switcher__label {
  font-weight: 500;
  color: #34495e;
  min-width: 100px;
}

.sp-theme-switcher__preset-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.sp-theme-switcher__preset-btn {
  padding: 8px 16px;
  border: 2px solid transparent;
  border-radius: 6px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sp-theme-switcher__preset-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.sp-theme-switcher__preset-btn--active {
  border-color: #fff;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.sp-theme-switcher__custom-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.sp-theme-switcher__color-picker {
  width: 50px;
  height: 35px;
  border: 2px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sp-theme-switcher__color-picker:hover {
  border-color: var(--sp-color-primary, #667eea);
  transform: scale(1.1);
}

.sp-theme-switcher__apply-btn,
.sp-theme-switcher__reset-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.sp-theme-switcher__apply-btn:hover,
.sp-theme-switcher__reset-btn:hover {
  border-color: var(--sp-color-primary, #667eea);
  color: var(--sp-color-primary, #667eea);
  background: var(--sp-color-primary-lightest, #eef2ff);
}

.sp-theme-switcher__color-value {
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  font-size: 0.9rem;
}
</style>
