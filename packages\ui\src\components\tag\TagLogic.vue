<!--
  TagLogic.vue - 逻辑处理层
  
  职责：
  - 状态管理和业务逻辑
  - 事件处理
  - 样式类名计算
  - 主题色支持
-->

<template>
  <TagInner
    :class="tagClasses"
    :style="tagStyles"
    :aria-label="ariaLabel"
    :role="role"
    :aria-checked="ariaChecked"
    :aria-disabled="ariaDisabled"
    :tabindex="tabindex"
    @click="handleClick"
    @keydown="handleKeydown"
  >
    <!-- 前置图标 -->
    <span
      v-if="props.icon || $slots.icon"
      :class="bem.e('icon')"
    >
      <slot name="icon">
        <Icon
          v-if="props.icon"
          :name="props.icon"
        />
      </slot>
    </span>

    <!-- 标签内容 -->
    <span
      v-if="hasContent"
      :class="[bem.e('content'), { [bem.e('truncate')]: props.truncate }]"
    >
      <slot>{{ props.label }}</slot>
    </span>

    <!-- 关闭按钮 -->
    <span
      v-if="props.closable"
      :class="bem.e('close')"
      @click="handleClose"
    >
      <slot name="close">
        <Icon
          :name="props.closeIcon || 'Close'"
          :class="bem.e('close-icon')"
        />
      </slot>
    </span>
  </TagInner>
</template>

<script setup lang="ts">
  import { computed, useSlots } from 'vue'
  import { bemHelper } from '@speed-ui/config'
  import TagInner from './TagInner.vue'
  import Icon from '../icon/Icon.vue'
  import type { TagLogicProps, TagLogicEmits } from './types'

  /**
   * TagLogic - 逻辑处理层
   *
   * 特点：
   * 1. 状态管理和业务逻辑
   * 2. 事件处理
   * 3. 样式类名计算
   * 4. 主题色支持
   */

  const props = defineProps<TagLogicProps>()
  const emit = defineEmits<TagLogicEmits>()
  const slots = useSlots()

  // BEM helper
  const bem = bemHelper('tag')

  // 计算是否有内容
  const hasContent = computed(() => {
    return props.label || slots.default
  })

  // 计算样式类
  const tagClasses = computed(() => [
    bem.b(), // sp-tag
    bem.m(props.size || 'medium'), // sp-tag--small/medium/large
    bem.m(props.type || 'default'), // sp-tag--default/primary/success等
    bem.m(props.variant || 'filled'), // sp-tag--filled/outlined/light
    {
      [bem.m('disabled')]: props.disabled,
      [bem.m('checked')]: props.checked,
      [bem.m('closable')]: props.closable,
      [bem.m('round')]: props.round,
      [bem.m('bordered')]: props.bordered,
      [bem.m('checkable')]: props.checkable,
    },
  ])

  // 计算样式
  const tagStyles = computed(() => {
    const styles: Record<string, any> = {}
    
    // 自定义颜色
    if (props.color) {
      styles['--tag-custom-color'] = props.color
    }
    
    // 最大宽度
    if (props.maxWidth) {
      styles.maxWidth = typeof props.maxWidth === 'number' 
        ? `${props.maxWidth}px` 
        : props.maxWidth
    }
    
    return styles
  })

  // 无障碍属性
  const ariaLabel = computed(() => {
    return props.label || undefined
  })

  const role = computed(() => {
    if (props.checkable) return 'checkbox'
    if (props.closable) return 'button'
    return undefined
  })

  const ariaChecked = computed(() => {
    return props.checkable ? props.checked : undefined
  })

  const ariaDisabled = computed(() => {
    return props.disabled || undefined
  })

  const tabindex = computed(() => {
    if (props.disabled) return -1
    if (props.checkable || props.closable) return 0
    return undefined
  })

  // 事件处理
  const handleClick = (event: MouseEvent) => {
    if (props.disabled) return
    
    emit('click', event)
    
    // 可选中功能
    if (props.checkable) {
      emit('check', !props.checked)
    }
  }

  const handleClose = (event: MouseEvent) => {
    event.stopPropagation()
    if (props.disabled) return
    emit('close', props.value)
  }

  const handleKeydown = (event: KeyboardEvent) => {
    if (props.disabled) return
    
    // 空格键或回车键触发点击
    if (event.code === 'Space' || event.code === 'Enter') {
      event.preventDefault()
      handleClick(event as any)
    }
    
    // Delete键触发关闭
    if (event.code === 'Delete' || event.code === 'Backspace') {
      if (props.closable) {
        event.preventDefault()
        handleClose(event as any)
      }
    }
  }
</script>

<script lang="ts">
  export default {
    name: 'TagLogic',
  }
</script>
