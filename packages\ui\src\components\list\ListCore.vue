<!--
  ListCore.vue - 核心控制层
  
  职责：
  - DOM事件处理
  - 焦点管理
  - 键盘导航
  - 选择控制
-->

<template>
  <ListInner
    ref="listInnerRef"
    v-bind="innerProps"
    @click="handleClick"
    @keydown="handleKeydown"
    @focus="handleFocus"
    @blur="handleBlur"
  >
    <slot />
  </ListInner>
</template>

<script setup lang="ts">
  import { ref, computed, nextTick } from 'vue'
  import ListInner from './ListInner.vue'
  import type { ListCoreProps, ListCoreEvents } from './types/list-core'

  /**
   * ListCore - 核心控制层
   *
   * 特点：
   * 1. DOM事件处理
   * 2. 焦点和键盘管理
   * 3. 选择控制
   * 4. 无障碍支持
   */

  interface Props extends ListCoreProps {}

  interface Emits extends ListCoreEvents {}

  const props = withDefaults(defineProps<Props>(), {
    disabled: false,
    readonly: false,
    multiple: false,
    selectable: false,
    hoverable: true,
    tabindex: 0,
  })

  const emit = defineEmits<Emits>()

  // 组件引用
  const listInnerRef = ref<InstanceType<typeof ListInner> | null>(null)

  // 内部状态
  const focused = ref(false)
  const activeIndex = ref(-1)

  // 计算属性 - 传递给 ListInner 的属性
  const innerProps = computed(() => {
    const classes = ['sp-list-inner']

    if (props.disabled) classes.push('sp-list-inner--disabled')
    if (props.readonly) classes.push('sp-list-inner--readonly')
    if (focused.value) classes.push('sp-list-inner--focused')
    if (props.selectable) classes.push('sp-list-inner--selectable')
    if (props.multiple) classes.push('sp-list-inner--multiple')

    return {
      classes,
      styles: {},
      role: 'listbox',
      ariaLabel: props.ariaLabel,
      ariaMultiselectable: props.multiple,
    }
  })

  // 事件处理
  const handleClick = (event: MouseEvent) => {
    if (props.disabled || props.readonly) return
    emit('click', event)
  }

  const handleKeydown = (event: KeyboardEvent) => {
    if (props.disabled || props.readonly) return

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault()
        handleArrowDown()
        break
      case 'ArrowUp':
        event.preventDefault()
        handleArrowUp()
        break
      case 'Enter':
      case ' ':
        event.preventDefault()
        handleSelect()
        break
      case 'Escape':
        handleEscape()
        break
      case 'Home':
        event.preventDefault()
        handleHome()
        break
      case 'End':
        event.preventDefault()
        handleEnd()
        break
    }

    emit('keydown', event)
  }

  const handleFocus = (event: FocusEvent) => {
    if (props.disabled) return
    focused.value = true
    emit('focus', event)
  }

  const handleBlur = (event: FocusEvent) => {
    focused.value = false
    activeIndex.value = -1
    emit('blur', event)
  }

  // 键盘导航
  const handleArrowDown = () => {
    const itemCount = getItemCount()
    if (itemCount === 0) return

    if (activeIndex.value < itemCount - 1) {
      activeIndex.value++
    } else {
      activeIndex.value = 0 // 循环到第一项
    }

    emit('navigate', { direction: 'down', index: activeIndex.value })
  }

  const handleArrowUp = () => {
    const itemCount = getItemCount()
    if (itemCount === 0) return

    if (activeIndex.value > 0) {
      activeIndex.value--
    } else {
      activeIndex.value = itemCount - 1 // 循环到最后一项
    }

    emit('navigate', { direction: 'up', index: activeIndex.value })
  }

  const handleSelect = () => {
    if (activeIndex.value >= 0) {
      emit('select-by-keyboard', { index: activeIndex.value })
    }
  }

  const handleEscape = () => {
    emit('escape')
  }

  const handleHome = () => {
    if (getItemCount() > 0) {
      activeIndex.value = 0
      emit('navigate', { direction: 'home', index: 0 })
    }
  }

  const handleEnd = () => {
    const itemCount = getItemCount()
    if (itemCount > 0) {
      activeIndex.value = itemCount - 1
      emit('navigate', { direction: 'end', index: itemCount - 1 })
    }
  }

  // 工具方法
  const getItemCount = (): number => {
    // 这里需要从父组件或slot中获取item数量
    // 暂时返回固定值，后续需要优化
    return props.itemCount || 0
  }

  // DOM操作方法
  const focus = (options?: FocusOptions) => {
    return nextTick(() => {
      const element = listInnerRef.value?.$el
      if (element) {
        element.focus(options)
      }
    })
  }

  const blur = () => {
    return nextTick(() => {
      const element = listInnerRef.value?.$el
      if (element) {
        element.blur()
      }
    })
  }

  // 暴露方法
  defineExpose({
    focus,
    blur,
    activeIndex: computed(() => activeIndex.value),
    focused: computed(() => focused.value),
  })
</script>
