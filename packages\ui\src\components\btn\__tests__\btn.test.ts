import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import Btn from '../btn.vue'

describe('Btn Component', () => {
  it('renders correctly with default props', () => {
    const wrapper = mount(Btn, {
      slots: {
        default: 'Test Button',
      },
    })

    expect(wrapper.text()).toBe('Test Button')
    expect(wrapper.classes()).toContain('sp-btn')
    expect(wrapper.classes()).toContain('sp-btn--default')
    expect(wrapper.classes()).toContain('sp-btn--medium')
  })

  it('applies variant classes correctly', () => {
    const wrapper = mount(Btn, {
      props: {
        variant: 'outlined',
      },
      slots: {
        default: 'Outlined Button',
      },
    })

    expect(wrapper.classes()).toContain('sp-btn--outlined')
  })

  it('applies size classes correctly', () => {
    const wrapper = mount(Btn, {
      props: {
        size: 'large',
      },
      slots: {
        default: 'Large Button',
      },
    })

    expect(wrapper.classes()).toContain('sp-btn--large')
  })

  it('handles disabled state correctly', () => {
    const wrapper = mount(Btn, {
      props: {
        disabled: true,
      },
      slots: {
        default: 'Disabled Button',
      },
    })

    expect(wrapper.classes()).toContain('sp-btn--disabled')
    expect(wrapper.find('button').attributes('disabled')).toBeDefined()
  })

  it('emits click event when clicked', async () => {
    const wrapper = mount(Btn, {
      slots: {
        default: 'Clickable Button',
      },
    })

    await wrapper.trigger('click')

    expect(wrapper.emitted('click')).toBeTruthy()
    expect(wrapper.emitted('click')).toHaveLength(1)
  })

  it('does not emit click event when disabled', async () => {
    const wrapper = mount(Btn, {
      props: {
        disabled: true,
      },
      slots: {
        default: 'Disabled Button',
      },
    })

    await wrapper.trigger('click')

    expect(wrapper.emitted('click')).toBeFalsy()
  })

  it('applies hover state correctly', async () => {
    const wrapper = mount(Btn, {
      slots: {
        default: 'Hover Button',
      },
    })

    await wrapper.trigger('mouseenter')

    expect(wrapper.classes()).toContain('sp-btn--hovered')

    await wrapper.trigger('mouseleave')

    expect(wrapper.classes()).not.toContain('sp-btn--hovered')
  })

  it('applies pressed state correctly', async () => {
    const wrapper = mount(Btn, {
      slots: {
        default: 'Press Button',
      },
    })

    await wrapper.trigger('mousedown')

    expect(wrapper.classes()).toContain('sp-btn--pressed')

    await wrapper.trigger('mouseup')

    expect(wrapper.classes()).not.toContain('sp-btn--pressed')
  })
})
