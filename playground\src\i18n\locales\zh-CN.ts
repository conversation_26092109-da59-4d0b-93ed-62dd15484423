export default {
  title: 'Speed UI 组件库',
  subtitle: '基于 Vue 3 + TypeScript + Vite 的组件库',
  
  // 导航
  nav: {
    components: '组件',
    button: '按钮',
    layout: '布局',
    input: '输入框',
    language: '语言',
  },

  // 按钮组件
  button: {
    title: 'Button 按钮',
    basicTypes: '基础类型（点击可切换激活状态）',
    basicTypesDesc: '使用 type 属性来定义按钮的样式',
    sizes: '不同尺寸',
    sizesDesc: '使用 size 属性额外配置尺寸',
    states: '特殊状态',
    statesDesc: '按钮的禁用状态、加载状态等',
    default: '默认按钮',
    outline: '轮廓按钮',
    secondary: '次要按钮',
    success: '成功按钮',
    warning: '警告按钮',
    danger: '危险按钮',
    small: '小号按钮',
    medium: '中号按钮',
    large: '大号按钮',
    disabled: '禁用按钮',
    loading: '加载中',
    notToggleable: '不可切换',
  },

  // 输入框组件
  input: {
    title: 'Input 输入框',
    basic: '基础用法',
    basicDesc: '基础的输入框，支持常用的输入类型',
    clearable: '可清空',
    clearableDesc: '使用 clearable 属性即可得到一个可清空的输入框',
    password: '密码框',
    passwordDesc: '使用 show-password 属性即可得到一个可切换显示隐藏的密码框',
    textarea: '文本域',
    textareaDesc: '用于输入多行文本信息',
    sizes: '尺寸',
    sizesDesc: '可通过 size 属性指定输入框的尺寸',
    maxlength: '输入长度限制',
    maxlengthDesc: '使用 maxlength 和 show-word-limit 属性可以限制输入长度',
    disabled: '禁用状态',
    disabledDesc: '通过 disabled 属性指定是否禁用输入框',
    placeholder: '请输入内容',
    placeholderPassword: '请输入密码',
    placeholderTextarea: '请输入多行文本',
    small: '小尺寸',
    default: '默认尺寸',
    large: '大尺寸',
    autosize: '自适应文本域',
    autosizeDesc: '设置 autosize 属性可以使文本域的高度自适应',
    validateStates: '输入验证',
    validateStatesDesc: '输入框支持不同的验证状态',
    success: '验证成功',
    warning: '验证警告',
    error: '验证失败',
    successMessage: '输入正确',
    warningMessage: '请注意格式',
    errorMessage: '输入有误，请重新输入',
  },

  // 布局组件
  layout: {
    title: 'Layout 布局',
    basic: '基础布局',
    basicDesc: '使用列创建基础网格布局',
    gutter: '分栏间隔',
    gutterDesc: '行提供 gutter 属性来指定列之间的间距',
    offset: '列偏移',
    offsetDesc: '支持偏移指定的列数',
    align: '对齐方式',
    alignDesc: '通过 justify 属性来对分栏进行灵活的对齐',
    responsive: '响应式布局',
    responsiveDesc: '参照了 Bootstrap 的响应式设计，预设了五个响应尺寸',
  },

  // 通用
  common: {
    clickToToggle: '点击可切换激活状态',
    demo: '演示',
    code: '代码',
    copy: '复制',
    copied: '已复制',
  },
} 