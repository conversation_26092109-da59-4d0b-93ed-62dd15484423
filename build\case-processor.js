const fs = require('fs')
const path = require('path')
const { marked } = require('marked')

/**
 * 处理 .case.vue 文件，将自定义标签转换为文档风格的展示组件
 */
class CaseProcessor {
  constructor() {
    // 组件源目录
    this.componentsDir = path.join(process.cwd(), 'packages/ui/src/components')
    // playground 输出目录
    this.playgroundDir = path.join(process.cwd(), 'playground/src/views/components')
    // 路由文件路径
    this.routerFile = path.join(process.cwd(), 'playground/src/router/components.ts')
  }

  /**
   * 解析 .case.vue 文件内容
   */
  parseCaseFile(content) {
    const result = {
      text: '',
      template: '',
      script: '',
      style: ''
    }

    // 提取 <text> 标签内容
    const textMatch = content.match(/<text>([\s\S]*?)<\/text>/)
    if (textMatch) {
      result.text = textMatch[1].trim()
    }

    // 提取 <case-ele> 标签内容
    const templateMatch = content.match(/<case-ele>([\s\S]*?)<\/case-ele>/)
    if (templateMatch) {
      result.template = templateMatch[1].trim()
    }

    // 提取 <script> 标签内容
    const scriptMatch = content.match(/<script>([\s\S]*?)<\/script>/)
    if (scriptMatch) {
      result.script = scriptMatch[1].trim()
    }

    // 提取 <style> 标签内容
    const styleMatch = content.match(/<style[^>]*>([\s\S]*?)<\/style>/)
    if (styleMatch) {
      result.style = styleMatch[1].trim()
    }

    return result
  }

  /**
   * 将 markdown 转换为 HTML
   */
  markdownToHtml(markdown) {
    return marked(markdown)
  }

  /**
   * 转义代码用于显示
   */
  escapeCode(code) {
    return code
      .replace(/\\/g, '\\\\')
      .replace(/'/g, "\\'")
      .replace(/"/g, '\\"')
      .replace(/\n/g, '\\n')
      .replace(/\r/g, '\\r')
      .replace(/\t/g, '\\t')
  }

  /**
   * 生成代码示例
   */
  generateCodeExamples(template, script) {
    // 处理脚本中的导入语句
    const processedScript = this.processImports(script)
    
    // TypeScript 版本
    const tsCode = `<template>
${template}
</template>

<script setup lang="ts">
${processedScript}
</script>`

    // JavaScript 版本  
    const jsCode = `<template>
${template}
</template>

<script setup>
${processedScript.replace(/: \w+/g, '').replace(/lang="ts"/g, '')}
</script>`

    return { tsCode, jsCode }
  }

  /**
   * 生成文档风格的 Vue 组件
   */
  generateDocComponent(parsed, componentInfo) {
    const { text, template, script, style } = parsed
    const { componentName, caseName } = componentInfo
    
    // 生成代码示例
    const { tsCode, jsCode } = this.generateCodeExamples(template, script)
    
    let vueContent = '<template>\n'
    vueContent += '  <div class="component-doc">\n'
    
    // 标题区域
    vueContent += '    <div class="doc-header">\n'
    vueContent += `      <h1>${caseName}</h1>\n`
    if (text) {
      vueContent += '      <div class="doc-description" v-html="description"></div>\n'
    }
    vueContent += '    </div>\n\n'
    
    // 演示区域
    if (template) {
      vueContent += '    <div class="doc-demo">\n'
      vueContent += '      <div class="demo-showcase">\n'
      vueContent += template.split('\n').map(line => '        ' + line).join('\n') + '\n'
      vueContent += '      </div>\n'
      vueContent += '      \n'
      vueContent += '      <div class="demo-actions">\n'
      vueContent += '        <button \n'
      vueContent += '          class="action-btn"\n'
      vueContent += '          :class="{ active: showCode }"\n'
      vueContent += '          @click="showCode = !showCode"\n'
      vueContent += '        >\n'
      vueContent += '          <svg class="icon" viewBox="0 0 24 24">\n'
      vueContent += '            <path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>\n'
      vueContent += '          </svg>\n'
      vueContent += '          {{ showCode ? "隐藏代码" : "显示代码" }}\n'
      vueContent += '        </button>\n'
      vueContent += '      </div>\n'
      vueContent += '      \n'
      vueContent += '      <div v-show="showCode" class="demo-code">\n'
      vueContent += '        <div class="code-tabs">\n'
      vueContent += '          <button \n'
      vueContent += '            class="tab-btn"\n'
      vueContent += '            :class="{ active: activeTab === \'typescript\' }"\n'
      vueContent += '            @click="activeTab = \'typescript\'"\n'
      vueContent += '          >\n'
      vueContent += '            TypeScript\n'
      vueContent += '          </button>\n'
      vueContent += '          <button \n'
      vueContent += '            class="tab-btn"\n'
      vueContent += '            :class="{ active: activeTab === \'javascript\' }"\n'
      vueContent += '            @click="activeTab = \'javascript\'"\n'
      vueContent += '          >\n'
      vueContent += '            JavaScript\n'
      vueContent += '          </button>\n'
      vueContent += '        </div>\n'
      vueContent += '        <div class="code-content">\n'
      vueContent += '          <pre v-show="activeTab === \'typescript\'"><code>{{ tsCode }}</code></pre>\n'
      vueContent += '          <pre v-show="activeTab === \'javascript\'"><code>{{ jsCode }}</code></pre>\n'
      vueContent += '        </div>\n'
      vueContent += '      </div>\n'
      vueContent += '    </div>\n'
    }
    
    vueContent += '  </div>\n'
    vueContent += '</template>\n\n'

    // Script 部分
    vueContent += '<script setup lang="ts">\n'
    vueContent += 'import { ref } from "vue"\n'
    
    // 添加组件导入
    if (script) {
      const processedScript = this.processImports(script)
      // 提取导入语句
      const importStatements = processedScript.match(/import\s+[^;]+;?/g)
      if (importStatements) {
        vueContent += importStatements.join('\n') + '\n'
      }
      vueContent += '\n'
    }
    
    if (text) {
      const htmlContent = this.markdownToHtml(text)
      const escapedHtml = this.escapeCode(htmlContent)
      vueContent += `const description = '${escapedHtml}'\n\n`
    }
    
    // 代码展示相关状态
    vueContent += 'const showCode = ref(false)\n'
    vueContent += 'const activeTab = ref("typescript")\n\n'
    
    // 代码内容
    const escapedTsCode = this.escapeCode(tsCode)
    const escapedJsCode = this.escapeCode(jsCode)
    vueContent += `const tsCode = \`${escapedTsCode}\`\n`
    vueContent += `const jsCode = \`${escapedJsCode}\`\n\n`
    
    // 添加其他脚本逻辑（排除导入语句）
    if (script) {
      const processedScript = this.processImports(script)
      const scriptWithoutImports = processedScript.replace(/import\s+[^;]+;?\n?/g, '').trim()
      if (scriptWithoutImports) {
        vueContent += scriptWithoutImports + '\n'
      }
    }
    vueContent += '</script>\n\n'

    // 样式部分
    vueContent += '<style scoped>\n'
    vueContent += '.component-doc {\n'
    vueContent += '  max-width: 1200px;\n'
    vueContent += '  margin: 0 auto;\n'
    vueContent += '  padding: 24px;\n'
    vueContent += '  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;\n'
    vueContent += '}\n\n'
    
    vueContent += '.doc-header {\n'
    vueContent += '  margin-bottom: 32px;\n'
    vueContent += '  padding-bottom: 24px;\n'
    vueContent += '  border-bottom: 1px solid #e5e7eb;\n'
    vueContent += '}\n\n'
    
    vueContent += '.doc-header h1 {\n'
    vueContent += '  margin: 0 0 16px 0;\n'
    vueContent += '  font-size: 32px;\n'
    vueContent += '  font-weight: 600;\n'
    vueContent += '  color: #1f2937;\n'
    vueContent += '}\n\n'
    
    vueContent += '.doc-description {\n'
    vueContent += '  color: #6b7280;\n'
    vueContent += '  line-height: 1.6;\n'
    vueContent += '}\n\n'
    
    vueContent += '.doc-demo {\n'
    vueContent += '  margin-bottom: 32px;\n'
    vueContent += '  border: 1px solid #e5e7eb;\n'
    vueContent += '  border-radius: 8px;\n'
    vueContent += '  overflow: hidden;\n'
    vueContent += '}\n\n'
    
    vueContent += '.demo-showcase {\n'
    vueContent += '  padding: 32px;\n'
    vueContent += '  background: #ffffff;\n'
    vueContent += '  border-bottom: 1px solid #e5e7eb;\n'
    vueContent += '}\n\n'
    
    vueContent += '.demo-actions {\n'
    vueContent += '  padding: 12px 16px;\n'
    vueContent += '  background: #fafafa;\n'
    vueContent += '  border-bottom: 1px solid #e5e7eb;\n'
    vueContent += '  display: flex;\n'
    vueContent += '  justify-content: center;\n'
    vueContent += '}\n\n'
    
    vueContent += '.action-btn {\n'
    vueContent += '  display: flex;\n'
    vueContent += '  align-items: center;\n'
    vueContent += '  gap: 6px;\n'
    vueContent += '  padding: 6px 12px;\n'
    vueContent += '  background: transparent;\n'
    vueContent += '  border: 1px solid #d1d5db;\n'
    vueContent += '  border-radius: 4px;\n'
    vueContent += '  color: #6b7280;\n'
    vueContent += '  font-size: 14px;\n'
    vueContent += '  cursor: pointer;\n'
    vueContent += '  transition: all 0.2s;\n'
    vueContent += '}\n\n'
    
    vueContent += '.action-btn:hover {\n'
    vueContent += '  color: #374151;\n'
    vueContent += '  border-color: #9ca3af;\n'
    vueContent += '}\n\n'
    
    vueContent += '.action-btn.active {\n'
    vueContent += '  color: #3b82f6;\n'
    vueContent += '  border-color: #3b82f6;\n'
    vueContent += '  background: #eff6ff;\n'
    vueContent += '}\n\n'
    
    vueContent += '.icon {\n'
    vueContent += '  width: 16px;\n'
    vueContent += '  height: 16px;\n'
    vueContent += '  fill: currentColor;\n'
    vueContent += '}\n\n'
    
    vueContent += '.demo-code {\n'
    vueContent += '  background: #f8fafc;\n'
    vueContent += '}\n\n'
    
    vueContent += '.code-tabs {\n'
    vueContent += '  display: flex;\n'
    vueContent += '  border-bottom: 1px solid #e5e7eb;\n'
    vueContent += '}\n\n'
    
    vueContent += '.tab-btn {\n'
    vueContent += '  padding: 12px 20px;\n'
    vueContent += '  background: transparent;\n'
    vueContent += '  border: none;\n'
    vueContent += '  color: #6b7280;\n'
    vueContent += '  font-size: 14px;\n'
    vueContent += '  cursor: pointer;\n'
    vueContent += '  border-bottom: 2px solid transparent;\n'
    vueContent += '  transition: all 0.2s;\n'
    vueContent += '}\n\n'
    
    vueContent += '.tab-btn:hover {\n'
    vueContent += '  color: #374151;\n'
    vueContent += '}\n\n'
    
    vueContent += '.tab-btn.active {\n'
    vueContent += '  color: #3b82f6;\n'
    vueContent += '  border-bottom-color: #3b82f6;\n'
    vueContent += '}\n\n'
    
    vueContent += '.code-content {\n'
    vueContent += '  position: relative;\n'
    vueContent += '}\n\n'
    
    vueContent += '.code-content pre {\n'
    vueContent += '  margin: 0;\n'
    vueContent += '  padding: 20px;\n'
    vueContent += '  background: #1e293b;\n'
    vueContent += '  color: #e2e8f0;\n'
    vueContent += '  font-family: "Fira Code", "Monaco", "Consolas", monospace;\n'
    vueContent += '  font-size: 14px;\n'
    vueContent += '  line-height: 1.5;\n'
    vueContent += '  overflow-x: auto;\n'
    vueContent += '}\n\n'
    
    vueContent += '.code-content code {\n'
    vueContent += '  color: inherit;\n'
    vueContent += '  background: transparent;\n'
    vueContent += '}\n\n'
    
    if (style) {
      vueContent += '/* 自定义样式 */\n'
      vueContent += style + '\n'
    }
    
    vueContent += '</style>\n'

    return vueContent
  }

  /**
   * 从文件路径提取组件信息
   */
  extractComponentInfo(filePath) {
    const relativePath = path.relative(this.componentsDir, filePath)
    const parts = relativePath.split(path.sep)
    
    const componentName = parts[0] // 例如: Button
    const fileName = path.basename(filePath, '.case.vue') // 例如: default
    const caseName = fileName.charAt(0).toUpperCase() + fileName.slice(1) // 例如: Default
    
    return {
      componentName,
      caseName,
      fileName,
      routePath: `/${componentName.toLowerCase()}/${fileName}`,
      routeName: `${componentName}${caseName}`
    }
  }

  /**
   * 处理单个 .case.vue 文件
   */
  processFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf-8')
      const parsed = this.parseCaseFile(content)
      const componentInfo = this.extractComponentInfo(filePath)
      const vueContent = this.generateDocComponent(parsed, componentInfo)
      
      // 生成输出文件路径
      const outputDir = path.join(this.playgroundDir, componentInfo.componentName)
      const outputPath = path.join(outputDir, `${componentInfo.fileName}.vue`)
      
      // 确保输出目录存在
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true })
      }
      
      // 写入生成的文件
      fs.writeFileSync(outputPath, vueContent)
      
      console.log(`✅ 已处理: ${path.relative(process.cwd(), filePath)} -> ${path.relative(process.cwd(), outputPath)}`)
      return componentInfo
    } catch (error) {
      console.error(`❌ 处理文件失败: ${path.relative(process.cwd(), filePath)}`, error.message)
      return null
    }
  }

  /**
   * 递归查找所有 .case.vue 文件
   */
  findCaseFiles(dir) {
    const files = []
    
    if (!fs.existsSync(dir)) {
      return files
    }
    
    const items = fs.readdirSync(dir)
    
    for (const item of items) {
      const fullPath = path.join(dir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory()) {
        files.push(...this.findCaseFiles(fullPath))
      } else if (item.endsWith('.case.vue')) {
        files.push(fullPath)
      }
    }
    
    return files
  }

  /**
   * 生成路由配置文件
   */
  generateRoutes(componentInfos) {
    let routeContent = '// 自动生成的组件路由配置\n'
    routeContent += '// 请勿手动编辑此文件\n\n'
    
    // 导入语句
    const imports = []
    const routes = []
    
    // 按组件分组
    const componentGroups = {}
    componentInfos.forEach(info => {
      if (!componentGroups[info.componentName]) {
        componentGroups[info.componentName] = []
      }
      componentGroups[info.componentName].push(info)
    })
    
    // 生成导入和路由
    Object.entries(componentGroups).forEach(([componentName, cases]) => {
      cases.forEach(info => {
        imports.push(`import ${info.routeName} from '../views/components/${componentName}/${info.fileName}.vue'`)
        routes.push({
          path: info.routePath,
          name: info.routeName,
          component: info.routeName,
          meta: {
            title: `${componentName} - ${info.caseName}`,
            componentName,
            caseName: info.caseName
          }
        })
      })
    })
    
    routeContent += imports.join('\n') + '\n\n'
    routeContent += 'export const componentRoutes = [\n'
    routes.forEach(route => {
      routeContent += `  {\n`
      routeContent += `    path: '${route.path}',\n`
      routeContent += `    name: '${route.name}',\n`
      routeContent += `    component: ${route.component},\n`
      routeContent += `    meta: ${JSON.stringify(route.meta, null, 6).replace(/\n/g, '\n    ')}\n`
      routeContent += `  },\n`
    })
    routeContent += ']\n\n'
    
    // 生成组件导航数据
    routeContent += 'export const componentNavigation = [\n'
    Object.entries(componentGroups).forEach(([componentName, cases]) => {
      routeContent += `  {\n`
      routeContent += `    name: '${componentName}',\n`
      routeContent += `    cases: [\n`
      cases.forEach(info => {
        routeContent += `      {\n`
        routeContent += `        name: '${info.caseName}',\n`
        routeContent += `        path: '${info.routePath}',\n`
        routeContent += `        routeName: '${info.routeName}'\n`
        routeContent += `      },\n`
      })
      routeContent += `    ]\n`
      routeContent += `  },\n`
    })
    routeContent += ']\n'
    
    return routeContent
  }

  /**
   * 处理所有 .case.vue 文件
   */
  processAll() {
    console.log('🚀 开始处理 .case.vue 文件...')
    
    const caseFiles = this.findCaseFiles(this.componentsDir)
    
    if (caseFiles.length === 0) {
      console.log('📝 没有找到 .case.vue 文件')
      return
    }
    
    console.log(`📁 找到 ${caseFiles.length} 个 .case.vue 文件`)
    
    const componentInfos = []
    let successCount = 0
    
    for (const file of caseFiles) {
      const info = this.processFile(file)
      if (info) {
        componentInfos.push(info)
        successCount++
      }
    }
    
    // 生成路由配置
    if (componentInfos.length > 0) {
      const routeContent = this.generateRoutes(componentInfos)
      
      // 确保路由目录存在
      const routeDir = path.dirname(this.routerFile)
      if (!fs.existsSync(routeDir)) {
        fs.mkdirSync(routeDir, { recursive: true })
      }
      
      fs.writeFileSync(this.routerFile, routeContent)
      console.log(`📝 已生成路由配置: ${path.relative(process.cwd(), this.routerFile)}`)
    }
    
    console.log(`✨ 处理完成! 成功: ${successCount}/${caseFiles.length}`)
  }

  /**
   * 处理脚本中的导入语句，将相对路径改为包导入
   */
  processImports(script) {
    if (!script) return script
    
    // 将相对路径的组件导入改为从 @speed-ui/ui 包导入
    const processedScript = script.replace(
      /import\s+(\w+)\s+from\s+['"]\.\.\/(\w+)\.vue['"]/g,
      'import { $1 } from "@speed-ui/ui"'
    )
    
    return processedScript
  }
}

module.exports = CaseProcessor

// 如果直接运行此文件
if (require.main === module) {
  const processor = new CaseProcessor()
  processor.processAll()
} 