# InputCore Props Factory 重构

## 🎯 重构目标

将 InputCore 组件的 props 定义重构为使用 `propsFactory` 模式，提高代码复用性和类型安全性。

## 🔧 重构前后对比

### 重构前
```typescript
// inputcore.tsx - 冗长的 props 定义
export default defineComponent({
  name: 'InputCore',
  props: {
    value: {
      type: [String, Number],
      default: '',
    },
    type: {
      type: String as PropType<InputType>,
      default: 'text',
    },
    placeholder: {
      type: String,
      default: '',
    },
    // ... 40+ 行的 props 定义
  },
})
```

### 重构后
```typescript
// inputCoreProps.ts - 可复用的 props 工厂
export const makeInputCoreProps = propsFactory({
  value: [String, Number],
  type: String as PropType<InputType>,
  placeholder: String,
  // ... 简洁的类型定义
}, 'InputCore')

// inputcore.tsx - 简洁的组件定义
export default defineComponent({
  name: 'InputCore',
  props: createInputCoreProps(),
})
```

## 🎨 使用方式

### 1. 基础使用
```typescript
import { createInputCoreProps } from './inputCoreProps'

export default defineComponent({
  props: createInputCoreProps(),
})
```

### 2. 覆盖默认值
```typescript
export default defineComponent({
  props: createInputCoreProps({
    clearable: true,      // 覆盖默认的 false
    showWordLimit: true,  // 覆盖默认的 false
    size: 'large',        // 覆盖默认的 'medium'
  }),
})
```

### 3. 扩展 Props
```typescript
const makeInputProps = () => ({
  // 继承 InputCore 的所有 props
  ...makeInputCoreProps(),
  
  // 添加新的 props
  modelValue: String,
  label: String,
  helperText: String,
})

export default defineComponent({
  props: makeInputProps(),
})
```

### 4. 选择性继承
```typescript
const makeTextareaProps = () => {
  const { type, ...inputCoreProps } = makeInputCoreProps()
  
  return {
    ...inputCoreProps,  // 继承除 type 外的所有 props
    rows: Number,
    cols: Number,
    autosize: Boolean,
  }
}
```

## 💡 优势

### 1. **代码复用**
- ✅ 多个组件可以共享相同的 props 定义
- ✅ 避免重复代码，减少维护成本
- ✅ 统一的 props 接口，保持一致性

### 2. **类型安全**
- ✅ 完整的 TypeScript 类型推导
- ✅ 编译时类型检查
- ✅ IDE 智能提示和自动补全

### 3. **灵活性**
- ✅ 可以轻松覆盖默认值
- ✅ 可以选择性继承 props
- ✅ 可以扩展新的 props

### 4. **维护性**
- ✅ 集中管理 props 定义
- ✅ 修改一处，影响所有使用的组件
- ✅ 更好的代码组织结构

## 🏗️ 架构设计

```
utils/
├── propsFactory.ts          # 通用 props 工厂函数

components/input/
├── inputCoreProps.ts        # InputCore props 定义
├── inputcore.tsx            # InputCore 组件实现
├── inputExample.vue         # Input 组件示例
└── types.ts                 # 类型定义

components/textarea/
├── textareaExample.vue      # Textarea 组件示例（复用 InputCore props）
└── textarea.vue             # 现有 Textarea 组件
```

## 🔄 迁移指南

### 对于现有组件
1. 现有的 InputCore 组件保持向后兼容
2. 新组件建议使用 props 工厂模式
3. 逐步迁移现有组件到新模式

### 对于新组件
1. 优先使用 `makeInputCoreProps()` 继承基础 props
2. 根据需要覆盖默认值
3. 添加组件特有的 props

## 📝 最佳实践

1. **命名规范**
   - Props 工厂：`makeXxxProps`
   - 创建函数：`createXxxProps`
   - 默认值：`xxxDefaults`

2. **类型定义**
   - 导出所有相关类型
   - 使用 `PropType` 进行类型约束
   - 提供完整的 TypeScript 支持

3. **文档说明**
   - 为每个 props 工厂添加注释
   - 提供使用示例
   - 说明继承关系

## 🚀 未来扩展

1. **更多组件支持**
   - Select 组件 props 工厂
   - Button 组件 props 工厂
   - Form 组件 props 工厂

2. **工具函数增强**
   - 支持 props 验证
   - 支持条件 props
   - 支持 props 转换

3. **开发工具**
   - Props 可视化工具
   - 自动生成 props 文档
   - Props 兼容性检查
