import {
  defineComponent,
  computed,
  ref,
  type PropType,
  type VNode,
  type CSSProperties,
} from 'vue'
import SpIcon from '../icon/Icon.vue'

// JSX 类型声明 - 确保 Vue JSX 正常工作
declare global {
  namespace JSX {
    interface IntrinsicElements {
      div: any
      span: any
    }
  }
}

// 基础输入框的渲染模式
export type InputBaseMode = 'input' | 'select' | 'display' | 'custom'

// 基础输入框属性接口
export interface InputBaseProps {
  // 基础属性
  value?: string | number | Array<string | number>
  mode?: InputBaseMode

  // 样式属性
  variant?: 'default' | 'underlined' | 'filled' | 'pill' | 'square' | 'unborder'
  effect?: 'none' | 'glow'
  size?: 'small' | 'medium' | 'large'

  // 状态属性
  disabled?: boolean
  readonly?: boolean
  focused?: boolean
  loading?: boolean
  error?: boolean

  // 功能属性
  clearable?: boolean
  prefixIcon?: string
  suffixIcon?: string
  placeholder?: string

  // 验证相关
  validateState?: 'success' | 'warning' | 'error' | ''
  validateMessage?: string

  // 自定义样式
  wrapperStyle?: CSSProperties
  innerStyle?: CSSProperties

  // 特殊标识
  hasValue?: boolean
  dropdownVisible?: boolean // 用于 select 类组件
  multiple?: boolean // 用于多选组件

  // 样式类名（从外部传入）
  rootClasses?: any[]
  wrapperClasses?: any[]
  innerClasses?: any[]
  prefixClasses?: any[]
  suffixClasses?: any[]
  iconSize?: number
}

// 基础输入框事件接口
export interface InputBaseEmits {
  click: [event: MouseEvent]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
  clear: []
  'prefix-click': [event: MouseEvent]
  'suffix-click': [event: MouseEvent]
}

export default defineComponent({
  name: 'InputBase',
  props: {
    value: {
      type: [String, Number, Array] as PropType<
        string | number | Array<string | number>
      >,
      default: undefined,
    },
    mode: {
      type: String as PropType<InputBaseMode>,
      default: 'input',
    },
    variant: {
      type: String as PropType<
        'default' | 'underlined' | 'filled' | 'pill' | 'square' | 'unborder'
      >,
      default: 'default',
    },
    effect: {
      type: String as PropType<'none' | 'glow'>,
      default: 'none',
    },
    size: {
      type: String as PropType<'small' | 'medium' | 'large'>,
      default: 'medium',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    focused: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    error: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    prefixIcon: {
      type: String,
      default: undefined,
    },
    suffixIcon: {
      type: String,
      default: undefined,
    },
    placeholder: {
      type: String,
      default: '',
    },
    validateState: {
      type: String as PropType<'success' | 'warning' | 'error' | ''>,
      default: '',
    },
    validateMessage: {
      type: String,
      default: '',
    },
    wrapperStyle: {
      type: Object as PropType<CSSProperties>,
      default: () => ({}),
    },
    innerStyle: {
      type: Object as PropType<CSSProperties>,
      default: () => ({}),
    },
    hasValue: {
      type: Boolean,
      default: false,
    },
    dropdownVisible: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    // 样式类名（从外部传入）
    rootClasses: {
      type: Array,
      default: () => [],
    },
    wrapperClasses: {
      type: Array,
      default: () => [],
    },
    innerClasses: {
      type: Array,
      default: () => [],
    },
    prefixClasses: {
      type: Array,
      default: () => [],
    },
    suffixClasses: {
      type: Array,
      default: () => [],
    },
    iconSize: {
      type: Number,
      default: 16,
    },
  },
  emits: {
    click: (_event: MouseEvent) => true,
    focus: (_event: FocusEvent) => true,
    blur: (_event: FocusEvent) => true,
    clear: () => true,
    'prefix-click': (_event: MouseEvent) => true,
    'suffix-click': (_event: MouseEvent) => true,
  },
  setup(props, { emit, slots, expose }) {
    const wrapperRef = ref<HTMLDivElement>()
    const innerRef = ref<HTMLElement>()

    // 显示前缀区域
    const showPrefix = computed(() => {
      return !!props.prefixIcon || !!slots.prefix
    })

    // 显示后缀区域
    const showSuffix = computed(() => {
      return (
        !!props.suffixIcon ||
        !!slots.suffix ||
        (props.clearable && props.hasValue && !props.disabled) ||
        props.mode === 'select'
      )
    })

    // 显示清除按钮
    const showClearIcon = computed(() => {
      return (
        props.clearable && props.hasValue && !props.disabled && !props.readonly
      )
    })

    // 事件处理
    const handleWrapperClick = (event: MouseEvent) => {
      if (props.disabled || props.readonly) return
      emit('click', event)
    }

    const handlePrefixClick = (event: MouseEvent) => {
      event.stopPropagation()
      emit('prefix-click', event)
    }

    const handleSuffixClick = (event: MouseEvent) => {
      event.stopPropagation()
      emit('suffix-click', event)
    }

    const handleClear = (event: MouseEvent) => {
      event.stopPropagation()
      emit('clear')
    }

    const handleFocus = (event: FocusEvent) => {
      emit('focus', event)
    }

    const handleBlur = (event: FocusEvent) => {
      emit('blur', event)
    }

    // 渲染前缀区域
    const renderPrefix = (): VNode | null => {
      if (!showPrefix.value) return null

      return (
        <div
          class={props.prefixClasses}
          onClick={handlePrefixClick}
        >
          {props.prefixIcon && (
            <SpIcon
              name={props.prefixIcon}
              size={props.iconSize}
              class="prefix-icon"
            />
          )}
          {slots.prefix?.()}
        </div>
      )
    }

    // 渲染后缀区域
    const renderSuffix = (): VNode | null => {
      if (!showSuffix.value) return null

      return (
        <div
          class={props.suffixClasses}
          onClick={handleSuffixClick}
        >
          {/* 清除按钮 */}
          {showClearIcon.value && (
            <SpIcon
              name="CloseCircle"
              size={props.iconSize}
              class="clear-icon"
              onClick={handleClear}
            />
          )}

          {/* 加载状态 */}
          {props.loading && (
            <SpIcon
              name="Loading"
              size={props.iconSize}
              class="loading-icon"
            />
          )}

          {/* 下拉箭头（用于 select 类组件） */}
          {props.mode === 'select' && (
            <SpIcon
              name="ChevronDown"
              size={props.iconSize}
              class={['arrow-icon', { 'arrow-reverse': props.dropdownVisible }]}
            />
          )}

          {/* 自定义后缀图标 */}
          {props.suffixIcon && !props.loading && (
            <SpIcon
              name={props.suffixIcon}
              size={props.iconSize}
              class="suffix-icon"
            />
          )}

          {/* 后缀插槽 */}
          {slots.suffix?.()}
        </div>
      )
    }

    // 渲染内容区域
    const renderInner = (): VNode => {
      const innerProps = {
        class: props.innerClasses,
        style: props.innerStyle,
        onFocus: handleFocus,
        onBlur: handleBlur,
      }

      // 如果有自定义内容插槽，使用插槽
      if (slots.inner) {
        return (
          <div {...innerProps}>
            {slots.inner({
              props,
              disabled: props.disabled,
              readonly: props.readonly,
              placeholder: props.placeholder,
              focused: props.focused,
            })}
          </div>
        )
      }

      // 根据模式渲染不同内容
      switch (props.mode) {
        case 'input':
          // 标准输入框模式 - 由具体组件实现
          return <div {...innerProps}>{slots.default?.()}</div>

        case 'select':
          // 选择器模式 - 显示选中内容
          return (
            <div {...innerProps}>
              {slots.default?.() || (
                <span class="placeholder">{props.placeholder}</span>
              )}
            </div>
          )

        case 'display':
          // 纯展示模式
          return (
            <div {...innerProps}>
              {slots.default?.() || props.value || (
                <span class="placeholder">{props.placeholder}</span>
              )}
            </div>
          )

        case 'custom':
          // 完全自定义模式
          return <div {...innerProps}>{slots.default?.()}</div>

        default:
          return <div {...innerProps}>{slots.default?.()}</div>
      }
    }

    // 暴露的方法和属性
    expose({
      wrapperRef,
      innerRef,
      focus: () => {
        const focusable = wrapperRef.value?.querySelector(
          'input, textarea, [tabindex]'
        ) as HTMLElement
        focusable?.focus()
      },
      blur: () => {
        const focusable = wrapperRef.value?.querySelector(
          'input, textarea, [tabindex]'
        ) as HTMLElement
        focusable?.blur()
      },
    })

    // 主渲染函数
    return () => (
      <div class={props.rootClasses}>
        <div
          ref={wrapperRef}
          class={props.wrapperClasses}
          style={props.wrapperStyle}
          onClick={handleWrapperClick}
        >
          {renderPrefix()}
          {renderInner()}
          {renderSuffix()}
        </div>

        {/* 加载进度条 */}
        {props.loading && (
          <div class="loading-bar">
            <div class="loading-progress" />
          </div>
        )}

        {/* 验证消息 */}
        {slots.message?.() ||
          (props.validateMessage && (
            <div
              class={['message', `message--${props.validateState || 'error'}`]}
            >
              {props.validateMessage}
            </div>
          ))}
      </div>
    )
  },
})
