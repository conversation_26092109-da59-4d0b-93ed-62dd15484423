# Speed UI 开发规范与 AI 辅助规则

## 🎯 项目概述

Speed UI 是一个基于 Vue 3 + TypeScript 的现代化组件库，采用 Monorepo 架构。

## 📋 AI 辅助开发规则

### 1. **代码生成规范**

#### 组件开发

- **必须使用** `<script setup lang="ts">` 语法
- **必须提供** 完整的 TypeScript 类型定义
- **必须使用** BEM 命名规范（通过 `@speed-ui/bem-helper`）
- **必须包含** Props 接口和 Emits 接口定义
- **组件前缀**: 所有组件类名以 `sp-` 开头
- **主题支持**: 所有组件必须支持主题切换功能

#### 样式开发规范

- **样式文件位置**: 所有样式必须写在 `packages/theme-default/src/` 目录下
- **组件样式文件**: `packages/theme-default/src/{component-name}.scss`
- **主题变量文件**: `packages/theme-default/src/common/var.scss`
- **样式导入**: 组件 Vue 文件中不写具体样式，只保留必要的 scoped 样式

#### 文件结构

```
packages/ui/src/components/{component-name}/
├── index.ts                 # 导出文件
├── {component-name}.vue     # 主组件文件
├── {component-name}.ts      # 类型定义
├── README.md               # 组件文档
└── __tests__/              # 测试文件
    ├── {component-name}.test.ts
    └── {component-name}-class-builder.test.ts (如适用)
```

#### 类名生成规则

- **基础类名**: `sp-{component}`
- **元素类名**: `sp-{component}__{element}`
- **修饰符类名**: `sp-{component}--{modifier}`
- **状态类名**: `sp-{component}--{state}`
- **主题类名**: `sp-{component}-custom` (通过 `bem.s('custom')` 生成)
- **优先使用** ButtonClassBuilder 模式进行类名管理

#### 主题切换系统

- **主题变量**: 使用 CSS 自定义属性 (CSS Variables) 实现主题切换
- **主色调变量**: `--sp-color-primary`, `--sp-color-primary-hover`, `--sp-color-primary-active` 等
- **主题类名**: 每个组件都必须包含 `sp-{component}-custom` 类名
- **主题生成**: 通过 `bem.s('custom')` 方法生成主题类名
- **全局主题**: 通过修改 `:root` 中的 CSS 变量实现全局主题切换
- **组件主题**: 每个组件的主题样式都应该响应主色调变量的变化

### 2. **代码风格规范**

#### HTML 模板

- **每个属性独占一行** (`singleAttributePerLine: true`)
- **使用双引号** 包裹属性值
- **v-if/v-show** 指令优先放在第一行
- **事件处理器** 使用 `@` 语法

#### TypeScript

- **使用接口** 而非 type 定义复杂类型
- **导出类型** 必须明确标注 `export type` 或 `export interface`
- **枚举类型** 优先使用 union types
- **必须提供** JSDoc 注释

#### CSS

- **使用 scoped 样式**
- **CSS 变量** 命名格式：`--sp-{component}-{property}`
- **BEM 选择器** 严格遵循规范
- **响应式设计** 优先考虑

### 3. **测试规范**

#### 单元测试

- **必须使用** Vitest 框架
- **测试覆盖率** 目标 > 80%
- **测试文件** 必须包含组件的所有 props 和 emits
- **类名构建器** 必须有独立测试

#### 测试结构

```typescript
describe('ComponentName', () => {
  describe('基础功能', () => {
    // 基础渲染测试
  })

  describe('Props 测试', () => {
    // 各种 props 组合测试
  })

  describe('事件测试', () => {
    // 事件触发和参数测试
  })

  describe('状态测试', () => {
    // 组件状态变化测试
  })
})
```

### 4. **文档规范**

#### README.md 结构

```markdown
# ComponentName 组件

## 概述

简要描述组件功能

## 基础用法

基本使用示例

## API

### Props

### Events

### Slots

## 示例

各种使用场景的示例

## 样式定制

CSS 变量和主题定制说明
```

### 5. **AI 代码生成指令**

#### 创建新组件时

1. 生成完整的文件结构
2. 包含类型定义和接口
3. 实现 BEM 类名系统
4. 添加基础测试文件
5. 创建组件文档

#### 优化现有组件时

1. 保持向后兼容性
2. 优先优化类名生成逻辑
3. 增强类型安全
4. 补充测试覆盖
5. 更新文档

#### 代码审查要点

1. TypeScript 类型完整性
2. BEM 命名规范遵循
3. 测试覆盖率
4. 文档完整性
5. 性能考虑

### 6. **禁止操作**

#### 绝对禁止

- ❌ 直接修改 package.json 依赖（必须使用包管理器）
- ❌ 破坏现有组件的 API 兼容性
- ❌ 使用内联样式替代 CSS 类
- ❌ 忽略 TypeScript 类型检查
- ❌ 跳过测试编写

#### 谨慎操作

- ⚠️ 修改核心 BEM 辅助工具
- ⚠️ 更改构建配置
- ⚠️ 修改全局样式变量
- ⚠️ 重构公共工具函数

### 7. **性能优化规则**

#### 组件性能

- 使用 `computed` 进行复杂计算
- 避免在模板中使用复杂表达式
- 合理使用 `v-memo` 和 `v-once`
- 类名生成使用构建器模式

#### 包体积优化

- 按需导入第三方库
- 使用 Tree Shaking 友好的导出方式
- CSS 使用变量减少重复代码

### 8. **版本管理规则**

#### 语义化版本

- **MAJOR**: 破坏性 API 变更
- **MINOR**: 新功能添加（向后兼容）
- **PATCH**: Bug 修复

#### 变更日志

- 每次发布必须更新 CHANGELOG.md
- 使用 Changesets 管理版本发布
- 重要变更必须提供迁移指南

### 9. **主题系统详细规范**

#### 主题变量命名规范

```scss
// 主色调系统 (必须支持动态切换)
:root {
  --sp-color-primary: #667eea; // 主色调
  --sp-color-primary-hover: #7c8aeb; // 悬停色
  --sp-color-primary-active: #5a6ce8; // 激活色
  --sp-color-primary-disabled: #b8c5ff; // 禁用色
  --sp-color-primary-lightest: #eef2ff; // 最浅色 (hover背景)
  --sp-color-primary-light: #d6e0ff; // 浅色 (active背景)
}
```

#### 组件主题实现模式

```vue
<!-- 组件模板中必须包含主题类名 -->
<template>
  <button :class="buttonClassName">
    <!-- 内容 -->
  </button>
</template>

<script setup>
  // 类名生成器中必须包含主题类名
  const buttonClassName = computed(() => {
    const classes = [
      bem.b(), // sp-button
      bem.s('custom'), // sp-button-custom (主题类名)
      // 其他类名...
    ]
    return classes
  })
</script>
```

#### 主题样式文件结构

```scss
// packages/theme-default/src/{component}.scss
.sp-{component} {
  // 基础样式
}

// 主题响应样式 (必须包含)
.sp-{component}-custom {
  // 使用主题变量的样式
  --{component}-primary-color: var(--sp-color-primary);
  --{component}-hover-color: var(--sp-color-primary-hover);
  --{component}-active-color: var(--sp-color-primary-active);
}

// 变体样式中使用主题变量
.sp-{component}--primary.sp-{component}-custom {
  background-color: var(--{component}-primary-color);
  border-color: var(--{component}-primary-color);

  &:hover {
    background-color: var(--{component}-hover-color);
    border-color: var(--{component}-hover-color);
  }
}
```

#### 主题切换实现

- **全局主题切换**: 通过 JavaScript 动态修改 `:root` 中的 CSS 变量
- **组件级主题**: 每个组件都应该响应全局主题变量的变化
- **主题预设**: 提供多套预设主题 (如: 蓝色、绿色、紫色等)
- **自定义主题**: 支持用户自定义主色调

#### 主题类名使用规则

- **必须添加**: 每个组件的类名数组中必须包含 `bem.s('custom')`
- **样式响应**: 所有与主色调相关的样式都应该在 `-custom` 类名下定义
- **变量引用**: 使用 `var(--sp-color-primary)` 等变量而非硬编码颜色
- **兼容性**: 确保在没有主题类名时组件仍能正常显示

## 🔧 使用方式

将此规则文件放在项目根目录的 `.augment/` 文件夹中，AI 助手会参考这些规则进行代码生成和优化建议。

## 📝 规则更新

此规则文件应该随着项目发展不断更新和完善，确保始终反映最新的开发实践和项目需求。
