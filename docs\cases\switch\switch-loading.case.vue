<text>
> 添加loading状态, 通过 button-icon 可以修改loading图标
</text>

<template>
    <sp-switch v-model:value="value1" loading/>
    <sp-switch v-model:value="value1" :button-icon="Close" loading/>
    <sp-switch v-model:value="value1" :button-icon="SyncOutline" loading/>
    <sp-switch v-model:value="value1" :button-icon="RefreshOutline" loading/>
</template>

<script setup>
    import { Close, SyncOutline, RefreshOutline } from '@vicons/ionicons5'
    import { ref } from 'vue'
    const value1 = ref(true)
</script>