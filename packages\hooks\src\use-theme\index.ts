/**
 * 主题相关的组合式函数
 * 统一管理主题状态和操作
 */

import { ref, computed, watch, type Ref } from 'vue'

/**
 * 主题配置接口
 */
export interface ThemeConfig {
  /** 主色调 */
  primaryColor?: string
  /** 成功色 */
  successColor?: string
  /** 警告色 */
  warningColor?: string
  /** 危险色 */
  dangerColor?: string
  /** 信息色 */
  infoColor?: string
  /** 边框圆角 */
  borderRadius?: string
  /** 字体大小 */
  fontSize?: string
  /** 字体家族 */
  fontFamily?: string
}

/**
 * 预设主题
 */
export const PRESET_THEMES = {
  default: {
    primaryColor: '#667eea',
    successColor: '#67c23a',
    warningColor: '#e6a23c',
    dangerColor: '#f56c6c',
    infoColor: '#909399',
  },
  blue: {
    primaryColor: '#1890ff',
    successColor: '#52c41a',
    warningColor: '#faad14',
    dangerColor: '#ff4d4f',
    infoColor: '#909399',
  },
  green: {
    primaryColor: '#52c41a',
    successColor: '#52c41a',
    warningColor: '#faad14',
    dangerColor: '#ff4d4f',
    infoColor: '#909399',
  },
  red: {
    primaryColor: '#ff4d4f',
    successColor: '#52c41a',
    warningColor: '#faad14',
    dangerColor: '#ff4d4f',
    infoColor: '#909399',
  },
  orange: {
    primaryColor: '#fa8c16',
    successColor: '#52c41a',
    warningColor: '#faad14',
    dangerColor: '#ff4d4f',
    infoColor: '#909399',
  },
  purple: {
    primaryColor: '#722ed1',
    successColor: '#52c41a',
    warningColor: '#faad14',
    dangerColor: '#ff4d4f',
    infoColor: '#909399',
  },
} as const

export type PresetThemeName = keyof typeof PRESET_THEMES

/**
 * 主题管理器
 */
export class ThemeManager {
  private currentTheme: Ref<ThemeConfig>
  private storageKey = 'speed-ui-theme'

  constructor(initialTheme: ThemeConfig = PRESET_THEMES.default) {
    this.currentTheme = ref(this.loadThemeFromStorage() || initialTheme)
    
    // 监听主题变化并保存到本地存储
    watch(
      this.currentTheme,
      (newTheme) => {
        this.applyTheme(newTheme)
        this.saveThemeToStorage(newTheme)
      },
      { immediate: true, deep: true }
    )
  }

  /**
   * 获取当前主题
   */
  get theme() {
    return this.currentTheme
  }

  /**
   * 设置主题
   */
  setTheme(theme: ThemeConfig | PresetThemeName) {
    if (typeof theme === 'string') {
      this.currentTheme.value = { ...PRESET_THEMES[theme] }
    } else {
      this.currentTheme.value = { ...this.currentTheme.value, ...theme }
    }
  }

  /**
   * 设置主色调
   */
  setPrimaryColor(color: string) {
    this.currentTheme.value = {
      ...this.currentTheme.value,
      primaryColor: color,
    }
  }

  /**
   * 重置为默认主题
   */
  resetTheme() {
    this.setTheme('default')
  }

  /**
   * 应用主题到 CSS 变量
   */
  private applyTheme(theme: ThemeConfig) {
    const root = document.documentElement
    
    if (theme.primaryColor) {
      const primary = theme.primaryColor
      root.style.setProperty('--sp-color-primary', primary)
      root.style.setProperty('--sp-color-primary-hover', this.lightenColor(primary, 0.1))
      root.style.setProperty('--sp-color-primary-active', this.darkenColor(primary, 0.1))
      root.style.setProperty('--sp-color-primary-disabled', this.lightenColor(primary, 0.4))
      root.style.setProperty('--sp-color-primary-lightest', this.lightenColor(primary, 0.8))
      root.style.setProperty('--sp-color-primary-light', this.lightenColor(primary, 0.6))
    }
    
    if (theme.successColor) {
      root.style.setProperty('--sp-color-success', theme.successColor)
    }
    
    if (theme.warningColor) {
      root.style.setProperty('--sp-color-warning', theme.warningColor)
    }
    
    if (theme.dangerColor) {
      root.style.setProperty('--sp-color-danger', theme.dangerColor)
    }
    
    if (theme.infoColor) {
      root.style.setProperty('--sp-color-info', theme.infoColor)
    }
    
    if (theme.borderRadius) {
      root.style.setProperty('--sp-border-radius', theme.borderRadius)
    }
    
    if (theme.fontSize) {
      root.style.setProperty('--sp-font-size', theme.fontSize)
    }
    
    if (theme.fontFamily) {
      root.style.setProperty('--sp-font-family', theme.fontFamily)
    }
  }

  /**
   * 保存主题到本地存储
   */
  private saveThemeToStorage(theme: ThemeConfig) {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(theme))
    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error)
    }
  }

  /**
   * 从本地存储加载主题
   */
  private loadThemeFromStorage(): ThemeConfig | null {
    try {
      const stored = localStorage.getItem(this.storageKey)
      return stored ? JSON.parse(stored) : null
    } catch (error) {
      console.warn('Failed to load theme from localStorage:', error)
      return null
    }
  }

  /**
   * 颜色处理工具
   */
  private lightenColor(color: string, amount: number): string {
    const hex = color.replace('#', '')
    const num = parseInt(hex, 16)
    const r = Math.min(255, Math.floor((num >> 16) + 255 * amount))
    const g = Math.min(255, Math.floor(((num >> 8) & 0x00ff) + 255 * amount))
    const b = Math.min(255, Math.floor((num & 0x0000ff) + 255 * amount))
    return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`
  }

  private darkenColor(color: string, amount: number): string {
    const hex = color.replace('#', '')
    const num = parseInt(hex, 16)
    const r = Math.max(0, Math.floor((num >> 16) * (1 - amount)))
    const g = Math.max(0, Math.floor(((num >> 8) & 0x00ff) * (1 - amount)))
    const b = Math.max(0, Math.floor((num & 0x0000ff) * (1 - amount)))
    return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`
  }
}

/**
 * 全局主题管理器实例
 */
export const globalThemeManager = new ThemeManager()

/**
 * 使用主题的组合式函数
 */
export function useTheme() {
  return {
    theme: globalThemeManager.theme,
    setTheme: globalThemeManager.setTheme.bind(globalThemeManager),
    setPrimaryColor: globalThemeManager.setPrimaryColor.bind(globalThemeManager),
    resetTheme: globalThemeManager.resetTheme.bind(globalThemeManager),
    presetThemes: PRESET_THEMES,
  }
}

/**
 * 便捷函数
 */
export const setTheme = (theme: ThemeConfig | PresetThemeName) => {
  globalThemeManager.setTheme(theme)
}

export const setPrimaryColor = (color: string) => {
  globalThemeManager.setPrimaryColor(color)
}

export const resetTheme = () => {
  globalThemeManager.resetTheme()
}
