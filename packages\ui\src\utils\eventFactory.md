# Event Factory 事件处理工厂

## 🎯 概述

`eventFactory` 是一个强大的事件处理工厂函数集合，用于简化和标准化 Vue 组件中的事件处理逻辑。

## 🔧 核心功能

### 1. 基础事件处理器

```typescript
import { createEventHandler } from '@/utils/eventFactory'

// 简单的事件处理器
const handleClick = createEventHandler(emit, 'click')

// 带选项的事件处理器
const handleSubmit = createEventHandler(emit, 'submit', {
  preventDefault: true,
  stopPropagation: true,
})
```

### 2. 状态检查事件处理器

```typescript
import { createStatefulEventHandler } from '@/utils/eventFactory'

// 自动检查 disabled 和 readonly 状态
const handleClick = createStatefulEventHandler(emit, 'click', props, {
  checkDisabled: true,
  checkReadonly: true,
})
```

### 3. 防抖和节流

```typescript
// 防抖事件处理器（适用于搜索输入）
const handleSearch = createEventHandler(emit, 'search', {
  debounce: 300, // 300ms 防抖
})

// 节流事件处理器（适用于滚动事件）
const handleScroll = createEventHandler(emit, 'scroll', {
  throttle: 100, // 100ms 节流
})
```

## 🎨 预设事件处理器

### 1. 基础输入框事件处理器

```typescript
import { createInputEventHandlers } from '@/utils/eventFactory'

export default defineComponent({
  setup(props, { emit }) {
    const inputRef = ref<HTMLInputElement>()
    
    // 一次性创建所有输入框事件处理器
    const {
      handleWrapperClick,
      handleFocus,
      handleBlur,
      handleInput,
      handleChange,
      handleKeydown,
      handlePrefixClick,
      handleSuffixClick,
      handleClear,
    } = createInputEventHandlers(emit, inputRef, props)
    
    return {
      inputRef,
      handleWrapperClick,
      handleFocus,
      // ... 其他处理器
    }
  }
})
```

### 2. 搜索输入框事件处理器

```typescript
import { createSearchEventHandlers } from '@/utils/eventFactory'

export default defineComponent({
  setup(props, { emit }) {
    const inputRef = ref<HTMLInputElement>()
    
    // 搜索输入框，input 事件自动防抖 300ms
    const handlers = createSearchEventHandlers(emit, inputRef, props, 300)
    
    return handlers
  }
})
```

### 3. 表单输入框事件处理器（带验证）

```typescript
import { createFormEventHandlers } from '@/utils/eventFactory'

export default defineComponent({
  setup(props, { emit }) {
    const inputRef = ref<HTMLInputElement>()
    
    // 邮箱验证器
    const emailValidator = (value: string) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(value)) {
        return '请输入有效的邮箱地址'
      }
      return true
    }
    
    // 带验证的表单输入框
    const handlers = createFormEventHandlers(emit, inputRef, props, emailValidator)
    
    return handlers
  }
})
```

### 4. 数字输入框事件处理器

```typescript
import { createNumberEventHandlers } from '@/utils/eventFactory'

export default defineComponent({
  setup(props, { emit }) {
    const inputRef = ref<HTMLInputElement>()
    
    // 数字输入框，自动限制只能输入数字
    const handlers = createNumberEventHandlers(emit, inputRef, props)
    
    return handlers
  }
})
```

## 🔍 高级用法

### 1. 自定义前置和后置逻辑

```typescript
const handleInput = createEventHandler(emit, 'input', {
  before: (event) => {
    console.log('输入前的逻辑')
    // 返回 false 可以阻止事件继续执行
    if (someCondition) return false
    return true
  },
  after: (event) => {
    console.log('输入后的逻辑')
    // 可以在这里执行额外的操作
    updateSomeState()
  }
})
```

### 2. 组合多个事件处理器

```typescript
export default defineComponent({
  setup(props, { emit }) {
    const inputRef = ref<HTMLInputElement>()
    
    // 基础事件处理器
    const baseHandlers = createInputEventHandlers(emit, inputRef, props)
    
    // 自定义事件处理器
    const customHandlers = {
      handleEnterKey: createEventHandler(emit, 'enter', {
        before: (event) => {
          return (event as KeyboardEvent).key === 'Enter'
        }
      }),
      
      handleEscapeKey: createEventHandler(emit, 'escape', {
        before: (event) => {
          return (event as KeyboardEvent).key === 'Escape'
        }
      })
    }
    
    return {
      ...baseHandlers,
      ...customHandlers,
    }
  }
})
```

### 3. 条件性事件处理

```typescript
const handleClick = createEventHandler(emit, 'click', {
  before: (event) => {
    // 只在特定条件下处理事件
    if (!props.enabled) return false
    if (props.loading) return false
    return true
  }
})
```

## 💡 最佳实践

### 1. 命名规范
- 事件处理器以 `handle` 开头
- 使用驼峰命名法
- 名称要清晰表达处理的事件类型

### 2. 性能优化
- 对于频繁触发的事件（如 input、scroll），使用防抖或节流
- 避免在事件处理器中进行复杂计算
- 使用 `before` 选项进行早期返回

### 3. 错误处理
```typescript
const handleInput = createEventHandler(emit, 'input', {
  before: (event) => {
    try {
      // 可能出错的逻辑
      return validateInput(event)
    } catch (error) {
      console.error('Input validation error:', error)
      return false
    }
  }
})
```

### 4. 类型安全
```typescript
// 为特定事件类型提供类型约束
const handleKeydown = createEventHandler<KeyboardEvent>(emit, 'keydown', {
  before: (event) => {
    // event 自动推断为 KeyboardEvent 类型
    return event.key === 'Enter'
  }
})
```

## 🚀 扩展性

### 创建自定义事件处理器工厂

```typescript
// 创建专门的密码输入框事件处理器
export function createPasswordEventHandlers(
  emit: (...args: any[]) => void,
  inputRef: Ref<HTMLInputElement | undefined>,
  props: { disabled?: boolean; readonly?: boolean }
) {
  return {
    ...createInputEventHandlers(emit, inputRef, props),
    
    // 密码强度检查
    handleInput: createEventHandler<Event>(emit, 'input', {
      after: (event) => {
        const target = event.target as HTMLInputElement
        const strength = calculatePasswordStrength(target.value)
        emit('password-strength', strength)
      }
    }),
    
    // 密码可见性切换
    handleToggleVisibility: createEventHandler<MouseEvent>(emit, 'toggle-visibility', {
      stopPropagation: true,
    })
  }
}
```

这个事件工厂系统大大简化了组件中的事件处理逻辑，提高了代码的复用性和维护性！
