// ================================
// Speed UI Scrollbar 组件样式
// ================================

@use './common/var.scss' as *;

.sp-scrollbar {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;

  // 包装器
  &__wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: auto;
    scrollbar-width: none; // Firefox
    -ms-overflow-style: none; // IE/Edge

    // 隐藏原生滚动条
    &::-webkit-scrollbar {
      display: none;
    }
  }

  // 内容区域
  &__content {
    min-height: 100%;
    min-width: 100%;
  }

  // 垂直滚动条
  &__bar-vertical {
    position: absolute;
    top: 0;
    right: 0;
    width: 8px;
    height: 100%;
    background: transparent;
    cursor: pointer;
    z-index: 10;
    opacity: 0;
    transition: opacity $transition-duration-base $transition-timing-base;

    &:hover {
      opacity: 1;
    }
  }

  // 水平滚动条
  &__bar-horizontal {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 8px;
    background: transparent;
    cursor: pointer;
    z-index: 10;
    opacity: 0;
    transition: opacity $transition-duration-base $transition-timing-base;

    &:hover {
      opacity: 1;
    }
  }

  // 垂直滚动条拇指
  &__thumb-vertical {
    position: absolute;
    top: 0;
    right: 1px;
    width: 6px;
    min-height: 20px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
    cursor: pointer;
    transition: background $transition-duration-base $transition-timing-base;

    &:hover {
      background: rgba(0, 0, 0, 0.5);
    }
  }

  // 水平滚动条拇指
  &__thumb-horizontal {
    position: absolute;
    bottom: 1px;
    left: 0;
    height: 6px;
    min-width: 20px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
    cursor: pointer;
    transition: background $transition-duration-base $transition-timing-base;

    &:hover {
      background: rgba(0, 0, 0, 0.5);
    }
  }

  // 尺寸变体
  &--small {
    .sp-scrollbar__bar-vertical {
      width: 6px;
    }

    .sp-scrollbar__bar-horizontal {
      height: 6px;
    }

    .sp-scrollbar__thumb-vertical {
      width: 4px;
      right: 1px;
    }

    .sp-scrollbar__thumb-horizontal {
      height: 4px;
      bottom: 1px;
    }
  }

  &--large {
    .sp-scrollbar__bar-vertical {
      width: 12px;
    }

    .sp-scrollbar__bar-horizontal {
      height: 12px;
    }

    .sp-scrollbar__thumb-vertical {
      width: 10px;
      right: 1px;
    }

    .sp-scrollbar__thumb-horizontal {
      height: 10px;
      bottom: 1px;
    }
  }

  // 始终显示滚动条
  &--always {
    .sp-scrollbar__bar-vertical,
    .sp-scrollbar__bar-horizontal {
      opacity: 1;
    }
  }

  // 滚动状态
  &--scrolling {
    .sp-scrollbar__bar-vertical,
    .sp-scrollbar__bar-horizontal {
      opacity: 1;
    }
  }

  // 原生滚动条模式
  &--native {
    .sp-scrollbar__wrapper {
      overflow: auto;
      scrollbar-width: thin;
      -ms-overflow-style: auto;

      &::-webkit-scrollbar {
        display: block;
        width: 8px;
        height: 8px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }
    }

    .sp-scrollbar__bar-vertical,
    .sp-scrollbar__bar-horizontal {
      display: none;
    }
  }

  // 主题变体
  &--light {
    .sp-scrollbar__thumb-vertical,
    .sp-scrollbar__thumb-horizontal {
      background: rgba(0, 0, 0, 0.2);

      &:hover {
        background: rgba(0, 0, 0, 0.4);
      }
    }
  }

  &--dark {
    .sp-scrollbar__thumb-vertical,
    .sp-scrollbar__thumb-horizontal {
      background: rgba(255, 255, 255, 0.3);

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }
  }

  // 悬停时显示滚动条
  &:hover {
    .sp-scrollbar__bar-vertical,
    .sp-scrollbar__bar-horizontal {
      opacity: 1;
    }
  }
}

// ========== 主题支持 ==========
// 主题类名，用于响应全局主题变量
.sp-scrollbar-custom {
  // 定义组件级主题变量，引用全局主题变量
  --scrollbar-thumb-color: rgba(0, 0, 0, 0.3);
  --scrollbar-thumb-hover-color: rgba(0, 0, 0, 0.5);
  --scrollbar-track-color: transparent;

  .sp-scrollbar__thumb-vertical,
  .sp-scrollbar__thumb-horizontal {
    background: var(--scrollbar-thumb-color);

    &:hover {
      background: var(--scrollbar-thumb-hover-color);
    }
  }

  .sp-scrollbar__bar-vertical,
  .sp-scrollbar__bar-horizontal {
    background: var(--scrollbar-track-color);
  }

  // 原生滚动条主题
  &.sp-scrollbar--native .sp-scrollbar__wrapper {
    &::-webkit-scrollbar-track {
      background: var(--scrollbar-track-color, #f1f1f1);
    }

    &::-webkit-scrollbar-thumb {
      background: var(--scrollbar-thumb-color, #c1c1c1);

      &:hover {
        background: var(--scrollbar-thumb-hover-color, #a8a8a8);
      }
    }
  }
}
