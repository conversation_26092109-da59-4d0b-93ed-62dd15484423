import type { PropType } from 'vue'
import { propsFactory } from '../../utils/propsFactory'

// 输入框类型
export type InputType =
  | 'text'
  | 'password'
  | 'email'
  | 'number'
  | 'tel'
  | 'url'
  | 'search'

// 变体类型
export type InputVariant =
  | 'default'
  | 'underlined'
  | 'filled'
  | 'pill'
  | 'square'
  | 'unborder'

// 效果类型
export type InputEffect = 'none' | 'glow'

// 尺寸类型
export type InputSize = 'small' | 'medium' | 'large'

// 验证状态类型
export type ValidateState = 'success' | 'warning' | 'error'

// 渲染模式类型
export type InputMode = 'input' | 'display'

/**
 * InputCore 基础 props 定义
 */
export const makeInputCoreProps = propsFactory(
  {
    // 基础属性
    value: [String, Number],
    type: String as PropType<InputType>,
    placeholder: String,
    disabled: Boolean,
    readonly: Boolean,
    maxlength: Number,

    // 样式属性
    variant: String as PropType<InputVariant>,
    effect: String as PropType<InputEffect>,
    size: String as PropType<InputSize>,

    // 功能属性
    clearable: Boolean,
    prefixIcon: String,
    suffixIcon: String,
    loading: Boolean,

    // 验证相关
    error: Boolean,
    validateState: String as PropType<ValidateState>,
    validateMessage: String,

    // 状态属性
    hasValue: Boolean,
    focused: Boolean,

    // 字数统计
    showWordLimit: Boolean,

    // 渲染模式
    mode: String as PropType<InputMode>,
  },
  'InputCore'
)

/**
 * InputCore 默认 props 值
 */
export const inputCoreDefaults = {
  value: '',
  type: 'text' as InputType,
  placeholder: '',
  disabled: false,
  readonly: false,
  maxlength: undefined,
  variant: 'default' as InputVariant,
  effect: 'none' as InputEffect,
  size: 'medium' as InputSize,
  clearable: false,
  prefixIcon: undefined,
  suffixIcon: undefined,
  loading: false,
  error: false,
  validateState: undefined,
  validateMessage: '',
  hasValue: false,
  focused: false,
  showWordLimit: false,
  mode: 'input' as InputMode,
}

/**
 * 创建带默认值的 InputCore props
 */
export const createInputCoreProps = (
  overrides?: Partial<typeof inputCoreDefaults>
) => {
  return makeInputCoreProps({
    ...inputCoreDefaults,
    ...overrides,
  })
}
