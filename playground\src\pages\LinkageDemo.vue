<template>
  <div class="linkage-demo">
    <h1>v-linkage 指令演示</h1>
    
    <!-- 输入框有值时按钮为主要样式，无值时禁用 -->
    <sp-input v-linkage:submitBtn="{ hasValue: 'primary', disabled: true }" />
    <sp-button ref="submitBtn" :disabled="false">提交</sp-button>

    <!-- 复杂配置：批量设置多个属性 -->


    <!-- 基本联动示例 -->
    <section class="demo-section">
      <h2>1. 基本联动</h2>
      <p>输入框为空时，按钮被禁用</p>
      <div class="demo-content">
        <sp-input 
          v-model:value="basicInput" 
          v-linkage="{
            target: 'basicBtn',
            when: 'value',
            then: 'disabled',
            condition: (value) => !value || value.trim() === ''
          }"
          placeholder="请输入内容"
        />
        <sp-button ref="basicBtn" primary>提交</sp-button>
      </div>
    </section>

    <!-- 值转换示例 -->
    <section class="demo-section">
      <h2>2. 值转换</h2>
      <p>输入小写字母，自动转换为大写显示</p>
      <div class="demo-content">
        <sp-input 
          v-model="transformInput" 
          v-linkage="{
            target: 'transformOutput',
            when: 'transformInput',
            then: 'value',
            transform: (value) => value?.toUpperCase() || ''
          }"
          placeholder="输入小写字母"
        />
        <sp-input ref="transformOutput" placeholder="自动转换为大写" readonly />
      </div>
    </section>

    <!-- 多目标联动示例 -->
    <section class="demo-section">
      <h2>3. 多目标联动</h2>
      <p>开关控制多个组件的启用/禁用状态</p>
      <div class="demo-content">
        <div class="switch-container">
          <sp-switch 
            v-model="multiTargetSwitch" 
            v-linkage="{
              target: ['multiInput1', 'multiInput2', 'multiBtn'],
              when: 'multiTargetSwitch',
              then: 'disabled',
              transform: (value) => !value
            }"
          />
          <span>启用组件</span>
        </div>
        <div class="multi-targets">
          <sp-input ref="multiInput1" placeholder="输入框1" />
          <sp-input ref="multiInput2" placeholder="输入框2" />
          <sp-button ref="multiBtn">操作按钮</sp-button>
        </div>
      </div>
    </section>

    <!-- 复杂动作示例 -->
    <section class="demo-section">
      <h2>4. 密码强度指示</h2>
      <p>根据密码长度动态改变按钮类型</p>
      <div class="demo-content">
        <sp-input 
          v-model="passwordInput" 
          v-linkage="{
            target: 'strengthBtn',
            when: 'value',
            then: {
              prop: 'type',
              transform: (value) => {
                if (!value) return 'default'
                if (value.length < 6) return 'danger'
                if (value.length < 10) return 'warning'
                return 'success'
              }
            }
          }"
          type="password"
          placeholder="请输入密码"
        />
        <sp-button ref="strengthBtn" size="small">
          密码强度: {{ getPasswordStrength(passwordInput) }}
        </sp-button>
      </div>
    </section>

    <!-- 多重联动示例 -->
    <section class="demo-section">
      <h2>5. 多重联动</h2>
      <p>邮箱验证：同时控制按钮状态和提示信息</p>
      <div class="demo-content">
        <sp-input 
          v-model="emailInput" 
          v-linkage="[
            {
              target: 'emailSubmitBtn',
              when: 'value',
              then: 'disabled',
              condition: (value) => !value?.includes('@') || !value?.includes('.')
            },
            {
              target: 'emailTip',
              when: 'value',
              then: 'hidden',
              condition: (value) => value?.includes('@') && value?.includes('.')
            }
          ]"
          placeholder="请输入邮箱地址"
        />
        <div ref="emailTip" class="tip error">请输入有效的邮箱地址</div>
        <sp-button ref="emailSubmitBtn" type="primary">发送验证邮件</sp-button>
      </div>
    </section>

    <!-- 延迟联动示例 -->
    <section class="demo-section">
      <h2>6. 延迟联动</h2>
      <p>搜索框：延迟500ms后触发搜索状态</p>
      <div class="demo-content">
        <sp-input 
          v-model="searchInput" 
          v-linkage="{
            target: 'searchBtn',
            when: 'value',
            then: 'loading',
            delay: 500,
            condition: (value) => value?.length > 2,
            transform: (value) => value?.length > 2
          }"
          placeholder="输入3个字符以上开始搜索"
        />
        <sp-button ref="searchBtn" type="primary">搜索</sp-button>
      </div>
    </section>

    <!-- 级联选择示例 -->
    <section class="demo-section">
      <h2>7. 级联选择</h2>
      <p>选择类型后，自动更新相关选项</p>
      <div class="demo-content">
        <sp-select 
          v-model="categorySelect" 
          v-linkage="{
            target: 'subCategorySelect',
            when: 'value',
            then: 'disabled',
            condition: (value) => !value
          }"
          placeholder="请选择分类"
        >
          <option value="">请选择</option>
          <option value="electronics">电子产品</option>
          <option value="clothing">服装</option>
          <option value="books">图书</option>
        </sp-select>
        
        <sp-select ref="subCategorySelect" placeholder="请选择子分类">
          <option value="">请选择</option>
          <option v-if="categorySelect === 'electronics'" value="phone">手机</option>
          <option v-if="categorySelect === 'electronics'" value="laptop">笔记本</option>
          <option v-if="categorySelect === 'clothing'" value="shirt">衬衫</option>
          <option v-if="categorySelect === 'clothing'" value="pants">裤子</option>
          <option v-if="categorySelect === 'books'" value="novel">小说</option>
          <option v-if="categorySelect === 'books'" value="tech">技术书籍</option>
        </sp-select>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 响应式数据
const basicInput = ref('')
const transformInput = ref('')
const multiTargetSwitch = ref(false)
const passwordInput = ref('')
const emailInput = ref('')
const searchInput = ref('')
const categorySelect = ref('')

// 模板引用
const basicBtn = ref()
const transformOutput = ref()
const multiInput1 = ref()
const multiInput2 = ref()
const multiBtn = ref()
const strengthBtn = ref()
const emailTip = ref()
const emailSubmitBtn = ref()
const searchBtn = ref()
const subCategorySelect = ref()

// 计算密码强度文本
const getPasswordStrength = (password: string) => {
  if (!password) return '无'
  if (password.length < 6) return '弱'
  if (password.length < 10) return '中'
  return '强'
}
</script>

<style scoped>
.linkage-demo {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.demo-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.demo-section h2 {
  margin-top: 0;
  color: #333;
  font-size: 18px;
}

.demo-section p {
  color: #666;
  margin-bottom: 15px;
}

.demo-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.demo-content > * {
  margin-bottom: 10px;
}

.switch-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.multi-targets {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.tip {
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
}

.tip.error {
  background: #fee;
  color: #c33;
  border: 1px solid #fcc;
}

.tip.success {
  background: #efe;
  color: #3c3;
  border: 1px solid #cfc;
}

@media (min-width: 768px) {
  .demo-content {
    flex-direction: row;
    align-items: center;
  }
  
  .demo-content > * {
    margin-bottom: 0;
  }
  
  .multi-targets {
    flex-wrap: nowrap;
  }
}
</style>