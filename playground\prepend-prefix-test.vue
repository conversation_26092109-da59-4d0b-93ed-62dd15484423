<template>
  <div class="prepend-prefix-test">
    <h2>前置图标 + 前缀组合测试</h2>
    <p>测试前置内部图标和前缀同时存在时的标签偏移效果</p>
    
    <div class="test-section">
      <h3>组合对比测试</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>1. 无前置无前缀</h4>
          <SPInputField
            size="medium"
            label="用户名"
            placeholder="请输入用户名"
            required
          />
        </div>
        
        <div class="test-item">
          <h4>2. 只有前置图标</h4>
          <SPInputField
            size="medium"
            label="用户名"
            placeholder="请输入用户名"
            required
            prepend-icon-inner="User"
          />
        </div>
        
        <div class="test-item">
          <h4>3. 只有前缀</h4>
          <SPInputField
            size="medium"
            label="用户名"
            placeholder="请输入用户名"
            required
            prefix="@"
          />
        </div>
        
        <div class="test-item">
          <h4>4. 前置图标 + 前缀</h4>
          <SPInputField
            size="medium"
            label="用户名"
            placeholder="请输入用户名"
            required
            prepend-icon-inner="User"
            prefix="@"
          />
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>不同前缀长度测试</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>短前缀 (@)</h4>
          <SPInputField
            size="medium"
            label="邮箱地址"
            placeholder="请输入邮箱"
            required
            prepend-icon-inner="Mail"
            prefix="@"
          />
        </div>
        
        <div class="test-item">
          <h4>中等前缀 (https://)</h4>
          <SPInputField
            size="medium"
            label="网站地址"
            placeholder="请输入网址"
            required
            prepend-icon-inner="Link"
            prefix="https://"
          />
        </div>
        
        <div class="test-item">
          <h4>长前缀 (用户名前缀:)</h4>
          <SPInputField
            size="medium"
            label="完整用户名"
            placeholder="请输入用户名"
            required
            prepend-icon-inner="User"
            prefix="用户名前缀:"
          />
        </div>
        
        <div class="test-item">
          <h4>货币前缀 (¥)</h4>
          <SPInputField
            size="medium"
            label="价格"
            placeholder="请输入价格"
            required
            prepend-icon-inner="Money"
            prefix="¥"
          />
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>不同尺寸测试</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>Small 尺寸</h4>
          <SPInputField
            size="small"
            label="邮箱地址"
            placeholder="请输入邮箱"
            required
            prepend-icon-inner="Mail"
            prefix="@"
          />
        </div>
        
        <div class="test-item">
          <h4>Medium 尺寸</h4>
          <SPInputField
            size="medium"
            label="邮箱地址"
            placeholder="请输入邮箱"
            required
            prepend-icon-inner="Mail"
            prefix="@"
          />
        </div>
        
        <div class="test-item">
          <h4>Large 尺寸</h4>
          <SPInputField
            size="large"
            label="邮箱地址"
            placeholder="请输入邮箱"
            required
            prepend-icon-inner="Mail"
            prefix="@"
          />
        </div>
        
        <div class="test-item">
          <h4>动态尺寸 (50px)</h4>
          <SPInputField
            :size="50"
            label="邮箱地址"
            placeholder="请输入邮箱"
            required
            prepend-icon-inner="Mail"
            prefix="@"
          />
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>长标签测试</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>只有前置图标</h4>
          <SPInputField
            size="medium"
            label="这是一个很长的标签文本用来测试浮动位置"
            placeholder="请输入内容"
            required
            prepend-icon-inner="Search"
          />
        </div>
        
        <div class="test-item">
          <h4>前置图标 + 前缀</h4>
          <SPInputField
            size="medium"
            label="这是一个很长的标签文本用来测试浮动位置"
            placeholder="请输入内容"
            required
            prepend-icon-inner="Search"
            prefix="搜索:"
          />
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>实际使用场景</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>邮箱输入</h4>
          <SPInputField
            size="medium"
            label="邮箱地址"
            placeholder="example.com"
            required
            prepend-icon-inner="Mail"
            prefix="@"
          />
        </div>
        
        <div class="test-item">
          <h4>网址输入</h4>
          <SPInputField
            size="medium"
            label="网站地址"
            placeholder="www.example.com"
            required
            prepend-icon-inner="Link"
            prefix="https://"
          />
        </div>
        
        <div class="test-item">
          <h4>价格输入</h4>
          <SPInputField
            size="medium"
            label="商品价格"
            placeholder="0.00"
            required
            prepend-icon-inner="Money"
            prefix="¥"
          />
        </div>
        
        <div class="test-item">
          <h4>电话输入</h4>
          <SPInputField
            size="medium"
            label="电话号码"
            placeholder="138****8888"
            required
            prepend-icon-inner="Phone"
            prefix="+86"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SPInputField from '../packages/ui/src/components/input-field/input-field.vue'
</script>

<style scoped>
.prepend-prefix-test {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.test-item {
  padding: 15px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafafa;
}

.test-item h4 {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 14px;
  font-weight: 600;
}
</style>
