<template>
  <div class="demo-container">
    <h1>Textarea 组件演示</h1>

    <!-- 基础用法 -->
    <section class="demo-section">
      <h2>基础用法</h2>
      <div class="demo-row">
        <sp-textarea
          v-model:value="basicValue"
          placeholder="请输入内容..."
          rows="4"
        />
      </div>
      <p class="demo-value">当前值: {{ basicValue }}</p>
    </section>

    <!-- 尺寸 -->
    <section class="demo-section">
      <h2>不同尺寸</h2>
      <div class="demo-row">
        <sp-textarea
          v-model:value="sizeValue"
          size="small"
          placeholder="小号文本域"
          rows="3"
        />
        <sp-textarea
          v-model:value="sizeValue"
          size="medium"
          placeholder="中号文本域"
          rows="3"
        />
        <sp-textarea
          v-model:value="sizeValue"
          size="large"
          prefix-icon="Person"
          placeholder="大号文本域"
          rows="3"
        />
      </div>
    </section>

    <!-- 变体 -->
    <section class="demo-section">
      <h2>不同变体</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h4>默认</h4>
          <sp-textarea
            v-model:value="variantValue"
            variant="default"
            placeholder="默认变体"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>下划线</h4>
          <sp-textarea
            v-model:value="variantValue"
            variant="underlined"
            placeholder="下划线变体"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>填充</h4>
          <sp-textarea
            v-model:value="variantValue"
            variant="filled"
            placeholder="填充变体"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>幽灵</h4>
          <sp-textarea
            v-model:value="variantValue"
            variant="ghost"
            placeholder="幽灵变体"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>胶囊</h4>
          <sp-textarea
            v-model:value="variantValue"
            variant="pill"
            placeholder="胶囊变体"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>方形</h4>
          <sp-textarea
            v-model:value="variantValue"
            variant="square"
            placeholder="方形变体"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>无边框</h4>
          <sp-textarea
            v-model:value="variantValue"
            variant="unborder"
            placeholder="无边框变体"
            rows="3"
          />
        </div>
      </div>
    </section>

    <!-- 状态 -->
    <section class="demo-section">
      <h2>不同状态</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h4>普通</h4>
          <sp-textarea
            v-model:value="stateValue"
            placeholder="普通状态"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>禁用</h4>
          <sp-textarea
            v-model:value="stateValue"
            disabled
            placeholder="禁用状态"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>只读</h4>
          <sp-textarea
            v-model:value="stateValue"
            readonly
            placeholder="只读状态"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>错误</h4>
          <sp-textarea
            v-model:value="stateValue"
            error
            placeholder="错误状态"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>警告</h4>
          <sp-textarea
            v-model:value="stateValue"
            validate-state="warning"
            placeholder="警告状态"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>成功</h4>
          <sp-textarea
            v-model:value="stateValue"
            validate-state="success"
            placeholder="成功状态"
            rows="3"
          />
        </div>
      </div>
    </section>

    <!-- 功能特性 -->
    <section class="demo-section">
      <h2>功能特性</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h4>可清除</h4>
          <sp-textarea
            v-model:value="featureValue"
            clearable
            placeholder="可清除内容"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>字数统计</h4>
          <sp-textarea
            v-model:value="featureValue"
            show-word-limit
            maxlength="100"
            placeholder="最多100字符"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>前缀图标</h4>
          <sp-textarea
            v-model:value="featureValue"
            prefix-icon="Person"
            placeholder="带前缀图标"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>后缀图标</h4>
          <sp-textarea
            v-model:value="featureValue"
            suffix-icon="Person"
            placeholder="带后缀图标"
            rows="3"
          />
        </div>
      </div>
    </section>

    <!-- 加载动画 -->
    <section class="demo-section">
      <h2>加载动画</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h4>默认变体 - 加载中</h4>
          <sp-textarea
            v-model:value="loadingValue"
            loading
            placeholder="加载中..."
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>下划线变体 - 加载中</h4>
          <sp-textarea
            v-model:value="loadingValue"
            variant="underlined"
            loading
            placeholder="加载中..."
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>填充变体 - 加载中</h4>
          <sp-textarea
            v-model:value="loadingValue"
            variant="filled"
            loading
            placeholder="加载中..."
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>胶囊变体 - 加载中</h4>
          <sp-textarea
            v-model:value="loadingValue"
            variant="pill"
            loading
            placeholder="加载中..."
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>无边框变体 - 加载中</h4>
          <sp-textarea
            v-model:value="loadingValue"
            variant="unborder"
            loading
            placeholder="加载中..."
            rows="3"
          />
        </div>
      </div>
    </section>

    <!-- 发光效果 -->
    <section class="demo-section">
      <h2>发光效果</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h4>普通发光</h4>
          <sp-textarea
            v-model:value="glowValue"
            effect="glow"
            placeholder="聚焦时发光"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>错误发光</h4>
          <sp-textarea
            v-model:value="glowValue"
            effect="glow"
            error
            placeholder="错误状态发光"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>警告发光</h4>
          <sp-textarea
            v-model:value="glowValue"
            effect="glow"
            validate-state="warning"
            placeholder="警告状态发光"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>成功发光</h4>
          <sp-textarea
            v-model:value="glowValue"
            effect="glow"
            validate-state="success"
            placeholder="成功状态发光"
            rows="3"
          />
        </div>
      </div>
    </section>

    <!-- 自动调整高度 -->
    <section class="demo-section">
      <h2>自动调整高度</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h4>自动高度</h4>
          <sp-textarea
            v-model:value="autosizeValue"
            autosize
            placeholder="自动调整高度，输入多行文本试试..."
          />
        </div>
        <div class="demo-item">
          <h4>限制行数 (2-6行)</h4>
          <sp-textarea
            v-model:value="autosizeValue"
            :autosize="{ minRows: 2, maxRows: 6 }"
            placeholder="最少2行，最多6行"
          />
        </div>
      </div>
    </section>

    <!-- 调整大小 -->
    <section class="demo-section">
      <h2>调整大小</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h4>垂直调整</h4>
          <sp-textarea
            v-model:value="resizeValue"
            resize="vertical"
            placeholder="只能垂直调整大小"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>水平调整</h4>
          <sp-textarea
            v-model:value="resizeValue"
            resize="horizontal"
            placeholder="只能水平调整大小"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>双向调整</h4>
          <sp-textarea
            v-model:value="resizeValue"
            resize="both"
            placeholder="可以双向调整大小"
            rows="3"
          />
        </div>
        <div class="demo-item">
          <h4>禁止调整</h4>
          <sp-textarea
            v-model:value="resizeValue"
            resize="none"
            placeholder="禁止调整大小"
            rows="3"
          />
        </div>
      </div>
    </section>

    <!-- 最新修复功能演示 -->
    <section class="demo-section">
      <h2>最新修复功能演示</h2>

      <!-- 前缀图标布局修复 -->
      <div class="demo-subsection">
        <h3>前缀图标布局修复</h3>
        <p class="demo-description">
          修复了前缀图标与文字重叠的问题，现在前缀图标占据整个左侧空间
        </p>
        <div class="demo-grid">
          <div class="demo-item">
            <h4>默认变体 + 前缀图标</h4>
            <sp-textarea
              v-model:value="fixedValue"
              prefix-icon="Person"
              placeholder="前缀图标现在正确布局了&#10;支持多行文本显示&#10;图标不会重叠"
              rows="3"
            />
          </div>
          <div class="demo-item">
            <h4>填充变体 + 前缀图标</h4>
            <sp-textarea
              v-model:value="fixedValue"
              variant="filled"
              prefix-icon="Person"
              placeholder="填充样式 + 前缀图标&#10;完美配合&#10;无重叠问题"
              rows="3"
            />
          </div>
          <div class="demo-item">
            <h4>下划线变体 + 前缀图标</h4>
            <sp-textarea
              v-model:value="fixedValue"
              variant="underlined"
              prefix-icon="Person"
              placeholder="下划线样式 + 前缀图标&#10;布局整齐&#10;视觉美观"
              rows="3"
            />
          </div>
        </div>
      </div>

      <!-- 填充样式完善 -->
      <div class="demo-subsection">
        <h3>填充样式完善</h3>
        <p class="demo-description">
          填充变体现在具备完整的交互效果，与input组件保持一致
        </p>
        <div class="demo-grid">
          <div class="demo-item">
            <h4>填充样式 - 基础交互</h4>
            <sp-textarea
              v-model:value="filledDemoValue"
              variant="filled"
              placeholder="聚焦时观察动画效果&#10;悬停时背景色变化&#10;底边框动画"
              rows="3"
            />
          </div>
          <div class="demo-item">
            <h4>填充样式 - 错误状态</h4>
            <sp-textarea
              v-model:value="filledDemoValue"
              variant="filled"
              error
              placeholder="错误状态的填充样式&#10;红色主题&#10;完整动画效果"
              rows="3"
            />
          </div>
          <div class="demo-item">
            <h4>填充样式 - 成功状态</h4>
            <sp-textarea
              v-model:value="filledDemoValue"
              variant="filled"
              validate-state="success"
              placeholder="成功状态的填充样式&#10;绿色主题&#10;聚焦动画正常"
              rows="3"
            />
          </div>
          <div class="demo-item">
            <h4>填充样式 - 警告状态</h4>
            <sp-textarea
              v-model:value="filledDemoValue"
              variant="filled"
              validate-state="warning"
              placeholder="警告状态的填充样式&#10;橙色主题&#10;悬停效果正常"
              rows="3"
            />
          </div>
        </div>
      </div>

      <!-- 填充样式发光效果 -->
      <div class="demo-subsection">
        <h3>填充样式发光效果</h3>
        <p class="demo-description">
          填充变体现在支持发光效果，在禁用状态下会正确隐藏
        </p>
        <div class="demo-grid">
          <div class="demo-item">
            <h4>填充 + 发光效果</h4>
            <sp-textarea
              v-model:value="filledGlowValue"
              variant="filled"
              effect="glow"
              placeholder="填充样式的发光效果&#10;聚焦时会发光&#10;与底边框动画配合"
              rows="3"
            />
          </div>
          <div class="demo-item">
            <h4>填充 + 发光 + 错误状态</h4>
            <sp-textarea
              v-model:value="filledGlowValue"
              variant="filled"
              effect="glow"
              error
              placeholder="错误状态的发光效果&#10;红色发光&#10;视觉效果突出"
              rows="3"
            />
          </div>
          <div class="demo-item">
            <h4>填充 + 发光 + 禁用状态</h4>
            <sp-textarea
              v-model:value="filledGlowValue"
              variant="filled"
              effect="glow"
              disabled
              placeholder="禁用状态不显示发光&#10;避免混淆&#10;用户体验优化"
              rows="3"
            />
          </div>
        </div>
      </div>

      <!-- 尺寸适配展示 -->
      <div class="demo-subsection">
        <h3>不同尺寸的前缀图标适配</h3>
        <p class="demo-description">前缀图标在不同尺寸下都能正确显示和对齐</p>
        <div class="demo-grid">
          <div class="demo-item">
            <h4>小尺寸 + 前缀图标</h4>
            <sp-textarea
              v-model:value="sizeAdaptValue"
              size="small"
              variant="filled"
              prefix-icon="Person"
              placeholder="小尺寸文本域&#10;前缀图标适配&#10;间距合理"
              rows="2"
            />
          </div>
          <div class="demo-item">
            <h4>中尺寸 + 前缀图标</h4>
            <sp-textarea
              v-model:value="sizeAdaptValue"
              size="medium"
              variant="filled"
              prefix-icon="Person"
              placeholder="中等尺寸文本域&#10;前缀图标适配&#10;布局协调"
              rows="2"
            />
          </div>
          <div class="demo-item">
            <h4>大尺寸 + 前缀图标</h4>
            <sp-textarea
              v-model:value="sizeAdaptValue"
              size="large"
              variant="filled"
              prefix-icon="Person"
              placeholder="大尺寸文本域&#10;前缀图标适配&#10;视觉平衡"
              rows="2"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- 事件测试 -->
    <section class="demo-section">
      <h2>事件测试</h2>
      <div class="demo-row">
        <sp-textarea
          v-model:value="eventValue"
          clearable
          placeholder="测试各种事件..."
          rows="4"
          @input="onInput"
          @change="onChange"
          @focus="onFocus"
          @blur="onBlur"
          @clear="onClear"
        />
      </div>
      <div class="event-log">
        <h4>事件日志:</h4>
        <ul>
          <li
            v-for="(log, index) in eventLogs"
            :key="index"
          >
            {{ log }}
          </li>
        </ul>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  // 数据
  const basicValue = ref('')
  const sizeValue = ref('不同尺寸的文本域')
  const variantValue = ref('不同变体的文本域')
  const stateValue = ref('不同状态的文本域')
  const featureValue = ref('功能特性演示')
  const loadingValue = ref('加载中的文本域')
  const glowValue = ref('发光效果的文本域')
  const autosizeValue = ref(
    '这是一个可以自动调整高度的文本域。\n你可以输入多行文本来测试这个功能。\n继续输入更多内容...'
  )
  const resizeValue = ref('可以调整大小的文本域')
  const eventValue = ref('')
  const eventLogs = ref<string[]>([])

  // 新增的演示数据
  const fixedValue = ref(
    '这是演示前缀图标布局修复的文本域\n现在前缀图标不会与文字重叠了\n支持多行文本显示'
  )
  const filledDemoValue = ref(
    '填充样式现在具备完整的交互效果\n聚焦时有动画\n悬停时背景色变化'
  )
  const filledGlowValue = ref('填充样式 + 发光效果\n现在可以正确配合使用')
  const sizeAdaptValue = ref('不同尺寸的前缀图标适配演示')

  // 事件处理
  const onInput = (event: Event) => {
    const target = event.target as HTMLTextAreaElement
    eventLogs.value.unshift(`Input: ${target.value}`)
    if (eventLogs.value.length > 10) {
      eventLogs.value = eventLogs.value.slice(0, 10)
    }
  }

  const onChange = (value: string | undefined) => {
    eventLogs.value.unshift(`Change: ${value}`)
    if (eventLogs.value.length > 10) {
      eventLogs.value = eventLogs.value.slice(0, 10)
    }
  }

  const onFocus = (event: FocusEvent) => {
    eventLogs.value.unshift(`Focus: 获得焦点`)
    if (eventLogs.value.length > 10) {
      eventLogs.value = eventLogs.value.slice(0, 10)
    }
  }

  const onBlur = (event: FocusEvent) => {
    eventLogs.value.unshift(`Blur: 失去焦点`)
    if (eventLogs.value.length > 10) {
      eventLogs.value = eventLogs.value.slice(0, 10)
    }
  }

  const onClear = () => {
    eventLogs.value.unshift(`Clear: 内容已清除`)
    if (eventLogs.value.length > 10) {
      eventLogs.value = eventLogs.value.slice(0, 10)
    }
  }
</script>

<style scoped>
  .demo-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .demo-section {
    margin-bottom: 40px;
  }

  .demo-section h2 {
    margin-bottom: 20px;
    color: #333;
    font-size: 24px;
  }

  .demo-section h4 {
    margin-bottom: 10px;
    color: #666;
    font-size: 14px;
  }

  .demo-row {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
  }

  .demo-row > * {
    flex: 1;
  }

  .demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
  }

  .demo-item {
    display: flex;
    flex-direction: column;
  }

  .demo-value {
    color: #666;
    font-size: 14px;
    margin-top: 8px;
  }

  .event-log {
    margin-top: 20px;
    padding: 16px;
    background: #f5f5f5;
    border-radius: 4px;
  }

  .event-log h4 {
    margin-bottom: 10px;
    color: #333;
  }

  .event-log ul {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 200px;
    overflow-y: auto;
  }

  .event-log li {
    padding: 4px 0;
    border-bottom: 1px solid #eee;
    font-family: monospace;
    font-size: 12px;
    color: #666;
  }

  .event-log li:last-child {
    border-bottom: none;
  }

  /* 新增演示部分的样式 */
  .demo-subsection {
    margin-bottom: 30px;
    padding: 20px;
    background: #fafafa;
    border-radius: 8px;
    border-left: 4px solid #007acc;
  }

  .demo-subsection h3 {
    margin: 0 0 12px 0;
    color: #007acc;
    font-size: 18px;
    font-weight: 600;
  }

  .demo-description {
    margin: 0 0 20px 0;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    background: #fff;
    padding: 12px 16px;
    border-radius: 4px;
    border-left: 3px solid #e1ecf4;
  }
</style>
