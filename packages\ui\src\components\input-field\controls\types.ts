/**
 * 输入框控件系统类型定义
 * 统一管理所有输入框控件的配置、属性和事件
 */

import type { Component } from 'vue'

/** 控件位置 */
export type ControlPosition =
  | 'prepend'
  | 'append'
  | 'prependOuter'
  | 'appendOuter'

/** 控件类型 */
export type ControlType =
  | 'clear'
  | 'password'
  | 'number'
  | 'search'
  | 'tel'
  | 'smscode'
  | 'wordLimit'
  | 'prependIcon'
  | 'appendIcon'

/** 基础控件属性 */
export interface BaseControlProps {
  /** 图标大小 */
  iconSize: number
  /** 是否禁用 */
  disabled?: boolean
}

/** 控件配置接口 */
export interface ControlConfig {
  /** 控件类型标识 */
  type: ControlType
  /** 控件组件 */
  component: Component
  /** 控件位置 */
  position: ControlPosition
  /** 显示顺序（数字越小越靠前） */
  order: number
  /** 显示条件函数 */
  condition: (context: ControlContext) => boolean
  /** 属性生成函数 */
  propsFactory: (context: ControlContext) => Record<string, any>
  /** 事件处理函数映射 */
  eventsFactory: (context: ControlContext) => Record<string, Function>
}

/** 控件上下文 - 包含控件需要的所有信息 */
export interface ControlContext {
  /** 组件 props */
  props: any
  /** 组件 emit 函数 */
  emit: Function
  /** slot props（来自 FieldContainer） */
  slotProps: any
  /** 来自 useInputLogic 的状态和方法 */
  inputLogic: any
  /** 计算属性状态 */
  computed: {
    hasValue: boolean
    computedDisabled: boolean
    [key: string]: any
  }
}

/** 渲染的控件实例 */
export interface RenderedControl {
  /** 控件类型 */
  type: ControlType
  /** 控件组件 */
  component: Component
  /** 传递给组件的属性 */
  props: Record<string, any>
  /** 事件处理器 */
  events: Record<string, Function>
  /** 显示顺序 */
  order: number
  /** 唯一键 */
  key: string
}

/** 控件事件数据 */
export interface ControlEventData {
  /** 控件类型 */
  type: ControlType
  /** 事件名称 */
  event: string
  /** 事件数据 */
  data?: any
}

/** 控件注册表接口 */
export interface ControlRegistry {
  /** 注册控件 */
  register(config: ControlConfig): void
  /** 注销控件 */
  unregister(type: ControlType): void
  /** 获取控件配置 */
  get(type: ControlType): ControlConfig | undefined
  /** 获取所有控件配置 */
  getAll(): ControlConfig[]
  /** 根据位置获取控件 */
  getByPosition(position: ControlPosition): ControlConfig[]
}
