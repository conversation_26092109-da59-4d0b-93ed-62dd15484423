<template>
  <div class="page-container">
    <h1>InputControl 组件演示</h1>
    <p>
      从 FieldContainer
      中抽离出来的控件布局组件，专注于处理外部图标、内部控件区域、加载动画等布局逻辑
    </p>

    <div class="demo-section">
      <h2>🌟 基础用法</h2>
      <div class="demo-item">
        <label>基础测试：</label>
        <div>Hello World - 页面可以正常显示</div>
      </div>

      <div class="demo-item">
        <label>基础 InputControl（内置 BaseInput）：</label>
        <InputControl placeholder="请输入内容" />
      </div>

      <div class="demo-item">
        <label>带内部前置/后置插槽：</label>
        <InputControl placeholder="请输入金额">
          <template #prepend>
            <span
              style="
                padding: 0 8px;
                color: #666;
                background: #f5f5f5;
                border-right: 1px solid #ddd;
              "
            >
              ¥
            </span>
          </template>
          <!-- <template #default="slotProps">
            <input
              :placeholder="slotProps.placeholder"
              style="
                border: 1px solid #ccc;
                padding: 8px;
                border-radius: 4px;
                width: 100%;
              "
            />
          </template> -->
          <template #append>
            <span
              style="
                padding: 0 8px;
                color: #666;
                background: #f5f5f5;
                border-left: 1px solid #ddd;
              "
            >
              元
            </span>
          </template>
        </InputControl>
      </div>

      <div class="demo-item">
        <label>带外部前置/后置插槽：</label>
        <InputControl placeholder="请输入用户名">
          <template #prependOuter>
            <div
              style="
                padding: 0 8px;
                color: #666;
                background: #e6f7ff;
                border: 1px solid #91d5ff;
                border-radius: 4px;
                margin-right: 8px;
              "
            >
              👤
            </div>
          </template>
          <template #prepend>
            <span
              style="
                padding: 0 8px;
                color: #666;
                background: #f5f5f5;
                border-right: 1px solid #ddd;
              "
            >
              ¥
            </span>
          </template>
          <template #default="slotProps">
            <input
              :placeholder="slotProps.placeholder"
              style="
                border: 1px solid #ccc;
                padding: 8px;
                border-radius: 4px;
                width: 100%;
              "
            />
          </template>
          <template #append>
            <span
              style="
                padding: 0 8px;
                color: #666;
                background: #f5f5f5;
                border-left: 1px solid #ddd;
              "
            >
              元
            </span>
          </template>
          <template #appendOuter>
            <div
              style="
                padding: 0 8px;
                color: #666;
                background: #f6ffed;
                border: 1px solid #b7eb8f;
                border-radius: 4px;
                margin-left: 8px;
              "
            >
              ✓
            </div>
          </template>
        </InputControl>
      </div>

      <div class="demo-item">
        <label>带前缀/后缀插槽：</label>
        <InputControl placeholder="请输入网址">
          <template #prefix>
            <span style="color: #666; font-size: 14px">https://</span>
          </template>
          <template #default="slotProps">
            <input
              :placeholder="slotProps.placeholder"
              style="
                border: 1px solid #ccc;
                padding: 8px;
                border-radius: 4px;
                width: 100%;
              "
            />
          </template>
          <template #suffix>
            <span style="color: #666; font-size: 14px">.com</span>
          </template>
        </InputControl>
      </div>

      <div class="demo-item">
        <label>通过 props 传递前缀/后缀：</label>
        <InputControl
          placeholder="请输入价格"
          prefix="$"
          suffix="USD"
        >
          <template #default="slotProps">
            <input
              :placeholder="slotProps.placeholder"
              style="
                border: 1px solid #ccc;
                padding: 8px;
                border-radius: 4px;
                width: 100%;
              "
            />
          </template>
        </InputControl>
      </div>

      <div class="demo-item">
        <label>禁用状态：</label>
        <InputControl
          placeholder="禁用状态"
          :disabled="true"
        >
          <template #default="slotProps">
            <input
              :placeholder="slotProps.placeholder"
              :disabled="slotProps.disabled"
              style="
                border: 1px solid #ccc;
                padding: 8px;
                border-radius: 4px;
                width: 100%;
              "
            />
          </template>
        </InputControl>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { InputControl } from '../../../packages/ui/src/components/input-control'
</script>

<style scoped>
  .page-container {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
  }

  .demo-section {
    margin-bottom: 40px;
  }

  .demo-item {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .demo-item label {
    min-width: 120px;
    font-weight: 500;
  }

  .demo-item > :last-child {
    flex: 1;
    max-width: 300px;
  }

  h1 {
    color: #333;
    margin-bottom: 8px;
  }

  h2 {
    color: #666;
    margin-bottom: 16px;
    font-size: 18px;
  }

  p {
    color: #666;
    margin-bottom: 24px;
    line-height: 1.6;
  }
</style>
