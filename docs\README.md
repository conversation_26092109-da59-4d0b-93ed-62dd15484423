# Speed UI 组件库使用文档

## 快速开始

### 1. 启动开发环境

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev
```

访问 http://localhost:5174 查看组件库演示

### 2. 在项目中使用

#### 安装
```bash
npm install @speed-ui/ui
# 或
pnpm add @speed-ui/ui
```

#### 全局注册
```ts
// main.ts
import { createApp } from 'vue'
import SpeedUI from '@speed-ui/ui'
import '@speed-ui/ui/styles'

const app = createApp(App)
app.use(SpeedUI)
app.mount('#app')
```

#### 按需引入
```vue
<script setup>
import { Button } from '@speed-ui/ui'
</script>

<template>
  <Button>点击我</Button>
</template>
```

## 组件

### Button 按钮

```vue
<template>
  <!-- 基础用法 -->
  <SpButton>默认按钮</SpButton>
  
  <!-- 不同类型 -->
  <SpButton type="primary">主要按钮</SpButton>
  <SpButton type="success">成功按钮</SpButton>
  <SpButton type="warning">警告按钮</SpButton>
  <SpButton type="danger">危险按钮</SpButton>
  
  <!-- 不同尺寸 -->
  <SpButton size="small">小按钮</SpButton>
  <SpButton size="medium">中按钮</SpButton>
  <SpButton size="large">大按钮</SpButton>
  
  <!-- 状态 -->
  <SpButton disabled>禁用</SpButton>
  <SpButton loading>加载中</SpButton>
</template>
```

#### Props
- `type`: 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
- `size`: 'small' | 'medium' | 'large'
- `disabled`: boolean
- `loading`: boolean

#### Events
- `click`: 点击事件

## 开发

### 项目结构
```
speed-ui/
├── packages/
│   ├── ui/          # 组件库
│   └── utils/       # 工具函数
├── playground/      # 开发环境
└── docs/           # 文档
```

### 添加新组件
1. 在 `packages/ui/src/components/` 下创建组件文件夹
2. 在 `packages/ui/src/index.ts` 中导出新组件
3. 在 playground 中测试组件

### 构建
```bash
pnpm build
```

### 发布
```bash
pnpm changeset
pnpm version-packages
pnpm release
``` 