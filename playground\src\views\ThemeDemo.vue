<template>
  <div class="theme-demo">
    <h2>简化主题设置演示</h2>
    
    <div class="demo-section">
      <h3>当前主题色：{{ currentTheme }}</h3>
      
      <div class="theme-buttons">
        <button 
          v-for="(color, name) in presetThemes" 
          :key="name"
          @click="changeTheme(color)"
          :style="{ backgroundColor: color }"
          class="theme-btn"
        >
          {{ name }}
        </button>
      </div>
      
      <div class="custom-theme">
        <label>自定义颜色：</label>
        <input 
          type="color" 
          v-model="customColor" 
          @change="changeTheme(customColor)"
        />
        <button @click="changeTheme(customColor)">应用自定义颜色</button>
      </div>
    </div>

    <div class="demo-components">
      <h3>组件预览</h3>
      
      <div class="component-row">
        <sp-button primary>主要按钮</sp-button>
        <sp-button>默认按钮</sp-button>
        <sp-button disabled>禁用按钮</sp-button>
      </div>
      
      <div class="component-row">
        <sp-input-field 
          label="输入框" 
          placeholder="请输入内容"
          prefix-icon="Search"
        />
      </div>
      
      <div class="component-row">
        <sp-select-field 
          label="选择器"
          placeholder="请选择"
          :options="[
            { label: '选项1', value: '1' },
            { label: '选项2', value: '2' },
            { label: '选项3', value: '3' }
          ]"
        />
      </div>
      
      <div class="component-row">
        <sp-switch v-model="switchValue" />
        <sp-tag>标签</sp-tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { setTheme, getCurrentTheme, PRESET_THEMES } from '../../../packages/ui/src'

const currentTheme = ref('#1890ff')
const customColor = ref('#ff6b6b')
const switchValue = ref(false)

const presetThemes = PRESET_THEMES

const changeTheme = (color: string) => {
  setTheme(color)
  currentTheme.value = getCurrentTheme()
}

onMounted(() => {
  currentTheme.value = getCurrentTheme()
})
</script>

<style scoped>
.theme-demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.theme-buttons {
  display: flex;
  gap: 10px;
  margin: 20px 0;
  flex-wrap: wrap;
}

.theme-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-weight: 500;
  text-transform: capitalize;
}

.theme-btn:hover {
  opacity: 0.8;
}

.custom-theme {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}

.custom-theme input[type="color"] {
  width: 50px;
  height: 30px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.custom-theme button {
  padding: 6px 12px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.demo-components {
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.component-row {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.component-row:last-child {
  margin-bottom: 0;
}
</style>
