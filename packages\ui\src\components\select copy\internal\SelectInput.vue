<template>
  <div :class="`${prefixCls}__inner`">
    <!-- 多选标签 -->
    <template v-if="multiple && selectedOptions.length > 0">
      <div :class="`${prefixCls}__tags`">
        <Tag
          v-for="option in selectedOptions"
          :key="getOptionValue(option, valueKey)"
          :label="getOptionLabel(option, labelKey)"
          :value="getOptionValue(option, valueKey)"
          :closable="!disabled"
          :size="getTagSize()"
          type="default"
          variant="light"
          :class="`${prefixCls}__tag`"
          @close="handleRemoveTag(option)"
        />
      </div>
    </template>

    <!-- 单选显示 -->
    <template v-else>
      <span
        v-if="selectedOption"
        :class="`${prefixCls}__selected`"
      >
        {{ getOptionLabel(selectedOption, labelKey) }}
      </span>
      <span
        v-else
        :class="`${prefixCls}__placeholder`"
      >
        {{ placeholder }}
      </span>
    </template>
  </div>
</template>

<script setup lang="ts">
  import type { SelectOption } from '../select'
  import { getOptionValue, getOptionLabel } from '../select'
  import { Tag } from '../../tag'

  interface Props {
    prefixCls: string
    multiple: boolean
    disabled: boolean
    placeholder: string
    selectedOption: SelectOption | null
    selectedOptions: SelectOption[]
    valueKey: string
    labelKey: string
    size?: 'small' | 'default' | 'large'
  }

  interface Emits {
    removeTag: [option: SelectOption]
  }

  const props = withDefaults(defineProps<Props>(), {
    size: 'default',
  })
  const emit = defineEmits<Emits>()

  // 根据 Select 尺寸获取 Tag 尺寸
  const getTagSize = () => {
    // Select 的 size 映射到 Tag 的 size
    const sizeMap = {
      small: 'small',
      default: 'medium',
      large: 'large',
    }
    // 如果没有传递 size 属性，默认使用 medium
    const selectSize = props.size || 'default'
    return sizeMap[selectSize] as 'small' | 'medium' | 'large'
  }

  // 处理移除标签
  const handleRemoveTag = (option: SelectOption) => {
    emit('removeTag', option)
  }
</script>

<script lang="ts">
  export default {
    name: 'SelectInput',
  }
</script>
