/**
 * Speed UI 主题切换工具
 * 用于动态切换组件库的主题色
 */

export interface ThemeColors {
  primary: string
  primaryHover: string
  primaryActive: string
  primaryDisabled: string
  primaryLightest: string
  primaryLight: string
}

/**
 * 预设主题
 */
export const PRESET_THEMES: Record<string, ThemeColors> = {
  // 默认紫色主题
  default: {
    primary: '#667eea',
    primaryHover: '#7c8aeb',
    primaryActive: '#5a6ce8',
    primaryDisabled: '#b8c5ff',
    primaryLightest: '#eef2ff',
    primaryLight: '#d6e0ff',
  },
  
  // 蓝色主题
  blue: {
    primary: '#1890ff',
    primaryHover: '#40a9ff',
    primaryActive: '#096dd9',
    primaryDisabled: '#91d5ff',
    primaryLightest: '#e6f7ff',
    primaryLight: '#bae7ff',
  },
  
  // 绿色主题
  green: {
    primary: '#52c41a',
    primaryHover: '#73d13d',
    primaryActive: '#389e0d',
    primaryDisabled: '#95de64',
    primaryLightest: '#f6ffed',
    primaryLight: '#d9f7be',
  },
  
  // 红色主题
  red: {
    primary: '#ff4d4f',
    primaryHover: '#ff7875',
    primaryActive: '#d9363e',
    primaryDisabled: '#ffadd2',
    primaryLightest: '#fff1f0',
    primaryLight: '#ffccc7',
  },
  
  // 橙色主题
  orange: {
    primary: '#fa8c16',
    primaryHover: '#ffa940',
    primaryActive: '#d46b08',
    primaryDisabled: '#ffbb96',
    primaryLightest: '#fff7e6',
    primaryLight: '#ffe7ba',
  },
  
  // 紫色主题
  purple: {
    primary: '#722ed1',
    primaryHover: '#9254de',
    primaryActive: '#531dab',
    primaryDisabled: '#b37feb',
    primaryLightest: '#f9f0ff',
    primaryLight: '#efdbff',
  },
}

/**
 * 主题切换器类
 */
export class ThemeSwitcher {
  private currentTheme: string = 'default'
  
  /**
   * 设置主题
   * @param theme 主题名称或自定义主题色
   */
  setTheme(theme: string | ThemeColors): void {
    let colors: ThemeColors
    
    if (typeof theme === 'string') {
      if (!PRESET_THEMES[theme]) {
        console.warn(`主题 "${theme}" 不存在，使用默认主题`)
        colors = PRESET_THEMES.default
        this.currentTheme = 'default'
      } else {
        colors = PRESET_THEMES[theme]
        this.currentTheme = theme
      }
    } else {
      colors = theme
      this.currentTheme = 'custom'
    }
    
    this.applyTheme(colors)
  }
  
  /**
   * 应用主题色到 CSS 变量
   */
  private applyTheme(colors: ThemeColors): void {
    const root = document.documentElement
    
    root.style.setProperty('--sp-color-primary', colors.primary)
    root.style.setProperty('--sp-color-primary-hover', colors.primaryHover)
    root.style.setProperty('--sp-color-primary-active', colors.primaryActive)
    root.style.setProperty('--sp-color-primary-disabled', colors.primaryDisabled)
    root.style.setProperty('--sp-color-primary-lightest', colors.primaryLightest)
    root.style.setProperty('--sp-color-primary-light', colors.primaryLight)
    
    // 触发主题变更事件
    this.dispatchThemeChangeEvent()
  }
  
  /**
   * 获取当前主题
   */
  getCurrentTheme(): string {
    return this.currentTheme
  }
  
  /**
   * 获取所有预设主题名称
   */
  getPresetThemes(): string[] {
    return Object.keys(PRESET_THEMES)
  }
  
  /**
   * 根据主色调自动生成主题色
   */
  generateThemeFromPrimary(primaryColor: string): ThemeColors {
    // 这里可以实现颜色算法，自动生成 hover、active 等状态色
    // 简化版本，实际项目中可以使用更复杂的颜色算法
    return {
      primary: primaryColor,
      primaryHover: this.lightenColor(primaryColor, 0.1),
      primaryActive: this.darkenColor(primaryColor, 0.1),
      primaryDisabled: this.lightenColor(primaryColor, 0.4),
      primaryLightest: this.lightenColor(primaryColor, 0.8),
      primaryLight: this.lightenColor(primaryColor, 0.6),
    }
  }
  
  /**
   * 颜色变亮
   */
  private lightenColor(color: string, amount: number): string {
    // 简化的颜色处理，实际项目中建议使用专业的颜色处理库
    const hex = color.replace('#', '')
    const num = parseInt(hex, 16)
    const r = Math.min(255, Math.floor((num >> 16) + 255 * amount))
    const g = Math.min(255, Math.floor(((num >> 8) & 0x00FF) + 255 * amount))
    const b = Math.min(255, Math.floor((num & 0x0000FF) + 255 * amount))
    return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`
  }
  
  /**
   * 颜色变暗
   */
  private darkenColor(color: string, amount: number): string {
    const hex = color.replace('#', '')
    const num = parseInt(hex, 16)
    const r = Math.max(0, Math.floor((num >> 16) * (1 - amount)))
    const g = Math.max(0, Math.floor(((num >> 8) & 0x00FF) * (1 - amount)))
    const b = Math.max(0, Math.floor((num & 0x0000FF) * (1 - amount)))
    return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`
  }
  
  /**
   * 触发主题变更事件
   */
  private dispatchThemeChangeEvent(): void {
    const event = new CustomEvent('sp-theme-change', {
      detail: {
        theme: this.currentTheme,
        timestamp: Date.now(),
      }
    })
    document.dispatchEvent(event)
  }
  
  /**
   * 监听主题变更事件
   */
  onThemeChange(callback: (theme: string) => void): () => void {
    const handler = (event: CustomEvent) => {
      callback(event.detail.theme)
    }
    
    document.addEventListener('sp-theme-change', handler as EventListener)
    
    // 返回取消监听的函数
    return () => {
      document.removeEventListener('sp-theme-change', handler as EventListener)
    }
  }
}

/**
 * 全局主题切换器实例
 */
export const themeSwitcher = new ThemeSwitcher()

/**
 * 便捷函数：设置主题
 */
export const setTheme = (theme: string | ThemeColors) => {
  themeSwitcher.setTheme(theme)
}

/**
 * 便捷函数：获取当前主题
 */
export const getCurrentTheme = () => {
  return themeSwitcher.getCurrentTheme()
}

/**
 * 便捷函数：获取预设主题列表
 */
export const getPresetThemes = () => {
  return themeSwitcher.getPresetThemes()
}
