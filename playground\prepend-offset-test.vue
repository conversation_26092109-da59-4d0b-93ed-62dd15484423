<template>
  <div class="prepend-offset-test">
    <h2>前置内部图标标签偏移测试</h2>
    <p>测试有前置内部图标时，标签浮动后的 translateX 偏移效果</p>
    
    <div class="test-section">
      <h3>对比测试（无前置 vs 有前置）</h3>
      <div class="size-row">
        <div class="size-item">
          <h4>无前置内部图标</h4>
          <SPInputField
            size="medium"
            label="所有图标点击测试"
            placeholder="标签应该在左边"
            required
            style="margin-bottom: 20px;"
          />
          
          <SPInputField
            :size="50"
            label="动态尺寸测试"
            placeholder="标签应该在左边"
            required
          />
        </div>
        
        <div class="size-item">
          <h4>有前置内部图标</h4>
          <SPInputField
            size="medium"
            label="所有图标点击测试"
            placeholder="标签应该偏移避开图标"
            required
            prepend-icon-inner="Search"
            style="margin-bottom: 20px;"
          />
          
          <SPInputField
            :size="50"
            label="动态尺寸测试"
            placeholder="标签应该偏移避开图标"
            required
            prepend-icon-inner="Mail"
          />
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>不同尺寸测试</h3>
      <div class="size-row">
        <div class="size-item">
          <h4>Small (36px)</h4>
          <SPInputField
            size="small"
            label="邮箱地址"
            placeholder="请输入邮箱"
            required
            prepend-icon-inner="Mail"
          />
        </div>
        
        <div class="size-item">
          <h4>Medium (48px)</h4>
          <SPInputField
            size="medium"
            label="邮箱地址"
            placeholder="请输入邮箱"
            required
            prepend-icon-inner="Mail"
          />
        </div>
        
        <div class="size-item">
          <h4>Large (56px)</h4>
          <SPInputField
            size="large"
            label="邮箱地址"
            placeholder="请输入邮箱"
            required
            prepend-icon-inner="Mail"
          />
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>动态尺寸测试</h3>
      <div class="size-row">
        <div class="size-item">
          <h4>22px (超小)</h4>
          <SPInputField
            :size="22"
            label="邮箱地址"
            placeholder="请输入邮箱"
            required
            prepend-icon-inner="Mail"
          />
        </div>
        
        <div class="size-item">
          <h4>30px (小)</h4>
          <SPInputField
            :size="30"
            label="邮箱地址"
            placeholder="请输入邮箱"
            required
            prepend-icon-inner="Mail"
          />
        </div>
        
        <div class="size-item">
          <h4>70px (大)</h4>
          <SPInputField
            :size="70"
            label="邮箱地址"
            placeholder="请输入邮箱"
            required
            prepend-icon-inner="Mail"
          />
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>长标签测试</h3>
      <div class="size-row">
        <div class="size-item">
          <h4>Medium 长标签</h4>
          <SPInputField
            size="medium"
            label="这是一个很长的标签文本用来测试浮动位置和偏移效果"
            placeholder="请输入内容"
            required
            prepend-icon-inner="Search"
          />
        </div>
        
        <div class="size-item">
          <h4>50px 长标签</h4>
          <SPInputField
            :size="50"
            label="这是一个很长的标签文本用来测试浮动位置和偏移效果"
            placeholder="请输入内容"
            required
            prepend-icon-inner="User"
          />
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>不同图标测试</h3>
      <div class="size-row">
        <div class="size-item">
          <h4>搜索图标</h4>
          <SPInputField
            size="medium"
            label="搜索内容"
            placeholder="请输入搜索关键词"
            prepend-icon-inner="Search"
          />
        </div>
        
        <div class="size-item">
          <h4>用户图标</h4>
          <SPInputField
            size="medium"
            label="用户名"
            placeholder="请输入用户名"
            prepend-icon-inner="User"
          />
        </div>
        
        <div class="size-item">
          <h4>邮件图标</h4>
          <SPInputField
            size="medium"
            label="邮箱地址"
            placeholder="请输入邮箱"
            prepend-icon-inner="Mail"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SPInputField from '../packages/ui/src/components/input-field/input-field.vue'
</script>

<style scoped>
.prepend-offset-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.size-row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.size-item {
  flex: 1;
  min-width: 300px;
}

.size-item h4 {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
}
</style>
