import { defineComponent, computed, ref, type VNode } from 'vue'
import SpIcon from '../icon/Icon.vue'
import { useInputStyles } from '../../composables/useInputStyles'
import type { InputProps } from './types'
import { createInputCoreProps } from './inputCoreProps'
import { createInputEventHandlers } from '../../utils/eventFactory'

// JSX 类型声明
declare global {
  namespace JSX {
    interface IntrinsicElements {
      div: any
      span: any
      input: any
    }
  }
}

// InputCore 的属性（基于 InputProps 但去掉一些业务逻辑属性）
export interface InputCoreProps
  extends Omit<InputProps, 'value' | 'defaultValue'> {
  value?: string | number
  // 扩展属性
  hasValue?: boolean
  focused?: boolean
  mode?: 'input' | 'display'
}

// InputCore 的事件
export interface InputCoreEmits {
  click: [event: MouseEvent]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
  input: [event: Event]
  change: [event: Event]
  keydown: [event: KeyboardEvent]
  clear: []
  'prefix-click': [event: MouseEvent]
  'suffix-click': [event: MouseEvent]
}

export default defineComponent({
  name: 'InputCore',
  props: createInputCoreProps(),
  emits: {
    click: (_event: MouseEvent) => true,
    focus: (_event: FocusEvent) => true,
    blur: (_event: FocusEvent) => true,
    input: (_event: Event) => true,
    change: (_event: Event) => true,
    keydown: (_event: KeyboardEvent) => true,
    clear: () => true,
    'prefix-click': (_event: MouseEvent) => true,
    'suffix-click': (_event: MouseEvent) => true,
  },
  setup(props, { emit, slots, expose }) {
    const wrapperRef = ref<HTMLDivElement>()
    const inputRef = ref<HTMLInputElement>()

    // 计算状态
    const computedDisabled = computed(() => props.disabled)
    const isFocused = computed(() => props.focused)
    const hasValue = computed(() => props.hasValue)
    const showPrefix = computed(() => !!props.prefixIcon || !!slots.prefix)
    const showSuffix = computed(() => {
      return (
        !!props.suffixIcon ||
        !!slots.suffix ||
        (props.clearable && hasValue.value && !props.disabled) ||
        props.loading ||
        (props.showWordLimit && props.maxlength)
      )
    })

    // 使用样式 composable
    const {
      rootClasses,
      wrapperClasses,
      innerClasses,
      prefixClasses,
      suffixClasses,
      prefixIconClasses,
      suffixIconClasses,
      clearIconClasses,
      wordCountClasses,
      iconSize,
    } = useInputStyles(props as any, {
      computedDisabled,
      isFocused,
      hasValue,
      showPrefix,
      showSuffix,
    })

    // 显示清除按钮
    const showClearIcon = computed(() => {
      return (
        props.clearable && hasValue.value && !props.disabled && !props.readonly
      )
    })

    // 字数统计
    const wordCount = computed(() => {
      const value = props.value
      return typeof value === 'string' ? value.length : 0
    })

    // 事件处理 - 使用工厂函数创建
    const {
      handleWrapperClick,
      handleFocus,
      handleBlur,
      handleInput,
      handleChange,
      handleKeydown,
      handlePrefixClick,
      handleSuffixClick,
      handleClear,
    } = createInputEventHandlers(emit, inputRef, props)

    // 渲染前缀区域
    const renderPrefix = (): VNode | null => {
      if (!showPrefix.value) return null

      return (
        <div
          class={prefixClasses.value}
          onClick={handlePrefixClick}
        >
          {props.prefixIcon && (
            <SpIcon
              name={props.prefixIcon}
              size={iconSize.value}
              class={prefixIconClasses.value}
            />
          )}
          {slots.prefix?.()}
        </div>
      )
    }

    // 渲染后缀区域
    const renderSuffix = (): VNode | null => {
      if (!showSuffix.value) return null

      return (
        <div
          class={suffixClasses.value}
          onClick={handleSuffixClick}
        >
          {/* 清除按钮 */}
          {showClearIcon.value && (
            <span
              class={clearIconClasses.value}
              onClick={handleClear}
              onMousedown={(e: MouseEvent) => e.preventDefault()}
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <SpIcon
                name="CloseCircle"
                size={iconSize.value}
                clickable={true}
              />
            </span>
          )}

          {/* 加载状态 */}
          {props.loading && (
            <SpIcon
              name="Loading"
              size={iconSize.value}
              class="sp-input__loading"
            />
          )}

          {/* 自定义后缀图标 */}
          {props.suffixIcon && !props.loading && (
            <SpIcon
              name={props.suffixIcon}
              size={iconSize.value}
              class={suffixIconClasses.value}
            />
          )}

          {/* 字数统计 */}
          {props.showWordLimit && props.maxlength && (
            <span class={wordCountClasses.value}>
              {wordCount.value}/{props.maxlength}
            </span>
          )}

          {/* 后缀插槽 */}
          {slots.suffix?.()}
        </div>
      )
    }

    // 暴露的方法
    const focus = () => {
      inputRef.value?.focus()
    }

    const blur = () => {
      inputRef.value?.blur()
    }

    const select = () => {
      inputRef.value?.select()
    }

    expose({
      wrapperRef,
      inputRef,
      focus,
      blur,
      select,
    })

    // 主渲染函数
    return () => (
      <div class={rootClasses.value}>
        <div
          ref={wrapperRef}
          class={wrapperClasses.value}
          onClick={handleWrapperClick}
        >
          {renderPrefix()}

          {/* 核心输入区域 */}
          <div class={innerClasses.value}>
            {/* inner 插槽内容 - 用于多选标签或 textarea 等 */}
            {slots.inner?.()}

            {/* 只有在非 display 模式下才渲染 input 元素 */}
            {props.mode !== 'display' && (
              <input
                ref={inputRef}
                type={props.type}
                value={props.value}
                placeholder={props.placeholder}
                disabled={props.disabled}
                readonly={props.readonly}
                maxlength={props.maxlength}
                onInput={handleInput}
                onChange={handleChange}
                onFocus={handleFocus}
                onBlur={handleBlur}
                onKeydown={handleKeydown}
              />
            )}
          </div>

          {renderSuffix()}
        </div>

        {/* 验证消息 */}
        {slots.message?.() ||
          (props.validateMessage && (
            <div
              class={[
                'sp-input__message',
                `sp-input__message--${props.validateState || 'error'}`,
              ]}
            >
              {props.validateMessage}
            </div>
          ))}
      </div>
    )
  },
})
