<template>
  <div
    :class="scrollbarClasses"
    :style="scrollbarStyle"
  >
    <div
      ref="wrapperRef"
      :class="bem.e('wrapper')"
      :style="wrapperStyle"
      @scroll="handleScroll"
    >
      <div
        ref="contentRef"
        :class="bem.e('content')"
      >
        <slot />
      </div>
    </div>

    <!-- 垂直滚动条 -->
    <div
      v-show="showVerticalBar"
      :class="bem.e('bar-vertical')"
      @mousedown="handleVerticalBarMouseDown"
    >
      <div
        ref="verticalThumbRef"
        :class="bem.e('thumb-vertical')"
        :style="verticalThumbStyle"
        @mousedown="handleVerticalThumbMouseDown"
      />
    </div>

    <!-- 水平滚动条 -->
    <div
      v-show="showHorizontalBar"
      :class="bem.e('bar-horizontal')"
      @mousedown="handleHorizontalBarMouseDown"
    >
      <div
        ref="horizontalThumbRef"
        :class="bem.e('thumb-horizontal')"
        :style="horizontalThumbStyle"
        @mousedown="handleHorizontalThumbMouseDown"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
  import { bemHelper } from '@speed-ui/config'

  export interface ScrollbarProps {
    /**
     * 最大高度
     */
    maxHeight?: string | number
    /**
     * 最大宽度
     */
    maxWidth?: string | number
    /**
     * 是否始终显示滚动条
     */
    always?: boolean
    /**
     * 滚动条尺寸
     */
    size?: 'small' | 'medium' | 'large'
    /**
     * 是否禁用原生滚动条
     */
    native?: boolean
    /**
     * 滚动条主题
     */
    theme?: 'light' | 'dark' | 'auto'
  }

  const props = withDefaults(defineProps<ScrollbarProps>(), {
    maxHeight: undefined,
    maxWidth: undefined,
    always: false,
    size: 'medium',
    native: false,
    theme: 'auto',
  })

  const emit = defineEmits<{
    scroll: [event: Event]
  }>()

  // BEM 助手
  const bem = bemHelper('scrollbar')

  // 引用
  const wrapperRef = ref<HTMLElement>()
  const contentRef = ref<HTMLElement>()
  const verticalThumbRef = ref<HTMLElement>()
  const horizontalThumbRef = ref<HTMLElement>()

  // 状态
  const isScrolling = ref(false)
  const showVerticalBar = ref(false)
  const showHorizontalBar = ref(false)
  const verticalThumbHeight = ref(0)
  const verticalThumbTop = ref(0)
  const horizontalThumbWidth = ref(0)
  const horizontalThumbLeft = ref(0)

  // 拖拽状态
  const isDragging = ref(false)
  const dragStartY = ref(0)
  const dragStartX = ref(0)
  const dragStartScrollTop = ref(0)
  const dragStartScrollLeft = ref(0)

  // 计算样式类名
  const scrollbarClasses = computed(() => [
    bem.b(),
    bem.s('custom'), // 主题类名
    bem.m(props.size),
    bem.m(props.theme),
    {
      [bem.m('always')]: props.always,
      [bem.m('native')]: props.native,
      [bem.m('scrolling')]: isScrolling.value,
    },
  ])

  // 容器样式
  const scrollbarStyle = computed(() => {
    const styles: Record<string, string> = {}

    if (props.maxHeight) {
      styles.maxHeight =
        typeof props.maxHeight === 'number'
          ? `${props.maxHeight}px`
          : props.maxHeight
    }

    if (props.maxWidth) {
      styles.maxWidth =
        typeof props.maxWidth === 'number'
          ? `${props.maxWidth}px`
          : props.maxWidth
    }

    return styles
  })

  // 包装器样式
  const wrapperStyle = computed(() => {
    const styles: Record<string, string> = {}

    if (props.native) {
      // native 模式下显示原生滚动条
      styles.overflow = 'auto'

      // 设置最大高度和宽度
      if (props.maxHeight) {
        styles.maxHeight =
          typeof props.maxHeight === 'number'
            ? `${props.maxHeight}px`
            : props.maxHeight
      }

      if (props.maxWidth) {
        styles.maxWidth =
          typeof props.maxWidth === 'number'
            ? `${props.maxWidth}px`
            : props.maxWidth
      }
    } else {
      // 自定义滚动条模式
      styles.overflow = 'scroll'

      // 设置最大高度和宽度
      if (props.maxHeight) {
        styles.maxHeight =
          typeof props.maxHeight === 'number'
            ? `${props.maxHeight}px`
            : props.maxHeight
      }

      if (props.maxWidth) {
        styles.maxWidth =
          typeof props.maxWidth === 'number'
            ? `${props.maxWidth}px`
            : props.maxWidth
      }
    }

    return styles
  })

  // 垂直滚动条拇指样式
  const verticalThumbStyle = computed(() => ({
    height: `${verticalThumbHeight.value}px`,
    transform: `translateY(${verticalThumbTop.value}px)`,
  }))

  // 水平滚动条拇指样式
  const horizontalThumbStyle = computed(() => ({
    width: `${horizontalThumbWidth.value}px`,
    transform: `translateX(${horizontalThumbLeft.value}px)`,
  }))

  // 更新滚动条状态
  const updateScrollbars = () => {
    if (!wrapperRef.value || !contentRef.value) return

    const wrapper = wrapperRef.value
    const content = contentRef.value

    const wrapperHeight = wrapper.clientHeight
    const wrapperWidth = wrapper.clientWidth
    const contentHeight = content.scrollHeight
    const contentWidth = content.scrollWidth

    // 垂直滚动条
    const needVerticalBar = contentHeight > wrapperHeight
    showVerticalBar.value = needVerticalBar && !props.native

    if (needVerticalBar) {
      const scrollRatio = wrapper.scrollTop / (contentHeight - wrapperHeight)
      const thumbHeight = Math.max(
        20,
        (wrapperHeight / contentHeight) * wrapperHeight
      )
      const maxThumbTop = wrapperHeight - thumbHeight

      verticalThumbHeight.value = thumbHeight
      verticalThumbTop.value = scrollRatio * maxThumbTop
    }

    // 水平滚动条
    const needHorizontalBar = contentWidth > wrapperWidth
    showHorizontalBar.value = needHorizontalBar && !props.native

    if (needHorizontalBar) {
      const scrollRatio = wrapper.scrollLeft / (contentWidth - wrapperWidth)
      const thumbWidth = Math.max(
        20,
        (wrapperWidth / contentWidth) * wrapperWidth
      )
      const maxThumbLeft = wrapperWidth - thumbWidth

      horizontalThumbWidth.value = thumbWidth
      horizontalThumbLeft.value = scrollRatio * maxThumbLeft
    }
  }

  // 处理滚动事件
  const handleScroll = (event: Event) => {
    updateScrollbars()

    isScrolling.value = true
    clearTimeout(scrollTimer.value)
    scrollTimer.value = setTimeout(() => {
      isScrolling.value = false
    }, 300)

    emit('scroll', event)
  }

  // 滚动定时器
  const scrollTimer = ref<ReturnType<typeof setTimeout>>()

  // 垂直滚动条点击
  const handleVerticalBarMouseDown = (event: MouseEvent) => {
    // 阻止事件冒泡，防止触发外部点击处理（如关闭下拉框）
    event.preventDefault()
    event.stopPropagation()

    if (!wrapperRef.value || !verticalThumbRef.value) return

    const wrapper = wrapperRef.value
    const thumb = verticalThumbRef.value
    const rect = wrapper.getBoundingClientRect()
    const thumbRect = thumb.getBoundingClientRect()

    const clickY = event.clientY - rect.top
    const thumbTop = thumbRect.top - rect.top

    if (clickY < thumbTop) {
      // 点击在拇指上方，向上滚动一页
      wrapper.scrollTop -= wrapper.clientHeight
    } else if (clickY > thumbTop + thumb.clientHeight) {
      // 点击在拇指下方，向下滚动一页
      wrapper.scrollTop += wrapper.clientHeight
    }

    updateScrollbars()

    // 防止轨道点击产生 click 事件
    const preventClick = (e: Event) => {
      e.preventDefault()
      e.stopPropagation()
    }

    document.addEventListener('click', preventClick, {
      capture: true,
      once: true,
    })
  }

  // 水平滚动条点击
  const handleHorizontalBarMouseDown = (event: MouseEvent) => {
    // 阻止事件冒泡，防止触发外部点击处理（如关闭下拉框）
    event.preventDefault()
    event.stopPropagation()

    if (!wrapperRef.value || !horizontalThumbRef.value) return

    const wrapper = wrapperRef.value
    const thumb = horizontalThumbRef.value
    const rect = wrapper.getBoundingClientRect()
    const thumbRect = thumb.getBoundingClientRect()

    const clickX = event.clientX - rect.left
    const thumbLeft = thumbRect.left - rect.left

    if (clickX < thumbLeft) {
      // 点击在拇指左侧，向左滚动一页
      wrapper.scrollLeft -= wrapper.clientWidth
    } else if (clickX > thumbLeft + thumb.clientWidth) {
      // 点击在拇指右侧，向右滚动一页
      wrapper.scrollLeft += wrapper.clientWidth
    }

    updateScrollbars()

    // 防止轨道点击产生 click 事件
    const preventClick = (e: Event) => {
      e.preventDefault()
      e.stopPropagation()
    }

    document.addEventListener('click', preventClick, {
      capture: true,
      once: true,
    })
  }

  // 垂直拇指拖拽
  const handleVerticalThumbMouseDown = (event: MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()

    if (!wrapperRef.value) return

    isDragging.value = true
    dragStartY.value = event.clientY
    dragStartScrollTop.value = wrapperRef.value.scrollTop

    document.addEventListener('mousemove', handleVerticalThumbMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
    document.body.style.userSelect = 'none'
  }

  // 水平拇指拖拽
  const handleHorizontalThumbMouseDown = (event: MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()

    if (!wrapperRef.value) return

    isDragging.value = true
    dragStartX.value = event.clientX
    dragStartScrollLeft.value = wrapperRef.value.scrollLeft

    document.addEventListener('mousemove', handleHorizontalThumbMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
    document.body.style.userSelect = 'none'
  }

  // 垂直拇指移动
  const handleVerticalThumbMouseMove = (event: MouseEvent) => {
    if (!isDragging.value || !wrapperRef.value || !contentRef.value) return

    const wrapper = wrapperRef.value
    const content = contentRef.value
    const deltaY = event.clientY - dragStartY.value

    const wrapperHeight = wrapper.clientHeight
    const contentHeight = content.scrollHeight
    const maxScrollTop = contentHeight - wrapperHeight

    const scrollRatio = deltaY / wrapperHeight
    const newScrollTop = dragStartScrollTop.value + scrollRatio * maxScrollTop

    wrapper.scrollTop = Math.max(0, Math.min(maxScrollTop, newScrollTop))
    updateScrollbars()
  }

  // 水平拇指移动
  const handleHorizontalThumbMouseMove = (event: MouseEvent) => {
    if (!isDragging.value || !wrapperRef.value || !contentRef.value) return

    const wrapper = wrapperRef.value
    const content = contentRef.value
    const deltaX = event.clientX - dragStartX.value

    const wrapperWidth = wrapper.clientWidth
    const contentWidth = content.scrollWidth
    const maxScrollLeft = contentWidth - wrapperWidth

    const scrollRatio = deltaX / wrapperWidth
    const newScrollLeft =
      dragStartScrollLeft.value + scrollRatio * maxScrollLeft

    wrapper.scrollLeft = Math.max(0, Math.min(maxScrollLeft, newScrollLeft))
    updateScrollbars()
  }

  // 鼠标释放
  const handleMouseUp = (event: MouseEvent) => {
    // 阻止事件冒泡，防止触发外部点击处理（如关闭下拉框）
    event.preventDefault()
    event.stopPropagation()

    // 为了防止拖拽结束后产生 click 事件，我们需要暂时禁用点击事件
    if (isDragging.value) {
      const preventClick = (e: Event) => {
        e.preventDefault()
        e.stopPropagation()
      }

      // 暂时阻止 click 事件
      document.addEventListener('click', preventClick, {
        capture: true,
        once: true,
      })

      // 延迟移除，确保 click 事件被阻止
      setTimeout(() => {
        document.removeEventListener('click', preventClick, { capture: true })
      }, 0)
    }

    cleanupDrag()
  }

  // 清理拖拽状态
  const cleanupDrag = () => {
    isDragging.value = false
    document.removeEventListener('mousemove', handleVerticalThumbMouseMove)
    document.removeEventListener('mousemove', handleHorizontalThumbMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    document.body.style.userSelect = ''
  }

  // 生命周期
  onMounted(() => {
    nextTick(() => {
      updateScrollbars()
    })

    // 监听窗口大小变化
    window.addEventListener('resize', updateScrollbars)

    // 监听内容变化
    if (wrapperRef.value) {
      const resizeObserver = new ResizeObserver(updateScrollbars)
      resizeObserver.observe(wrapperRef.value)

      onUnmounted(() => {
        resizeObserver.disconnect()
      })
    }
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateScrollbars)
    clearTimeout(scrollTimer.value)
    cleanupDrag()
  })

  // 暴露方法
  defineExpose({
    scrollTo: (options: ScrollToOptions | number, y?: number) => {
      if (!wrapperRef.value) return

      if (typeof options === 'number') {
        wrapperRef.value.scrollTo(options, y || 0)
      } else {
        wrapperRef.value.scrollTo(options)
      }

      nextTick(updateScrollbars)
    },
    scrollTop: (top: number) => {
      if (!wrapperRef.value) return
      wrapperRef.value.scrollTop = top
      nextTick(updateScrollbars)
    },
    scrollLeft: (left: number) => {
      if (!wrapperRef.value) return
      wrapperRef.value.scrollLeft = left
      nextTick(updateScrollbars)
    },
    update: updateScrollbars,
  })
</script>

<script lang="ts">
  export default {
    name: 'SpScrollbar',
  }
</script>
