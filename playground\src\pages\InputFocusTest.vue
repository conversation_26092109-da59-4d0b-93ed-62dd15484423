<template>
  <div class="input-focus-test">
    <h1>Input Focus Test</h1>

    <div class="test-section">
      <h2>基础输入框聚焦测试</h2>
      <p>测试聚焦状态是否在鼠标移出后仍然保持</p>

      <div class="test-group">
        <label>普通输入框:</label>
        <sp-input
          v-model:value="value1"
          placeholder="点击聚焦，然后移动鼠标测试"
          style="width: 300px; margin: 10px 0"
        />
        <sp-input
          v-model:value="value1"
          placeholder="点击聚焦，然后移动鼠标测试"
          textarea
        />
        <p>当前值: {{ value1 }}</p>
      </div>

      <div class="test-group">
        <label>带清除按钮:</label>
        <sp-input
          v-model:value="value2"
          placeholder="带清除按钮的输入框"
          clearable
          style="width: 300px; margin: 10px 0"
        />
        <p>当前值: {{ value2 }}</p>
      </div>

      <div class="test-group">
        <label>密码输入框:</label>
        <sp-input
          v-model:value="value3"
          type="password"
          placeholder="密码输入框"
          show-password
          style="width: 300px; margin: 10px 0"
        />
        <p>当前值: {{ value3 }}</p>
      </div>

      <div class="test-group">
        <label>验证状态 - 错误:</label>
        <sp-input
          v-model:value="value4"
          placeholder="错误状态输入框"
          validate-state="error"
          validate-message="这是一个错误消息"
          style="width: 300px; margin: 10px 0"
        />
        <p>当前值: {{ value4 }}</p>
      </div>

      <div class="test-group">
        <label>验证状态 - 成功:</label>
        <sp-input
          v-model:value="value5"
          placeholder="成功状态输入框"
          validate-state="success"
          validate-message="验证成功"
          style="width: 300px; margin: 10px 0"
        />
        <p>当前值: {{ value5 }}</p>
      </div>

      <div class="test-group">
        <label>验证状态 - 警告:</label>
        <sp-input
          v-model:value="value6"
          placeholder="警告状态输入框"
          validate-state="warning"
          validate-message="这是一个警告"
          style="width: 300px; margin: 10px 0"
        />
        <p>当前值: {{ value6 }}</p>
      </div>

      <div class="test-group">
        <label>文本域测试:</label>
        <sp-input
          v-model:value="textareaValue"
          placeholder="这应该是一个文本域"
          textarea
          :rows="4"
          style="width: 300px; margin: 10px 0"
        />
        <p>当前值: {{ textareaValue }}</p>
      </div>

      <div class="test-group">
        <label>文本线模式:</label>
        <sp-input
          clearable
          v-model:value="textlineValue"
          placeholder="文本线模式输入框"
          textline
          style="width: 300px; margin: 10px 0"
        />
        <p>当前值: {{ textlineValue }}</p>
      </div>

      <div class="test-group">
        <label>格子输入模式:</label>
        <sp-input
          v-model:value="tabelValue"
          placeholder="格子输入"
          texttabel
          :td="6"
          style="margin: 10px 0"
        />
        <p>当前值: {{ tabelValue }}</p>
      </div>
    </div>

    <div class="instructions">
      <h3>测试说明:</h3>
      <ol>
        <li>点击输入框使其获得焦点</li>
        <li>观察边框颜色和阴影效果</li>
        <li>将鼠标移出输入框但不点击其他地方</li>
        <li>聚焦样式应该保持不变</li>
        <li>只有点击其他地方时聚焦样式才应该消失</li>
      </ol>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  const value1 = ref('')
  const value2 = ref('测试清除')
  const value3 = ref('')
  const value4 = ref('错误值')
  const value5 = ref('正确值')
  const value6 = ref('警告值')
  const textareaValue = ref('这是文本域的内容\n可以多行输入')
  const textlineValue = ref('')
  const tabelValue = ref('')
</script>

<style scoped>
  .input-focus-test {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
  }

  .test-section {
    margin: 20px 0;
  }

  .test-group {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;
  }

  .test-group label {
    display: block;
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
  }

  .test-group p {
    margin: 8px 0 0 0;
    color: #666;
    font-size: 14px;
  }

  .instructions {
    margin-top: 30px;
    padding: 20px;
    background: #f0f8ff;
    border-radius: 8px;
    border-left: 4px solid #1890ff;
  }

  .instructions h3 {
    margin-top: 0;
    color: #1890ff;
  }

  .instructions ol {
    margin: 10px 0;
    padding-left: 20px;
  }

  .instructions li {
    margin: 5px 0;
    line-height: 1.5;
  }
</style>
