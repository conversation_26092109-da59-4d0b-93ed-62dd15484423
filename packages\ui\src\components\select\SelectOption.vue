<!--
  SelectOption.vue - Select 选项子组件
  
  用法：
  <sp-select v-model:value="value">
    <sp-select-option value="1" label="选项一" />
    <sp-select-option value="2" label="选项二" />
    <sp-select-option value="3" label="选项三" disabled />
  </sp-select>
-->

<template>
  <!-- 这个组件不渲染任何内容，只是用来收集选项数据 -->
</template>

<script setup lang="ts">
  import { inject, onMounted, onUnmounted } from 'vue'
  import type { SelectOption } from './select'

  /**
   * SelectOption - Select 选项子组件
   * 
   * 特点：
   * 1. 不渲染任何 DOM，只用于收集选项数据
   * 2. 通过 inject/provide 与父组件 Select 通信
   * 3. 支持 value、label、disabled 等属性
   * 4. 支持插槽内容作为 label
   */

  interface SelectOptionProps {
    // 选项值
    value: string | number
    // 选项标签（如果不提供，使用默认插槽内容）
    label?: string
    // 是否禁用
    disabled?: boolean
    // 其他自定义属性
    [key: string]: any
  }

  interface SelectOptionSlots {
    default?: () => any  // 默认插槽作为 label
  }

  const props = defineProps<SelectOptionProps>()
  const slots = defineSlots<SelectOptionSlots>()

  // 从父组件 Select 注入选项注册函数
  const registerOption = inject<(option: SelectOption) => void>('selectRegisterOption')
  const unregisterOption = inject<(value: string | number) => void>('selectUnregisterOption')

  // 计算选项数据
  const getOptionData = (): SelectOption => {
    // 如果有 label 属性，使用 label；否则使用插槽内容
    let label = props.label
    
    // 如果没有 label 属性且有默认插槽，尝试获取插槽文本内容
    if (!label && slots.default) {
      // 注意：这里只是一个简化实现，实际项目中可能需要更复杂的插槽内容提取
      label = 'Option' // 占位符，实际使用时建议提供 label 属性
    }

    return {
      value: props.value,
      label: label || String(props.value),
      disabled: props.disabled || false,
      // 传递其他自定义属性
      ...Object.fromEntries(
        Object.entries(props).filter(([key]) => 
          !['value', 'label', 'disabled'].includes(key)
        )
      )
    }
  }

  // 组件挂载时注册选项
  onMounted(() => {
    if (registerOption) {
      registerOption(getOptionData())
    } else {
      console.warn('SelectOption: 必须在 Select 组件内使用')
    }
  })

  // 组件卸载时注销选项
  onUnmounted(() => {
    if (unregisterOption) {
      unregisterOption(props.value)
    }
  })

  // 监听 props 变化，更新选项数据
  // 注意：这里简化处理，实际项目中可能需要 watch 来处理 props 变化
</script>

<script lang="ts">
  export default {
    name: 'SpSelectOption',
  }
</script>
