# Input 输入框

通过鼠标或键盘输入字符。

## 基础用法

基本的输入框用法。

<script setup>
import { ref } from 'vue'
import { Input } from '@speed-ui/ui'
import '@speed-ui/ui/styles'

const value1 = ref('')
const value2 = ref('默认值')
const value3 = ref('')
const value4 = ref('')
const value5 = ref('')
const password = ref('')
const textarea = ref('')
const value6 = ref('')
const value7 = ref('')
const value8 = ref('')
const value9 = ref('')
</script>

<div class="demo-block">
  <Input v-model="value1" placeholder="请输入内容" />
  <Input v-model="value2" placeholder="请输入内容" />
  <p>输入的值：{{ value1 }}</p>
</div>

```vue
<template>
  <Input v-model="value" placeholder="请输入内容" />
</template>

<script setup>
import { ref } from 'vue'
import { Input } from '@speed-ui/ui'

const value = ref('')
</script>
```

## 禁用状态

通过 `disabled` 属性指定是否禁用 input 组件。

<div class="demo-block">
  <Input v-model="value1" placeholder="请输入内容" disabled />
</div>

```vue
<template>
  <Input v-model="value" placeholder="请输入内容" disabled />
</template>
```

## 不同尺寸

使用 `size` 属性改变输入框大小。

<div class="demo-block">
  <div style="margin-bottom: 15px;">
    <Input v-model="value3" size="large" placeholder="大尺寸" />
  </div>
  <div style="margin-bottom: 15px;">
    <Input v-model="value4" placeholder="默认尺寸" />
  </div>
  <div>
    <Input v-model="value5" size="small" placeholder="小尺寸" />
  </div>
</div>

```vue
<template>
  <Input v-model="value1" size="large" placeholder="大尺寸" />
  <Input v-model="value2" placeholder="默认尺寸" />
  <Input v-model="value3" size="small" placeholder="小尺寸" />
</template>
```

## 密码框

使用 `type="password"` 即可得到一个密码框。

<div class="demo-block">
  <Input v-model="password" type="password" placeholder="请输入密码" show-password />
</div>

```vue
<template>
  <Input v-model="password" type="password" placeholder="请输入密码" show-password />
</template>
```

## 文本域

用于输入多行文本信息，通过将 `type` 属性的值指定为 textarea。

<div class="demo-block">
  <Input 
    v-model="textarea" 
    type="textarea" 
    placeholder="请输入内容" 
    :rows="3"
  />
</div>

```vue
<template>
  <Input 
    v-model="textarea" 
    type="textarea" 
    placeholder="请输入内容" 
    :rows="3"
  />
</template>
```

## 前缀和后缀

可以通过 slot 来放置前缀和后缀内容。

<div class="demo-block">
  <div style="margin-bottom: 15px;">
    <Input v-model="value6" placeholder="请输入内容">
      <template #prefix>
        <span>🔍</span>
      </template>
    </Input>
  </div>
  <div>
    <Input v-model="value7" placeholder="请输入内容">
      <template #suffix>
        <span>📧</span>
      </template>
    </Input>
  </div>
</div>

```vue
<template>
  <Input v-model="value1" placeholder="请输入内容">
    <template #prefix>
      <span>🔍</span>
    </template>
  </Input>
  
  <Input v-model="value2" placeholder="请输入内容">
    <template #suffix>
      <span>📧</span>
    </template>
  </Input>
</template>
```

## 字数限制

可以通过 `maxlength` 和 `show-word-limit` 属性来限制字符数并展示字数统计。

<div class="demo-block">
  <div style="margin-bottom: 15px;">
    <Input 
      v-model="value8" 
      placeholder="请输入内容" 
      maxlength="10" 
      show-word-limit 
    />
  </div>
  <div>
    <Input 
      v-model="value9" 
      type="textarea" 
      placeholder="请输入内容" 
      maxlength="30" 
      show-word-limit 
      :rows="3"
    />
  </div>
</div>

```vue
<template>
  <Input 
    v-model="value1" 
    placeholder="请输入内容" 
    maxlength="10" 
    show-word-limit 
  />
  
  <Input 
    v-model="value2" 
    type="textarea" 
    placeholder="请输入内容" 
    maxlength="30" 
    show-word-limit 
    :rows="3"
  />
</template>
```

## API

### Input Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| v-model | 绑定值 | string / number | — | — |
| type | 类型 | string | text / textarea / password | text |
| placeholder | 输入框占位文本 | string | — | — |
| disabled | 是否禁用 | boolean | — | false |
| readonly | 是否只读 | boolean | — | false |
| size | 输入框尺寸 | string | large / default / small | default |
| maxlength | 原生属性，最大输入长度 | number | — | — |
| show-word-limit | 是否显示输入字数统计 | boolean | — | false |
| show-password | 是否显示密码切换按钮 | boolean | — | false |
| clearable | 是否可清空 | boolean | — | false |
| rows | 输入框行数（仅 type="textarea" 时有效） | number | — | 2 |

### Input Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| blur | 当 input 失去焦点时触发 | (event: Event) |
| focus | 当 input 获得焦点时触发 | (event: Event) |
| change | 仅当 modelValue 改变时，当 input 失去焦点或用户按 Enter 时触发 | (value: string \| number) |
| input | 在 Input 值改变时触发 | (value: string \| number) |
| clear | 在点击由 clearable 属性生成的清空按钮时触发 | — |

### Input Slots

| 插槽名 | 说明 |
|--------|------|
| prefix | 输入框头部内容 |
| suffix | 输入框尾部内容 |

<style scoped>
.demo-block {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 24px;
  margin: 24px 0;
}

.demo-block > * {
  margin: 0;
}

.demo-block > *:not(:last-child) {
  margin-bottom: 16px;
}
</style> 