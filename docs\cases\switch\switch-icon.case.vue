<text>
> 允许添加icon图标
</text>

<template>
    <sp-switch v-model:value="value1" :button-icon="Person"/>
    <sp-switch v-model:value="value1" :button-icon="Notifications"/>
    <sp-switch v-model:value="value1" :button-icon="ArrowForward"/>
</template>

<script setup>
    import { Person, Notifications, ArrowForward } from '@vicons/ionicons5'
    import { ref } from 'vue'
    const value1 = ref(true)
</script>