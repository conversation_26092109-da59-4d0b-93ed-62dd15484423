import type { ComputedRef } from 'vue'

export type DropdownTrigger = 'click' | 'hover' | 'contextmenu' | 'focus'

export type DropdownPlacement =
  | 'bottom'
  | 'bottom-start'
  | 'bottom-end'
  | 'top'
  | 'top-start'
  | 'top-end'
  | 'left'
  | 'left-start'
  | 'left-end'
  | 'right'
  | 'right-start'
  | 'right-end'

export interface DropdownProps {
  /** 是否显示 */
  visible?: boolean
  /** 默认是否显示 */
  defaultVisible?: boolean
  /** 触发方式 */
  trigger?: DropdownTrigger | DropdownTrigger[]
  /** 弹出位置 */
  placement?: DropdownPlacement
  /** 偏移距离 */
  offset?: number
  /** 是否禁用 */
  disabled?: boolean
  /** 点击选项时是否关闭 */
  closeOnSelect?: boolean
  /** 点击外部时是否关闭 */
  closeOnClickOutside?: boolean
  /** 是否显示箭头 */
  arrow?: boolean
  /** 自定义容器 */
  getPopupContainer?: () => HTMLElement
  /** 层级 */
  zIndex?: number
  /** 无障碍标签 */
  ariaLabel?: string
}

export interface DropdownContext {
  props: DropdownProps
  visible: ComputedRef<boolean>
  handleUpdateVisible: (visible: boolean) => void
  handleSelect: () => void
  handleClickOutside: () => void
}

export interface DropdownEvents {
  'update:visible': [visible: boolean]
  visibleChange: [visible: boolean]
  select: []
  clickOutside: []
}
