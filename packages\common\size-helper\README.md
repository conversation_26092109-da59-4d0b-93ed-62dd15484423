# @speed-ui/size

Speed UI 组件尺寸助手工具，用于统一管理组件尺寸相关的类型、常量、工具函数和 composables。

## 特性

- 🎯 **统一的尺寸类型**：提供标准化的组件尺寸类型定义
- 🔄 **尺寸映射**：支持不同组件间的尺寸映射转换
- 🛠️ **工具函数**：丰富的尺寸处理工具函数
- ⚡ **Vue Composables**：响应式的尺寸处理逻辑
- 📦 **TypeScript 支持**：完整的类型定义和推断

## 安装

```bash
npm install @speed-ui/size
```

## 基础用法

### 类型定义

```typescript
import type { ComponentSize, SizeProps } from '@speed-ui/size'

// 标准尺寸类型
type Size = ComponentSize // 'small' | 'default' | 'large'

// 组件 Props 接口
interface ButtonProps extends SizeProps {
  // 其他属性
}
```

### 工具函数

```typescript
import {
  mapSizeToScrollbar,
  getSizeValue,
  getSizePixels,
  isValidSize,
  normalizeSizeValue,
} from '@speed-ui/size'

// 尺寸映射
const scrollbarSize = mapSizeToScrollbar('default') // 'medium'

// 获取尺寸数值
const sizeValue = getSizeValue('large') // 20

// 获取像素值
const pixels = getSizePixels('small') // '12px'

// 验证尺寸
const isValid = isValidSize('medium') // false

// 标准化尺寸
const normalized = normalizeSizeValue(undefined, 'large') // 'large'
```

### Vue Composables

```typescript
import { useSize } from '@speed-ui/size'

// 在组件中使用
const {
  currentSize,
  sizeValue,
  sizeClass,
  scrollbarSize,
  isSmall,
  isDefault,
  isLarge,
} = useSize(() => props.size)

// 检查特定尺寸
const isCurrentSmall = isSmall.value // boolean
```

## API 参考

### 类型

#### ComponentSize

标准组件尺寸类型：`'small' | 'default' | 'large'`

#### ExtendedComponentSize

扩展组件尺寸类型：`'mini' | 'small' | 'default' | 'large' | 'huge'`

#### SizeProps

尺寸 Props 接口：

```typescript
interface SizeProps {
  size?: ComponentSize
}
```

### 常量

#### DEFAULT_SIZE

默认尺寸值：`'default'`

#### COMPONENT_SIZES

所有可用尺寸：`['small', 'default', 'large']`

#### SIZE_MAPPING

尺寸映射配置：

```typescript
{
  selectToScrollbar: {
    small: 'small',
    default: 'medium',
    large: 'large'
  }
}
```

### 工具函数

#### mapSizeToScrollbar(size)

将组件尺寸映射到滚动条尺寸

#### getSizeValue(size)

获取尺寸对应的数值

#### getSizePixels(size)

获取尺寸对应的像素值字符串

#### isValidSize(size)

验证尺寸是否有效

#### normalizeSizeValue(size, defaultSize?)

标准化尺寸值

### Composables

#### useSize(size, options?)

尺寸相关的 composable

**参数：**

- `size`: 尺寸值或获取尺寸的函数
- `options`: 配置选项

**返回值：**

- `currentSize`: 当前尺寸
- `sizeValue`: 尺寸数值
- `sizeClass`: 尺寸类名
- `scrollbarSize`: 滚动条尺寸
- `isSize(size)`: 检查是否为指定尺寸
- `isSmall/isDefault/isLarge`: 尺寸检查快捷方法

## 使用场景

### 1. 新组件开发

```typescript
// Button 组件
import type { ComponentSize, SizeProps } from '@speed-ui/size'
import { useSize } from '@speed-ui/size'

interface ButtonProps extends SizeProps {
  // 其他属性
}

const { currentSize, sizeClass } = useSize(() => props.size)
```

### 2. 组件间尺寸映射

```typescript
// Select 组件内部的 Scrollbar
import { mapSizeToScrollbar } from '@speed-ui/size'

const scrollbarSize = computed(() => {
  return mapSizeToScrollbar(props.size || 'default')
})
```

### 3. 尺寸验证和标准化

```typescript
import { isValidSize, normalizeSizeValue } from '@speed-ui/size'

// 验证用户输入
if (!isValidSize(userInput)) {
  console.warn('Invalid size provided')
}

// 标准化尺寸
const finalSize = normalizeSizeValue(props.size, 'default')
```

## 扩展

要添加新的尺寸或映射关系，只需修改相应的常量定义：

```typescript
// 在 constants.ts 中添加新的映射
export const SIZE_MAPPING = {
  selectToScrollbar: {
    /* ... */
  },
  buttonToInput: {
    small: 'compact',
    default: 'normal',
    large: 'comfortable',
  },
} as const
```

## 许可证

MIT License
