# useVModel

通用的 v-model 双向绑定 composable，统一处理组件的双向绑定逻辑。

## 基础用法

```typescript
import { useVModel } from '@speed-ui/hooks'

// 在组件的 setup 函数中
const props = defineProps<{ value: string }>()
const emit = defineEmits<{ 'update:value': [value: string] }>()

const { value, updateValue, hasValue, isEmpty } = useVModel(props, emit, {
  prop: 'value',
  event: 'update:value',
  defaultValue: props.value,
})

// 更新值
const handleChange = (newValue: string) => {
  updateValue(newValue)
}
```

## 高级用法

```typescript
// 表单集成
const { value, updateValue } = useVModel(props, emit, {
  prop: 'value',
  event: 'update:value',
  defaultValue: props.value,
  formField: formItemField, // 注入的表单字段
  onAfterUpdate: newValue => {
    emit('change', newValue)
  },
})

// 自定义 v-model
const { value: visible, updateValue: updateVisible } = useVModel(props, emit, {
  prop: 'visible',
  event: 'update:visible',
  defaultValue: false,
})

// 类型转换和验证
const { value, updateValue } = useVModel(props, emit, {
  prop: 'value',
  event: 'update:value',
  defaultValue: '',
  parser: val => String(val),
  formatter: val => val.toUpperCase(),
  validator: val => val.length <= 10,
})
```

## API

### 参数

- `props` - 组件的 props 对象
- `emit` - 组件的 emit 函数
- `options` - 配置选项

### 配置选项

| 选项             | 类型       | 默认值           | 说明             |
| ---------------- | ---------- | ---------------- | ---------------- |
| `prop`           | `string`   | `'value'`        | 绑定的 prop 名称 |
| `event`          | `string`   | `'update:value'` | 发出的事件名称   |
| `defaultValue`   | `T`        | -                | 默认值           |
| `formField`      | `any`      | -                | 表单字段对象     |
| `onBeforeUpdate` | `function` | -                | 更新前处理函数   |
| `onAfterUpdate`  | `function` | -                | 更新后处理函数   |
| `validator`      | `function` | -                | 值验证函数       |
| `parser`         | `function` | -                | 输入值转换函数   |
| `formatter`      | `function` | -                | 输出值转换函数   |
| `debug`          | `boolean`  | `false`          | 是否开启调试日志 |

### 返回值

| 属性          | 类型                   | 说明               |
| ------------- | ---------------------- | ------------------ |
| `value`       | `Ref<T>`               | 当前值的响应式引用 |
| `updateValue` | `function`             | 更新值的方法       |
| `resetValue`  | `function`             | 重置值的方法       |
| `hasValue`    | `ComputedRef<boolean>` | 是否有值           |
| `isEmpty`     | `ComputedRef<boolean>` | 是否为空           |

## 支持的组件类型

- **Input** - `v-model:value`
- **Select** - `v-model:value`
- **Switch** - `v-model:checked`
- **Checkbox** - `v-model:value`
- **Modal** - `v-model:visible`
- **任意自定义组件** - `v-model:xxx`
