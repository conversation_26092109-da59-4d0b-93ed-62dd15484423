---
description: 
globs: packages/ui/src/components/**/*.vue,packages/ui/src/components/**/*.ts,packages/theme-default/src/**/*.scss
alwaysApply: false
---
# UI 组件开发指南

## 组件结构

每个组件应该遵循以下结构：

```
button/                    # 组件文件夹（小写）
├── button.vue            # 组件主文件（小写）
├── button.ts             # 组件类型定义（小写）
├── style/                # 样式文件
│   └── index.scss       # 组件样式引入文件
└── __tests__/           # 测试文件
```

## 样式分离规范

1. 组件样式定义在 `packages/theme-default/src` 目录下
2. 每个组件在 theme-default 中对应一个样式文件，如 `button.scss`
3. 组件目录下的 `style/index.scss` 用于引入主题包中的样式

示例：

```scss
// packages/theme-default/src/button.scss
.sp-button {
  // 按钮基础样式
  &--primary {
    // 主要按钮样式
  }
  &--secondary {
    // 次要按钮样式
  }
}

// packages/ui/src/components/button/style/index.scss
@import '@theme-default/src/button.scss';
```

## 类型定义规范

1. 组件类型定义放在与组件同名的 `.ts` 文件中
2. 类型文件命名与组件名保持一致（小写）

示例：

```typescript
// button.ts
export interface ButtonProps {
  type?: 'primary' | 'secondary' | 'text' | 'link'
  size?: 'small' | 'medium' | 'large'
  disabled?: boolean
  loading?: boolean
  // ... 其他属性
}

export interface ButtonEmits {
  (e: 'click', event: MouseEvent): void
  (e: 'toggle', value: boolean): void
}
```

## 组件开发规范

1. 使用 TypeScript 进行开发
2. 使用 `<script setup>` 语法
3. 组件 Props 和 Emits 类型定义放在同名的 `.ts` 文件中
4. 样式使用主题包中的定义
5. 所有文件和文件夹使用小写命名
6. 组件命名使用 kebab-case

## 组件示例

以 Button 组件为例：

```vue
<!-- button.vue -->
<template>
  <button
    :class="[
      'sp-button',
      typeClass,
      `sp-button--${size}`,
      { 'sp-button--circle': circle }
    ]"
    :disabled="disabled"
    @click="handleClick"
  >
    <slot />
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ButtonProps, ButtonEmits } from './button'

const props = withDefaults(defineProps<ButtonProps>(), {
  type: 'default',
  size: 'medium',
  disabled: false,
  circle: false
})

const emit = defineEmits<ButtonEmits>()
</script>
```

## 创建新组件步骤

1. 在 `packages/ui/src/components` 下创建小写命名的组件文件夹
2. 创建组件主文件（小写）
3. 创建类型定义文件（小写）
4. 创建样式文件夹和引入文件
5. 在 `packages/theme-default/src` 中创建对应的样式文件
6. 在组件的 `style/index.scss` 中引入主题包样式

## 最佳实践

1. Props 定义要详细，包含类型和默认值
2. 使用 computed 属性处理派生状态
3. 组件事件使用 emit 定义
4. 样式类名使用 BEM 命名规范
5. 保持组件的单一职责
6. 提供完整的类型定义
7. 编写组件文档和示例
8. 确保样式与主题包保持一致
9. 遵循小写命名规范





