{"name": "@speed-ui/utils", "version": "1.0.0", "description": "Vue 3 UI 组件库工具函数", "main": "lib/index.js", "module": "es/index.js", "types": "types/index.d.ts", "files": ["lib", "es", "types"], "exports": {".": {"types": "./types/index.d.ts", "import": "./es/index.js", "require": "./lib/index.js"}, "./package.json": "./package.json"}, "scripts": {"build": "vite build", "clean": "rimraf lib es types", "dev": "vite build --watch"}, "devDependencies": {}}