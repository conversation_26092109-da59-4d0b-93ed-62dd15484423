<!--
  Textarea.vue - 外层包装层
  职责：提供外层结构，使用 InputCore，处理 v-model、验证、表单集成等业务逻辑
-->

<template>
  <div :class="rootClasses">
    <InputCore
      ref="inputCoreRef"
      :value="currentValue"
      :variant="props.variant"
      :effect="props.effect"
      :size="props.size"
      :placeholder="props.placeholder"
      :disabled="props.disabled"
      :readonly="props.readonly"
      :clearable="props.clearable"
      :prefix-icon="props.prefixIcon"
      :suffix-icon="props.suffixIcon"
      :loading="props.loading"
      :maxlength="props.maxlength"
      :show-word-limit="props.showWordLimit"
      :error="props.error || computedValidateState === 'error'"
      :validate-state="computedValidateState"
      :focused="isFocused"
      :has-value="hasValueComputed"
      mode="display"
      @update:value="handleUpdateValue"
      @update:focused="handleFocusedUpdate"
      @input="handleInput"
      @change="handleChange"
      @focus="handleFocus"
      @blur="handleBlur"
      @clear="handleClear"
      @click="handleWrapperClick"
    >
      <!-- 前缀插槽 -->
      <template #prefix>
        <slot name="prefix" />
      </template>

      <!-- 后缀插槽 -->
      <template #suffix>
        <slot name="suffix" />
      </template>

      <!-- 内容插槽（用于 textarea 元素） -->
      <template #inner>
        <textarea
          ref="textareaRef"
          :value="currentValue"
          :placeholder="props.placeholder"
          :disabled="props.disabled"
          :readonly="props.readonly"
          :maxlength="props.maxlength"
          :rows="props.rows || 3"
          :cols="props.cols"
          :autocomplete="props.autocomplete"
          :autofocus="props.autofocus"
          :required="props.required"
          :name="props.name"
          :class="elementClasses"
          :style="textareaStyle"
          @input="handleTextareaInput"
          @change="handleTextareaChange"
          @focus="handleTextareaFocus"
          @blur="handleTextareaBlur"
          @keydown="handleKeydown"
        />
      </template>
    </InputCore>

    <!-- 验证消息 -->
    <div
      v-if="showValidationMessage"
      :class="messageClasses"
    >
      <slot name="message">
        {{ computedValidateMessage }}
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    ref,
    computed,
    inject,
    nextTick,
    watch,
    onMounted,
    onUnmounted,
  } from 'vue'
  import { useVModel } from '@speed-ui/hooks'
  import InputCore from '../input/inputcore'
  import { useTextareaStyles } from '../../composables/useTextareaStyles'
  import type { TextareaProps, TextareaEmits } from './types'

  /**
   * Textarea 组件 - 外层包装层
   *
   * 使用与 Input 组件相同的架构：
   * useInputLogic (纯逻辑层) ← InputCore (基础结构层) ← Textarea (业务包装层)
   */

  const props = withDefaults(defineProps<TextareaProps>(), {
    rows: 3,
    resize: 'vertical',
    autosize: false,
    variant: 'default',
    effect: 'none',
    size: 'medium',
    disabled: false,
    readonly: false,
    clearable: false,
    showWordLimit: false,
    error: false,
    loading: false,
  })
  const emit = defineEmits<TextareaEmits>()

  // 表单字段注入（用于表单验证）
  const formItemField = inject<any>('spFormItemField', null)

  // 组件引用
  const inputCoreRef = ref<InstanceType<typeof InputCore> | null>(null)
  const textareaRef = ref<HTMLTextAreaElement | null>(null)

  // 用户是否手动调整过高度
  const userResized = ref(false)

  // 使用 useVModel 进行双向绑定
  const {
    value: currentValue,
    updateValue,
    hasValue: hasValueComputed,
  } = useVModel<string | undefined>(props, emit, {
    prop: 'value',
    event: 'update:value',
    defaultValue: props.value,
    formField: formItemField,
    debug: false,
  })

  // 验证状态 - 优先使用表单字段
  const computedValidateState = computed(() => {
    if (formItemField && formItemField.validateState) {
      return formItemField.validateState.value
    }
    return props.validateState
  })

  const computedValidateMessage = computed(() => {
    if (formItemField && formItemField.errors.value.length > 0) {
      return formItemField.errors.value[0]
    }
    return props.validateMessage
  })

  const showValidationMessage = computed(() => {
    return !!(computedValidateMessage.value && computedValidateState.value)
  })

  // 计算样式相关状态
  const computedDisabled = computed(() => props.disabled)
  const isFocused = ref(false)
  const showPrefix = computed(() => !!props.prefixIcon)
  const showSuffix = computed(() => {
    return !!(
      props.suffixIcon ||
      props.clearable ||
      props.loading ||
      (props.showWordLimit && props.maxlength)
    )
  })

  // 使用 textarea 专用的样式 composable
  const { rootClasses, elementClasses, messageClasses } = useTextareaStyles(
    props,
    {
      computedDisabled,
      isFocused: computed(() => isFocused.value),
      hasValue: hasValueComputed,
      showPrefix,
      showSuffix,
    }
  )

  // 简单的样式对象
  const textareaStyle = computed(() => ({
    overflow: props.autosize ? 'hidden' : 'auto',
    resize: props.resize || 'vertical',
    minHeight: 'inherit',
  }))

  // 事件处理
  const handleUpdateValue = (value: string | number | undefined) => {
    updateValue(typeof value === 'string' ? value : String(value || ''))
  }

  const handleInput = (event: Event) => {
    emit('input', event)
  }

  const handleChange = (value: string | number | undefined) => {
    emit('change', typeof value === 'string' ? value : String(value || ''))
  }

  // 处理焦点状态同步 - 从 InputCore 接收状态更新
  const handleFocusedUpdate = (focused: boolean) => {
    isFocused.value = focused
  }

  const handleFocus = (event: FocusEvent) => {
    emit('focus', event)
  }

  const handleBlur = (event: FocusEvent) => {
    emit('blur', event)

    // 触发表单验证
    if (formItemField) {
      formItemField.handleBlur()
    }
  }

  const handleKeydown = (event: KeyboardEvent) => {
    emit('keydown', event)
  }

  const handleClear = () => {
    emit('clear')
  }

  const handleWrapperClick = () => {
    if (!props.disabled && !props.readonly) {
      textareaRef.value?.focus()
    }
  }

  // Textarea 特有的事件处理
  const handleTextareaInput = (event: Event) => {
    const target = event.target as HTMLTextAreaElement
    updateValue(target.value)
    emit('input', event)

    // 触发高度调整
    if (props.autosize) {
      nextTick(() => {
        adjustHeight()
      })
    }
  }

  const handleTextareaChange = (event: Event) => {
    const target = event.target as HTMLTextAreaElement
    emit('change', target.value)
  }

  const handleTextareaFocus = (event: FocusEvent) => {
    isFocused.value = true
    emit('focus', event)
  }

  const handleTextareaBlur = (event: FocusEvent) => {
    isFocused.value = false
    emit('blur', event)

    // 触发表单验证
    if (formItemField) {
      formItemField.handleBlur()
    }
  }

  // Textarea 特有的自动调整高度功能
  const adjustHeight = () => {
    if (!props.autosize || !textareaRef.value || userResized.value) return

    const textarea = textareaRef.value

    // 重置高度以获取 scrollHeight
    textarea.style.height = 'auto'

    let height = textarea.scrollHeight

    // 如果 autosize 是对象，处理最小/最大行数
    if (typeof props.autosize === 'object') {
      const { minRows, maxRows } = props.autosize
      const lineHeight =
        parseInt(getComputedStyle(textarea).lineHeight, 10) || 20

      if (minRows) {
        height = Math.max(height, minRows * lineHeight)
      }
      if (maxRows) {
        height = Math.min(height, maxRows * lineHeight)
      }
    }

    textarea.style.height = `${height}px`
  }

  // 强制设置初始高度，不受 Vue 响应式影响
  const setInitialHeight = () => {
    if (!props.autosize || !textareaRef.value) return

    const textarea = textareaRef.value
    // 移除任何可能冲突的内联样式
    textarea.style.removeProperty('height')

    nextTick(() => {
      adjustHeight()
    })
  }

  // 监听值变化，触发高度调整
  watch(
    () => currentValue.value,
    () => {
      if (props.autosize) {
        nextTick(() => {
          adjustHeight()
        })
      }
    },
    { immediate: true }
  )

  // 监听用户手动调整大小
  const setupResizeDetection = () => {
    if (!textareaRef.value || !props.autosize) return

    const textarea = textareaRef.value
    let isResizing = false

    const handleMouseDown = (event: MouseEvent) => {
      // 检测是否点击在右下角的拖拽区域
      const rect = textarea.getBoundingClientRect()
      const isInResizeArea =
        event.clientX > rect.right - 20 && event.clientY > rect.bottom - 20

      if (isInResizeArea) {
        isResizing = true
      }
    }

    const handleMouseUp = () => {
      if (isResizing) {
        userResized.value = true
        isResizing = false
        console.log('检测到用户手动调整大小，禁用自动调整')
      }
    }

    textarea.addEventListener('mousedown', handleMouseDown)
    document.addEventListener('mouseup', handleMouseUp)

    // 组件卸载时清理
    return () => {
      textarea.removeEventListener('mousedown', handleMouseDown)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }

  // 重置用户调整状态的方法
  const resetUserResize = () => {
    userResized.value = false
    if (textareaRef.value) {
      // 直接移除用户设置的高度样式
      textareaRef.value.style.removeProperty('height')
    }
    nextTick(() => {
      adjustHeight()
    })
  }

  // 设置和清理监听器
  let cleanupResizeDetection: (() => void) | null = null

  onMounted(() => {
    if (props.autosize) {
      nextTick(() => {
        setInitialHeight()
        cleanupResizeDetection = setupResizeDetection() || null
      })
    }
  })

  onUnmounted(() => {
    if (cleanupResizeDetection) {
      cleanupResizeDetection()
    }
  })

  // 暴露的方法
  const focus = () => {
    textareaRef.value?.focus()
  }

  const blur = () => {
    textareaRef.value?.blur()
  }

  const select = () => {
    textareaRef.value?.select()
  }

  const clear = () => {
    updateValue('')
    emit('clear')
    nextTick(() => {
      textareaRef.value?.focus()
    })
  }

  // 暴露给外部使用
  defineExpose({
    /** 使文本域获得焦点 */
    focus,
    /** 使文本域失去焦点 */
    blur,
    /** 选中文本域中的文字 */
    select,
    /** 清空文本域 */
    clear,
    /** 调整文本域高度（autosize 模式下） */
    adjustHeight,
    /** 重置用户手动调整状态，恢复自动调整 */
    resetUserResize,
    /** 文本域元素引用 */
    get textarea() {
      return textareaRef.value || null
    },
    /** 包装器元素引用 */
    get wrapper() {
      return (inputCoreRef.value as any)?.wrapperRef?.value || null
    },
  })
</script>

<script lang="ts">
  export default {
    name: 'SpTextarea',
  }
</script>
