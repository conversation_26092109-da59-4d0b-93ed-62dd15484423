<template>
  <div class="opacity-animation-test">
    <h2>输入框透明度动画测试</h2>
    <p>测试整个输入框在未激活时隐藏，激活时显示的效果</p>
    
    <div class="test-section">
      <h3>输入框透明度测试</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>基础透明度效果</h4>
          <SPInputField
            variant="default"
            size="medium"
            label="用户名"
            placeholder="点击查看输入框显示"
            required
          />
          <p class="note">输入框应该在未激活时完全透明，点击后显示</p>
        </div>
        
        <div class="test-item">
          <h4>有前置图标</h4>
          <SPInputField
            variant="default"
            size="medium"
            label="邮箱地址"
            placeholder="测试前置图标情况"
            required
            prepend-icon-inner="Mail"
          />
          <p class="note">有前置图标时输入框透明度效果应该正常</p>
        </div>

        <div class="test-item">
          <h4>有前缀</h4>
          <SPInputField
            variant="default"
            size="medium"
            label="网址"
            placeholder="测试前缀情况"
            required
            prefix="https://"
          />
          <p class="note">有前缀时输入框透明度效果应该正常</p>
        </div>

        <div class="test-item">
          <h4>前置图标 + 前缀</h4>
          <SPInputField
            variant="default"
            size="medium"
            label="用户名"
            placeholder="测试组合情况"
            required
            prepend-icon-inner="User"
            prefix="@"
          />
          <p class="note">组合情况下输入框透明度效果应该正常</p>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>对比其他变体（应该没有输入框透明度效果）</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>Filled 变体</h4>
          <SPInputField
            variant="filled"
            size="medium"
            label="用户名"
            placeholder="filled 变体输入框应该始终可见"
            required
          />
          <p class="note">filled 变体输入框应该始终可见</p>
        </div>

        <div class="test-item">
          <h4>Underlined 变体</h4>
          <SPInputField
            variant="underlined"
            size="medium"
            label="用户名"
            placeholder="underlined 变体输入框应该始终可见"
            required
          />
          <p class="note">underlined 变体输入框应该始终可见</p>
        </div>

        <div class="test-item">
          <h4>Outlined 变体</h4>
          <SPInputField
            variant="outlined"
            size="medium"
            label="用户名"
            placeholder="outlined 变体输入框应该始终可见"
            required
          />
          <p class="note">outlined 变体输入框应该始终可见</p>
        </div>

        <div class="test-item">
          <h4>Borderless 变体</h4>
          <SPInputField
            variant="borderless"
            size="medium"
            label="用户名"
            placeholder="borderless 变体输入框应该始终可见"
            required
          />
          <p class="note">borderless 变体输入框应该始终可见</p>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>不同状态下的透明度测试</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>错误状态</h4>
          <SPInputField
            variant="default"
            size="medium"
            label="邮箱"
            placeholder="错误状态测试"
            required
            error
            error-message="邮箱格式不正确"
          />
          <p class="note">错误状态下透明度效果应该正常</p>
        </div>
        
        <div class="test-item">
          <h4>警告状态</h4>
          <SPInputField
            variant="default"
            size="medium"
            label="密码"
            placeholder="警告状态测试"
            required
            warning
            warning-message="密码强度较弱"
          />
          <p class="note">警告状态下透明度效果应该正常</p>
        </div>
        
        <div class="test-item">
          <h4>成功状态</h4>
          <SPInputField
            variant="default"
            size="medium"
            label="用户名"
            placeholder="成功状态测试"
            required
            success
            success-message="用户名可用"
          />
          <p class="note">成功状态下透明度效果应该正常</p>
        </div>
        
        <div class="test-item">
          <h4>禁用状态</h4>
          <SPInputField
            variant="default"
            size="medium"
            label="只读字段"
            placeholder="禁用状态测试"
            required
            disabled
          />
          <p class="note">禁用状态下透明度效果应该正常</p>
        </div>
      </div>
    </div>

    <div class="animation-tips">
      <h3>🎭 输入框透明度动画测试说明</h3>
      <ul>
        <li><strong>未激活状态</strong>：输入框完全透明，只能看到标签</li>
        <li><strong>点击激活</strong>：输入框逐渐显示，标签浮动到上方并变透明</li>
        <li><strong>输入内容后失焦</strong>：输入框保持可见（因为有内容），标签保持浮动状态</li>
        <li><strong>清空内容后失焦</strong>：输入框重新变透明，标签回到原位并显示</li>
        <li><strong>只影响所有变体</strong>：所有变体都应该有这个效果</li>
        <li><strong>优化效果</strong>：不再需要计算 placeholder 显示逻辑，CSS 自动处理</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import SPInputField from '../packages/ui/src/components/input-field/input-field.vue'
</script>

<style scoped>
.opacity-animation-test {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.test-item {
  padding: 15px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafafa;
}

.test-item h4 {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 14px;
  font-weight: 600;
}

.note {
  margin: 10px 0 0 0;
  font-size: 12px;
  color: #888;
  font-style: italic;
}

.animation-tips {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.animation-tips h3 {
  margin-top: 0;
  color: #007bff;
}

.animation-tips ul {
  margin: 0;
  padding-left: 20px;
}

.animation-tips li {
  margin-bottom: 8px;
  color: #555;
}
</style>
