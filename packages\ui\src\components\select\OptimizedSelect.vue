<!--
  优化的 Select 组件 - 完全继承 Input 的所有功能
  使用 propsFactory 和 eventFactory
-->
<template>
  <div class="sp-select" :class="selectClasses">
    <!-- 使用 InputCore 而不是 sp-input，获得更好的控制 -->
    <InputCore
      ref="inputRef"
      v-bind="inputCoreProps"
      @click="handleWrapperClick"
      @focus="handleFocus"
      @blur="handleBlur"
      @input="handleInput"
      @change="handleChange"
      @keydown="handleKeydown"
      @clear="handleClear"
      @prefix-click="handlePrefixClick"
      @suffix-click="handleSuffixClick"
    >
      <!-- 多选模式：使用 inner 插槽显示标签 -->
      <template #inner v-if="props.multiple">
        <div
          class="sp-select__tags-container"
          tabindex="0"
          @focus="handleTagsContainerFocus"
          @blur="handleTagsContainerBlur"
        >
          <div v-if="selectedOptions.length > 0" class="sp-select__tags">
            <Tag
              v-for="option in selectedOptions"
              :key="getOptionValue(option)"
              :label="getOptionLabel(option)"
              :closable="!props.disabled && !props.readonly"
              :size="tagSize"
              type="default"
              variant="light"
              class="sp-select__tag"
              @close="handleRemoveTag(option)"
            />
          </div>
          <span
            v-if="selectedOptions.length === 0"
            class="sp-select__placeholder"
          >
            {{ props.placeholder }}
          </span>
        </div>
      </template>

      <!-- 后缀图标：下拉箭头 -->
      <template #suffix>
        <SpIcon
          :name="dropdownIcon"
          :class="arrowClasses"
          @click="handleArrowClick"
        />
      </template>
    </InputCore>

    <!-- 下拉选项 -->
    <SelectDropdown
      v-if="isDropdownOpen"
      ref="dropdownRef"
      :options="props.options"
      :selected-values="selectedValues"
      :multiple="props.multiple"
      :empty-text="props.emptyText"
      :value-key="props.valueKey"
      :label-key="props.labelKey"
      :style="dropdownStyle"
      @option-click="handleSelectOption"
      @mousedown.prevent.stop
    />
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits, ref, nextTick } from 'vue'
import InputCore from '../input/inputcore'
import SpIcon from '../icon/Icon.vue'
import { Tag } from '../tag'
import SelectDropdown from './internal/SelectDropdown.vue'
import { createSelectProps, selectDefaults, type SelectOption, type SelectEmits } from './selectProps'
import { createInputEventHandlers, createEventHandler } from '../../utils/eventFactory'

// 使用工厂函数创建 props
const props = defineProps(createSelectProps())
const emit = defineEmits<SelectEmits>()

// 组件引用
const inputRef = ref<InstanceType<typeof InputCore>>()
const dropdownRef = ref<InstanceType<typeof SelectDropdown>>()

// 状态
const isDropdownOpen = ref(false)
const dropdownStyle = ref({})
const isSelecting = ref(false)

// 计算属性
const selectedValues = computed(() => {
  if (props.multiple) {
    return Array.isArray(props.value) ? props.value : []
  }
  return props.value !== undefined ? [props.value] : []
})

const selectedOptions = computed(() => {
  if (!props.options) return []
  return selectedValues.value.map((value: any) => {
    return props.options?.find(opt => getOptionValue(opt) === value)
  }).filter(Boolean) as SelectOption[]
})

const displayValue = computed(() => {
  if (props.multiple) {
    return '' // 多选模式使用标签显示
  } else {
    const option = props.options?.find(
      (opt: SelectOption) => getOptionValue(opt) === props.value
    )
    return option ? getOptionLabel(option) : ''
  }
})

const hasValue = computed(() => {
  if (props.multiple) {
    return selectedValues.value.length > 0
  }
  return props.value !== undefined && props.value !== null && props.value !== ''
})

const tagSize = computed(() => {
  const sizeMap = {
    small: 'small',
    medium: 'medium', 
    large: 'large',
  }
  return sizeMap[props.size || 'medium'] as 'small' | 'medium' | 'large'
})

const dropdownIcon = computed(() => {
  return isDropdownOpen.value ? 'chevron-up' : 'chevron-down'
})

const arrowClasses = computed(() => {
  return [
    'sp-select__arrow',
    {
      'sp-select__arrow--open': isDropdownOpen.value,
    }
  ]
})

const selectClasses = computed(() => {
  return [
    {
      'sp-select--multiple': props.multiple,
      'sp-select--open': isDropdownOpen.value,
      'sp-select--disabled': props.disabled,
    }
  ]
})

// 传递给 InputCore 的 props
const inputCoreProps = computed(() => {
  const { value, options, multiple, emptyText, valueKey, labelKey, linkedGroup, linkedLevel, linkedData, ...coreProps } = props
  
  return {
    ...coreProps,
    value: displayValue.value,
    hasValue: hasValue.value,
    readonly: true, // Select 始终只读
  }
})

// 工具函数
const getOptionValue = (option: SelectOption) => {
  return option[props.valueKey || 'value']
}

const getOptionLabel = (option: SelectOption) => {
  return option[props.labelKey || 'label']
}

// 使用事件工厂创建基础事件处理器
const baseEventHandlers = createInputEventHandlers(emit, inputRef, props)

// 自定义事件处理器
const handleWrapperClick = createEventHandler(emit, 'click', {
  before: () => !props.disabled,
  after: () => {
    toggleDropdown()
  },
})

const handleArrowClick = createEventHandler(emit, 'suffix-click', {
  stopPropagation: true,
  before: () => !props.disabled,
  after: () => {
    toggleDropdown()
  },
})

// Select 特有的事件处理
const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value
  
  if (isDropdownOpen.value) {
    emit('dropdown-open')
    nextTick(() => {
      updateDropdownPosition()
    })
  } else {
    emit('dropdown-close')
  }
}

const handleSelectOption = (option: SelectOption) => {
  if (option.disabled) return
  
  isSelecting.value = true
  const optionValue = getOptionValue(option)
  let newValue: string | number | Array<string | number> | undefined
  
  if (props.multiple) {
    const currentValues = Array.isArray(props.value) ? props.value : []
    const index = currentValues.indexOf(optionValue)
    
    if (index > -1) {
      newValue = currentValues.filter((v: string | number) => v !== optionValue)
      emit('option-remove', option)
    } else {
      newValue = [...currentValues, optionValue]
      emit('option-select', option)
    }
    
    // 多选保持下拉框打开
  } else {
    newValue = optionValue
    emit('option-select', option)
    
    // 单选关闭下拉框
    nextTick(() => {
      isDropdownOpen.value = false
      emit('dropdown-close')
    })
  }
  
  emit('update:value', newValue)
  emit('change', newValue)
  
  setTimeout(() => {
    isSelecting.value = false
  }, 0)
}

const handleRemoveTag = (option: SelectOption) => {
  if (props.disabled || props.readonly) return
  
  const optionValue = getOptionValue(option)
  const currentValues = Array.isArray(props.value) ? props.value : []
  const newValue = currentValues.filter((v: string | number) => v !== optionValue)
  
  emit('update:value', newValue)
  emit('change', newValue)
  emit('option-remove', option)
}

const handleClear = () => {
  const newValue = props.multiple ? [] : undefined
  emit('update:value', newValue)
  emit('clear')
}

// 继承基础事件处理器
const { handleFocus, handleBlur, handleInput, handleChange, handleKeydown, handlePrefixClick, handleSuffixClick } = baseEventHandlers

// 多选标签容器的焦点处理
const handleTagsContainerFocus = () => {
  handleFocus(new FocusEvent('focus'))
}

const handleTagsContainerBlur = () => {
  handleBlur(new FocusEvent('blur'))
}

// 下拉框位置更新
const updateDropdownPosition = () => {
  if (!inputRef.value) return
  
  const rect = (inputRef.value.$el || inputRef.value).getBoundingClientRect()
  
  dropdownStyle.value = {
    position: 'fixed',
    top: `${rect.bottom + 4}px`,
    left: `${rect.left}px`,
    width: `${rect.width}px`,
    zIndex: 9999,
  }
}

// 暴露方法
defineExpose({
  focus: () => inputRef.value?.focus?.(),
  blur: () => inputRef.value?.blur?.(),
  toggleDropdown,
  openDropdown: () => {
    if (!isDropdownOpen.value) toggleDropdown()
  },
  closeDropdown: () => {
    if (isDropdownOpen.value) toggleDropdown()
  },
})
</script>

<style scoped>
.sp-select {
  position: relative;
}

.sp-select--multiple .sp-select__tags-container {
  width: 100%;
  min-height: 20px;
  outline: none;
}

.sp-select__tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.sp-select__tag {
  flex-shrink: 0;
}

.sp-select__placeholder {
  color: #c0c4cc;
  font-size: inherit;
}

.sp-select__arrow {
  transition: transform 0.2s;
  cursor: pointer;
}

.sp-select__arrow--open {
  transform: rotate(180deg);
}
</style>
