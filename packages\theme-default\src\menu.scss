// ================================
// Menu 组件样式
// ================================

@use './common/var.scss' as *;

// Menu 容器样式
.sp-menu {
  position: relative;
  margin: 0;
  padding: 0;
  outline: none;
  list-style: none;
  background: var(--sp-menu-bg);
  border-radius: #{$border-radius-base};
  box-shadow: var(--sp-menu-shadow);

  // 垂直模式
  &--vertical {
    .sp-menu-item {
      padding: #{$spacing-sm} #{$spacing-md};
    }
  }

  // 水平模式
  &--horizontal {
    display: flex;
    border-bottom: 1px solid #{$border-color-base};

    .sp-menu-item {
      padding: #{$spacing-md} #{$spacing-lg};
      border-bottom: 2px solid transparent;

      &--selected {
        border-bottom-color: var(--sp-color-primary);
      }
    }
  }

  // 内联模式
  &--inline {
    .sp-menu-item {
      padding-left: calc(#{$spacing-md} + var(--sp-menu-indent, 0px));
    }
  }

  // 主题
  &--light {
    --sp-menu-bg: #{$background-color-base};
    --sp-menu-color: #{$color-text-primary};
    --sp-menu-shadow: #{$box-shadow-light};
  }

  &--dark {
    --sp-menu-bg: #001529;
    --sp-menu-color: rgba(255, 255, 255, 0.65);
    --sp-menu-shadow: none;
  }

  // 禁用状态
  &--disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}

// MenuItem 样式
.sp-menu-item {
  position: relative;
  display: flex;
  align-items: center;
  margin: 0;
  padding: #{$spacing-sm} #{$spacing-md};
  overflow: hidden;
  color: var(--sp-menu-color, #{$color-text-primary});
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: #{$border-radius-small};

  &:hover {
    background: var(--sp-menu-item-hover-bg, #{$background-color-hover});
    color: var(--sp-color-primary);
  }

  &:focus {
    outline: none;
    background: var(--sp-menu-item-hover-bg, #{$background-color-hover});
  }

  // 选中状态
  &--selected {
    background: var(--sp-menu-item-selected-bg, #{$color-primary-lightest});
    color: var(--sp-color-primary);
    font-weight: 600;

    &::after {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      border-right: 3px solid var(--sp-color-primary);
      transform: scaleY(1);
      opacity: 1;
      content: '';
    }
  }

  // 禁用状态
  &--disabled {
    color: #{$color-text-disabled};
    cursor: not-allowed;

    &:hover {
      background: transparent;
      color: #{$color-text-disabled};
    }
  }

  // 危险状态
  &--danger {
    color: #{$color-danger};

    &:hover {
      background: #fff1f0;
      color: #{$color-danger};
    }

    &.sp-menu-item--selected {
      background: #fff1f0;
      color: #{$color-danger};
    }
  }

  // 分组标题
  &--group {
    padding: #{$spacing-xs} #{$spacing-md};
    color: #{$color-text-secondary};
    font-size: #{$font-size-sm};
    font-weight: 600;
    cursor: default;

    &:hover {
      background: transparent;
      color: #{$color-text-secondary};
    }
  }

  // 子元素样式
  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    margin-right: #{$spacing-xs};
    font-size: 16px;
  }

  &__content {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__suffix {
    margin-left: auto;
    color: #{$color-text-secondary};
  }
}

// 暗色主题下的样式调整
.sp-menu--dark .sp-menu-item {
  &:hover {
    background: rgba(255, 255, 255, 0.08);
    color: #ffffff;
  }

  &--selected {
    background: var(--sp-color-primary);
    color: #ffffff;
  }
}
