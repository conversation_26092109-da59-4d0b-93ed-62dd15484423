<template>
  <div class="btn-test">
    <h2>按钮两层架构测试</h2>

    <div class="test-section">
      <h3>1. 基础使用（样式层）</h3>
      <div class="btn-group">
        <Btn>默认按钮</Btn>
        <Btn variant="outlined">轮廓按钮</Btn>
        <Btn variant="text">文本按钮</Btn>
        <Btn disabled>禁用按钮</Btn>
      </div>
    </div>

    <div class="test-section">
      <h3>2. 不同尺寸</h3>
      <div class="btn-group">
        <Btn size="small">小按钮</Btn>
        <Btn size="medium">中按钮</Btn>
        <Btn size="large">大按钮</Btn>
      </div>
    </div>

    <div class="test-section">
      <h3>3. 逻辑层单独使用（BtnBase）</h3>
      <div class="btn-group">
        <BtnBase
          style="
            padding: 8px 16px;
            background: #f0f0f0;
            border: 1px solid #ccc;
            border-radius: 4px;
          "
          @click="handleBaseClick"
        >
          逻辑层按钮（无样式）
        </BtnBase>
      </div>
    </div>

    <div class="test-section">
      <h3>4. 自定义样式层</h3>
      <div class="btn-group">
        <BtnBase
          :class="customButtonClasses"
          :style="customButtonStyles"
          @click="handleCustomClick"
          @mouseenter="isCustomHovered = true"
          @mouseleave="isCustomHovered = false"
        >
          自定义样式按钮
        </BtnBase>
      </div>
    </div>

    <div class="test-section">
      <h3>5. 事件测试</h3>
      <div class="btn-group">
        <Btn @click="handleClick">点击我</Btn>
      </div>
      <p v-if="clickCount > 0">点击次数: {{ clickCount }}</p>
    </div>

    <div class="test-section">
      <h3>6. 通用 useEvent 测试</h3>
      <div class="btn-group">
        <Btn
          @click="addEventLog('样式层点击事件')"
          @mouseenter="handleMouseEnter"
          @mouseleave="handleMouseLeave"
          @mousedown="handleMouseDown"
          @mouseup="handleMouseUp"
        >
          通用事件转发按钮
        </Btn>
      </div>
      <div class="event-log">
        <p>鼠标状态: {{ mouseStatus }}</p>
        <p>事件日志 (证明 useEvent 自动转发了所有事件):</p>
        <ul>
          <li
            v-for="(event, index) in eventLog"
            :key="index"
          >
            {{ event }}
          </li>
        </ul>
      </div>
    </div>

    <div class="test-section">
      <h3>7. 直接使用逻辑层事件</h3>
      <div class="btn-group">
        <BtnBase
          style="
            padding: 8px 16px;
            background: #52c41a;
            color: white;
            border: none;
            border-radius: 4px;
          "
          @mouseenter="handleBaseMouseEnter"
          @mouseleave="handleBaseMouseLeave"
          @click="handleBaseClick"
        >
          逻辑层事件按钮
        </BtnBase>
      </div>
      <p>逻辑层事件: {{ baseEventStatus }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { Btn, BtnBase } from '@speed-ui/ui'

  const clickCount = ref(0)
  const isCustomHovered = ref(false)
  const mouseStatus = ref('正常')
  const eventLog = ref<string[]>([])
  const baseEventStatus = ref('等待事件...')

  const addEventLog = (event: string) => {
    eventLog.value.unshift(`${new Date().toLocaleTimeString()}: ${event}`)
    if (eventLog.value.length > 5) {
      eventLog.value = eventLog.value.slice(0, 5)
    }
  }

  const handleClick = () => {
    clickCount.value++
    console.log('样式层按钮被点击')
  }

  const handleBaseClick = () => {
    console.log('逻辑层按钮被点击')
    baseEventStatus.value = '逻辑层按钮被点击'
  }

  const handleCustomClick = () => {
    console.log('自定义样式按钮被点击')
  }

  // 样式层鼠标事件
  const handleMouseEnter = () => {
    mouseStatus.value = '鼠标进入'
    addEventLog('鼠标进入样式层按钮')
  }

  const handleMouseLeave = () => {
    mouseStatus.value = '鼠标离开'
    addEventLog('鼠标离开样式层按钮')
  }

  const handleMouseDown = () => {
    mouseStatus.value = '鼠标按下'
    addEventLog('鼠标按下样式层按钮')
  }

  const handleMouseUp = () => {
    mouseStatus.value = '鼠标释放'
    addEventLog('鼠标释放样式层按钮')
  }

  // 逻辑层鼠标事件
  const handleBaseMouseEnter = () => {
    baseEventStatus.value = '鼠标进入逻辑层'
  }

  const handleBaseMouseLeave = () => {
    baseEventStatus.value = '鼠标离开逻辑层'
  }

  // 自定义样式示例
  const customButtonClasses = computed(() => [
    'custom-btn',
    {
      'custom-btn--hovered': isCustomHovered.value,
    },
  ])

  const customButtonStyles = computed(() => ({
    padding: '12px 24px',
    background: isCustomHovered.value ? '#ff6b6b' : '#4ecdc4',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    transform: isCustomHovered.value ? 'scale(1.05)' : 'scale(1)',
  }))
</script>

<style scoped>
  .btn-test {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
  }

  .test-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
  }

  .test-section h3 {
    margin-top: 0;
    color: #333;
  }

  .btn-group {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    align-items: center;
  }

  .custom-btn {
    font-family: inherit;
    font-size: 14px;
    font-weight: 500;
    outline: none;
    user-select: none;
  }

  .custom-btn:focus {
    box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.3);
  }

  .event-log {
    margin-top: 12px;
    padding: 12px;
    background: #f5f5f5;
    border-radius: 4px;
    font-size: 12px;
  }

  .event-log ul {
    margin: 8px 0 0 0;
    padding-left: 20px;
    max-height: 120px;
    overflow-y: auto;
  }

  .event-log li {
    margin-bottom: 4px;
    color: #666;
  }
</style>
