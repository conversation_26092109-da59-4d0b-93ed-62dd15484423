<!--
  SelectFieldCore.vue - SelectField 核心实现
  结合了 Select 的所有功能和 FormItem 的表单集成，并添加浮动标签效果
-->

<template>
  <div :class="rootClasses">
    <!-- 选择框包装器 -->
    <div
      ref="wrapperRef"
      :class="wrapperClasses"
      @click="handleWrapperClick"
    >
      <!-- 浮动标签 -->
      <label
        v-if="label"
        :class="labelClasses"
        :for="selectId"
        @click="handleLabelClick"
      >
        <span
          v-if="required"
          class="sp-select-field__label-asterisk"
        >
          *
        </span>
        {{ label }}
      </label>

      <!-- Select 显示区域 -->
      <SelectInput
        :prefixCls="'sp-select-field'"
        :multiple="multiple"
        :disabled="computedDisabled"
        :placeholder="computedPlaceholder"
        :selectedOption="selectedOption"
        :selectedOptions="selectedOptions"
        :valueKey="valueKey"
        :labelKey="labelKey"
        :size="size"
        @removeTag="handleRemoveTag"
      />

      <!-- 后缀区域 -->
      <div :class="suffixClasses">
        <!-- 清除按钮 -->
        <sp-icon
          v-if="showClearIcon"
          name="CloseCircle"
          :size="iconSize"
          clickable
          :class="clearIconClasses"
          @click.stop.prevent="handleClear"
          @mousedown.stop.prevent
        />

        <!-- 下拉箭头 -->
        <sp-icon
          :name="'ChevronDown'"
          :size="iconSize"
          :class="arrowIconClasses"
        />

        <!-- 后缀插槽 -->
        <slot name="suffix"></slot>
      </div>
    </div>

    <!-- 下拉选项 -->
    <SelectDropdown
      :visible="isDropdownOpen"
      :options="actualOptions"
      :prefixCls="'sp-select-field'"
      :dropdownStyle="dropdownStyle"
      :value="value"
      :multiple="multiple"
      :valueKey="valueKey"
      :labelKey="labelKey"
      :emptyText="emptyText"
      :variant="variant"
      :size="size"
      :validate-state="validateState"
      @optionClick="handleSelectOption"
    />

    <!-- 加载动画条 -->
    <div
      v-if="loading"
      :class="loadingBarClasses"
    >
      <div :class="loadingProgressClasses"></div>
    </div>

    <!-- 帮助文本和验证消息 -->
    <transition name="sp-select-field-message">
      <div
        v-if="shouldShowMessage"
        :class="messageClasses"
      >
        {{ currentMessage }}
      </div>
    </transition>

    <div
      v-if="helperText && !shouldShowMessage"
      :class="helperTextClasses"
    >
      {{ helperText }}
    </div>

    <!-- 隐藏的默认插槽，用于渲染子组件 -->
    <div style="display: none">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    computed,
    inject,
    useSlots,
    useId,
    ref,
    watch,
    nextTick,
    provide,
    onMounted,
    onUnmounted,
  } from 'vue'
  import { useField } from 'vee-validate'
  import SpIcon from '../icon/Icon.vue'
  import SelectInput from '../select/internal/SelectInput.vue'
  import SelectDropdown from '../select/internal/SelectDropdown.vue'
  import { useSelectFieldStyles } from './useSelectFieldStyles'
  import type { SelectFieldProps, SelectFieldEmits } from './types'
  import type { FormContext } from '../Form/types'
  import {
    type SelectOption,
    getOptionValue,
    getOptionLabel,
    isOptionDisabled,
    getSelectedOption,
    getSelectedOptions,
    updateDropdownPosition,
    hasValue as selectHasValue,
    multiLevelLinkedSelectManager,
  } from '../select/select'

  // ===== Props 和 Emits =====
  const props = withDefaults(defineProps<SelectFieldProps>(), {
    value: '',
    placeholder: '请选择',
    clearable: false,
    disabled: false,
    multiple: false,
    size: 'default',
    variant: 'default',
    emptyText: '暂无数据',
    valueKey: 'value',
    labelKey: 'label',
    required: false,
    showMessage: true,
    persistentLabel: false,
    options: [] as SelectOption[],
  })

  const emit = defineEmits<SelectFieldEmits>()
  const slots = useSlots()

  // ===== 表单集成 =====
  const formContext = inject<FormContext>('spForm', {})

  // VeeValidate 字段集成
  const veeField = useField(props.name, props.rules as any, {
    validateOnValueUpdate: false,
  })

  // ===== 组件引用 =====
  const wrapperRef = ref<HTMLDivElement>()
  const selectId = computed(() => useId())

  // ===== 状态管理 =====
  const isFocused = ref(false)
  const isDropdownOpen = ref(false)
  const dropdownStyle = ref({})

  // 联动选择器相关状态
  const linkedOptions = ref<SelectOption[]>([])

  // 子组件选项数据管理
  const childOptions = ref<SelectOption[]>([])

  // 提供给子组件的选项注册函数
  const registerOption = (option: SelectOption) => {
    const existingIndex = childOptions.value.findIndex(
      item => item.value === option.value
    )
    if (existingIndex > -1) {
      childOptions.value[existingIndex] = option
    } else {
      childOptions.value.push(option)
    }
  }

  const unregisterOption = (value: string | number) => {
    const index = childOptions.value.findIndex(item => item.value === value)
    if (index > -1) {
      childOptions.value.splice(index, 1)
    }
  }

  // 向子组件提供注册函数
  provide('selectRegisterOption', registerOption)
  provide('selectUnregisterOption', unregisterOption)

  // ===== 计算属性 =====
  const computedDisabled = computed(() => {
    return props.disabled || formContext?.disabled || false
  })

  const computedPlaceholder = computed(() => {
    // 浮动标签模式下，有标签时不显示placeholder
    if (props.label && !isLabelFloating.value) {
      return ''
    }
    return props.placeholder
  })

  const hasValue = computed(() => {
    return selectHasValue(props.value, props.multiple)
  })

  const isLabelFloating = computed(() => {
    return (
      props.persistentLabel ||
      isFocused.value ||
      hasValue.value ||
      isDropdownOpen.value
    )
  })

  const iconSize = computed(() => {
    switch (props.size) {
      case 'small':
        return 14
      case 'large':
        return 18
      case 'default':
      default:
        return 16
    }
  })

  // 计算实际使用的选项列表
  const actualOptions = computed(() => {
    if (props.linkedGroup && props.linkedLevel) {
      return linkedOptions.value
    }

    // 优先使用子组件注册的选项
    if (childOptions.value.length > 0) {
      return childOptions.value
    }

    return props.options || []
  })

  // 选中的选项
  const selectedOption = computed(() => {
    return getSelectedOption(
      actualOptions.value,
      props.value,
      props.multiple,
      props.valueKey
    )
  })

  const selectedOptions = computed(() => {
    return getSelectedOptions(
      actualOptions.value,
      props.value,
      props.multiple,
      props.valueKey
    )
  })

  // 显示逻辑
  const showClearIcon = computed(() => {
    return props.clearable && hasValue.value && !computedDisabled.value
  })

  // 验证状态管理
  const validateState = computed(() => {
    if (veeField.errorMessage.value) return 'error'
    if (veeField.meta.valid && veeField.meta.dirty) return 'success'
    return ''
  })

  const currentMessage = computed(() => {
    return veeField.errorMessage.value || ''
  })

  const shouldShowMessage = computed(() => {
    return props.showMessage && !!currentMessage.value
  })

  // ===== 使用样式 Composable =====
  const {
    rootClasses,
    wrapperClasses,
    labelClasses,
    suffixClasses,
    clearIconClasses,
    arrowIconClasses,
    loadingBarClasses,
    loadingProgressClasses,
    helperTextClasses,
    messageClasses,
  } = useSelectFieldStyles(props, {
    computedDisabled,
    isFocused,
    hasValue,
    isLabelFloating,
    validateState,
  })

  // ===== 事件处理 =====
  const handleWrapperClick = () => {
    if (computedDisabled.value) return

    isDropdownOpen.value = !isDropdownOpen.value

    if (isDropdownOpen.value) {
      isFocused.value = true
      emit('focus')

      nextTick(() => {
        if (wrapperRef.value) {
          dropdownStyle.value = updateDropdownPosition(wrapperRef.value)
        }
      })
    } else {
      isFocused.value = false
      emit('blur')
    }
  }

  const handleLabelClick = () => {
    handleWrapperClick()
  }

  const handleSelectOption = (option: SelectOption) => {
    const optionValue = getOptionValue(option, props.valueKey)

    if (isOptionDisabled(option)) return

    let newValue: string | number | Array<string | number> | undefined

    if (props.multiple) {
      const currentValues = Array.isArray(props.value) ? props.value : []
      const index = currentValues.indexOf(optionValue)

      if (index > -1) {
        // 取消选择
        newValue = currentValues.filter(v => v !== optionValue)
      } else {
        // 添加选择
        newValue = [...currentValues, optionValue]
      }
    } else {
      newValue = optionValue
      isDropdownOpen.value = false
      isFocused.value = false
    }

    emit('update:value', newValue)
    emit('change', newValue)

    // 触发验证
    veeField.setValue(newValue)
  }

  const handleRemoveTag = (value: string | number) => {
    if (!props.multiple || computedDisabled.value) return

    const currentValues = Array.isArray(props.value) ? props.value : []
    const newValue = currentValues.filter(v => v !== value)

    emit('update:value', newValue)
    emit('change', newValue)

    veeField.setValue(newValue)
  }

  const handleClear = () => {
    const newValue = props.multiple ? [] : undefined
    emit('update:value', newValue)
    emit('change', newValue)
    emit('clear')

    veeField.setValue(newValue)
  }

  // ===== 联动选择器处理 =====
  if (props.linkedGroup && props.linkedLevel) {
    onMounted(() => {
      multiLevelLinkedSelectManager.registerComponent(
        props.linkedGroup!,
        props.linkedLevel!,
        (options: SelectOption[]) => {
          linkedOptions.value = options
        },
        () => {
          emit('update:value', undefined)
          emit('change', undefined)
          veeField.setValue(undefined)
        },
        props.linkedData
      )
    })

    onUnmounted(() => {
      if (props.linkedGroup && props.linkedLevel) {
        multiLevelLinkedSelectManager.unregister(
          props.linkedGroup,
          props.linkedLevel
        )
      }
    })
  }

  // ===== 暴露的方法 =====
  const focus = () => {
    if (!computedDisabled.value) {
      isDropdownOpen.value = true
      isFocused.value = true
      emit('focus')
    }
  }

  const blur = () => {
    isDropdownOpen.value = false
    isFocused.value = false
    emit('blur')
  }

  const clear = () => {
    handleClear()
  }

  const validate = async (): Promise<boolean> => {
    const result = await veeField.validate()

    const isValid = result.valid
    const message = result.errorMessage || ''

    emit('validate', props.name, isValid, message)

    return isValid
  }

  const resetField = () => {
    veeField.resetField()
    const newValue = props.multiple ? [] : undefined
    emit('update:value', newValue)
    emit('change', newValue)
  }

  const clearValidate = () => {
    veeField.setErrors([])
  }

  // ===== 暴露给外部 =====
  defineExpose({
    focus,
    blur,
    clear,
    validate,
    resetField,
    clearValidate,
    get select() {
      return wrapperRef.value || null
    },
    get wrapper() {
      return wrapperRef.value || null
    },
  })

  // ===== 监听点击外部关闭下拉框 =====
  const handleClickOutside = (event: MouseEvent) => {
    if (wrapperRef.value && !wrapperRef.value.contains(event.target as Node)) {
      isDropdownOpen.value = false
      isFocused.value = false
    }
  }

  onMounted(() => {
    document.addEventListener('click', handleClickOutside)
  })

  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
  })
</script>

<script lang="ts">
  export default {
    name: 'SpSelectFieldCore',
  }
</script>
