/**
 * BEM 命名空间工具
 * 用于生成统一的 BEM 风格类名
 */

export interface NamespaceHelper {
  /** 生成基础块名 */
  b(): string
  /** 生成元素名 */
  be(element: string): string
  /** 生成修饰符名 */
  bm(modifier: string): string
  /** 生成状态名 */
  bs(state: string): string
  /** 生成变量名 */
  bv(variable: string): string
}

/**
 * 创建命名空间助手
 * @param namespace 命名空间（如 'button'、'input' 等）
 * @param prefix 前缀（默认为 'sp'）
 * @returns 命名空间助手对象
 */
export function createNamespace(namespace: string, prefix: string = 'sp'): NamespaceHelper {
  const block = `${prefix}-${namespace}`
  
  return {
    /** 生成基础块名 */
    b(): string {
      return block
    },
    
    /** 生成元素名 */
    be(element: string): string {
      return `${block}__${element}`
    },
    
    /** 生成修饰符名 */
    bm(modifier: string): string {
      return `${block}--${modifier}`
    },
    
    /** 生成状态名 */
    bs(state: string): string {
      return `${block}--${state}`
    },
    
    /** 生成变量名 */
    bv(variable: string): string {
      return `${block}--${variable}`
    }
  }
}

/**
 * 默认的命名空间助手工厂
 * @param namespace 命名空间
 * @returns 命名空间助手对象
 */
export function useNamespace(namespace: string): NamespaceHelper {
  return createNamespace(namespace)
}

// 导出类型