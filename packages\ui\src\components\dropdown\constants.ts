import type { InjectionKey } from 'vue'
import type { DropdownContext } from './types'

export const dropdownKey: InjectionKey<DropdownContext> = Symbol('dropdown')

export const DROPDOWN_TRIGGER_EVENTS = {
  click: ['click'],
  hover: ['mouseenter', 'mouseleave'],
  contextmenu: ['contextmenu'],
  focus: ['focus', 'blur'],
} as const

export const DEFAULT_OFFSET = 4
export const DEFAULT_Z_INDEX = 1000
