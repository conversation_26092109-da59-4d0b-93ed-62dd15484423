import { ref, computed, nextTick, type Ref } from 'vue'
import type { InputProps, InputEmits } from '../components/input/types'
import { useConfigurableSize } from './useConfigurableSize'

export function useInputLogic(
  props: InputProps,
  emit: InputEmits,
  externalInputRef?: Ref<HTMLInputElement | undefined>
) {
  // ===== 组件引用 =====
  const wrapperRef = ref<HTMLDivElement>()
  const inputRef = externalInputRef || ref<HTMLInputElement>()

  // ===== 状态管理 =====
  const passwordVisible = ref(false)
  const isFocused = ref(false)

  // ===== 计算属性 =====
  const actualType = computed(() => {
    if (props.type === 'password') {
      return passwordVisible.value ? 'text' : 'password'
    }
    return props.type
  })

  const iconSize = computed(() => {
    // 如果是数字，按比例计算图标大小
    if (typeof props.size === 'number') {
      // 基准：48px高度对应14px图标
      return Math.round((props.size / 48) * 14)
    }

    // 兼容预设尺寸
    switch (props.size) {
      case 'small':
        return 12
      case 'large':
        return 18
      case 'medium':
      default:
        return 14
    }
  })

  const hasValue = computed(() => {
    return !!props.value && String(props.value).length > 0
  })

  const wordCount = computed(() => {
    return String(props.value || '').length
  })

  const computedDisabled = computed(() => {
    return props.disabled || props.loading
  })

  const showClearIcon = computed(() => {
    return (
      props.clearable &&
      !props.disabled &&
      !props.readonly &&
      !props.loading &&
      hasValue.value
    )
  })

  const showPasswordIcon = computed(() => {
    return (
      props.showPassword &&
      props.type === 'password' &&
      !props.disabled &&
      !props.readonly &&
      !props.loading
    )
  })

  const passwordIconName = computed(() => {
    return passwordVisible.value ? 'EyeOff' : 'Eye'
  })

  // ===== Number 类型相关逻辑 =====
  const showNumberControls = computed(() => {
    return (
      props.type === 'number' &&
      !props.disabled &&
      !props.readonly &&
      !props.loading
    )
  })

  // ===== Search 控件相关逻辑 =====
  const showSearchControls = computed(() => {
    return (
      props.type === 'search' &&
      !props.disabled &&
      !props.readonly &&
      // 只有外部 loading 时才隐藏，搜索内部 loading 时仍然显示
      !props.loading
    )
  })

  const currentNumberValue = computed(() => {
    const value = props.value
    if (value === undefined || value === '') return 0
    const num = typeof value === 'number' ? value : parseFloat(String(value))
    return isNaN(num) ? 0 : num
  })

  const canIncrement = computed(() => {
    const current = currentNumberValue.value
    return props.max === undefined || current < props.max
  })

  const canDecrement = computed(() => {
    const current = currentNumberValue.value
    return props.min === undefined || current > props.min
  })

  const showSuffix = computed(() => {
    return (
      showClearIcon.value ||
      showPasswordIcon.value ||
      showNumberControls.value ||
      showSearchControls.value ||
      props.showWordLimit ||
      props.suffixIcon
    )
  })

  // ===== 使用新的配置化尺寸系统 =====
  const sizeRef = computed(() => props.size || 'medium')
  const { dynamicSizeVars, sizeClass } = useConfigurableSize(sizeRef)

  // ===== 事件处理 =====
  const handleInput = (event: Event) => {
    const target = event.target as HTMLInputElement
    const value = target.value
    emit('update:value', value === '' ? undefined : value)
    emit('input', event)
  }

  const handleChange = (event: Event) => {
    const target = event.target as HTMLInputElement
    const value = target.value
    emit('change', value === '' ? undefined : value)
  }

  const handleFocus = (event: FocusEvent) => {
    isFocused.value = true
    emit('focus', event)
  }

  const handleBlur = (event: FocusEvent) => {
    isFocused.value = false
    emit('blur', event)
  }

  const handleKeydown = (event: KeyboardEvent) => {
    // 处理特殊按键
    if (event.key === 'Enter') {
      // 回车键处理
    }
  }

  const handleWrapperClick = () => {
    if (!props.disabled && !props.readonly && !props.loading) {
      focus()
    }
  }

  const handleClear = () => {
    emit('update:value', undefined)
    emit('clear')
    nextTick(() => {
      focus()
    })
  }

  const togglePassword = () => {
    passwordVisible.value = !passwordVisible.value
    nextTick(() => {
      focus()
    })
  }

  // ===== Number 类型方法 =====
  const incrementNumber = () => {
    if (!canIncrement.value) return

    const step = props.step || 1
    const current = currentNumberValue.value
    let newValue = current + step

    // 检查最大值限制
    if (props.max !== undefined && newValue > props.max) {
      newValue = props.max
    }

    // 处理精度问题
    if (step % 1 !== 0) {
      const decimals = step.toString().split('.')[1]?.length || 0
      newValue = parseFloat(newValue.toFixed(decimals))
    }

    emit('update:value', newValue)
  }

  const decrementNumber = () => {
    if (!canDecrement.value) return

    const step = props.step || 1
    const current = currentNumberValue.value
    let newValue = current - step

    // 检查最小值限制
    if (props.min !== undefined && newValue < props.min) {
      newValue = props.min
    }

    // 处理精度问题
    if (step % 1 !== 0) {
      const decimals = step.toString().split('.')[1]?.length || 0
      newValue = parseFloat(newValue.toFixed(decimals))
    }

    emit('update:value', newValue)
  }

  // ===== Search 控件方法 =====
  const isSearchLoading = ref(false)

  const executeSearch = () => {
    // 检查是否有输入内容
    if (!hasValue.value) {
      // 没有内容，什么都不做
      return
    }

    // 有内容，开始搜索并显示 loading
    isSearchLoading.value = true
    emit('search')

    // 模拟搜索过程，2秒后隐藏 loading
    setTimeout(() => {
      isSearchLoading.value = false
    }, 2000)
  }

  const clearSearch = () => {
    emit('update:value', undefined)
    emit('clear')
    // 清除时也重置搜索 loading 状态
    isSearchLoading.value = false
    nextTick(() => {
      focus()
    })
  }

  // ===== 暴露的方法 =====
  const focus = () => {
    inputRef.value?.focus()
  }

  const blur = () => {
    inputRef.value?.blur()
  }

  const select = () => {
    inputRef.value?.select()
  }

  const clear = () => {
    emit('update:value', undefined)
    emit('clear')
  }

  // ===== 返回所有状态和方法 =====
  return {
    // 引用
    wrapperRef,
    inputRef,

    // 状态
    passwordVisible,
    isFocused,

    // 计算属性
    actualType,
    iconSize,
    hasValue,
    wordCount,
    computedDisabled,
    dynamicSizeVars,

    // 显示逻辑
    showClearIcon,
    showPasswordIcon,
    passwordIconName,
    showNumberControls,
    canIncrement,
    canDecrement,
    showSearchControls,
    isSearchLoading,
    showSuffix,

    // 事件处理
    handleInput,
    handleChange,
    handleFocus,
    handleBlur,
    handleKeydown,
    handleWrapperClick,
    handleClear,
    togglePassword,
    incrementNumber,
    decrementNumber,
    executeSearch,
    clearSearch,

    // 方法
    focus,
    blur,
    select,
    clear,
  }
}
