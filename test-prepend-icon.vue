<template>
  <div style="padding: 20px">
    <h2>测试外部前置图标</h2>

    <!-- 测试外部前置图标 -->
    <sp-input-field
      v-model:value="value1"
      prepend-icon="Search"
      label="外部前置图标测试"
      placeholder="这应该有外部前置图标"
      style="margin-bottom: 20px"
    />

    <!-- 测试内部前置图标 -->
    <sp-input-field
      v-model:value="value2"
      prepend-icon-inner="User"
      label="内部前置图标测试"
      placeholder="这应该有内部前置图标"
      style="margin-bottom: 20px"
    />

    <!-- 测试外部后置图标 -->
    <sp-input-field
      v-model:value="value3"
      append-icon="Settings"
      label="外部后置图标测试"
      placeholder="这应该有外部后置图标"
      style="margin-bottom: 20px"
    />

    <!-- 测试内部后置图标 -->
    <sp-input-field
      v-model:value="value4"
      append-icon-inner="Clear"
      label="内部后置图标测试"
      placeholder="这应该有内部后置图标"
      style="margin-bottom: 20px"
    />

    <div>
      <p>值1：{{ value1 }}</p>
      <p>值2：{{ value2 }}</p>
      <p>值3：{{ value3 }}</p>
      <p>值4：{{ value4 }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { SpInputField } from './packages/ui/src'

  const value1 = ref('')
  const value2 = ref('')
  const value3 = ref('')
  const value4 = ref('')
</script>
