<template>
  <div class="scrollbar-simple-test">
    <h1>Scrollbar 组件简单测试</h1>
    
    <div class="test-container">
      <h2>基础测试</h2>
      <sp-scrollbar max-height="200px" style="border: 1px solid #ccc;">
        <div style="padding: 20px;">
          <p v-for="i in 20" :key="i">
            这是第 {{ i }} 行内容，用于测试滚动条功能。Lorem ipsum dolor sit amet, consectetur adipiscing elit.
          </p>
        </div>
      </sp-scrollbar>
    </div>

    <div class="test-container">
      <h2>水平滚动测试</h2>
      <sp-scrollbar max-height="100px" max-width="300px" style="border: 1px solid #ccc;">
        <div style="display: flex; gap: 16px; padding: 20px; width: 800px;">
          <div v-for="i in 10" :key="i" style="flex-shrink: 0; width: 100px; height: 60px; background: #f0f0f0; display: flex; align-items: center; justify-content: center;">
            项目 {{ i }}
          </div>
        </div>
      </sp-scrollbar>
    </div>

    <div class="test-container">
      <h2>不同尺寸测试</h2>
      <div style="display: flex; gap: 20px;">
        <div>
          <h3>小尺寸</h3>
          <sp-scrollbar size="small" max-height="150px" style="border: 1px solid #ccc; width: 200px;">
            <div style="padding: 16px;">
              <p v-for="i in 10" :key="i">小尺寸 - 第 {{ i }} 行</p>
            </div>
          </sp-scrollbar>
        </div>
        
        <div>
          <h3>中等尺寸</h3>
          <sp-scrollbar size="medium" max-height="150px" style="border: 1px solid #ccc; width: 200px;">
            <div style="padding: 16px;">
              <p v-for="i in 10" :key="i">中等尺寸 - 第 {{ i }} 行</p>
            </div>
          </sp-scrollbar>
        </div>
        
        <div>
          <h3>大尺寸</h3>
          <sp-scrollbar size="large" max-height="150px" style="border: 1px solid #ccc; width: 200px;">
            <div style="padding: 16px;">
              <p v-for="i in 10" :key="i">大尺寸 - 第 {{ i }} 行</p>
            </div>
          </sp-scrollbar>
        </div>
      </div>
    </div>

    <div class="test-container">
      <h2>始终显示滚动条</h2>
      <sp-scrollbar :always="true" max-height="150px" style="border: 1px solid #ccc;">
        <div style="padding: 20px;">
          <p v-for="i in 10" :key="i">
            始终显示滚动条 - 第 {{ i }} 行内容
          </p>
        </div>
      </sp-scrollbar>
    </div>

    <div class="test-container">
      <h2>程序控制测试</h2>
      <div style="margin-bottom: 16px;">
        <button @click="scrollToTop" style="margin-right: 8px;">滚动到顶部</button>
        <button @click="scrollToBottom" style="margin-right: 8px;">滚动到底部</button>
        <button @click="scrollToMiddle">滚动到中间</button>
      </div>
      <sp-scrollbar ref="scrollbarRef" max-height="200px" style="border: 1px solid #ccc;">
        <div style="padding: 20px;">
          <p v-for="i in 30" :key="i">
            可控制滚动条 - 第 {{ i }} 行内容
          </p>
        </div>
      </sp-scrollbar>
    </div>

    <div class="test-container">
      <h2>原生滚动条模式</h2>
      <sp-scrollbar :native="true" max-height="150px" style="border: 1px solid #ccc;">
        <div style="padding: 20px;">
          <p v-for="i in 10" :key="i">
            原生滚动条模式 - 第 {{ i }} 行内容
          </p>
        </div>
      </sp-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const scrollbarRef = ref()

const scrollToTop = () => {
  scrollbarRef.value?.scrollTop(0)
}

const scrollToBottom = () => {
  scrollbarRef.value?.scrollTop(999999)
}

const scrollToMiddle = () => {
  scrollbarRef.value?.scrollTop(300)
}
</script>

<style scoped>
.scrollbar-simple-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-container {
  margin-bottom: 40px;
}

.test-container h2 {
  margin-bottom: 16px;
  color: #333;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.test-container h3 {
  margin-bottom: 12px;
  color: #666;
  font-size: 14px;
}

button {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

button:hover {
  background: #66b1ff;
}

p {
  margin: 8px 0;
  line-height: 1.6;
  color: #606266;
}
</style>
