/**
 * 基础组件事件处理 composable
 * 用于处理逻辑层的状态管理和事件转发
 */

import { ref, type Ref } from 'vue'

export interface BaseEventConfig {
  disabled: Ref<boolean> | (() => boolean)
  emit: (event: any, ...args: any[]) => void
}

/**
 * 基础事件处理 hook
 * 处理常见的鼠标交互状态和事件转发
 */
export function useBaseEvents(config: BaseEventConfig) {
  const { disabled, emit } = config

  // 状态管理
  const isHovered = ref(false)
  const isPressed = ref(false)

  // 获取禁用状态
  const getDisabled = () => {
    return typeof disabled === 'function' ? disabled() : disabled.value
  }

  // 事件处理器映射
  const eventHandlers = {
    click: () => {
      if (!getDisabled()) {
        emit('click')
      }
    },

    mouseenter: () => {
      if (!getDisabled()) {
        isHovered.value = true
        emit('mouseenter')
      }
    },

    mouseleave: () => {
      isHovered.value = false
      isPressed.value = false
      emit('mouseleave')
    },

    mousedown: () => {
      if (!getDisabled()) {
        isPressed.value = true
        emit('mousedown')
      }
    },

    mouseup: () => {
      isPressed.value = false
      emit('mouseup')
    },
  }

  return {
    // 状态
    isHovered,
    isPressed,
    // 事件处理器
    eventHandlers,
    // 单独的处理器（如果需要）
    handleClick: eventHandlers.click,
    handleMouseEnter: eventHandlers.mouseenter,
    handleMouseLeave: eventHandlers.mouseleave,
    handleMouseDown: eventHandlers.mousedown,
    handleMouseUp: eventHandlers.mouseup,
  }
}
