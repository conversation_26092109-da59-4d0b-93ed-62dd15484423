<!-- 
  这是一个示例文件，展示如何使用 propsFactory 来创建 Input 组件
  可以基于 InputCore 的 props 工厂来扩展自己的 props
-->
<template>
  <div class="sp-input">
    <InputCore
      v-bind="inputCoreProps"
      @input="handleInput"
      @change="handleChange"
      @focus="handleFocus"
      @blur="handleBlur"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits } from 'vue'
import InputCore from './inputcore'
import { makeInputCoreProps, inputCoreDefaults } from './inputCoreProps'

// 扩展 InputCore 的 props，添加 Input 特有的属性
const makeInputProps = () => {
  return {
    // 继承 InputCore 的所有 props
    ...makeInputCoreProps(),
    
    // Input 特有的 props
    modelValue: {
      type: [String, Number],
      default: '',
    },
    label: {
      type: String,
      default: '',
    },
    helperText: {
      type: String,
      default: '',
    },
  }
}

// 定义 props，可以覆盖默认值
const props = defineProps({
  ...makeInputProps(),
  // 可以在这里覆盖默认值
  // 例如：让 Input 组件默认可清除
  clearable: {
    type: Boolean,
    default: true,  // 覆盖 InputCore 的默认值 false
  },
  // 让 Input 组件默认显示字数统计
  showWordLimit: {
    type: Boolean,
    default: true,  // 覆盖 InputCore 的默认值 false
  },
})

// 定义事件
const emit = defineEmits<{
  'update:modelValue': [value: string | number]
  input: [event: Event]
  change: [event: Event]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
}>()

// 计算传递给 InputCore 的 props
const inputCoreProps = computed(() => {
  const { modelValue, label, helperText, ...coreProps } = props
  return {
    ...coreProps,
    value: modelValue,
  }
})

// 事件处理
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:modelValue', target.value)
  emit('input', event)
}

const handleChange = (event: Event) => {
  emit('change', event)
}

const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}
</script>

<style scoped>
.sp-input {
  /* Input 组件特有的样式 */
}
</style>
