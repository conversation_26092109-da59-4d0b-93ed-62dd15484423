# 快速开始

## 安装

```bash
npm install @speed-ui/ui
# 或
pnpm add @speed-ui/ui
```

## 使用

### 全局注册

```ts
// main.ts
import { createApp } from 'vue'
import SpeedUI from '@speed-ui/ui'

const app = createApp(App)
app.use(SpeedUI)
app.mount('#app')
```

### 按需引入

```vue
<script setup>
import { Button } from '@speed-ui/ui'
</script>

<template>
  <Button>点击我</Button>
</template>
```

## 开发

克隆项目并启动开发环境：

```bash
git clone https://github.com/your-username/speed-ui.git
cd speed-ui
pnpm install
pnpm dev
``` 