/**
 * 按钮类名构建器测试
 */

import { describe, it, expect } from 'vitest'
import { useBEM } from '@speed-ui/bem-helper'
import { ButtonClassBuilder, createButtonClassBuilder, type ButtonState } from '../button-class-builder'
import type { ButtonProps } from '../button'

describe('ButtonClassBuilder', () => {
  const bem = useBEM('button')
  
  const createMockProps = (overrides: Partial<ButtonProps> = {}): ButtonProps => ({
    primary: false,
    secondary: false,
    dashed: false,
    text: false,
    link: false,
    disabled: false,
    loading: false,
    size: 'medium',
    circle: false,
    round: false,
    square: false,
    vertical: false,
    ...overrides
  })

  const createMockState = (overrides: Partial<ButtonState> = {}): ButtonState => ({
    isActive: false,
    isPressing: false,
    isCountingDown: false,
    ...overrides
  })

  describe('基础功能', () => {
    it('应该生成基础类名', () => {
      const props = createMockProps()
      const state = createMockState()
      const builder = createButtonClassBuilder(bem, props, state)
      
      const classes = builder.build()
      expect(classes).toContain('sp-button')
    })

    it('应该生成默认变体类名', () => {
      const props = createMockProps()
      const state = createMockState()
      const builder = createButtonClassBuilder(bem, props, state)
      
      const classes = builder.build()
      expect(classes).toContain('sp-button--secondary') // 默认类型
    })

    it('应该生成默认尺寸类名', () => {
      const props = createMockProps()
      const state = createMockState()
      const builder = createButtonClassBuilder(bem, props, state)
      
      const classes = builder.build()
      expect(classes).toContain('sp-button--medium') // 默认尺寸
    })
  })

  describe('变体类型', () => {
    it('应该正确处理 primary 变体', () => {
      const props = createMockProps({ primary: true })
      const state = createMockState()
      const builder = createButtonClassBuilder(bem, props, state)
      
      const classes = builder.build()
      expect(classes).toContain('sp-button--primary')
      expect(classes).not.toContain('sp-button--secondary')
    })

    it('应该按优先级处理多个变体', () => {
      const props = createMockProps({ 
        primary: true, 
        secondary: true, 
        text: true 
      })
      const state = createMockState()
      const builder = createButtonClassBuilder(bem, props, state)
      
      const classes = builder.build()
      expect(classes).toContain('sp-button--primary') // 最高优先级
      expect(classes).not.toContain('sp-button--secondary')
      expect(classes).not.toContain('sp-button--text')
    })
  })

  describe('形状类型', () => {
    it('应该正确处理 circle 形状', () => {
      const props = createMockProps({ circle: true })
      const state = createMockState()
      const builder = createButtonClassBuilder(bem, props, state)
      
      const classes = builder.build()
      expect(classes).toContain('sp-button--circle')
    })

    it('应该按优先级处理多个形状', () => {
      const props = createMockProps({ 
        circle: true, 
        round: true, 
        square: true 
      })
      const state = createMockState()
      const builder = createButtonClassBuilder(bem, props, state)
      
      const classes = builder.build()
      expect(classes).toContain('sp-button--circle') // 最高优先级
      expect(classes).not.toContain('sp-button--round')
      expect(classes).not.toContain('sp-button--square')
    })

    it('应该在没有形状时不添加形状类名', () => {
      const props = createMockProps()
      const state = createMockState()
      const builder = createButtonClassBuilder(bem, props, state)
      
      const classes = builder.build()
      expect(classes).not.toContain('sp-button--circle')
      expect(classes).not.toContain('sp-button--round')
      expect(classes).not.toContain('sp-button--square')
    })
  })

  describe('颜色类型', () => {
    it('应该正确处理颜色类型', () => {
      const props = createMockProps({ type: 'success' })
      const state = createMockState()
      const builder = createButtonClassBuilder(bem, props, state)
      
      const classes = builder.build()
      expect(classes).toContain('sp-button--success')
    })

    it('应该在没有颜色类型时不添加颜色类名', () => {
      const props = createMockProps()
      const state = createMockState()
      const builder = createButtonClassBuilder(bem, props, state)
      
      const classes = builder.build()
      expect(classes).not.toContain('sp-button--success')
      expect(classes).not.toContain('sp-button--warning')
      expect(classes).not.toContain('sp-button--danger')
      expect(classes).not.toContain('sp-button--default')
    })
  })

  describe('状态类名', () => {
    it('应该正确处理禁用状态', () => {
      const props = createMockProps({ disabled: true })
      const state = createMockState()
      const builder = createButtonClassBuilder(bem, props, state)
      
      const classes = builder.build()
      expect(classes).toContain('sp-button--disabled')
    })

    it('应该正确处理加载状态', () => {
      const props = createMockProps({ loading: true })
      const state = createMockState()
      const builder = createButtonClassBuilder(bem, props, state)
      
      const classes = builder.build()
      expect(classes).toContain('sp-button--loading')
    })

    it('应该正确处理激活状态', () => {
      const props = createMockProps()
      const state = createMockState({ isActive: true })
      const builder = createButtonClassBuilder(bem, props, state)
      
      const classes = builder.build()
      expect(classes).toContain('sp-button--active')
    })

    it('应该正确处理长按状态', () => {
      const props = createMockProps()
      const state = createMockState({ isPressing: true })
      const builder = createButtonClassBuilder(bem, props, state)
      
      const classes = builder.build()
      expect(classes).toContain('sp-button--pressing')
    })

    it('应该正确处理倒计时状态', () => {
      const props = createMockProps()
      const state = createMockState({ isCountingDown: true })
      const builder = createButtonClassBuilder(bem, props, state)
      
      const classes = builder.build()
      expect(classes).toContain('sp-button--countdown')
    })
  })

  describe('布局类名', () => {
    it('应该正确处理垂直布局', () => {
      const props = createMockProps({ vertical: true })
      const state = createMockState()
      const builder = createButtonClassBuilder(bem, props, state)
      
      const classes = builder.build()
      expect(classes).toContain('sp-button--vertical')
    })
  })

  describe('调试信息', () => {
    it('应该提供正确的调试信息', () => {
      const props = createMockProps({ 
        primary: true, 
        type: 'success', 
        size: 'large',
        circle: true,
        disabled: true,
        vertical: true
      })
      const state = createMockState({ 
        isActive: true, 
        isPressing: false, 
        isCountingDown: true 
      })
      const builder = createButtonClassBuilder(bem, props, state)
      
      const debugInfo = builder.getDebugInfo()
      expect(debugInfo.variant).toBe('primary')
      expect(debugInfo.colorType).toBe('success')
      expect(debugInfo.size).toBe('large')
      expect(debugInfo.shape).toBe('circle')
      expect(debugInfo.state.isActive).toBe(true)
      expect(debugInfo.state.isPressing).toBe(false)
      expect(debugInfo.state.isCountingDown).toBe(true)
      expect(debugInfo.props.disabled).toBe(true)
      expect(debugInfo.props.vertical).toBe(true)
    })
  })
})
