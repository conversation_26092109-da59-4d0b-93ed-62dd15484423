<template>
  <div style="padding: 20px;">
    <h1>简单 Select 测试</h1>
    
    <div style="margin: 20px 0;">
      <label>基础测试：</label>
      <select-component
        v-model:value="testValue"
        :options="testOptions"
        placeholder="请选择"
        style="width: 200px; margin-left: 10px;"
      />
      <span style="margin-left: 10px;">选中值: {{ testValue || '无' }}</span>
    </div>
    
    <div style="margin: 20px 0;">
      <label>多选测试：</label>
      <select-component
        v-model:value="multiValue"
        :options="testOptions"
        placeholder="请选择多个"
        mode="multiple"
        style="width: 300px; margin-left: 10px;"
      />
      <span style="margin-left: 10px;">选中值: {{ multiValue.length ? multiValue.join(', ') : '无' }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SelectComponent from '../../../packages/ui/src/components/select/Select.vue'

const testValue = ref('')
const multiValue = ref([])

const testOptions = [
  { label: '选项1', value: 'option1' },
  { label: '选项2', value: 'option2' },
  { label: '选项3', value: 'option3' },
]
</script>
