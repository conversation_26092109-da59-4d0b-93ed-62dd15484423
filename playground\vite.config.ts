import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { resolve } from 'path'
import { caseFilePlugin } from './vite-plugin-case'

export default defineConfig({
  plugins: [vue(), vueJsx(), caseFilePlugin()],
  resolve: {
    alias: {
      // UI 包
      '@speed-ui/ui': resolve(__dirname, '../packages/ui/src'),
      '@speed-ui/utils': resolve(__dirname, '../packages/utils/src'),
      '@speed-ui/theme-default': resolve(
        __dirname,
        '../packages/theme-default'
      ),
      '@theme-default': resolve(__dirname, '../packages/theme-default'),

      // 公共包 - 直接指向源码
      '@speed-ui/bem-helper': resolve(
        __dirname,
        '../packages/common/bem-helper/src'
      ),
      '@speed-ui/config': resolve(__dirname, '../packages/common/config/src'),
      '@speed-ui/hooks': resolve(__dirname, '../packages/hooks/src'),
    },
  },
})
