<template>
  <div :class="sliderClasses">
    <!-- 使用 BaseInput 组件 -->
    <BaseInput
      ref="inputRef"
      v-model:value="currentValue"
      type="range"
      class="sp-slider__input"
      :min="min"
      :max="max"
      :step="step"
      :disabled="disabled"
      :style="inputStyle"
      @change="handleSliderChange"
    />

    <!-- 标记点 -->
    <div
      v-if="marks && Object.keys(marks).length > 0"
      class="sp-slider__marks"
    >
      <div
        v-for="(label, value) in marks"
        :key="value"
        class="sp-slider__mark"
        :class="{ 'sp-slider__mark--active': Number(value) <= currentValue }"
        :style="{ left: `${((Number(value) - min) / (max - min)) * 100}%` }"
      >
        {{ label }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { bemHelper } from '@speed-ui/config'
  import BaseInput from '../baseinput/baseinput'

  // 创建 BEM 类名生成器
  const bem = bemHelper('slider')

  // Props 定义
  interface SliderProps {
    modelValue?: number
    min?: number
    max?: number
    step?: number
    disabled?: boolean
    variant?: 'default' | 'filled' | 'filled-square'
    size?: 'small' | 'medium' | 'large'
    marks?: Record<number, string>
  }

  const props = withDefaults(defineProps<SliderProps>(), {
    modelValue: 0,
    min: 0,
    max: 100,
    step: 1,
    disabled: false,
    variant: 'default',
    size: 'medium',
    marks: () => ({}),
  })

  // Emits 定义
  interface SliderEmits {
    'update:modelValue': [value: number]
    change: [value: number]
  }

  const emit = defineEmits<SliderEmits>()

  // 响应式数据
  const inputRef = ref()

  // 双向绑定的当前值
  const currentValue = computed({
    get: () => props.modelValue,
    set: (value: number) => emit('update:modelValue', value)
  })

  // 计算属性
  const sliderClasses = computed(() => [
    bem.b(),
    bem.m(props.variant),
    bem.m(props.size),
    {
      [bem.m('disabled')]: props.disabled,
    },
  ])

  // 计算进度条样式（用于WebKit浏览器）
  const inputStyle = computed(() => {
    const percentage =
      ((currentValue.value - props.min) / (props.max - props.min)) * 100

    if (props.variant === 'filled' || props.variant === 'filled-square') {
      // 填充变体和正方形填充变体：直接使用percentage，确保当滑块在最左边时进度条为0
      return {
        '--progress-width':
          percentage < 50 ? `${percentage + 1}%` : `${percentage - 0.5}%`,
      }
    } else {
      // 默认变体的进度条样式
      return {
        background: `linear-gradient(to right, var(--sp-color-primary) 0%, var(--sp-color-primary) ${percentage}%, #d9d9d9 ${percentage}%, #d9d9d9 100%)`,
      }
    }
  })

  // 事件处理
  const handleSliderChange = (event: Event) => {
    const target = event.target as HTMLInputElement
    const value = Number(target.value)
    emit('change', value)
  }

  // 暴露方法和属性
  const focus = () => {
    inputRef.value?.focus()
  }

  const blur = () => {
    inputRef.value?.blur()
  }

  defineExpose({
    inputRef,
    focus,
    blur,
  })
</script>

<script lang="ts">
  export default {
    name: 'Slider',
  }
</script>
