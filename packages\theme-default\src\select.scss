// ================================
// Speed UI Select 组件样式
// ================================

@use './common/var.scss' as *;

.sp-select {
  position: relative;
  font-size: $font-size-base;
  display: inline-block;
  width: 100%;

  &--small {
    font-size: $button-font-size-small;

    .sp-select__inner {
      padding: 4px 8px;
      padding-right: 30px; // 为后缀图标留出空间
    }
  }

  &--large {
    font-size: $button-font-size-large;

    .sp-select__inner {
      padding: 12px 16px;
      padding-right: 30px; // 为后缀图标留出空间
    }
  }

  // 选择器包装器
  &__wrapper {
    position: relative;
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  // 选择器主体 - 与 Input 保持一致
  &__inner {
    width: 100%;
    padding: $spacing-sm $spacing-sm + 4px;
    border: 1px solid $border-color-base;
    border-radius: $border-radius-base;
    font-size: inherit;
    line-height: 1.5;
    color: $color-text-primary;
    background-color: $background-color-base;
    background-image: none;
    transition: all $transition-duration-base $transition-timing-base;
    box-sizing: border-box;
    position: relative;
    display: flex;
    align-items: center;
    padding-right: 30px; // 为后缀图标留出空间

    &::before {
      content: '';
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      border-radius: $border-radius-base;
      background: linear-gradient(135deg, $color-primary 0%, $color-info 100%);
      z-index: -1;
      opacity: 0;
      transition: opacity $transition-duration-base ease;
    }
  }

  // 悬停效果 - 与 Input 一致
  &:not(&--disabled) &__wrapper:hover &__inner {
    border-color: $border-color-hover;
  }

  // 聚焦效果 - 与 Input 完全一致
  &--focused &__inner {
    outline: none;
    border-color: $color-primary;
    background: $background-color-base;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2),
      inset 0 0 0 1px $color-primary;

    &::before {
      opacity: 1;
    }
  }

  // 占位符 - 与 Input 一致
  &__placeholder {
    color: $color-text-disabled;
    user-select: none;
  }

  // 选中的值
  &__selected {
    color: $color-text-primary;
    user-select: none;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // 标签
  &__tag {
    display: inline-flex;
    align-items: center;
    background-color: $background-color-hover;
    color: $color-text-primary;
    padding: 2px $spacing-sm;
    border-radius: $border-radius-small;
    font-size: $button-font-size-small;
    max-width: 100%;
    box-sizing: border-box;
  }

  &__tag-close {
    margin-left: $spacing-xs;
    cursor: pointer;
    font-size: 10px;
    color: $color-text-secondary;
    transition: color 0.2s;
    display: inline-flex;
    align-items: center;
    justify-content: center;

    // xicons SVG 图标样式
    svg {
      width: 1em;
      height: 1em;
      fill: currentColor;
    }

    &:hover {
      color: $color-primary;
    }
  }

  // 后缀图标区域 - 与 Input 一致
  &__suffix {
    position: absolute;
    right: $spacing-sm;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    color: $color-text-disabled;
    z-index: 1;
  }

  // 图标 - 与 Input 一致
  &__icon {
    cursor: pointer;
    font-size: $font-size-base;
    margin-left: $spacing-xs;
    transition: color 0.2s;
    display: inline-flex;
    align-items: center;
    justify-content: center;

    // xicons SVG 图标样式
    svg {
      width: 1em;
      height: 1em;
      fill: currentColor;
      transition: inherit;
    }

    &:first-child {
      margin-left: 0;
    }

    &:hover {
      color: $color-text-secondary;
    }
  }

  &__clear:hover {
    color: $color-primary;
  }

  &__arrow {
    transition: transform $transition-duration-base;

    &--reverse {
      transform: rotate(180deg);
    }
  }

  // 下拉选项容器
  &__dropdown {
    position: absolute;
    left: 0;
    right: 0;
    z-index: 1000;
    background: $background-color-base;
    border: 1px solid $border-color-base;
    border-radius: $border-radius-base;
    box-shadow: $box-shadow-base;
    margin-top: $spacing-xs;

    // 验证状态的下拉框边框颜色
    &--success {
      border-color: $color-success;
      box-shadow: 0 4px 12px rgba($color-success, 0.15);
    }

    &--warning {
      border-color: $color-warning;
      box-shadow: 0 4px 12px rgba($color-warning, 0.15);
    }

    &--error {
      border-color: $color-danger;
      box-shadow: 0 4px 12px rgba($color-danger, 0.15);
    }
  }

  // 选项列表容器
  &__options {
    // 确保 scrollbar 组件样式正常工作
    .sp-scrollbar {
      background: transparent;
    }
  }

  // 选项列表
  &__option-list {
    list-style: none;
    padding: $spacing-xs 0;
    margin: 0;
  }

  // 选项项
  &__option {
    padding: $spacing-sm $spacing-sm + 4px;
    cursor: pointer;
    color: $color-text-primary;
    font-size: $font-size-base;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &:hover {
      background-color: $background-color-hover;
    }

    &--selected {
      color: $color-primary;
      background-color: $color-primary-lightest;
      font-weight: 500;
    }

    &--disabled {
      color: $color-text-disabled;
      cursor: not-allowed;
    }
  }

  // 状态
  &--disabled {
    cursor: not-allowed;
    opacity: 0.6;

    .sp-select__inner {
      background-color: $background-color-hover;
      border-color: $border-color-base;
      color: $color-text-disabled;
    }

    .sp-select__placeholder,
    .sp-select__selected {
      color: $color-text-disabled;
    }

    .sp-select__suffix {
      color: $color-text-disabled;
    }

    .sp-select__icon {
      color: $color-text-disabled;
    }
  }

  &--success {
    .sp-select__inner {
      border-color: $color-success;

      &::before {
        background: linear-gradient(
          135deg,
          $color-success 0%,
          rgba($color-success, 0.8) 100%
        );
      }

      &:focus {
        border-color: $color-success;
        box-shadow: 0 0 0 2px rgba($color-success, 0.2),
          inset 0 0 0 1px $color-success;
      }
    }

    // 覆盖默认的 hover 效果
    &:not(.sp-select--disabled) .sp-select__wrapper:hover .sp-select__inner {
      border-color: rgba($color-success, 0.8);
    }

    // 覆盖默认的 focus 效果
    &.sp-select--focused .sp-select__inner {
      border-color: $color-success;
      box-shadow: 0 0 0 2px rgba($color-success, 0.2),
        inset 0 0 0 1px $color-success;

      &::before {
        opacity: 1;
        background: linear-gradient(
          135deg,
          $color-success 0%,
          rgba($color-success, 0.8) 100%
        );
      }
    }
  }

  &--warning {
    .sp-select__inner {
      border-color: $color-warning;

      &::before {
        background: linear-gradient(
          135deg,
          $color-warning 0%,
          rgba($color-warning, 0.8) 100%
        );
      }

      &:focus {
        border-color: $color-warning;
        box-shadow: 0 0 0 2px rgba($color-warning, 0.2),
          inset 0 0 0 1px $color-warning;
      }
    }

    // 覆盖默认的 hover 效果
    &:not(.sp-select--disabled) .sp-select__wrapper:hover .sp-select__inner {
      border-color: rgba($color-warning, 0.8);
    }

    // 覆盖默认的 focus 效果
    &.sp-select--focused .sp-select__inner {
      border-color: $color-warning;
      box-shadow: 0 0 0 2px rgba($color-warning, 0.2),
        inset 0 0 0 1px $color-warning;

      &::before {
        opacity: 1;
        background: linear-gradient(
          135deg,
          $color-warning 0%,
          rgba($color-warning, 0.8) 100%
        );
      }
    }
  }

  &--error {
    .sp-select__inner {
      border-color: $color-danger;

      &::before {
        background: linear-gradient(
          135deg,
          $color-danger 0%,
          rgba($color-danger, 0.8) 100%
        );
      }

      &:focus {
        border-color: $color-danger;
        box-shadow: 0 0 0 2px rgba($color-danger, 0.2),
          inset 0 0 0 1px $color-danger;
      }
    }

    // 覆盖默认的 hover 效果
    &:not(.sp-select--disabled) .sp-select__wrapper:hover .sp-select__inner {
      border-color: rgba($color-danger, 0.8);
    }

    // 覆盖默认的 focus 效果
    &.sp-select--focused .sp-select__inner {
      border-color: $color-danger;
      box-shadow: 0 0 0 2px rgba($color-danger, 0.2),
        inset 0 0 0 1px $color-danger;

      &::before {
        opacity: 1;
        background: linear-gradient(
          135deg,
          $color-danger 0%,
          rgba($color-danger, 0.8) 100%
        );
      }
    }
  }

  // 多选样式 - 允许换行但保持最小高度
  &--multiple &__inner {
    min-height: 36px; // Medium 尺寸的最小高度，与单选保持一致
    align-items: flex-start; // 顶部对齐，适应换行
    flex-wrap: wrap; // 允许换行
    padding: $spacing-sm $spacing-sm + 4px; // 与单选模式保持一致：8px 12px
    padding-right: 30px; // 为后缀图标留出空间
    transition: all $transition-duration-base $transition-timing-base;
  }

  // 多选模式下的尺寸调整 - 与单选模式完全一致
  &--small.sp-select--multiple &__inner {
    min-height: 32px; // Small 尺寸的最小高度，与单选保持一致
    padding: 4px 8px; // 与单选 small 完全一致
    padding-right: 30px; // 为后缀图标留出空间
  }

  &--large.sp-select--multiple &__inner {
    min-height: 48px; // Large 尺寸的最小高度，与单选保持一致
    padding: 12px 16px; // 与单选 large 完全一致
    padding-right: 30px; // 为后缀图标留出空间
  }

  // 多选标签容器样式
  &__tags {
    display: flex;
    flex-wrap: wrap; // 允许换行
    gap: $spacing-xs;
    flex: 1;
    min-width: 0;
    align-items: flex-start; // 顶部对齐
  }

  // 校验状态
  &--validating {
    .sp-select__inner {
      border-color: $color-warning;

      &::before {
        background: linear-gradient(
          135deg,
          $color-warning 0%,
          rgba($color-warning, 0.8) 100%
        );
      }

      &:focus {
        border-color: $color-warning;
        box-shadow: 0 0 0 2px rgba($color-warning, 0.2),
          inset 0 0 0 1px $color-warning;
      }
    }

    // 覆盖默认的 hover 效果
    &:not(.sp-select--disabled) .sp-select__wrapper:hover .sp-select__inner {
      border-color: rgba($color-warning, 0.8);
    }

    // 覆盖默认的 focus 效果
    &.sp-select--focused .sp-select__inner {
      border-color: $color-warning;
      box-shadow: 0 0 0 2px rgba($color-warning, 0.2),
        inset 0 0 0 1px $color-warning;

      &::before {
        opacity: 1;
        background: linear-gradient(
          135deg,
          $color-warning 0%,
          rgba($color-warning, 0.8) 100%
        );
      }
    }
  }

  &__feedback {
    font-size: $button-font-size-small;
    margin-top: $spacing-xs;
    color: $color-text-secondary;

    &--success {
      color: $color-success;
    }

    &--warning {
      color: $color-warning;
    }

    &--error {
      color: $color-danger;
    }
  }
}

// 下拉动画
.sp-select-dropdown-enter-active,
.sp-select-dropdown-leave-active {
  transition: all $transition-duration-base $transition-timing-base;
  transform-origin: center top;
}

.sp-select-dropdown-enter-from,
.sp-select-dropdown-leave-to {
  opacity: 0;
  transform: scaleY(0.8);
}

// Scrollbar 组件已经提供了自定义滚动条样式
