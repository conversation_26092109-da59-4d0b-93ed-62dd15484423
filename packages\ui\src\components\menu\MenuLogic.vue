<template>
  <MenuInner
    :class="menuClasses"
    :ariaOrientation="props.mode === 'horizontal' ? 'horizontal' : 'vertical'"
  >
    <slot />
  </MenuInner>
</template>

<script setup lang="ts">
  import { computed, provide } from 'vue'
  import { bemHelper } from '@speed-ui/config'
  import type { MenuProps, MenuContext } from './types'
  import MenuInner from './MenuInner.vue'

  console.log('🚀 MenuLogic 开始执行!')

  const props = withDefaults(defineProps<MenuProps>(), {
    mode: 'vertical',
    theme: 'light',
    selectedKeys: () => [],
    openKeys: () => [],
    inlineIndent: 24,
    selectable: true,
  })

  console.log('📋 MenuLogic props 接收到:', {
    selectedKeys: props.selectedKeys,
    mode: props.mode,
    theme: props.theme,
  })

  const emit = defineEmits<{
    'update:selectedKeys': [keys: string[]]
    'update:openKeys': [keys: string[]]
    select: [info: { key: string; selectedKeys: string[] }]
    openChange: [openKeys: string[]]
  }>()

  // BEM helper
  const bem = bemHelper('menu')

  // 计算样式类
  const menuClasses = computed(() => [
    bem.b(), // sp-menu
    bem.m(props.mode), // sp-menu--vertical/horizontal
    bem.m(props.theme), // sp-menu--light/dark
    {
      [bem.m('disabled')]: props.disabled,
      [bem.m('loading')]: props.loading,
    },
  ])

  // 选择处理逻辑
  const handleSelect = (key: string) => {
    try {
      if (!props.selectable || props.disabled) return
      if (!key) {
        console.warn('Menu: 选择项缺少 key 属性')
        return
      }

      let newSelectedKeys: string[]

      if (props.multiple) {
        newSelectedKeys = props.selectedKeys.includes(key)
          ? props.selectedKeys.filter(k => k !== key)
          : [...props.selectedKeys, key]
      } else {
        newSelectedKeys = [key]
      }

      emit('update:selectedKeys', newSelectedKeys)
      emit('select', { key, selectedKeys: newSelectedKeys })
    } catch (error) {
      console.error('Menu selection error:', error)
      // 可以添加错误回调或用户友好的错误提示
    }
  }

  // 展开处理逻辑
  const handleOpenChange = (key: string, open: boolean) => {
    try {
      if (!key) {
        console.warn('Menu: 展开项缺少 key 属性')
        return
      }

      const newOpenKeys = open
        ? [...props.openKeys, key]
        : props.openKeys.filter(k => k !== key)

      emit('update:openKeys', newOpenKeys)
      emit('openChange', newOpenKeys)
    } catch (error) {
      console.error('Menu open change error:', error)
    }
  }

  // 菜单上下文
  const menuContext: MenuContext = {
    props,
    selectedKeys: computed(() => {
      console.log('📋 MenuLogic selectedKeys computed:', props.selectedKeys)
      return props.selectedKeys
    }),
    openKeys: computed(() => props.openKeys),
    handleSelect,
    handleOpenChange,
  }

  // 提供上下文给子组件
  provide('menuContext', menuContext)
  console.log('🎯 MenuContext 已提供:', menuContext)
</script>
