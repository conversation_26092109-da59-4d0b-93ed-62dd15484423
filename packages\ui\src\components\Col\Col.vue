<template>
  <component
    :is="tag"
    :class="colClass"
    :style="colStyle"
  >
    <slot></slot>
  </component>
</template>

<script setup lang="ts">
import { computed, inject } from 'vue'

export interface ResponsiveProps {
  span?: number
  offset?: number
  pull?: number
  push?: number
}

export interface ColProps {
  /**
   * 栅格占据的列数
   */
  span?: number
  /**
   * 栅格左侧的间隔格数
   */
  offset?: number
  /**
   * 栅格向右移动格数
   */
  push?: number
  /**
   * 栅格向左移动格数
   */
  pull?: number
  /**
   * <768px 响应式栅格数或者栅格属性对象
   */
  xs?: number | ResponsiveProps
  /**
   * ≥768px 响应式栅格数或者栅格属性对象
   */
  sm?: number | ResponsiveProps
  /**
   * ≥992px 响应式栅格数或者栅格属性对象
   */
  md?: number | ResponsiveProps
  /**
   * ≥1200px 响应式栅格数或者栅格属性对象
   */
  lg?: number | ResponsiveProps
  /**
   * ≥1920px 响应式栅格数或者栅格属性对象
   */
  xl?: number | ResponsiveProps
  /**
   * 自定义元素标签
   */
  tag?: string
}

const props = withDefaults(defineProps<ColProps>(), {
  span: 24,
  offset: 0,
  push: 0,
  pull: 0,
  tag: 'div',
})

// 注入父组件的 gutter
const rowContext = inject<{ gutter: { value: number } }>('spRow', { gutter: { value: 0 } })

const gutter = computed(() => {
  return rowContext?.gutter?.value || 0
})

// 生成响应式类名
const getResponsiveClass = (size: string, props: number | ResponsiveProps | undefined) => {
  const classes: string[] = []
  
  if (typeof props === 'number') {
    classes.push(`sp-col-${size}-${props}`)
  } else if (typeof props === 'object' && props) {
    if (props.span) classes.push(`sp-col-${size}-${props.span}`)
    if (props.offset) classes.push(`sp-col-${size}-offset-${props.offset}`)
    if (props.pull) classes.push(`sp-col-${size}-pull-${props.pull}`)
    if (props.push) classes.push(`sp-col-${size}-push-${props.push}`)
  }
  
  return classes
}

const colClass = computed(() => [
  'sp-col',
  props.span ? `sp-col-${props.span}` : '',
  props.offset ? `sp-col-offset-${props.offset}` : '',
  props.pull ? `sp-col-pull-${props.pull}` : '',
  props.push ? `sp-col-push-${props.push}` : '',
  ...getResponsiveClass('xs', props.xs),
  ...getResponsiveClass('sm', props.sm),
  ...getResponsiveClass('md', props.md),
  ...getResponsiveClass('lg', props.lg),
  ...getResponsiveClass('xl', props.xl),
])

const colStyle = computed(() => {
  const styles: Record<string, string> = {}
  
  if (gutter.value) {
    const gutterValue = gutter.value / 2
    styles.paddingLeft = `${gutterValue}px`
    styles.paddingRight = `${gutterValue}px`
  }
  
  return styles
})
</script>

<script lang="ts">
export default {
  name: 'SpCol',
}
</script>

<style lang="scss" scoped>
@use "sass:math";

.sp-col {
  box-sizing: border-box;
  position: relative;
}

// 生成栅格类
@for $i from 0 through 24 {
  .sp-col-#{$i} {
    max-width: (math.div(1, 24) * $i * 100) * 1%;
    flex: 0 0 (math.div(1, 24) * $i * 100) * 1%;
  }

  .sp-col-offset-#{$i} {
    margin-left: (math.div(1, 24) * $i * 100) * 1%;
  }

  .sp-col-pull-#{$i} {
    position: relative;
    right: (math.div(1, 24) * $i * 100) * 1%;
  }

  .sp-col-push-#{$i} {
    position: relative;
    left: (math.div(1, 24) * $i * 100) * 1%;
  }
}

// 特殊处理 span 为 0 的情况
.sp-col-0 {
  display: none;
}

// 响应式设计断点
@media (max-width: 767px) {
  @for $i from 0 through 24 {
    .sp-col-xs-#{$i} {
      max-width: (math.div(1, 24) * $i * 100) * 1%;
      flex: 0 0 (math.div(1, 24) * $i * 100) * 1%;
    }

    .sp-col-xs-offset-#{$i} {
      margin-left: (math.div(1, 24) * $i * 100) * 1%;
    }

    .sp-col-xs-pull-#{$i} {
      position: relative;
      right: (math.div(1, 24) * $i * 100) * 1%;
    }

    .sp-col-xs-push-#{$i} {
      position: relative;
      left: (math.div(1, 24) * $i * 100) * 1%;
    }
  }

  .sp-col-xs-0 {
    display: none;
  }
}

@media (min-width: 768px) {
  @for $i from 0 through 24 {
    .sp-col-sm-#{$i} {
      max-width: (math.div(1, 24) * $i * 100) * 1%;
      flex: 0 0 (math.div(1, 24) * $i * 100) * 1%;
    }

    .sp-col-sm-offset-#{$i} {
      margin-left: (math.div(1, 24) * $i * 100) * 1%;
    }

    .sp-col-sm-pull-#{$i} {
      position: relative;
      right: (math.div(1, 24) * $i * 100) * 1%;
    }

    .sp-col-sm-push-#{$i} {
      position: relative;
      left: (math.div(1, 24) * $i * 100) * 1%;
    }
  }

  .sp-col-sm-0 {
    display: none;
  }
}

@media (min-width: 992px) {
  @for $i from 0 through 24 {
    .sp-col-md-#{$i} {
      max-width: (math.div(1, 24) * $i * 100) * 1%;
      flex: 0 0 (math.div(1, 24) * $i * 100) * 1%;
    }

    .sp-col-md-offset-#{$i} {
      margin-left: (math.div(1, 24) * $i * 100) * 1%;
    }

    .sp-col-md-pull-#{$i} {
      position: relative;
      right: (math.div(1, 24) * $i * 100) * 1%;
    }

    .sp-col-md-push-#{$i} {
      position: relative;
      left: (math.div(1, 24) * $i * 100) * 1%;
    }
  }

  .sp-col-md-0 {
    display: none;
  }
}

@media (min-width: 1200px) {
  @for $i from 0 through 24 {
    .sp-col-lg-#{$i} {
      max-width: (math.div(1, 24) * $i * 100) * 1%;
      flex: 0 0 (math.div(1, 24) * $i * 100) * 1%;
    }

    .sp-col-lg-offset-#{$i} {
      margin-left: (math.div(1, 24) * $i * 100) * 1%;
    }

    .sp-col-lg-pull-#{$i} {
      position: relative;
      right: (math.div(1, 24) * $i * 100) * 1%;
    }

    .sp-col-lg-push-#{$i} {
      position: relative;
      left: (math.div(1, 24) * $i * 100) * 1%;
    }
  }

  .sp-col-lg-0 {
    display: none;
  }
}

@media (min-width: 1920px) {
  @for $i from 0 through 24 {
    .sp-col-xl-#{$i} {
      max-width: (math.div(1, 24) * $i * 100) * 1%;
      flex: 0 0 (math.div(1, 24) * $i * 100) * 1%;
    }

    .sp-col-xl-offset-#{$i} {
      margin-left: (math.div(1, 24) * $i * 100) * 1%;
    }

    .sp-col-xl-pull-#{$i} {
      position: relative;
      right: (math.div(1, 24) * $i * 100) * 1%;
    }

    .sp-col-xl-push-#{$i} {
      position: relative;
      left: (math.div(1, 24) * $i * 100) * 1%;
    }
  }

  .sp-col-xl-0 {
    display: none;
  }
}
</style> 