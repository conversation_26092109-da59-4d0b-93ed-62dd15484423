/**
 * Btn 组件相关类型定义
 */

// 按钮变体类型
export type BtnVariant = 'default' | 'outlined' | 'text'

// 按钮尺寸类型
export type BtnSize = 'small' | 'medium' | 'large'

// 按钮属性接口（继承样式层属性）
export interface BtnProps extends BtnStyleProps {}

// 按钮事件接口
export interface BtnEmits {
  /**
   * 点击事件
   */
  click: [event?: MouseEvent]
  /**
   * 鼠标进入事件
   */
  mouseenter: []
  /**
   * 鼠标离开事件
   */
  mouseleave: []
  /**
   * 鼠标按下事件
   */
  mousedown: []
  /**
   * 鼠标释放事件
   */
  mouseup: []
}

// 按钮基础属性接口（逻辑层）
export interface BtnBaseProps {
  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean
}

// 按钮样式属性接口（样式层）
export interface BtnStyleProps {
  /**
   * 按钮变体
   * @default 'default'
   */
  variant?: BtnVariant

  /**
   * 按钮尺寸
   * @default 'medium'
   */
  size?: BtnSize

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean

  /**
   * 是否悬停状态
   */
  isHovered?: boolean

  /**
   * 是否按下状态
   */
  isPressed?: boolean
}

// 按钮实例类型
export interface BtnInstance {
  // 暂时没有暴露的方法，预留接口
}
