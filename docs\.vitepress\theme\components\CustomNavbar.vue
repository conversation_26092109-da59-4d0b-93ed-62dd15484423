<template>
  <nav class="custom-navbar" :class="{ 'dark-theme': isDark }">
    <div class="navbar-container">
      <!-- Logo 和品牌名 -->
      <div class="navbar-brand">
        <router-link to="/" class="brand-link">
          <div class="brand-logo">
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect width="32" height="32" rx="8" fill="#409EFF"/>
              <path d="M8 12h16v2H8v-2zm0 4h12v2H8v-2zm0 4h8v2H8v-2z" fill="white"/>
            </svg>
          </div>
          <span class="brand-text">Speed UI</span>
        </router-link>
      </div>

      <!-- 主导航菜单 -->
      <div class="navbar-menu">
        <div class="menu-items">
          <router-link 
            v-for="item in mainNavItems" 
            :key="item.text"
            :to="item.link"
            class="menu-item"
            :class="{ active: isActiveRoute(item.link) }"
          >
            {{ item.text }}
          </router-link>
          
          <!-- 下拉菜单 -->
          <div 
            v-for="dropdown in dropdownItems" 
            :key="dropdown.text"
            class="dropdown-menu"
            @mouseenter="showDropdown = dropdown.text"
            @mouseleave="showDropdown = null"
          >
            <button class="dropdown-trigger">
              {{ dropdown.text }}
              <svg class="dropdown-icon" width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                <path d="M6 8L2 4h8L6 8z"/>
              </svg>
            </button>
            
            <div 
              class="dropdown-panel"
              :class="{ show: showDropdown === dropdown.text }"
            >
              <router-link 
                v-for="subItem in dropdown.items" 
                :key="subItem.text"
                :to="subItem.link"
                class="dropdown-item"
                @click="showDropdown = null"
              >
                {{ subItem.text }}
              </router-link>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧操作区 -->
      <div class="navbar-actions">
        <!-- GitHub 链接 -->
        <a 
          href="https://github.com/your-username/speed-ui" 
          target="_blank"
          class="action-link"
          title="GitHub"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 0C5.37 0 0 5.37 0 12c0 5.31 3.435 9.795 8.205 11.385.6.105.825-.255.825-.57 0-.285-.015-1.23-.015-2.235-3.015.555-3.795-.735-4.035-1.41-.135-.345-.72-1.41-1.23-1.695-.42-.225-1.02-.78-.015-.795.945-.015 1.62.87 1.845 1.23 1.08 1.815 2.805 1.305 3.495.99.105-.78.42-1.305.765-1.605-2.67-.3-5.46-1.335-5.46-5.925 0-1.305.465-2.385 1.23-3.225-.12-.3-.54-1.53.12-3.18 0 0 1.005-.315 3.3 1.23.96-.27 1.98-.405 3-.405s2.04.135 3 .405c2.295-1.56 3.3-1.23 3.3-1.23.66 1.65.24 2.88.12 3.18.765.84 1.23 1.905 1.23 3.225 0 4.605-2.805 5.625-5.475 5.925.435.375.81 1.095.81 2.22 0 1.605-.015 2.895-.015 3.3 0 .315.225.69.825.57A12.02 12.02 0 0024 12c0-6.63-5.37-12-12-12z"/>
          </svg>
        </a>
        
        <!-- 主题切换 -->
        <button class="action-button theme-toggle" @click="toggleTheme" title="切换主题">
          <svg v-if="isDark" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 18c-3.3 0-6-2.7-6-6s2.7-6 6-6 6 2.7 6 6-2.7 6-6 6zm0-10c-2.2 0-4 1.8-4 4s1.8 4 4 4 4-1.8 4-4-1.8-4-4-4z"/>
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
          </svg>
          <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9c0-.46-.04-.92-.1-1.36-.98 1.37-2.58 2.26-4.4 2.26-2.98 0-5.4-2.42-5.4-5.4 0-1.81.89-3.42 2.26-4.4-.44-.06-.9-.1-1.36-.1z"/>
          </svg>
        </button>
        
        <!-- 移动端菜单按钮 -->
        <button 
          class="action-button mobile-menu-toggle"
          @click="showMobileMenu = !showMobileMenu"
          title="菜单"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <div class="mobile-menu" :class="{ show: showMobileMenu }">
      <div class="mobile-menu-content">
        <router-link 
          v-for="item in allNavItems" 
          :key="item.text"
          :to="item.link"
          class="mobile-menu-item"
          @click="showMobileMenu = false"
        >
          {{ item.text }}
        </router-link>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
// import { useRoute } from 'vue-router'

// const route = useRoute()
const showDropdown = ref<string | null>(null)
const showMobileMenu = ref(false)
const isDark = ref(false)

// 主导航项
const mainNavItems = [
  { text: '指南', link: '/guide/getting-started' },
  { text: '组件', link: '/components/button' }
]

// 下拉菜单项
const dropdownItems = [
  {
    text: '资源',
    items: [
      { text: '设计原则', link: '/design' },
      { text: '更新日志', link: '/changelog' },
      { text: '常见问题', link: '/faq' }
    ]
  },
  {
    text: '链接',
    items: [
      { text: 'GitHub', link: 'https://github.com/your-username/speed-ui' },
      { text: 'NPM', link: 'https://www.npmjs.com/package/@speed-ui/ui' }
    ]
  }
]

// 所有导航项（用于移动端）
const allNavItems = computed(() => {
  const items = [...mainNavItems]
  dropdownItems.forEach(dropdown => {
    items.push(...dropdown.items)
  })
  return items
})

// 判断是否为活跃路由
const isActiveRoute = (link: string) => {
  // return route.path.startsWith(link)
}

// 主题切换
const toggleTheme = () => {
  isDark.value = !isDark.value
  document.documentElement.classList.toggle('dark', isDark.value)
  localStorage.setItem('theme', isDark.value ? 'dark' : 'light')
}

// 初始化主题
onMounted(() => {
  const savedTheme = localStorage.getItem('theme')
  isDark.value = savedTheme === 'dark' || (!savedTheme && window.matchMedia('(prefers-color-scheme: dark)').matches)
  document.documentElement.classList.toggle('dark', isDark.value)
})
</script>

<style scoped>
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: saturate(50%) blur(8px);
  -webkit-backdrop-filter: saturate(50%) blur(8px);
  border-bottom: 1px solid #e4e7ed;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 品牌区域 */
.navbar-brand {
  flex-shrink: 0;
}

.brand-link {
  display: flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  color: #303133;
  font-weight: 600;
  font-size: 18px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.brand-link:hover {
  color: #409eff;
  transform: translateY(-1px);
}

.brand-logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-text {
  letter-spacing: -0.02em;
}

/* 导航菜单 */
.navbar-menu {
  flex: 1;
  display: flex;
  justify-content: center;
}

.menu-items {
  display: flex;
  align-items: center;
  gap: 0;
}

.menu-item {
  color: #606266;
  font-weight: 500;
  font-size: 14px;
  padding: 0 16px;
  height: 60px;
  line-height: 60px;
  text-decoration: none;
  transition: all 0.3s;
  position: relative;
  border-bottom: 2px solid transparent;
}

.menu-item:hover,
.menu-item.active {
  color: #409eff;
}

.menu-item.active {
  border-bottom-color: #409eff;
}

/* 下拉菜单 */
.dropdown-menu {
  position: relative;
}

.dropdown-trigger {
  color: #606266;
  font-weight: 500;
  font-size: 14px;
  padding: 0 16px;
  height: 60px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s;
  border-bottom: 2px solid transparent;
}

.dropdown-trigger:hover {
  color: #409eff;
}

.dropdown-icon {
  transition: transform 0.3s;
}

.dropdown-menu:hover .dropdown-icon {
  transform: rotate(180deg);
}

.dropdown-panel {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.08);
  padding: 8px 0;
  margin-top: 8px;
  min-width: 160px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-panel.show {
  opacity: 1;
  visibility: visible;
}

.dropdown-item {
  display: block;
  padding: 8px 20px;
  color: #606266;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s;
}

.dropdown-item:hover {
  background: #f5f7fa;
  color: #409eff;
}

/* 右侧操作区 */
.navbar-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-link,
.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 6px;
  color: #606266;
  text-decoration: none;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s;
}

.action-link:hover,
.action-button:hover {
  background: #f5f7fa;
  color: #409eff;
}

.mobile-menu-toggle {
  display: none;
}

/* 移动端菜单 */
.mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #ffffff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.08);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.mobile-menu-content {
  padding: 16px 24px;
}

.mobile-menu-item {
  display: block;
  padding: 12px 0;
  color: #606266;
  text-decoration: none;
  font-size: 16px;
  border-bottom: 1px solid #f5f7fa;
  transition: all 0.3s;
}

.mobile-menu-item:hover {
  color: #409eff;
}

.mobile-menu-item:last-child {
  border-bottom: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .navbar-container {
    padding: 0 16px;
  }
  
  .navbar-menu {
    display: none;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }
}

/* 暗色主题 */
.custom-navbar.dark-theme {
  background: rgba(26, 26, 26, 0.95);
  border-bottom-color: #3c3c3c;
}

.custom-navbar.dark-theme .brand-link {
  color: #e5e7eb;
}

.custom-navbar.dark-theme .brand-link:hover {
  color: #409eff;
}

.custom-navbar.dark-theme .menu-item,
.custom-navbar.dark-theme .dropdown-trigger {
  color: #9ca3af;
}

.custom-navbar.dark-theme .menu-item:hover,
.custom-navbar.dark-theme .menu-item.active,
.custom-navbar.dark-theme .dropdown-trigger:hover {
  color: #409eff;
}

.custom-navbar.dark-theme .dropdown-panel {
  background: #1f1f1f;
  border-color: #3c3c3c;
}

.custom-navbar.dark-theme .dropdown-item {
  color: #9ca3af;
}

.custom-navbar.dark-theme .dropdown-item:hover {
  background: #2d2d2d;
  color: #409eff;
}

.custom-navbar.dark-theme .action-link,
.custom-navbar.dark-theme .action-button {
  color: #9ca3af;
}

.custom-navbar.dark-theme .action-link:hover,
.custom-navbar.dark-theme .action-button:hover {
  background: #2d2d2d;
  color: #409eff;
}

.custom-navbar.dark-theme .mobile-menu {
  background: #1f1f1f;
  border-bottom-color: #3c3c3c;
}

.custom-navbar.dark-theme .mobile-menu-item {
  color: #9ca3af;
  border-bottom-color: #2d2d2d;
}

.custom-navbar.dark-theme .mobile-menu-item:hover {
  color: #409eff;
}

/* 保持全局暗色主题兼容性 */
:global(.dark) .custom-navbar {
  background: rgba(26, 26, 26, 0.95);
  border-bottom-color: #3c3c3c;
}

:global(.dark) .custom-navbar .brand-link {
  color: #e5e7eb;
}

:global(.dark) .custom-navbar .brand-link:hover {
  color: #409eff;
}

:global(.dark) .custom-navbar .menu-item,
:global(.dark) .custom-navbar .dropdown-trigger {
  color: #9ca3af;
}

:global(.dark) .custom-navbar .menu-item:hover,
:global(.dark) .custom-navbar .menu-item.active,
:global(.dark) .custom-navbar .dropdown-trigger:hover {
  color: #409eff;
}

:global(.dark) .custom-navbar .dropdown-panel {
  background: #1f1f1f;
  border-color: #3c3c3c;
}

:global(.dark) .custom-navbar .dropdown-item {
  color: #9ca3af;
}

:global(.dark) .custom-navbar .dropdown-item:hover {
  background: #2d2d2d;
  color: #409eff;
}

:global(.dark) .custom-navbar .action-link,
:global(.dark) .custom-navbar .action-button {
  color: #9ca3af;
}

:global(.dark) .custom-navbar .action-link:hover,
:global(.dark) .custom-navbar .action-button:hover {
  background: #2d2d2d;
  color: #409eff;
}

:global(.dark) .custom-navbar .mobile-menu {
  background: #1f1f1f;
  border-bottom-color: #3c3c3c;
}

:global(.dark) .custom-navbar .mobile-menu-item {
  color: #9ca3af;
  border-bottom-color: #2d2d2d;
}

:global(.dark) .custom-navbar .mobile-menu-item:hover {
  color: #409eff;
}
</style>