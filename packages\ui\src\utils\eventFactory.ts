import type { Ref } from 'vue'

/**
 * 事件处理工厂函数的配置选项
 */
export interface EventHandlerOptions {
  /** 是否阻止事件冒泡 */
  stopPropagation?: boolean
  /** 是否阻止默认行为 */
  preventDefault?: boolean
  /** 是否在禁用状态下阻止事件 */
  checkDisabled?: boolean
  /** 是否在只读状态下阻止事件 */
  checkReadonly?: boolean
  /** 事件处理前的自定义逻辑 */
  before?: (event: Event) => void | boolean
  /** 事件处理后的自定义逻辑 */
  after?: (event: Event) => void
  /** 防抖延迟（毫秒） */
  debounce?: number
  /** 节流延迟（毫秒） */
  throttle?: number
}

/**
 * 防抖函数
 */
function debounce<T extends (...args: any[]) => void>(
  func: T,
  delay: number
): T {
  let timeoutId: ReturnType<typeof setTimeout>
  return ((...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }) as T
}

/**
 * 节流函数
 */
function throttle<T extends (...args: any[]) => void>(
  func: T,
  delay: number
): T {
  let lastCall = 0
  return ((...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      func(...args)
    }
  }) as T
}

/**
 * 创建标准事件处理器
 * @param emit - Vue 的 emit 函数
 * @param eventName - 要触发的事件名称
 * @param options - 事件处理选项
 */
export function createEventHandler<T extends Event = Event>(
  emit: (...args: any[]) => void,
  eventName: string,
  options: EventHandlerOptions = {}
) {
  const {
    stopPropagation = false,
    preventDefault = false,
    before,
    after,
    debounce: debounceDelay,
    throttle: throttleDelay,
  } = options

  // 核心处理函数
  const coreHandler = (event: T) => {
    // 执行前置逻辑
    if (before) {
      const result = before(event)
      // 如果前置逻辑返回 false，则停止执行
      if (result === false) return
    }

    // 阻止事件冒泡
    if (stopPropagation) {
      event.stopPropagation()
    }

    // 阻止默认行为
    if (preventDefault) {
      event.preventDefault()
    }

    // 触发事件
    emit(eventName, event)

    // 执行后置逻辑
    if (after) {
      after(event)
    }
  }

  // 应用防抖或节流
  if (debounceDelay) {
    return debounce(coreHandler, debounceDelay)
  } else if (throttleDelay) {
    return throttle(coreHandler, throttleDelay)
  } else {
    return coreHandler
  }
}

/**
 * 创建带状态检查的事件处理器
 * @param emit - Vue 的 emit 函数
 * @param eventName - 要触发的事件名称
 * @param props - 组件 props（用于状态检查）
 * @param options - 事件处理选项
 */
export function createStatefulEventHandler<T extends Event = Event>(
  emit: (...args: any[]) => void,
  eventName: string,
  props: { disabled?: boolean; readonly?: boolean },
  options: EventHandlerOptions = {}
) {
  return createEventHandler<T>(emit, eventName, {
    ...options,
    before: event => {
      // 检查禁用状态
      if (options.checkDisabled !== false && props.disabled) {
        return false
      }

      // 检查只读状态
      if (options.checkReadonly !== false && props.readonly) {
        return false
      }

      // 执行用户自定义的前置逻辑
      if (options.before) {
        return options.before(event)
      }

      return true
    },
  })
}

/**
 * 创建焦点相关的事件处理器
 * @param emit - Vue 的 emit 函数
 * @param inputRef - 输入框引用
 * @param props - 组件 props
 */
export function createFocusHandlers(
  emit: (...args: any[]) => void,
  inputRef: Ref<HTMLInputElement | undefined>,
  props: { disabled?: boolean; readonly?: boolean }
) {
  return {
    handleWrapperClick: createStatefulEventHandler(emit, 'click', props, {
      after: () => {
        // 点击包装器时聚焦输入框
        inputRef.value?.focus()
      },
    }),

    handleFocus: createEventHandler<FocusEvent>(emit, 'focus'),

    handleBlur: createEventHandler<FocusEvent>(emit, 'blur'),
  }
}

/**
 * 创建输入相关的事件处理器
 * @param emit - Vue 的 emit 函数
 */
export function createInputHandlers(emit: (...args: any[]) => void) {
  return {
    handleInput: createEventHandler<Event>(emit, 'input'),
    handleChange: createEventHandler<Event>(emit, 'change'),
    handleKeydown: createEventHandler<KeyboardEvent>(emit, 'keydown'),
  }
}

/**
 * 创建交互相关的事件处理器
 * @param emit - Vue 的 emit 函数
 * @param inputRef - 输入框引用（用于清除后聚焦）
 */
export function createInteractionHandlers(
  emit: (...args: any[]) => void,
  inputRef?: Ref<HTMLInputElement | undefined>
) {
  return {
    handlePrefixClick: createEventHandler<MouseEvent>(emit, 'prefix-click', {
      stopPropagation: true,
    }),

    handleSuffixClick: createEventHandler<MouseEvent>(emit, 'suffix-click', {
      stopPropagation: true,
    }),

    handleClear: createEventHandler<MouseEvent>(emit, 'clear', {
      stopPropagation: true,
      after: () => {
        // 清除后聚焦输入框
        inputRef?.value?.focus()
      },
    }),
  }
}

/**
 * 创建完整的输入框事件处理器集合
 * @param emit - Vue 的 emit 函数
 * @param inputRef - 输入框引用
 * @param props - 组件 props
 */
export function createInputEventHandlers(
  emit: (...args: any[]) => void,
  inputRef: Ref<HTMLInputElement | undefined>,
  props: { disabled?: boolean; readonly?: boolean }
) {
  return {
    ...createFocusHandlers(emit, inputRef, props),
    ...createInputHandlers(emit),
    ...createInteractionHandlers(emit, inputRef),
  }
}

/**
 * 创建搜索输入框的事件处理器（带防抖）
 * @param emit - Vue 的 emit 函数
 * @param inputRef - 输入框引用
 * @param props - 组件 props
 * @param debounceDelay - 防抖延迟，默认 300ms
 */
export function createSearchEventHandlers(
  emit: (...args: any[]) => void,
  inputRef: Ref<HTMLInputElement | undefined>,
  props: { disabled?: boolean; readonly?: boolean },
  debounceDelay = 300
) {
  return {
    ...createFocusHandlers(emit, inputRef, props),
    ...createInteractionHandlers(emit, inputRef),

    // 搜索输入使用防抖
    handleInput: createEventHandler<Event>(emit, 'input', {
      debounce: debounceDelay,
    }),

    // 普通的 change 事件
    handleChange: createEventHandler<Event>(emit, 'change'),
    handleKeydown: createEventHandler<KeyboardEvent>(emit, 'keydown'),
  }
}

/**
 * 创建表单输入框的事件处理器（带验证）
 * @param emit - Vue 的 emit 函数
 * @param inputRef - 输入框引用
 * @param props - 组件 props
 * @param validator - 验证函数
 */
export function createFormEventHandlers(
  emit: (...args: any[]) => void,
  inputRef: Ref<HTMLInputElement | undefined>,
  props: { disabled?: boolean; readonly?: boolean },
  validator?: (value: string) => boolean | string
) {
  return {
    ...createFocusHandlers(emit, inputRef, props),
    ...createInteractionHandlers(emit, inputRef),

    handleInput: createEventHandler<Event>(emit, 'input', {
      after: event => {
        if (validator) {
          const target = event.target as HTMLInputElement
          const result = validator(target.value)
          if (typeof result === 'string') {
            emit('validation-error', result)
          } else if (result === false) {
            emit('validation-error', 'Invalid input')
          } else {
            emit('validation-success')
          }
        }
      },
    }),

    handleChange: createEventHandler<Event>(emit, 'change'),
    handleKeydown: createEventHandler<KeyboardEvent>(emit, 'keydown'),
  }
}

/**
 * 创建数字输入框的事件处理器
 * @param emit - Vue 的 emit 函数
 * @param inputRef - 输入框引用
 * @param props - 组件 props
 */
export function createNumberEventHandlers(
  emit: (...args: any[]) => void,
  inputRef: Ref<HTMLInputElement | undefined>,
  props: { disabled?: boolean; readonly?: boolean }
) {
  return {
    ...createFocusHandlers(emit, inputRef, props),
    ...createInteractionHandlers(emit, inputRef),

    handleInput: createEventHandler<Event>(emit, 'input'),
    handleChange: createEventHandler<Event>(emit, 'change'),

    // 数字输入框特殊的键盘事件处理
    handleKeydown: createEventHandler<KeyboardEvent>(emit, 'keydown', {
      before: event => {
        const key = event.key
        const target = event.target as HTMLInputElement

        // 允许的键：数字、退格、删除、箭头键、Tab、Enter、小数点、负号
        const allowedKeys = [
          'Backspace',
          'Delete',
          'ArrowLeft',
          'ArrowRight',
          'ArrowUp',
          'ArrowDown',
          'Tab',
          'Enter',
          '.',
          '-',
        ]

        const isNumber = /^[0-9]$/.test(key)
        const isAllowedKey = allowedKeys.includes(key)
        const isCtrlCmd = event.ctrlKey || event.metaKey

        // 如果不是数字、不是允许的键、且不是 Ctrl/Cmd 组合键，则阻止输入
        if (!isNumber && !isAllowedKey && !isCtrlCmd) {
          event.preventDefault()
          return false
        }

        // 防止多个小数点
        if (key === '.' && target.value.includes('.')) {
          event.preventDefault()
          return false
        }

        // 防止多个负号或负号不在开头
        if (
          key === '-' &&
          (target.value.includes('-') || target.selectionStart !== 0)
        ) {
          event.preventDefault()
          return false
        }

        return true
      },
    }),
  }
}
