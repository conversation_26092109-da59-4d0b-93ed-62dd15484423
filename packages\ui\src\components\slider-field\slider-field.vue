<!--
  SliderField.vue - 带浮动标签的滑块组件
  使用 FieldContainer 作为基础容器，集成 Slider 组件
-->

<template>
  <FieldContainer
    ref="fieldContainerRef"
    v-bind="containerProps"
    @update:value="updateValue"
    @input="event => emit('input', event)"
    @change="() => emit('change', currentValue || 0)"
    @focus="event => emit('focus', event)"
    @blur="event => emit('blur', event)"
    @wrapper-click="handleWrapperClick"
    @label-click="handleLabelClick"
    @click:prepend="event => emit('click:prepend', event)"
    @click:prepend-inner="event => emit('click:prepend-inner', event)"
    @click:append="event => emit('click:append', event)"
    @click:append-inner="event => emit('click:append-inner', event)"
  >
    <!-- 前置区域 -->
    <template
      #prepend="slotProps"
      v-if="props.prependIconInner || slots.prepend"
    >
      <div :class="slotProps.prependClasses">
        <!-- 内部前置图标 -->
        <sp-icon
          v-if="props.prependIconInner"
          :name="props.prependIconInner"
          :size="slotProps.iconSize"
          clickable
          @click.stop.prevent="event => emit('click:prepend-inner', event)"
          @mousedown.stop.prevent
        />
        <!-- 前置插槽 -->
        <slot name="prepend" />
      </div>
    </template>

    <!-- 前缀区域 -->
    <template
      #prefix="slotProps"
      v-if="props.prefix || slots.prefix"
    >
      <slot
        name="prefix"
        v-bind="slotProps"
      >
        <!-- 默认显示前缀内容 -->
        <span v-if="props.prefix">
          {{ props.prefix }}
        </span>
      </slot>
    </template>

    <!-- 滑块元素 -->
    <template #default="slotProps">
      <div
        :class="['sp-slider-field__slider-wrapper', slotProps.inputClasses]"
        :style="slotProps.inputStyle"
      >
        <!-- 滑块组件 -->
        <sp-slider
          ref="sliderRef"
          :model-value="currentValue"
          :min="min"
          :max="max"
          :step="step"
          :disabled="slotProps.disabled"
          :variant="sliderVariant"
          :size="size"
          :marks="marks"
          @update:model-value="handleSliderChange"
          @change="handleSliderChange"
        />

        <!-- 数值显示 -->
        <div
          v-if="showValue"
          class="sp-slider-field__value-display"
        >
          {{ formatValue(currentValue || 0) }}
        </div>
      </div>
    </template>

    <!-- 后缀区域 -->
    <template
      #suffix="slotProps"
      v-if="props.suffix || slots.suffix"
    >
      <slot
        name="suffix"
        v-bind="slotProps"
      >
        <!-- 默认显示后缀内容 -->
        <span v-if="props.suffix">
          {{ props.suffix }}
        </span>
      </slot>
    </template>

    <!-- 后置区域 -->
    <template
      #append="slotProps"
      v-if="props.appendIconInner || slots.append"
    >
      <div :class="slotProps.appendClasses">
        <!-- 内部后置图标 -->
        <sp-icon
          v-if="props.appendIconInner"
          :name="props.appendIconInner"
          :size="slotProps.iconSize"
          clickable
          @click.stop.prevent="event => emit('click:append-inner', event)"
          @mousedown.stop.prevent
        />
        <!-- 后置插槽 -->
        <slot name="append" />
      </div>
    </template>
  </FieldContainer>
</template>

<script setup lang="ts">
  import { computed, ref, useSlots } from 'vue'
  import { useVModel } from '@speed-ui/hooks'
  import FieldContainer from '../field-container/FieldContainer.vue'
  import SpSlider from '../slider/slider.vue'
  import SpIcon from '../icon/Icon.vue'
  import type { SliderFieldProps, SliderFieldEmits } from './types'
  import { sliderFieldPropsDefaults } from './types'

  /**
   * SliderField 组件 - 带浮动标签的滑块
   *
   * 集成了滑块功能和浮动标签、表单验证等功能
   * 直接使用 FieldContainer 作为基础容器
   */

  // ===== Props 和 Emits =====
  const props = withDefaults(
    defineProps<SliderFieldProps>(),
    sliderFieldPropsDefaults
  )
  const emit = defineEmits<SliderFieldEmits>()

  // ===== 插槽 =====
  const slots = useSlots()

  // ===== 响应式数据 =====
  const fieldContainerRef = ref<InstanceType<typeof FieldContainer>>()
  const sliderRef = ref<InstanceType<typeof SpSlider>>()

  // ===== 双向绑定 =====
  // 使用 useVModel 进行双向绑定，参考 input-field 的实现
  const { value: currentValue, updateValue } = useVModel<number | undefined>(
    props,
    emit,
    {
      prop: 'value',
      event: 'update:value',
      defaultValue: props.value || props.min || 0,
      onAfterUpdate: (newValue: number | undefined) => {
        emit('change', newValue || 0)
      },
      debug: false,
    }
  )

  // ===== 计算属性 =====
  const containerProps = computed(() => {
    const {
      // 排除 slider 特有的属性
      value: _value,
      min: _min,
      max: _max,
      step: _step,
      variant: _variant,
      marks: _marks,
      showValue: _showValue,
      valueFormatter: _valueFormatter,
      // 其余属性传递给 FieldContainer
      ...containerProps
    } = props

    return {
      ...containerProps,
      value: String(currentValue.value || 0), // FieldContainer 需要字符串值
    }
  })

  // 滑块变体（区分于 FieldContainer 的变体）
  const sliderVariant = computed(() => {
    // 如果有明确的滑块变体设置，使用它，否则使用默认值
    return props.variant === 'filled' ? 'filled' : 'default'
  })

  // ===== 方法 =====
  const handleSliderChange = (value: number) => {
    updateValue(value)
  }

  const handleWrapperClick = () => {
    // 聚焦到滑块
    sliderRef.value?.inputRef?.focus()
  }

  const handleLabelClick = () => {
    // 聚焦到滑块
    sliderRef.value?.inputRef?.focus()
  }

  const formatValue = (value: number): string => {
    if (props.valueFormatter) {
      return props.valueFormatter(value)
    }
    return String(value)
  }

  // ===== 暴露方法 =====
  const focus = () => {
    sliderRef.value?.inputRef?.focus()
  }

  const blur = () => {
    sliderRef.value?.inputRef?.blur()
  }

  const validate = async (): Promise<boolean> => {
    return fieldContainerRef.value?.validate() || false
  }

  const resetField = () => {
    fieldContainerRef.value?.resetField()
    currentValue.value = props.min || 0
  }

  const clearValidate = () => {
    fieldContainerRef.value?.clearValidate()
  }

  defineExpose({
    focus,
    blur,
    validate,
    resetField,
    clearValidate,
    get slider() {
      return sliderRef.value || null
    },
    get fieldContainer() {
      return fieldContainerRef.value || null
    },
  })
</script>

<script lang="ts">
  export default {
    name: 'SliderField',
  }
</script>

<style scoped>
  .sp-slider-field__slider-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
    min-height: 20px;
  }

  .sp-slider-field__value-display {
    flex-shrink: 0;
    min-width: 40px;
    text-align: center;
    font-size: 14px;
    color: var(--sp-color-text-primary);
    font-weight: 500;
  }

  /* 去除 FieldContainer 的边框样式 */
  :deep(.sp-field-container__field-wrapper) {
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
  }

  :deep(.sp-field-container__wrapper) {
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
  }
</style>
