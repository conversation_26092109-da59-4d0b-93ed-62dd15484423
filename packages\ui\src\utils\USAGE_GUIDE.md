# Speed UI 工具函数使用指南

本指南展示了如何在实际组件开发中使用 Speed UI 工具函数库，通过重构现有的 Input 组件来演示最佳实践。

## 🔄 重构前后对比

### 重构前的 Input 组件

```typescript
// 原始实现 - 大量重复代码
export function useInput(props: any, emit: any) {
  const formItemField = inject<any>('spFormItemField', null)
  const isFocused = ref(false)
  
  // 手动处理表单集成
  const inputValue = computed({
    get: () => {
      if (formItemField) {
        return formItemField.value.value ?? ''
      }
      return props.value ?? ''
    },
    set: (value) => {
      if (formItemField) {
        formItemField.setValue(value)
      }
      emit('update:value', value)
    },
  })
  
  // 手动处理验证状态
  const computedValidateState = computed(() => {
    if (formItemField) {
      // 复杂的验证逻辑...
    }
    return props.validateState
  })
  
  // 手动事件处理
  const handleFocus = (event: FocusEvent) => {
    isFocused.value = true
    emit('focus', event)
  }
  
  const handleBlur = (event: FocusEvent) => {
    isFocused.value = false
    if (formItemField) {
      formItemField.handleBlur()
    }
    emit('blur', event)
  }
  
  // 手动类名生成
  const classes = computed(() => [
    'sp-input',
    `sp-input--${props.size}`,
    {
      'sp-input--disabled': props.disabled,
      'sp-input--focused': isFocused.value,
      // 更多条件类名...
    }
  ])
}
```

### 重构后的 Input 组件

```typescript
// 使用工具函数 - 简洁且一致
export function useInput(props: any, emit: any) {
  // 🎯 使用表单集成工具函数
  const { computedValidateState, computedValidateMessage, handleValueUpdate } = useFormField(props, emit)
  
  // 🎯 使用组件状态管理工具函数
  const { isFocused, setFocused } = useComponentState()
  
  // 🎯 创建事件处理器工厂
  const eventFactory = new EventHandlerFactory({
    emit,
    setState: (key: string, value: any) => {
      if (key === 'focused') setFocused(value)
    },
    updateValue: handleValueUpdate
  })

  // 🎯 使用标准化事件处理器
  const handlers = eventFactory.createHandlers({
    disabled: props.disabled,
    loading: false
  })

  // 🎯 使用工具函数生成组件类名
  const componentClasses = computed(() => 
    generateComponentClasses('sp-input', props, {
      isFocused: isFocused.value,
      readonly: props.readonly,
      // 更多状态...
    })
  )
  
  // 简化的事件处理
  const handleFocus = (event: FocusEvent) => {
    setFocused(true)
    handlers.handleFocus(event)
  }
}
```

## 📊 重构收益

### 代码量减少
- **原始代码**: ~350 行
- **重构后**: ~250 行
- **减少**: 约 30% 的代码量

### 功能提升
- ✅ **标准化**: 所有组件使用相同的事件处理模式
- ✅ **类型安全**: 完整的 TypeScript 支持
- ✅ **可维护性**: 逻辑集中在工具函数中
- ✅ **可测试性**: 工具函数可以独立测试
- ✅ **一致性**: 统一的 API 和行为

## 🛠️ 实际应用步骤

### 1. 更新组件接口

```typescript
// 让组件属性继承基础接口
interface InputProps extends BaseComponentProps {
  // 只定义组件特有的属性
  value?: string | number
  type?: string
  placeholder?: string
  // 移除 disabled, size, validateState 等通用属性
}
```

### 2. 使用表单集成

```typescript
// 替换手动表单处理
const { computedValidateState, computedValidateMessage, handleValueUpdate } = useFormField(props, emit)

// 简化值处理
const inputValue = computed({
  get: () => props.value ?? '',
  set: (value) => handleValueUpdate(value)
})
```

### 3. 使用状态管理

```typescript
// 替换手动状态管理
const { isFocused, isLoading, setFocused, setLoading } = useComponentState()
```

### 4. 使用事件处理器工厂

```typescript
// 创建标准化事件处理器
const eventFactory = new EventHandlerFactory({
  emit,
  setState: (key, value) => {
    if (key === 'focused') setFocused(value)
  },
  updateValue: handleValueUpdate
})

const handlers = eventFactory.createHandlers({
  disabled: props.disabled,
  loading: props.loading
})
```

### 5. 使用类名生成工具

```typescript
// 替换手动类名拼接
const componentClasses = computed(() => 
  generateComponentClasses('sp-input', props, {
    isFocused: isFocused.value,
    // 其他状态
  })
)
```

### 6. 更新模板

```vue
<template>
  <!-- 使用生成的类名 -->
  <div :class="componentClasses">
    <input 
      @focus="handlers.handleFocus"
      @blur="handlers.handleBlur"
      @input="handlers.handleInput"
    />
  </div>
</template>
```

## 🎯 最佳实践

### 1. 渐进式重构

```typescript
// ❌ 不要一次性重构所有组件
// ✅ 从最简单的组件开始，逐步应用工具函数

// 第一步：只使用 generateComponentClasses
const classes = generateComponentClasses('sp-button', props, state)

// 第二步：添加事件处理
const handlers = eventFactory.createHandlers(options)

// 第三步：集成表单功能
const formIntegration = useFormField(props, emit)
```

### 2. 保持向后兼容

```typescript
// 在重构过程中保持原有 API
export function useInput(props: any, emit: any) {
  // 新的工具函数实现
  const newImplementation = useNewTools(props, emit)
  
  // 保持原有的返回接口
  return {
    ...newImplementation,
    // 保留原有的属性名
    isFocused: newImplementation.isFocused,
  }
}
```

### 3. 类型安全

```typescript
// 确保工具函数的类型定义完整
interface ComponentState {
  isFocused: boolean
  isLoading: boolean
  isDisabled: boolean
}

// 使用泛型提供更好的类型推断
function useComponentState<T extends ComponentState>(): T {
  // 实现
}
```

## 🔍 调试和测试

### 1. 工具函数测试

```typescript
// 工具函数可以独立测试
import { generateComponentClasses } from '@speed-ui/ui/utils'

describe('generateComponentClasses', () => {
  it('should generate correct classes', () => {
    const classes = generateComponentClasses('sp-input', 
      { size: 'large', disabled: true },
      { isFocused: true }
    )
    
    expect(classes).toContain('sp-input')
    expect(classes).toContain('sp-input--large')
    expect(classes).toContain('sp-input--disabled')
    expect(classes).toContain('sp-input--focused')
  })
})
```

### 2. 组件集成测试

```typescript
// 测试组件是否正确使用工具函数
import { mount } from '@vue/test-utils'
import Input from './Input.vue'

describe('Input Component', () => {
  it('should apply correct classes', () => {
    const wrapper = mount(Input, {
      props: { size: 'large', disabled: true }
    })
    
    expect(wrapper.classes()).toContain('sp-input--large')
    expect(wrapper.classes()).toContain('sp-input--disabled')
  })
})
```

## 📈 性能优化

### 1. 计算属性缓存

```typescript
// 工具函数返回的计算属性会自动缓存
const componentClasses = computed(() => 
  generateComponentClasses('sp-input', props, state)
) // 只有依赖变化时才重新计算
```

### 2. 事件处理器复用

```typescript
// 事件处理器工厂创建的处理器可以复用
const handlers = eventFactory.createHandlers(options)
// handlers 对象在选项不变时保持引用稳定
```

## 🔮 未来扩展

### 1. 添加新的工具函数

```typescript
// 在 utils 目录下添加新功能
export function useAnimation() {
  // 动画相关工具函数
}

export function useAccessibility() {
  // 无障碍功能工具函数
}
```

### 2. 主题系统集成

```typescript
// 扩展样式工具函数支持主题
export function useTheme() {
  const theme = inject('theme')
  return {
    getColor: (name: string) => theme.colors[name],
    getSize: (name: string) => theme.sizes[name]
  }
}
```

通过这种方式，Speed UI 工具函数库不仅提高了开发效率，还确保了组件库的一致性和可维护性。