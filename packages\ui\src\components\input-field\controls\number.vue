<!--
  NumberControls.vue - 数字输入框的控制器组件
  包含上下箭头，用于数值的增减操作
-->

<template>
  <div
    class="sp-number-controls"
    @click.stop
    @mousedown.stop
  >
    <sp-icon
      name="ChevronUp"
      :size="iconSize"
      :clickable="canIncrement"
      :class="numberUpClasses"
      @click.stop.prevent="handleIncrementClick"
      @mousedown.stop.prevent
    />
    <sp-icon
      name="ChevronDown"
      :size="iconSize"
      :clickable="canDecrement"
      :class="numberDownClasses"
      @click.stop.prevent="handleDecrementClick"
      @mousedown.stop.prevent
    />
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import SpIcon from '../../icon/Icon.vue'

  interface NumberControlsProps {
    /** 图标大小 */
    iconSize: number
    /** 是否可以增加 */
    canIncrement: boolean
    /** 是否可以减少 */
    canDecrement: boolean
  }

  interface NumberControlsEmits {
    /** 增加数值 */
    (e: 'increment'): void
    /** 减少数值 */
    (e: 'decrement'): void
    /** 请求聚焦 */
    (e: 'focus'): void
  }

  const props = defineProps<NumberControlsProps>()
  const emit = defineEmits<NumberControlsEmits>()

  // ===== 样式类计算 =====
  const numberUpClasses = computed(() => ({
    'sp-number-controls__up': true,
    'sp-number-controls__disabled': !props.canIncrement,
  }))

  const numberDownClasses = computed(() => ({
    'sp-number-controls__down': true,
    'sp-number-controls__disabled': !props.canDecrement,
  }))

  // ===== 事件处理 =====
  const handleIncrementClick = () => {
    if (props.canIncrement) {
      emit('focus')
      emit('increment')
    }
  }

  const handleDecrementClick = () => {
    if (props.canDecrement) {
      emit('focus')
      emit('decrement')
    }
  }
</script>

<script lang="ts">
  export default {
    name: 'SpNumberControls',
  }
</script>

<style lang="scss">
  .sp-number-controls {
    display: flex;
    flex-direction: column;
    gap: 1px;
    padding: 0;
    align-items: center;
    justify-content: center;
    pointer-events: auto;

    &__up,
    &__down {
      color: var(--sp-color-text-secondary, #666);
      transition: all 0.3s ease;
      cursor: pointer;
      padding: 1px 2px;
      border-radius: 2px;
      font-size: 10px;
      line-height: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 16px;
      min-height: 12px;

      &:hover {
        color: var(--sp-color-primary, #409eff);
        background: rgba(64, 158, 255, 0.1);
      }

      &:active {
        background: rgba(64, 158, 255, 0.2);
      }
    }

    &__disabled {
      color: var(--sp-color-text-disabled, #c0c4cc) !important;
      cursor: not-allowed !important;

      &:hover,
      &:active {
        color: var(--sp-color-text-disabled, #c0c4cc) !important;
        background: transparent !important;
      }
    }
  }
</style>
