/**
 * 通用事件转发 composable
 * 自动从组件的 Emits 类型中推断并转发所有事件
 */

/**
 * 通用事件转发 hook
 * @param emit 组件的 emit 函数
 * @returns 事件处理器对象，包含所有需要转发的事件
 */
export function useEvent(emit: (event: any, ...args: any[]) => void) {
  // 创建一个 Proxy 来动态处理所有事件
  const eventHandlers = new Proxy(
    {} as Record<string, (...args: any[]) => void>,
    {
      get(target, prop: string) {
        // 如果属性不存在，动态创建事件处理器
        if (!(prop in target)) {
          target[prop] = (...args: any[]) => {
            emit(prop, ...args)
          }
        }
        return target[prop]
      },
    }
  )

  return {
    eventHandlers,
  }
}
