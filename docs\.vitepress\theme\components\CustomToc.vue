<template>
  <aside class="custom-toc" :class="{ 'dark-theme': isDark }">
    <div class="toc-container">
      <!-- 目录标题 -->
      <div class="toc-header">
        <h3 class="toc-title">On this page</h3>
      </div>

      <!-- 目录导航 -->
      <nav class="toc-nav" v-if="tocItems.length > 0">
        <ul class="toc-list">
          <li 
            v-for="item in tocItems" 
            :key="item.anchor" 
            class="toc-item"
            :class="`toc-level-${item.level}`"
          >
            <a 
              :href="item.anchor" 
              class="toc-link" 
              :class="{ active: activeAnchor === item.anchor }"
              @click="scrollToAnchor(item.anchor)"
            >
              {{ item.title }}
            </a>
          </li>
        </ul>
      </nav>

      <!-- 空状态 -->
      <div v-else class="toc-empty">
        <p class="empty-text">暂无目录</p>
      </div>
    </div>
  </aside>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute } from 'vitepress'

const route = useRoute()
const tocItems = ref([])
const activeAnchor = ref('')
const isDark = ref(false)

// 提取页面标题生成目录
const extractTocItems = () => {
  const headers = document.querySelectorAll('.VPDoc h1, .VPDoc h2, .VPDoc h3, .VPDoc h4')
  const items = []
  
  headers.forEach((header, index) => {
    const level = parseInt(header.tagName.charAt(1))
    const title = header.textContent?.trim() || ''
    let anchor = header.id
    
    // 如果没有 id，生成一个
    if (!anchor) {
      anchor = `heading-${index}`
      header.id = anchor
    }
    
    // 只显示 h2-h4 级别的标题
    if (level >= 2 && level <= 4 && title) {
      items.push({
        level,
        title,
        anchor: `#${anchor}`
      })
    }
  })
  
  tocItems.value = items
}

// 滚动到指定锚点
const scrollToAnchor = (anchor) => {
  const element = document.querySelector(anchor)
  if (element) {
    const rect = element.getBoundingClientRect()
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const targetTop = rect.top + scrollTop - 80 // 考虑导航栏高度和额外间距
    
    window.scrollTo({
      top: targetTop,
      behavior: 'smooth'
    })
  }
}

// 监听滚动，高亮当前章节
const updateActiveAnchor = () => {
  const scrollTop = window.scrollY
  const headers = document.querySelectorAll('.VPDoc h1, .VPDoc h2, .VPDoc h3, .VPDoc h4')
  
  let current = ''
  
  headers.forEach((header) => {
    const rect = header.getBoundingClientRect()
    const top = rect.top + scrollTop
    
    // 调整高亮判断的偏移量，考虑导航栏高度
    if (top <= scrollTop + 120) {
      current = `#${header.id}`
    }
  })
  
  activeAnchor.value = current
}

// 防抖函数
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

const debouncedUpdateActiveAnchor = debounce(updateActiveAnchor, 100)

onMounted(() => {
  nextTick(() => {
    extractTocItems()
    updateActiveAnchor()
  })
  
  window.addEventListener('scroll', debouncedUpdateActiveAnchor)
  
  // 初始化主题
  const savedTheme = localStorage.getItem('theme')
  isDark.value = savedTheme === 'dark' || (!savedTheme && window.matchMedia('(prefers-color-scheme: dark)').matches)
  
  // 监听主题变化
  const observer = new MutationObserver(() => {
    isDark.value = document.documentElement.classList.contains('dark')
  })
  
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  })
  
  // 监听路由变化，重新提取目录
  const unwatch = route.path
  nextTick(() => {
    extractTocItems()
    updateActiveAnchor()
  })
})

onUnmounted(() => {
  window.removeEventListener('scroll', debouncedUpdateActiveAnchor)
})

// 监听路由变化
const { path } = route
const unwatchRoute = () => {
  nextTick(() => {
    extractTocItems()
    updateActiveAnchor()
  })
}

// 使用 watch 监听路由变化
import { watch } from 'vue'
watch(() => route.path, () => {
  nextTick(() => {
    extractTocItems()
    updateActiveAnchor()
  })
})
</script>

<style scoped>
.custom-toc {
  position: fixed;
  top: 60px; /* 为顶部导航栏留出空间 */
  right: 0;
  width: 280px;
  height: calc(100vh - 60px);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: saturate(50%) blur(8px);
  -webkit-backdrop-filter: saturate(50%) blur(8px);
  border-left: 1px solid #e4e7ed;
  z-index: 999;
  overflow-y: auto;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toc-container {
  padding: 24px 0;
  height: 100%;
}

/* 目录标题 */
.toc-header {
  padding: 0 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 24px;
}

.toc-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  letter-spacing: -0.02em;
}

/* 目录导航 */
.toc-nav {
  padding: 0 16px;
}

.toc-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.toc-item {
  margin-bottom: 4px;
}

/* 不同级别的缩进 */
.toc-level-2 {
  padding-left: 0;
}

.toc-level-3 {
  padding-left: 16px;
}

.toc-level-4 {
  padding-left: 32px;
}

.toc-link {
  display: block;
  padding: 8px 16px;
  color: #606266;
  text-decoration: none;
  border-radius: 6px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 13px;
  line-height: 1.4;
  border-left: 2px solid transparent;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.toc-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #409eff, #67c23a);
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.toc-link:hover {
  color: #409eff;
  background: rgba(64, 158, 255, 0.08);
  border-left-color: #409eff;
  transform: translateX(2px);
}

.toc-link:hover::before {
  opacity: 0.03;
}

.toc-link.active {
  color: #409eff;
  background: rgba(64, 158, 255, 0.12);
  border-left-color: #409eff;
  font-weight: 600;
  transform: translateX(2px);
}

.toc-link.active::before {
  opacity: 0.05;
}

.toc-link:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.3);
}

.toc-link:focus:not(:focus-visible) {
  box-shadow: none;
}

/* 空状态 */
.toc-empty {
  padding: 0 24px;
  text-align: center;
}

.empty-text {
  color: #909399;
  font-size: 14px;
  margin: 0;
}

/* 滚动条样式 */
.custom-toc::-webkit-scrollbar {
  width: 6px;
}

.custom-toc::-webkit-scrollbar-track {
  background: transparent;
}

.custom-toc::-webkit-scrollbar-thumb {
  background: rgba(144, 147, 153, 0.3);
  border-radius: 3px;
  transition: background 0.3s;
}

.custom-toc::-webkit-scrollbar-thumb:hover {
  background: rgba(144, 147, 153, 0.5);
}

/* 暗色主题支持 */
.custom-toc.dark-theme {
  background: rgba(31, 31, 31, 0.95);
  border-left-color: #414243;
}

.custom-toc.dark-theme .toc-title {
  color: #e5eaf3;
}

.custom-toc.dark-theme .toc-link {
  color: #c9d1d9;
}

.custom-toc.dark-theme .toc-link:hover {
  color: #58a6ff;
  background: rgba(88, 166, 255, 0.08);
  border-left-color: #58a6ff;
}

.custom-toc.dark-theme .toc-link.active {
  color: #58a6ff;
  background: rgba(88, 166, 255, 0.12);
  border-left-color: #58a6ff;
}

.custom-toc.dark-theme .empty-text {
  color: #8b949e;
}

.custom-toc.dark-theme::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
}

.custom-toc.dark-theme::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 保留媒体查询作为备用 */
@media (prefers-color-scheme: dark) {
  .custom-toc:not(.dark-theme) {
    background: rgba(31, 31, 31, 0.95);
    border-left-color: #414243;
  }
  
  .custom-toc:not(.dark-theme) .toc-title {
    color: #e5eaf3;
  }
  
  .custom-toc:not(.dark-theme) .toc-link {
    color: #c9d1d9;
  }
  
  .custom-toc:not(.dark-theme) .toc-link:hover {
    color: #58a6ff;
    background: rgba(88, 166, 255, 0.08);
    border-left-color: #58a6ff;
  }
  
  .custom-toc:not(.dark-theme) .toc-link.active {
    color: #58a6ff;
    background: rgba(88, 166, 255, 0.12);
    border-left-color: #58a6ff;
  }
  
  .custom-toc:not(.dark-theme) .empty-text {
    color: #8b949e;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .custom-toc {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .custom-toc {
    transform: translateX(100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .custom-toc.show {
    transform: translateX(0);
  }
}
</style>