import { computed, type Ref } from 'vue'
import {
  getSizeConfig,
  ensureInitialized,
  type SizeConfig,
} from '../config/speedConfig'

/**
 * 可配置的尺寸系统
 * 支持预设尺寸（small/medium/large/xlarge）和数字尺寸
 */
export function useConfigurableSize(size: Ref<string | number>) {
  // 获取尺寸配置
  const sizeConfig = computed((): SizeConfig => {
    // 延迟初始化：只有当真正使用预设尺寸时才初始化 CSS 变量
    if (typeof size.value === 'string') {
      ensureInitialized()
    }
    return getSizeConfig(size.value)
  })

  // 生成 CSS 变量（只在使用数字尺寸时才生成内联样式）
  const dynamicSizeVars = computed(() => {
    // 如果是预设尺寸，使用全局 CSS 变量，不生成内联样式
    if (typeof size.value === 'string') {
      return {}
    }

    // 只有数字尺寸才生成内联样式
    if (typeof size.value === 'number') {
      const config = sizeConfig.value
      return {
        '--sp-field-size': `${config.base}px`,
        '--sp-field-font-size': `${config.fontSize}px`,
        '--sp-field-padding-top': `${config.paddingTop}px`,
        '--sp-field-padding-bottom': `${config.paddingBottom}px`,
        '--sp-field-label-floating-top': `${config.labelFloatingTop}px`,
        '--sp-field-label-font-size': `${config.labelFontSize}px`,
        '--sp-field-prefix-padding': `${config.prefixPadding}px`,
        '--sp-field-prefix-padding-left': `${config.prefixPaddingLeft}px`,
        '--sp-field-prefix-padding-default': `${config.prefixPaddingDefault}px`,
        '--sp-field-prefix-padding-square': `${config.prefixPaddingSquare}px`,
        '--sp-field-gap': `${config.gap}px`,
      }
    }

    return {}
  })

  // CSS 类名（用于预设尺寸）
  const sizeClass = computed(() => {
    if (typeof size.value === 'string') {
      return `sp-size--${size.value}`
    }
    return ''
  })

  return {
    sizeConfig,
    dynamicSizeVars,
    sizeClass,
  }
}
