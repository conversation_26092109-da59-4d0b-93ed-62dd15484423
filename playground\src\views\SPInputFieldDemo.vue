<template>
  <div class="sp-input-field-demo">
    <div class="demo-header">
      <h1>✨ SPInputField 组件演示</h1>
      <p>基于 SPInput + SPField 的简化版 VTextField 实现</p>
      <router-link to="/" class="back-link">← 返回首页</router-link>
    </div>

    <div class="demo-container">
      <!-- 基础用法 -->
      <section class="demo-section">
        <h2>🎯 基础用法</h2>
        <div class="demo-row">
          <div class="demo-item">
            <h3>基础输入框</h3>
            <SPInputField
              v-model="basicValue"
              label="用户名"
              placeholder="请输入用户名"
            />
            <p class="value-display">值: {{ basicValue }}</p>
          </div>

          <div class="demo-item">
            <h3>带前缀后缀</h3>
            <SPInputField
              v-model="prefixSuffixValue"
              label="金额"
              prefix="¥"
              suffix="元"
              placeholder="0.00"
            />
            <p class="value-display">值: {{ prefixSuffixValue }}</p>
          </div>
        </div>
      </section>

      <!-- 变体演示 -->
      <section class="demo-section">
        <h2>🎨 变体演示</h2>
        <div class="demo-row">
          <div class="demo-item">
            <h3>Filled (默认)</h3>
            <SPInputField
              v-model="filledValue"
              label="Filled 输入框"
              variant="filled"
              placeholder="Filled 变体"
            />
          </div>

          <div class="demo-item">
            <h3>Outlined</h3>
            <SPInputField
              v-model="outlinedValue"
              label="Outlined 输入框"
              variant="outlined"
              placeholder="Outlined 变体"
            />
          </div>

          <div class="demo-item">
            <h3>Underlined</h3>
            <SPInputField
              v-model="underlinedValue"
              label="Underlined 输入框"
              variant="underlined"
              placeholder="Underlined 变体"
            />
          </div>

          <div class="demo-item">
            <h3>Plain</h3>
            <SPInputField
              v-model="plainValue"
              label="Plain 输入框"
              variant="plain"
              placeholder="Plain 变体"
            />
          </div>
        </div>
      </section>

      <!-- 尺寸演示 -->
      <section class="demo-section">
        <h2>📏 尺寸演示</h2>
        <div class="demo-row">
          <div class="demo-item">
            <h3>Small</h3>
            <SPInputField
              v-model="sizeValue"
              label="小尺寸"
              size="small"
              placeholder="Small size"
            />
          </div>

          <div class="demo-item">
            <h3>Medium (默认)</h3>
            <SPInputField
              v-model="sizeValue"
              label="中等尺寸"
              size="medium"
              placeholder="Medium size"
            />
          </div>

          <div class="demo-item">
            <h3>Large</h3>
            <SPInputField
              v-model="sizeValue"
              label="大尺寸"
              size="large"
              placeholder="Large size"
            />
          </div>
        </div>
      </section>

      <!-- 功能演示 -->
      <section class="demo-section">
        <h2>⚡ 功能演示</h2>
        <div class="demo-row">
          <div class="demo-item">
            <h3>可清除</h3>
            <SPInputField
              v-model="clearableValue"
              label="可清除输入框"
              placeholder="输入内容后显示清除按钮"
              clearable
            />
          </div>

          <div class="demo-item">
            <h3>密码输入</h3>
            <SPInputField
              v-model="passwordValue"
              label="密码"
              type="password"
              placeholder="请输入密码"
              clearable
            />
          </div>

          <div class="demo-item">
            <h3>带图标</h3>
            <SPInputField
              v-model="iconValue"
              label="搜索"
              placeholder="搜索内容"
              prepend-inner-icon="🔍"
              append-inner-icon="⚙️"
            />
          </div>
        </div>
      </section>

      <!-- 计数器演示 -->
      <section class="demo-section">
        <h2>🔢 计数器演示</h2>
        <div class="demo-row">
          <div class="demo-item">
            <h3>字符计数</h3>
            <SPInputField
              v-model="counterValue"
              label="限制50字符"
              placeholder="输入内容..."
              :maxlength="50"
              counter
            />
          </div>

          <div class="demo-item">
            <h3>自定义计数器</h3>
            <SPInputField
              v-model="customCounterValue"
              label="自定义计数"
              placeholder="输入内容..."
              :counter="100"
              :counter-value="(val) => val ? val.length * 2 : 0"
            >
              <template #counter="{ value, max }">
                <span style="color: #ff6b6b;">{{ value }} / {{ max }} (双倍计数)</span>
              </template>
            </SPInputField>
          </div>
        </div>
      </section>

      <!-- 状态演示 -->
      <section class="demo-section">
        <h2>🚦 状态演示</h2>
        <div class="demo-row">
          <div class="demo-item">
            <h3>错误状态</h3>
            <SPInputField
              v-model="errorValue"
              label="邮箱地址"
              placeholder="请输入邮箱"
              :error="!isValidEmail(errorValue) && errorValue.length > 0"
              :error-messages="!isValidEmail(errorValue) && errorValue.length > 0 ? ['请输入有效的邮箱地址'] : []"
            />
          </div>

          <div class="demo-item">
            <h3>禁用状态</h3>
            <SPInputField
              v-model="disabledValue"
              label="禁用输入框"
              placeholder="此输入框已禁用"
              disabled
            />
          </div>

          <div class="demo-item">
            <h3>只读状态</h3>
            <SPInputField
              v-model="readonlyValue"
              label="只读输入框"
              placeholder="此输入框只读"
              readonly
            />
          </div>
        </div>
      </section>

      <!-- 提示信息演示 -->
      <section class="demo-section">
        <h2>💡 提示信息演示</h2>
        <div class="demo-row">
          <div class="demo-item">
            <h3>帮助提示</h3>
            <SPInputField
              v-model="hintValue"
              label="用户名"
              placeholder="请输入用户名"
              hint="用户名长度应为3-20个字符"
            />
          </div>

          <div class="demo-item">
            <h3>持久提示</h3>
            <SPInputField
              v-model="persistentHintValue"
              label="手机号"
              placeholder="请输入手机号"
              hint="格式：138-0013-8000"
              persistent-hint
            />
          </div>
        </div>
      </section>

      <!-- 高级功能演示 -->
      <section class="demo-section">
        <h2>🚀 高级功能演示</h2>
        <div class="demo-row">
          <div class="demo-item">
            <h3>组合功能</h3>
            <SPInputField
              v-model="advancedValue"
              label="高级输入框"
              placeholder="组合所有功能"
              prefix="$"
              suffix=".00"
              prepend-inner-icon="💰"
              clearable
              :maxlength="20"
              counter
              hint="这是一个功能齐全的输入框"
            />
          </div>

          <div class="demo-item">
            <h3>自定义插槽</h3>
            <SPInputField
              v-model="slotValue"
              label="自定义插槽"
              placeholder="带自定义内容"
            >
              <template #prepend-inner>
                <span style="color: #ff6b6b;">🎯</span>
              </template>
              <template #append-inner>
                <button style="background: none; border: none; cursor: pointer;">
                  ✨
                </button>
              </template>
            </SPInputField>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SPInputField from '../../../packages/ui/src/components/SPInputField/SPInputField.vue'

// 响应式数据
const basicValue = ref('')
const prefixSuffixValue = ref('')
const filledValue = ref('')
const outlinedValue = ref('')
const underlinedValue = ref('')
const plainValue = ref('')
const sizeValue = ref('')
const clearableValue = ref('')
const passwordValue = ref('')
const iconValue = ref('')
const counterValue = ref('')
const customCounterValue = ref('')
const errorValue = ref('')
const disabledValue = ref('禁用状态的值')
const readonlyValue = ref('只读状态的值')
const hintValue = ref('')
const persistentHintValue = ref('')
const advancedValue = ref('')
const slotValue = ref('')

// 邮箱验证函数
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}
</script>

<style lang="scss" scoped>
.sp-input-field-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.demo-header {
  text-align: center;
  margin-bottom: 3rem;
  
  h1 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 2.5rem;
  }
  
  p {
    color: #7f8c8d;
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
  }
}

.back-link {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    color: white;
  }
}

.demo-container {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.demo-section {
  h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #ecf0f1;
    font-size: 1.8rem;
  }
}

.demo-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.demo-item {
  padding: 1.5rem;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  background: #fafafa;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
  
  h3 {
    margin: 0 0 1rem 0;
    color: #34495e;
    font-size: 1.1rem;
    font-weight: 600;
  }
}

.value-display {
  margin-top: 0.75rem;
  padding: 0.5rem;
  background: #f0f0f0;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: #666;
  word-break: break-all;
}
</style>
