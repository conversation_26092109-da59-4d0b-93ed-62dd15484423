<!--
  ListInner.vue - DOM渲染层

  职责：
  - 纯DOM元素渲染
  - 最小化职责
  - 无状态组件
-->

<template>
  <div
    role="listbox"
    :aria-label="ariaLabel"
    :aria-multiselectable="ariaMultiselectable"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
  /**
   * ListInner - DOM渲染层
   *
   * 特点：
   * 1. 纯DOM渲染
   * 2. 无逻辑处理
   * 3. 最小化职责
   * 4. 支持无障碍访问
   */

  interface ListInnerProps {
    ariaLabel?: string
    ariaMultiselectable?: boolean
  }

  defineProps<ListInnerProps>()
</script>

<script lang="ts">
  export default {
    name: 'ListInner',
  }
</script>
