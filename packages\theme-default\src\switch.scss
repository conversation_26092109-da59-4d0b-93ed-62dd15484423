@use './common/var.scss' as *;

.sp-switch {
  display: inline-flex;
  align-items: center;
  position: relative;
  font-size: 14px;
  line-height: 18px;
  height: 20px;
  vertical-align: middle;
  cursor: pointer;

  &__core {
    margin: 0;
    display: inline-block;
    position: relative;
    min-width: 50px;
    height: 26px;
    border: none;
    outline: none;
    border-radius: 16px;
    box-sizing: border-box;
    background: #e5e6eb;
    transition: background .3s;
    padding: 0;
    overflow: hidden;
  }

  &__button {
    position: absolute;
    top: 50%;
    left: 2.5px;
    border-radius: 50%;
    transition: left .5s cubic-bezier(.175, .885, .32, 1.275);
    width: 22px;
    height: 22px;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0,0,0,0.08);
    z-index: 2;
    transform: translateY(-50%);
    cursor: pointer;
    box-sizing: border-box;
    border: none;
    outline: none;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__countdown {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    line-height: 1;
    user-select: none;
  }

  &__button-icon {
    width: 12px;
    height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &--loading {
      animation: sp-switch-loading 1s linear infinite;
    }
  }

  &__loading-icon {
    width: 12px;
    height: 12px;
    border: 2px solid #ccc;
    border-top: 2px solid #6c8cff;
    border-radius: 50%;
    animation: sp-switch-loading 1s linear infinite;
  }

  @keyframes sp-switch-loading {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  &__text {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    white-space: nowrap;
    z-index: 1;
    pointer-events: none;
    transition: transform .3s ease, opacity .3s ease;
    opacity: 0;
    max-width: calc(100% - 22px);
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__text--on {
    left: 8px;
    color: #fff;
    transform: translateY(-50%) translateX(-20px);
  }

  &__text--off {
    right: 8px;
    color: #999;
    transform: translateY(-50%) translateX(20px);
  }

  &__text--visible {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }

  &__icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    vertical-align: middle;
  }

  &__text-content {
    display: inline-block;
    vertical-align: middle;
    margin-left: 4px;
  }

  &__text--on &__text-content:only-child,
  &__text--off &__text-content:only-child {
    margin-left: 0;
  }

  &__label {
    margin-left: 5px;
    font-size: 14px;
  }

  &--checked {
    .sp-switch__core {
      background-color: #6c8cff;
    }

    .sp-switch__button {
      left: calc(100% - 22px - 2.5px);
    }

    .sp-switch__button--pressed {
      left: calc(100% - 22px - 2.5px);
    }
  }

  &--disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  // 尺寸变体
  &--small {
    font-size: 12px;
    line-height: 16px;
    height: 16px;

    .sp-switch__core {
      min-width: 30px; 
      height: 16px !important;
      border-radius: 12px;
    }

    .sp-switch__button {
      width: 14px !important;
      height: 14px !important;
      left: 1px !important;
    }

    .sp-switch__button-icon {
      width: 8px;
      height: 8px;
    }

    .sp-switch__loading-icon {
      width: 8px;
      height: 8px;
      border-width: 1px;
    }

    .sp-switch__countdown {
      font-size: 8px;
    }

    &.sp-switch--checked .sp-switch__button {
      left: calc(100% - 14px - 1px) !important;
    }

    .sp-switch__text {
      font-size: 12px;
    }

    .sp-switch__label {
      font-size: 12px;
    }
  }

  &--medium {
    // 默认样式已在基础样式中定义
  }

  &--large {
    font-size: 16px;
    line-height: 20px;
    height: 24px;

    .sp-switch__core {
      min-width: 60px;
      height: 34px !important;
      border-radius: 20px !important;
    }

    .sp-switch__button {
      width: 30px !important;
      height: 30px !important;
      left: 2px !important;
    }

    .sp-switch__button-icon {
      width: 16px;
      height: 16px;
    }

    .sp-switch__loading-icon {
      width: 16px;
      height: 16px;
      border-width: 3px;
    }

    .sp-switch__countdown {
      font-size: 14px;
    }

    &.sp-switch--checked .sp-switch__button {
      left: calc(100% - 30px - 2px) !important;
    }

    .sp-switch__text {
      font-size: 16px;
    }

    .sp-switch__label {
      font-size: 16px;
    }
  }

  &--huge {
    font-size: 18px;
    line-height: 22px;
    height: 28px;

    .sp-switch__core {
      min-width: 70px;
      height: 38px !important;
      border-radius: 24px !important;
    }

    .sp-switch__button {
      width: 34px !important;
      height: 34px !important;
      left: 2px !important;
    }

    .sp-switch__button-icon {
      width: 20px;
      height: 20px;
    }

    .sp-switch__loading-icon {
      width: 20px;
      height: 20px;
      border-width: 3px;
    }

    .sp-switch__countdown {
      font-size: 16px;
    }

    &.sp-switch--checked .sp-switch__button {
      left: calc(100% - 34px - 2px) !important;
    }

    .sp-switch__text {
      font-size: 18px;
    }

    .sp-switch__label {
      font-size: 18px;
    }
  }

  // 方形样式
  &--square {
    .sp-switch__core {
      border-radius: 4px !important;
    }

    .sp-switch__button {
      border-radius: 2px !important;
    }

    // 不同尺寸的方形样式
    &.sp-switch--small {
      .sp-switch__core {
        border-radius: 3px !important;
      }

      .sp-switch__button {
        border-radius: 1px !important;
      }
    }

    &.sp-switch--large {
      .sp-switch__core {
        border-radius: 6px !important;
      }

      .sp-switch__button {
        border-radius: 3px !important;
      }
    }

    &.sp-switch--huge {
      .sp-switch__core {
        border-radius: 8px !important;
      }

      .sp-switch__button {
        border-radius: 4px !important;
      }
    }
  }

  // 垂直样式
  &--vertical {
    .sp-switch__core {
      width: 26px !important;
      height: 50px !important;
      min-width: 26px !important;
      border-radius: 16px !important;
    }

    .sp-switch__button {
      top: 2.5px !important;
      left: 50% !important;
      transform: translateX(-50%) !important;
      transition: top .5s cubic-bezier(.175, .885, .32, 1.275) !important;
    }

    &.sp-switch--checked .sp-switch__button {
      top: calc(100% - 22px - 2.5px) !important;
      left: 50% !important;
      transform: translateX(-50%) !important;
    }

    .sp-switch__text {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      font-size: 12px;
      white-space: nowrap;
      z-index: 1;
      pointer-events: none;
      transition: transform .3s ease, opacity .3s ease;
      opacity: 0;
      max-width: calc(100% - 4px);
      overflow: hidden;
      text-overflow: ellipsis;
      writing-mode: vertical-rl;
      text-orientation: upright;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      left: 0;
      transform: none;
    }

    .sp-switch__text--on {
      top: 8px;
      color: #fff;
      transform: translateY(-20px);
    }

    .sp-switch__text--off {
      top: auto;
      bottom: 8px;
      color: #999;
      transform: translateY(20px);
    }

    .sp-switch__text--visible {
      opacity: 1;
      transform: translateY(0);
    }

    // 不同尺寸的垂直样式
    &.sp-switch--small {
      .sp-switch__core {
        width: 16px !important;
        height: 30px !important;
        min-width: 16px !important;
        border-radius: 12px !important;
      }

      .sp-switch__button {
        top: 1px !important;
        width: 14px !important;
        height: 14px !important;
      }

      &.sp-switch--checked .sp-switch__button {
        top: calc(100% - 14px - 1px) !important;
      }

      .sp-switch__text {
        font-size: 10px;
        writing-mode: vertical-rl;
        text-orientation: upright;
      }

      .sp-switch__text--on {
        top: 6px;
      }

      .sp-switch__text--off {
        top: auto;
        bottom: 6px;
      }
    }

    &.sp-switch--large {
      .sp-switch__core {
        width: 34px !important;
        height: 60px !important;
        min-width: 34px !important;
        border-radius: 20px !important;
      }

      .sp-switch__button {
        top: 2px !important;
        width: 30px !important;
        height: 30px !important;
      }

      &.sp-switch--checked .sp-switch__button {
        top: calc(100% - 30px - 2px) !important;
      }

      .sp-switch__text {
        font-size: 14px;
        writing-mode: vertical-rl;
        text-orientation: upright;
      }

      .sp-switch__text--on {
        top: 10px;
      }

      .sp-switch__text--off {
        top: auto;
        bottom: 10px;
      }
    }

    &.sp-switch--huge {
      .sp-switch__core {
        width: 38px !important;
        height: 70px !important;
        min-width: 38px !important;
        border-radius: 24px !important;
      }

      .sp-switch__button {
        top: 2px !important;
        width: 34px !important;
        height: 34px !important;
      }

      &.sp-switch--checked .sp-switch__button {
        top: calc(100% - 34px - 2px) !important;
      }

      .sp-switch__text {
        font-size: 16px;
        writing-mode: vertical-rl;
        text-orientation: upright;
      }

      .sp-switch__text--on {
        top: 12px;
      }

      .sp-switch__text--off {
        top: auto;
        bottom: 12px;
      }
    }

    // 垂直方形样式组合
    &.sp-switch--square {
      .sp-switch__core {
        border-radius: 4px !important;
      }

      .sp-switch__button {
        border-radius: 2px !important;
      }

      &.sp-switch--small {
        .sp-switch__core {
          border-radius: 3px !important;
        }

        .sp-switch__button {
          border-radius: 1px !important;
        }
      }

      &.sp-switch--large {
        .sp-switch__core {
          border-radius: 6px !important;
        }

        .sp-switch__button {
          border-radius: 3px !important;
        }
      }

      &.sp-switch--huge {
        .sp-switch__core {
          border-radius: 8px !important;
        }

        .sp-switch__button {
          border-radius: 4px !important;
        }
      }
    }
  }
}

// 外部文本包装器样式
.sp-switch-wrapper {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

// 外部文本样式
.sp-switch__out-text {
  font-size: 14px;
  color: #606266;
  transition: opacity 0.3s ease;
  white-space: nowrap;
  opacity: 0.6;
  
  &--visible {
    opacity: 1;
    color: #409eff;
  }
  
  &--on {
    order: 3; // 显示在开关右侧
  }
  
  &--off {
    order: 1; // 显示在开关左侧
  }
}

// 确保开关本身在中间
.sp-switch {
  order: 2;
}