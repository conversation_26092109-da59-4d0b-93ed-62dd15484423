<!--
  SmsCodeControls.vue - 短信验证码输入框的控制器组件
  包含获取验证码按钮和倒计时功能
-->

<template>
  <div
    class="sp-smscode-controls"
    @click.stop
    @mousedown.stop
  >
    <span
      :class="buttonClasses"
      @click.stop.prevent="handleSendClick"
      @mousedown.stop.prevent
    >
      {{ buttonText }}
    </span>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, onUnmounted, nextTick } from 'vue'

  interface SmsCodeControlsProps {
    /** 倒计时时长（秒），默认60秒 */
    countdown?: number
    /** 是否可以发送验证码 */
    canSend?: boolean
    /** 获取验证码按钮文字 */
    sendText?: string
    /** 重新获取验证码按钮文字 */
    resendText?: string
  }

  interface SmsCodeControlsEmits {
    /** 发送验证码 */
    (e: 'sms-send'): void
    /** 请求聚焦 */
    (e: 'focus'): void
    /** 倒计时开始 */
    (e: 'sms-start', countdown: number): void
    /** 倒计时进行中 */
    (e: 'sms-tick', remaining: number): void
    /** 倒计时结束 */
    (e: 'sms-end'): void
  }

  const props = withDefaults(defineProps<SmsCodeControlsProps>(), {
    countdown: 60,
    canSend: true,
    sendText: '获取验证码',
    resendText: '重新获取',
  })

  const emit = defineEmits<SmsCodeControlsEmits>()

  // ===== 状态管理 =====
  const isCountingDown = ref(false)
  const remainingTime = ref(0)
  let countdownTimer: NodeJS.Timeout | null = null

  // ===== 计算属性 =====
  const buttonText = computed(() => {
    if (isCountingDown.value) {
      return `${remainingTime.value}s可重新获取`
    }
    return remainingTime.value === 0 ? props.sendText : props.resendText
  })

  const buttonClasses = computed(() => ({
    'sp-smscode-controls__button': true,
    'sp-smscode-controls__button--disabled':
      !props.canSend || isCountingDown.value,
    'sp-smscode-controls__button--counting': isCountingDown.value,
  }))

  // ===== 倒计时逻辑 =====
  const startCountdown = () => {
    if (isCountingDown.value) return

    isCountingDown.value = true
    remainingTime.value = props.countdown

    emit('sms-start', props.countdown)
    emit('sms-tick', remainingTime.value)

    countdownTimer = setInterval(() => {
      remainingTime.value--
      emit('sms-tick', remainingTime.value)

      if (remainingTime.value <= 0) {
        stopCountdown()
        emit('sms-end')
      }
    }, 1000)
  }

  const stopCountdown = () => {
    if (countdownTimer) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
    isCountingDown.value = false
    remainingTime.value = 0
  }

  // ===== 事件处理 =====
  const handleSendClick = () => {
    if (props.canSend && !isCountingDown.value) {
      emit('sms-send')
      startCountdown()

      // 在下一个事件循环中聚焦，确保其他逻辑先完成
      nextTick(() => {
        emit('focus')
      })
    }
  }

  // ===== 清理 =====
  onUnmounted(() => {
    stopCountdown()
  })

  // ===== 暴露方法 =====
  defineExpose({
    /** 开始倒计时 */
    startCountdown,
    /** 停止倒计时 */
    stopCountdown,
    /** 获取当前状态 */
    get isCountingDown() {
      return isCountingDown.value
    },
    /** 获取剩余时间 */
    get remainingTime() {
      return remainingTime.value
    },
  })
</script>

<script lang="ts">
  export default {
    name: 'SpSmsCodeControls',
  }
</script>

<!-- <style lang="scss">
  .sp-smscode-controls {
    display: flex;
    align-items: center;
    padding: 0;
    pointer-events: auto;

    &__button {
      color: var(--sp-color-primary, #409eff);
      font-size: 14px;
      font-weight: 500;
      line-height: 1;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.3s ease;
      user-select: none;
      white-space: nowrap;
      min-width: 80px;
      text-align: center;
      background: transparent;
      border: 1px solid transparent;

      &:hover {
        color: var(--sp-color-primary-hover, #66b1ff);
        background: rgba(64, 158, 255, 0.1);
        border-color: rgba(64, 158, 255, 0.2);
      }

      &:active {
        color: var(--sp-color-primary-active, #3a8ee6);
        background: rgba(64, 158, 255, 0.2);
        transform: translateY(1px);
      }

      &--disabled {
        color: var(--sp-color-text-disabled, #c0c4cc) !important;
        cursor: not-allowed !important;
        background: transparent !important;
        border-color: transparent !important;
        transform: none !important;

        &:hover,
        &:active {
          color: var(--sp-color-text-disabled, #c0c4cc) !important;
          background: transparent !important;
          border-color: transparent !important;
          transform: none !important;
        }
      }

      &--counting {
        color: var(--sp-color-text-secondary, #909399);
        font-family: 'Courier New', Courier, monospace;
        font-weight: 600;
        background: rgba(144, 147, 153, 0.1);
        border-color: rgba(144, 147, 153, 0.2);
      }
    }
  }

  // 主题适配
  [data-theme='dark'] .sp-smscode-controls {
    &__button {
      &:hover {
        background: rgba(64, 158, 255, 0.2);
      }

      &:active {
        background: rgba(64, 158, 255, 0.3);
      }

      &--counting {
        background: rgba(144, 147, 153, 0.2);
      }
    }
  }
</style> -->
