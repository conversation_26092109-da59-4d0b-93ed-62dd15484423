<text>
> 提供了四种尺寸：small、medium、large、huge
</text>

<template>
    <sp-switch size="small" v-model:value="value1" />
    <sp-switch size="medium" v-model:value="value2" />
    <sp-switch size="large" v-model:value="value3" />
    <sp-switch size="huge" v-model:value="value4" />
</template>

<script setup>
import { ref } from 'vue'

const value1 = ref(false)
const value2 = ref(true)
const value3 = ref(false)
const value4 = ref(true)
</script>