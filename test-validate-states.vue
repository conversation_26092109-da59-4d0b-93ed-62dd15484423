<template>
  <div class="test-container">
    <h1>InputField 验证状态测试</h1>
    
    <div class="test-section">
      <h2>基础状态测试</h2>
      
      <div class="test-item">
        <h3>错误状态 (error)</h3>
        <sp-input-field
          v-model:value="errorValue"
          name="error-test"
          label="错误状态"
          validate-state="error"
          validate-message="这是一个错误消息"
          placeholder="错误状态测试"
        />
      </div>
      
      <div class="test-item">
        <h3>警告状态 (warning)</h3>
        <sp-input-field
          v-model:value="warningValue"
          name="warning-test"
          label="警告状态"
          validate-state="warning"
          validate-message="这是一个警告消息"
          placeholder="警告状态测试"
        />
      </div>
      
      <div class="test-item">
        <h3>成功状态 (success)</h3>
        <sp-input-field
          v-model:value="successValue"
          name="success-test"
          label="成功状态"
          validate-state="success"
          validate-message="验证成功！"
          placeholder="成功状态测试"
        />
      </div>
      
      <div class="test-item">
        <h3>正常状态 (无验证状态)</h3>
        <sp-input-field
          v-model:value="normalValue"
          name="normal-test"
          label="正常状态"
          placeholder="正常状态测试"
        />
      </div>
    </div>
    
    <div class="test-section">
      <h2>发光效果测试</h2>
      
      <div class="test-item">
        <h3>错误状态 + 发光效果</h3>
        <sp-input-field
          v-model:value="errorGlowValue"
          name="error-glow-test"
          label="错误发光"
          validate-state="error"
          validate-message="错误状态的发光效果"
          effect="glow"
          placeholder="点击聚焦查看发光效果"
        />
      </div>
      
      <div class="test-item">
        <h3>警告状态 + 发光效果</h3>
        <sp-input-field
          v-model:value="warningGlowValue"
          name="warning-glow-test"
          label="警告发光"
          validate-state="warning"
          validate-message="警告状态的发光效果"
          effect="glow"
          placeholder="点击聚焦查看发光效果"
        />
      </div>
      
      <div class="test-item">
        <h3>成功状态 + 发光效果</h3>
        <sp-input-field
          v-model:value="successGlowValue"
          name="success-glow-test"
          label="成功发光"
          validate-state="success"
          validate-message="成功状态的发光效果"
          effect="glow"
          placeholder="点击聚焦查看发光效果"
        />
      </div>
    </div>
    
    <div class="debug-section">
      <h2>调试信息</h2>
      <pre>{{ debugInfo }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 测试数据
const errorValue = ref('')
const warningValue = ref('警告示例文本')
const successValue = ref('成功示例文本')
const normalValue = ref('')
const errorGlowValue = ref('')
const warningGlowValue = ref('警告发光示例')
const successGlowValue = ref('成功发光示例')

// 调试信息
const debugInfo = computed(() => ({
  errorValue: errorValue.value,
  warningValue: warningValue.value,
  successValue: successValue.value,
  normalValue: normalValue.value,
  errorGlowValue: errorGlowValue.value,
  warningGlowValue: warningGlowValue.value,
  successGlowValue: successGlowValue.value,
}))
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
}

.test-item {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.test-item h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 16px;
}

.debug-section {
  margin-top: 40px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.debug-section pre {
  margin: 0;
  font-size: 12px;
  color: #666;
}
</style>
