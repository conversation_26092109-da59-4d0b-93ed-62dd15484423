<template>
  <div class="smooth-animation-test">
    <h2>丝滑动画效果测试</h2>
    <p>测试使用 translateX 的丝滑动画效果，标签应该斜着移动到正确位置</p>
    
    <div class="test-section">
      <h3>动画对比测试</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>无前置图标（基准）</h4>
          <SPInputField
            size="medium"
            label="用户名"
            placeholder="点击查看标签动画"
            required
          />
          <p class="note">标签应该直接向上移动</p>
        </div>
        
        <div class="test-item">
          <h4>有前置图标</h4>
          <SPInputField
            size="medium"
            label="用户名"
            placeholder="点击查看标签动画"
            required
            prepend-icon-inner="User"
          />
          <p class="note">标签应该斜着移动到图标右侧</p>
        </div>
        
        <div class="test-item">
          <h4>前置图标 + 前缀</h4>
          <SPInputField
            size="medium"
            label="用户名"
            placeholder="点击查看标签动画"
            required
            prepend-icon-inner="User"
            prefix="@"
          />
          <p class="note">标签应该斜着移动到前缀右侧</p>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>短标签测试</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>超短标签 - 只有前置图标</h4>
          <SPInputField
            size="medium"
            label="名"
            placeholder="短标签测试"
            required
            prepend-icon-inner="User"
          />
          <p class="note">短标签也应该正确定位</p>
        </div>
        
        <div class="test-item">
          <h4>超短标签 - 前置图标 + 前缀</h4>
          <SPInputField
            size="medium"
            label="名"
            placeholder="短标签测试"
            required
            prepend-icon-inner="User"
            prefix="@"
          />
          <p class="note">短标签不应该与前缀重叠</p>
        </div>
        
        <div class="test-item">
          <h4>单字符标签</h4>
          <SPInputField
            size="medium"
            label="A"
            placeholder="单字符测试"
            required
            prepend-icon-inner="Search"
            prefix="搜索:"
          />
          <p class="note">单字符也应该正确定位</p>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>长标签测试</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>长标签 - 只有前置图标</h4>
          <SPInputField
            size="medium"
            label="这是一个很长的标签文本用来测试动画效果"
            placeholder="长标签测试"
            required
            prepend-icon-inner="Mail"
          />
          <p class="note">长标签的动画应该也很丝滑</p>
        </div>
        
        <div class="test-item">
          <h4>长标签 - 前置图标 + 前缀</h4>
          <SPInputField
            size="medium"
            label="这是一个很长的标签文本用来测试动画效果"
            placeholder="长标签测试"
            required
            prepend-icon-inner="Mail"
            prefix="邮箱:"
          />
          <p class="note">长标签也应该正确定位到前缀右侧</p>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>不同前缀长度测试</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>短前缀</h4>
          <SPInputField
            size="medium"
            label="邮箱"
            placeholder="测试短前缀"
            required
            prepend-icon-inner="Mail"
            prefix="@"
          />
        </div>
        
        <div class="test-item">
          <h4>中等前缀</h4>
          <SPInputField
            size="medium"
            label="网址"
            placeholder="测试中等前缀"
            required
            prepend-icon-inner="Link"
            prefix="https://"
          />
        </div>
        
        <div class="test-item">
          <h4>长前缀</h4>
          <SPInputField
            size="medium"
            label="用户名"
            placeholder="测试长前缀"
            required
            prepend-icon-inner="User"
            prefix="用户名前缀:"
          />
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>不同尺寸动画测试</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>Small 尺寸</h4>
          <SPInputField
            size="small"
            label="测试"
            placeholder="小尺寸动画"
            required
            prepend-icon-inner="Search"
            prefix="搜:"
          />
        </div>
        
        <div class="test-item">
          <h4>Large 尺寸</h4>
          <SPInputField
            size="large"
            label="测试"
            placeholder="大尺寸动画"
            required
            prepend-icon-inner="Search"
            prefix="搜:"
          />
        </div>
        
        <div class="test-item">
          <h4>动态尺寸 (60px)</h4>
          <SPInputField
            :size="60"
            label="测试"
            placeholder="动态尺寸动画"
            required
            prepend-icon-inner="Search"
            prefix="搜:"
          />
        </div>
      </div>
    </div>

    <div class="animation-tips">
      <h3>🎬 动画测试说明</h3>
      <ul>
        <li><strong>点击输入框</strong>：观察标签向上浮动的动画</li>
        <li><strong>点击外部</strong>：观察标签向下回落的动画</li>
        <li><strong>注意观察</strong>：标签应该斜着移动到正确位置，而不是先移动再定位</li>
        <li><strong>短标签</strong>：即使很短的标签也不应该与图标/前缀重叠</li>
        <li><strong>长标签</strong>：长标签的动画应该同样丝滑</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import SPInputField from '../packages/ui/src/components/input-field/input-field.vue'
</script>

<style scoped>
.smooth-animation-test {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.test-item {
  padding: 15px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafafa;
}

.test-item h4 {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 14px;
  font-weight: 600;
}

.note {
  margin: 10px 0 0 0;
  font-size: 12px;
  color: #888;
  font-style: italic;
}

.animation-tips {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.animation-tips h3 {
  margin-top: 0;
  color: #007bff;
}

.animation-tips ul {
  margin: 0;
  padding-left: 20px;
}

.animation-tips li {
  margin-bottom: 8px;
  color: #555;
}
</style>
