<!--
  Input.vue - 业务逻辑层
  职责：基于 InputCore 扩展，处理 v-model、验证、密码显示等业务逻辑
-->

<template>
  <InputCore
    ref="inputCoreRef"
    :value="currentValue"
    :type="actualType"
    :variant="props.variant"
    :effect="props.effect"
    :size="props.size"
    :placeholder="props.placeholder"
    :disabled="props.disabled"
    :readonly="props.readonly"
    :clearable="props.clearable"
    :prefix-icon="props.prefixIcon"
    :suffix-icon="computedSuffixIcon"
    :loading="props.loading"
    :maxlength="props.maxlength"
    :show-word-limit="props.showWordLimit"
    :error="props.error || computedValidateState === 'error'"
    :validate-state="computedValidateState"
    :validate-message="computedValidateMessage"
    :has-value="hasValueComputed"
    :focused="focused"
    :mode="props.mode"
    @click="handleWrapperClick"
    @focus="handleFocus"
    @blur="handleBlur"
    @input="handleInput"
    @change="handleChange"
    @keydown="handleKeydown"
    @clear="handleClear"
  >
    <!-- 前缀插槽 -->
    <template #prefix>
      <slot name="prefix" />
    </template>

    <!-- 内部插槽 - 用于多选标签等 -->
    <template #inner>
      <slot name="inner" />
    </template>

    <!-- 后缀插槽 -->
    <template #suffix>
      <!-- 密码显示切换按钮 -->
      <sp-icon
        v-if="showPasswordIcon"
        :name="passwordIconName"
        :size="iconSize"
        :class="passwordIconClasses"
        @click.stop.prevent="togglePassword"
      />

      <slot name="suffix" />
    </template>

    <!-- 验证消息插槽 -->
    <template #message>
      <slot name="message" />
    </template>
  </InputCore>
</template>

<script setup lang="ts">
  import { ref, computed, inject } from 'vue'
  import { useVModel } from '@speed-ui/hooks'
  import InputCore from './inputcore'
  import SpIcon from '../icon/Icon.vue'
  import type { InputProps, InputEmits } from './types'
  import { inputPropsDefaults } from './types'

  /**
   * Input 组件 - 业务逻辑层
   *
   * 基于 InputCore 的增强设计：
   * InputCore (完整基础组件) ← Input (业务逻辑扩展)
   */

  const props = withDefaults(defineProps<InputProps>(), inputPropsDefaults)
  const emit = defineEmits<InputEmits>()

  // 表单字段注入（用于表单验证）
  const formItemField = inject<any>('spFormItemField', null)

  // 组件引用
  const inputCoreRef = ref<InstanceType<typeof InputCore> | null>(null)

  // 状态管理
  const focused = ref(false)
  const passwordVisible = ref(false)

  // 使用 useVModel 进行双向绑定
  const {
    value: currentValue,
    updateValue,
    hasValue: hasValueComputed,
  } = useVModel<string | number | undefined>(props, emit, {
    prop: 'value',
    event: 'update:value',
    defaultValue: props.value,
    formField: formItemField,
    onAfterUpdate: newValue => {
      emit('change', newValue)
    },
    debug: false,
  })

  // 验证状态 - 优先使用表单字段
  const computedValidateState = computed(() => {
    if (formItemField && formItemField.validateState) {
      return formItemField.validateState.value
    }
    return props.validateState
  })

  const computedValidateMessage = computed(() => {
    if (formItemField && formItemField.errors.value.length > 0) {
      return formItemField.errors.value[0]
    }
    return props.validateMessage
  })

  // 计算属性
  const actualType = computed(() => {
    if (props.type === 'password' && props.showPassword) {
      return passwordVisible.value ? 'text' : 'password'
    }
    return props.type
  })

  const showPasswordIcon = computed(() => {
    return (
      props.type === 'password' && props.showPassword && hasValueComputed.value
    )
  })

  const passwordIconName = computed(() => {
    return passwordVisible.value ? 'EyeOff' : 'Eye'
  })

  // 计算后缀图标 - 如果显示密码图标，则不显示原始后缀图标
  const computedSuffixIcon = computed(() => {
    return showPasswordIcon.value ? undefined : props.suffixIcon
  })

  // 图标样式
  const iconSize = computed(() => {
    switch (props.size) {
      case 'small':
        return 14
      case 'large':
        return 18
      case 'medium':
      default:
        return 16
    }
  })

  const passwordIconClasses = computed(() => [
    'sp-input__password',
    {
      'sp-input__password--visible': passwordVisible.value,
    },
  ])

  // 事件处理
  const handleWrapperClick = () => {
    if (props.disabled || props.readonly) return
    inputCoreRef.value?.focus()
  }

  const handleInput = (event: Event) => {
    const target = event.target as HTMLInputElement
    updateValue(target.value)
    emit('input', event)
  }

  const handleChange = () => {
    // change 事件已经通过 useVModel 的 onAfterUpdate 处理
    emit('change', currentValue.value)
  }

  const handleFocus = (event: FocusEvent) => {
    focused.value = true
    emit('focus', event)
  }

  const handleBlur = (event: FocusEvent) => {
    focused.value = false
    emit('blur', event)

    // 触发表单验证
    if (formItemField) {
      formItemField.handleBlur()
    }
  }

  const handleKeydown = (event: KeyboardEvent) => {
    emit('keydown', event)
  }

  const handleClear = () => {
    updateValue('')
    emit('clear')
    inputCoreRef.value?.focus()
  }

  const togglePassword = () => {
    passwordVisible.value = !passwordVisible.value
  }

  // 暴露的方法
  const focus = () => {
    inputCoreRef.value?.focus()
  }

  const blur = () => {
    inputCoreRef.value?.blur()
  }

  const select = () => {
    inputCoreRef.value?.select()
  }

  const clear = () => {
    handleClear()
  }

  // 暴露给外部使用
  defineExpose({
    /** 使输入框获得焦点 */
    focus,
    /** 使输入框失去焦点 */
    blur,
    /** 选中输入框中的文字 */
    select,
    /** 清空输入框 */
    clear,
    /** 输入框元素引用 */
    get input() {
      return inputCoreRef.value?.inputRef || null
    },
    /** 包装器元素引用 */
    get wrapper() {
      return inputCoreRef.value?.wrapperRef || null
    },
  })
</script>

<script lang="ts">
  export default {
    name: 'SpInput',
  }
</script>
