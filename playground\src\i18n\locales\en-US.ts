export default {
  title: 'Speed UI Component Library',
  subtitle: 'Vue 3 + TypeScript + Vite based component library',
  
  // Navigation
  nav: {
    components: 'Components',
    button: 'Button',
    layout: 'Layout',
    input: 'Input',
    language: 'Language',
  },

  // Button component
  button: {
    title: 'Button',
    basicTypes: 'Basic Types (Click to toggle active state)',
    basicTypesDesc: 'Use type attribute to define button styles',
    sizes: 'Sizes',
    sizesDesc: 'Use size attribute for additional size configuration',
    states: 'States',
    statesDesc: 'Button disabled state, loading state, etc.',
    default: 'Default',
    outline: 'Outline',
    secondary: 'Secondary',
    success: 'Success',
    warning: 'Warning',
    danger: 'Danger',
    small: 'Small',
    medium: 'Medium',
    large: 'Large',
    disabled: 'Disabled',
    loading: 'Loading',
    notToggleable: 'Not Toggleable',
  },

  // Input component
  input: {
    title: 'Input',
    basic: 'Basic Usage',
    basicDesc: 'Basic input field for common input types',
    clearable: 'Clearable',
    clearableDesc: 'Add a clear button with the clearable attribute',
    password: 'Password Input',
    passwordDesc: 'Use show-password attribute to toggle password visibility',
    textarea: 'Textarea',
    textareaDesc: 'For multi-line text input',
    sizes: 'Sizes',
    sizesDesc: 'Set input size using the size attribute',
    maxlength: 'Character Limit',
    maxlengthDesc: 'Use maxlength and show-word-limit to limit input length',
    disabled: 'Disabled',
    disabledDesc: 'Use disabled attribute to disable the input',
    placeholder: 'Please input',
    placeholderPassword: 'Please input password',
    placeholderTextarea: 'Please input multiline text',
    small: 'Small',
    default: 'Default',
    large: 'Large',
    autosize: 'Autosize Textarea',
    autosizeDesc: 'Set autosize to make textarea height adapt automatically',
    validateStates: 'Validation States',
    validateStatesDesc: 'Input supports different validation states',
    success: 'Success',
    warning: 'Warning',
    error: 'Error',
    successMessage: 'Input is correct',
    warningMessage: 'Please check the format',
    errorMessage: 'Input error, please re-enter',
  },

  // Layout component
  layout: {
    title: 'Layout',
    basic: 'Basic Layout',
    basicDesc: 'Create basic grid layout using columns',
    gutter: 'Column Spacing',
    gutterDesc: 'Row provides gutter attribute to specify spacing between columns',
    offset: 'Column Offset',
    offsetDesc: 'Support offset for specified number of columns',
    align: 'Alignment',
    alignDesc: 'Flexible alignment of columns through justify attribute',
    responsive: 'Responsive Layout',
    responsiveDesc: 'Referring to Bootstrap responsive design, five responsive sizes are preset',
  },

  // Common
  common: {
    clickToToggle: 'Click to toggle active state',
    demo: 'Demo',
    code: 'Code',
    copy: 'Copy',
    copied: 'Copied',
  },
} 