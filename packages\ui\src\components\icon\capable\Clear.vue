<template>
  <span
    v-if="visible"
    :class="clearClass"
    @click="handleClear"
    @mousedown.prevent
  >
    <slot name="icon">
      <Icon
        name="Close"
        :size="iconSize"
        :clickable="true"
        class="sp-input__clear-icon"
      />
    </slot>
  </span>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { bemHelper } from '@speed-ui/config'
  import Icon from '../Icon.vue'

  /**
   * Clear - 清除按钮组件
   *
   * 用于输入框的清除功能
   */

  interface ClearProps {
    // 是否显示
    visible?: boolean
    // 是否禁用
    disabled?: boolean
    // 是否只读
    readonly?: boolean
    // 尺寸
    size?: 'small' | 'medium' | 'large'
    // 当前值（用于判断是否显示）
    value?: string | number
  }

  interface ClearEmits {
    (e: 'clear'): void
  }

  const props = withDefaults(defineProps<ClearProps>(), {
    visible: false,
    disabled: false,
    readonly: false,
    size: 'medium',
  })

  const emit = defineEmits<ClearEmits>()

  // BEM helper
  const bem = bemHelper('input')

  // 计算图标尺寸
  const iconSize = computed(() => {
    switch (props.size) {
      case 'small':
        return 10
      case 'large':
        return 16
      case 'medium':
      default:
        return 12
    }
  })

  // 计算样式类名
  const clearClass = computed(() => [
    bem.e('clear'),
    bem.em('clear', props.size),
    {
      [bem.em('clear', 'visible')]: props.visible,
      [bem.em('clear', 'disabled')]: props.disabled,
    },
  ])

  // 处理清除事件
  const handleClear = () => {
    if (props.disabled || props.readonly) return
    emit('clear')
  }
</script>

<script lang="ts">
  export default {
    name: 'InputClear',
  }
</script>

<style scoped>
  /* 清除按钮样式由主题文件提供 */
</style>
