<!--
  VField.vue - 简化版的 Vuetify VField 组件
  提供类似 Vuetify 的字段容器功能
-->

<template>
  <div
    :class="fieldClasses"
    :style="fieldStyles"
    @click="handleClick"
  >
    <!-- 背景覆盖层 -->
    <div class="v-field__overlay" />

    <!-- 加载条 -->
    <div
      v-if="loading"
      class="v-field__loader"
    >
      <div class="v-field__loader-progress" />
    </div>

    <!-- 前置内部区域 -->
    <div
      v-if="hasPrepend"
      class="v-field__prepend-inner"
      @mousedown.prevent.stop
    >
      <!-- 前置图标 -->
      <div
        v-if="prependInnerIcon"
        class="v-field__icon v-field__prepend-icon"
        @click="$emit('click:prepend-inner', $event)"
      >
        <slot
          name="prepend-icon"
          :icon="prependInnerIcon"
        >
          {{ prependInnerIcon }}
        </slot>
      </div>

      <!-- 前置插槽 -->
      <slot
        name="prepend-inner"
        v-bind="slotProps"
      />
    </div>

    <!-- 字段主体 -->
    <div
      class="v-field__field"
      data-no-activator=""
    >
      <!-- 浮动标签（filled/solo 变体） -->
      <label
        v-if="hasFloatingLabel && ['filled', 'solo'].includes(variant)"
        ref="floatingLabelRef"
        :class="floatingLabelClasses"
        :for="fieldId"
        class="v-field__label v-field__label--floating"
      >
        <slot
          name="label"
          v-bind="{ ...slotProps, label }"
        >
          {{ label }}
        </slot>
      </label>

      <!-- 普通标签（未激活时的 outlined 变体也使用） -->
      <label
        v-if="hasLabel && (variant !== 'outlined' || !isActive)"
        ref="labelRef"
        :class="labelClasses"
        :for="fieldId"
        class="v-field__label"
      >
        <slot
          name="label"
          v-bind="{ ...slotProps, label }"
        >
          {{ label }}
        </slot>
      </label>

      <!-- 输入元素插槽 -->
      <slot
        v-bind="{
          ...slotProps,
          props: {
            id: fieldId,
            class: 'v-field__input',
            'aria-describedby': messagesId,
            onFocus: handleFocus,
            onBlur: handleBlur,
          },
        }"
      >
        <div
          :id="fieldId"
          class="v-field__input"
          :aria-describedby="messagesId"
        />
      </slot>
    </div>

    <!-- 清除按钮 -->
    <div
      v-if="hasClear && (dirty || persistentClear)"
      class="v-field__clearable"
      @mousedown.prevent.stop
    >
      <div
        class="v-field__icon v-field__clear-icon"
        @click="$emit('click:clear', $event)"
      >
        <slot
          name="clear"
          v-bind="slotProps"
        >
          {{ clearIcon }}
        </slot>
      </div>
    </div>

    <!-- 后置内部区域 -->
    <div
      v-if="hasAppend"
      class="v-field__append-inner"
      @mousedown.prevent.stop
    >
      <!-- 后置插槽 -->
      <slot
        name="append-inner"
        v-bind="slotProps"
      />

      <!-- 后置图标 -->
      <div
        v-if="appendInnerIcon"
        class="v-field__icon v-field__append-icon"
        @click="$emit('click:append-inner', $event)"
      >
        <slot
          name="append-icon"
          :icon="appendInnerIcon"
        >
          {{ appendInnerIcon }}
        </slot>
      </div>
    </div>

    <!-- 轮廓线系统 -->
    <div
      :class="outlineClasses"
      class="v-field__outline"
    >
      <!-- outlined 变体的三段式轮廓 -->
      <template v-if="variant === 'outlined'">
        <div class="v-field__outline__start" />

        <!-- 只有在激活且有浮动标签时才显示 notch -->
        <div
          v-if="hasFloatingLabel && isActive"
          class="v-field__outline__notch"
        >
          <!-- :class="floatingLabelClasses" -->
          <label
            ref="floatingLabelRef"
            :for="fieldId"
            class="v-field__label v-field__label--floating"
          >
            <slot
              name="label"
              v-bind="{ ...slotProps, label }"
            >
              {{ label }}
            </slot>
          </label>
        </div>

        <div class="v-field__outline__end" />
      </template>

      <!-- underlined/plain 变体的浮动标签 -->
      <label
        v-if="['underlined', 'plain'].includes(variant) && hasFloatingLabel"
        ref="floatingLabelRef"
        :class="floatingLabelClasses"
        :for="fieldId"
        class="v-field__label v-field__label--floating"
      >
        <slot
          name="label"
          v-bind="{ ...slotProps, label }"
        >
          {{ label }}
        </slot>
      </label>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, useId, useSlots } from 'vue'

  // ===== Props 定义 =====
  interface VFieldProps {
    // 基础属性
    label?: string
    variant?: 'filled' | 'outlined' | 'underlined' | 'solo' | 'plain'
    disabled?: boolean
    readonly?: boolean
    error?: boolean
    active?: boolean
    dirty?: boolean
    focused?: boolean
    loading?: boolean

    // 图标
    prependInnerIcon?: string
    appendInnerIcon?: string
    clearIcon?: string
    clearable?: boolean
    persistentClear?: boolean

    // 样式
    color?: string
    bgColor?: string
    flat?: boolean
    singleLine?: boolean
    centerAffix?: boolean
    reverse?: boolean
  }

  const props = withDefaults(defineProps<VFieldProps>(), {
    variant: 'filled',
    disabled: false,
    readonly: false,
    error: false,
    active: false,
    dirty: false,
    loading: false,
    clearable: false,
    persistentClear: false,
    clearIcon: '×',
    flat: false,
    singleLine: false,
    centerAffix: true,
    reverse: false,
  })

  // ===== Emits 定义 =====
  const emit = defineEmits<{
    'click:clear': [event: MouseEvent]
    'click:prepend-inner': [event: MouseEvent]
    'click:append-inner': [event: MouseEvent]
    'update:focused': [focused: boolean]
  }>()

  const slots = useSlots()

  // ===== 基础状态 =====
  const fieldId = useId()
  const messagesId = computed(() => `${fieldId}-messages`)
  const labelRef = ref<HTMLLabelElement>()
  const floatingLabelRef = ref<HTMLLabelElement>()
  const controlRef = ref<HTMLElement>()
  const isFocused = ref(false)

  // ===== 计算属性 =====
  const isActive = computed(
    () => props.dirty || props.active || props.focused || isFocused.value
  )
  const hasLabel = computed(() => !!(props.label || slots.label))
  const hasFloatingLabel = computed(() => !props.singleLine && hasLabel.value)
  const isPlainOrUnderlined = computed(() =>
    ['plain', 'underlined'].includes(props.variant)
  )

  const hasPrepend = computed(
    () => !!(slots['prepend-inner'] || props.prependInnerIcon)
  )
  const hasClear = computed(
    () => !!(props.clearable || slots.clear) && !props.disabled
  )
  const hasAppend = computed(
    () => !!(slots['append-inner'] || props.appendInnerIcon || hasClear.value)
  )

  // ===== 样式类 =====
  const fieldClasses = computed(() => [
    'v-field',
    {
      'v-field--active': isActive.value,
      'v-field--appended': hasAppend.value,
      'v-field--center-affix': props.centerAffix && !isPlainOrUnderlined.value,
      'v-field--disabled': props.disabled,
      'v-field--dirty': props.dirty,
      'v-field--error': props.error,
      'v-field--flat': props.flat,
      'v-field--has-background': !!props.bgColor,
      'v-field--persistent-clear': props.persistentClear,
      'v-field--prepended': hasPrepend.value,
      'v-field--reverse': props.reverse,
      'v-field--single-line': props.singleLine,
      'v-field--no-label': !hasLabel.value,
      [`v-field--variant-${props.variant}`]: true,
    },
  ])

  const fieldStyles = computed(() => ({
    backgroundColor: props.bgColor,
    color: props.color,
  }))

  const labelClasses = computed(() => [
    'v-field__label',
    {
      'v-field__label--active': isActive.value,
      'v-field__label--focused': isFocused.value,
    },
  ])

  const floatingLabelClasses = computed(() => [
    'v-field__label',
    'v-field__label--floating',
    {
      'v-field__label--active': isActive.value,
      'v-field__label--focused': isFocused.value,
    },
  ])

  const outlineClasses = computed(() => [
    'v-field__outline',
    {
      'v-field__outline--focused': isFocused.value,
    },
  ])

  // ===== 插槽属性 =====
  const slotProps = computed(() => ({
    isActive,
    isFocused,
    controlRef,
    focus: () => {
      isFocused.value = true
      emit('update:focused', true)
    },
    blur: () => {
      isFocused.value = false
      emit('update:focused', false)
    },
  }))

  // ===== 样式计算 =====

  // ===== 事件处理 =====
  const handleClick = (e: MouseEvent) => {
    if (e.target !== document.activeElement) {
      e.preventDefault()
    }
  }

  const handleFocus = () => {
    isFocused.value = true
  }

  const handleBlur = () => {
    isFocused.value = false
  }
</script>

<script lang="ts">
  export default {
    name: 'VField',
  }
</script>

<style lang="scss">
  .v-field {
    display: flex;
    align-items: stretch;
    position: relative;
    min-height: 56px;
    border-radius: 4px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: text;
    font-family: 'Roboto', sans-serif;

    // 背景覆盖层
    &__overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: transparent;
      border-radius: inherit;
      pointer-events: none;
      transition: background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    // 加载条
    &__loader {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: rgba(var(--v-theme-primary), 0.12);
      border-radius: 0 0 4px 4px;
      overflow: hidden;

      &-progress {
        height: 100%;
        background: rgb(var(--v-theme-primary));
        animation: loader-progress 1.5s infinite;
        transform: translateX(-100%);
      }
    }

    // 前置内部区域
    &__prepend-inner {
      display: flex;
      align-items: center;
      padding-left: 16px;
      padding-right: 4px;
    }

    // 后置内部区域
    &__append-inner {
      display: flex;
      align-items: center;
      padding-left: 4px;
      padding-right: 16px;
    }

    // 清除按钮区域
    &__clearable {
      display: flex;
      align-items: center;
      padding-right: 12px;
    }

    // 图标样式
    &__icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      cursor: pointer;
      color: rgba(var(--v-theme-on-surface), 0.6);
      transition: color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      font-size: 20px;

      &:hover {
        color: rgb(var(--v-theme-primary));
      }
    }

    // 字段主体
    &__field {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
      min-height: inherit;
      padding: 0;
    }

    // 标签样式
    &__label {
      position: absolute;
      left: 16px;
      color: rgba(var(--v-theme-on-surface), 0.6);
      font-size: 16px;
      line-height: 1;
      pointer-events: none;
      transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
      transform-origin: left top;
      max-width: calc(100% - 32px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      // &--floating {
      //   top: 50%;
      //   transform: translateY(-50%);
      // }

      &--active {
        top: 12px;
        font-size: 12px;
        transform: translateY(0) scale(0.75);
        transform-origin: left top;
        color: rgb(var(--v-theme-primary));
      }

      &--focused {
        color: rgb(var(--v-theme-primary));
      }
    }

    // 输入元素
    &__input {
      background: transparent;
      border: none;
      outline: none;
      font-size: 16px;
      line-height: 1.5;
      color: rgba(var(--v-theme-on-surface), 0.87);
      width: 100%;
      min-height: 24px;
      padding: 20px 16px 8px;
      margin: 0;

      &::placeholder {
        color: rgba(var(--v-theme-on-surface), 0.6);
        opacity: 1;
      }

      &:disabled {
        cursor: not-allowed;
        color: rgba(var(--v-theme-on-surface), 0.38);
      }
    }

    // 轮廓线系统
    &__outline {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: inherit;
      pointer-events: none;
      display: flex;

      &__start {
        flex: none;
        width: 12px;
        border-top: 1px solid rgba(var(--v-theme-outline), 1);
        border-bottom: 1px solid rgba(var(--v-theme-outline), 1);
        border-left: 1px solid rgba(var(--v-theme-outline), 1);
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }

      &__notch {
        flex: none;
        position: relative;
        max-width: calc(100% - 24px);

        &::before,
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          border-top: 1px solid rgba(var(--v-theme-outline), 1);
          transition: opacity 0.2s ease;
        }

        &::after {
          top: auto;
          bottom: 0;
          border-top: none;
          border-bottom: 1px solid rgba(var(--v-theme-outline), 1);
        }

        .v-field__label.v-field__label--floating {
          left: 0;
          // background: rgb(var(--v-theme-surface));
          padding: 0 4px;
          font-size: 12px;
          transform: translateY(-50%);
          // top: 50%;
          color: rgb(var(--v-theme-primary));
          white-space: nowrap;
          overflow: visible;
          text-overflow: unset;
          position: static;
          z-index: 1;
        }
      }

      &__end {
        flex: 1;
        border-top: 1px solid rgba(var(--v-theme-outline), 1);
        border-bottom: 1px solid rgba(var(--v-theme-outline), 1);
        border-right: 1px solid rgba(var(--v-theme-outline), 1);
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }

      &--focused {
        .v-field__outline__start,
        .v-field__outline__end,
        .v-field__outline__notch {
          border-color: rgb(var(--v-theme-primary));
          border-width: 2px;
        }

        .v-field__outline__notch {
          &::before {
            opacity: 0;
          }
        }
      }
    }

    // 变体样式
    &--variant-filled {
      background: rgba(var(--v-theme-on-surface), 0.04);
      border-radius: 4px 4px 0 0;
      border-bottom: 1px solid rgba(var(--v-theme-outline), 1);

      &::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: rgba(var(--v-theme-on-surface), 0.42);
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background: rgb(var(--v-theme-primary));
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateX(-50%);
      }

      &:hover {
        background: rgba(var(--v-theme-on-surface), 0.08);

        &::before {
          background: rgba(var(--v-theme-on-surface), 0.87);
        }
      }

      &.v-field--focused {
        &::after {
          width: 100%;
        }
      }

      .v-field__outline {
        display: none;
      }

      .v-field__input {
        padding: 25px 16px 8px;
      }

      .v-field__label {
        &--floating {
          top: 50%;
          transform: translateY(-50%);
        }

        &--active {
          top: 12px;
          font-size: 12px;
          transform: translateY(0) scale(0.75);
        }
      }
    }

    &--variant-outlined {
      background: transparent;

      &:hover {
        .v-field__outline__start,
        .v-field__outline__end,
        .v-field__outline__notch {
          border-color: rgba(var(--v-theme-on-surface), 0.87);
        }

        .v-field__outline__notch::before,
        .v-field__outline__notch::after {
          border-color: rgba(var(--v-theme-on-surface), 0.87);
        }
      }

      .v-field__input {
        padding: 16px;
      }

      // .v-field__label {
      //   &--floating {
      //     top: 50%;
      //     transform: translateY(-50%);
      //   }

      //   &--active {
      //     top: -6px;
      //     left: 0;
      //     background: rgb(var(--v-theme-surface));
      //     padding: 0 4px;
      //     font-size: 12px;
      //     transform: translateY(0) scale(0.75);
      //     color: rgb(var(--v-theme-primary));
      //   }
      // }

      // 当标签激活时，隐藏 notch 的顶部边框
      &.v-field--active {
        .v-field__outline__notch::before {
          opacity: 0;
        }
      }
    }

    &--variant-underlined {
      background: transparent;
      border-radius: 0;
      min-height: 48px;

      &::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: rgba(var(--v-theme-on-surface), 0.42);
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background: rgb(var(--v-theme-primary));
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateX(-50%);
      }

      &:hover {
        &::before {
          background: rgba(var(--v-theme-on-surface), 0.87);
        }
      }

      &.v-field--focused {
        &::after {
          width: 100%;
        }
      }

      .v-field__outline {
        display: none;
      }

      .v-field__input {
        padding: 8px 0;
      }

      .v-field__label {
        left: 0;

        &--floating {
          top: 50%;
          transform: translateY(-50%);
        }

        &--active {
          top: -20px;
          font-size: 12px;
          transform: translateY(0) scale(0.75);
        }
      }
    }

    &--variant-solo {
      background: rgb(var(--v-theme-surface));
      border-radius: 4px;
      box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2),
        0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);

      &:hover {
        box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2),
          0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
      }

      &.v-field--focused {
        box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
          0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
      }

      .v-field__outline {
        display: none;
      }

      .v-field__input {
        padding: 20px 16px 8px;
      }

      .v-field__label {
        &--floating {
          top: 50%;
          transform: translateY(-50%);
        }

        &--active {
          top: 12px;
          font-size: 12px;
          transform: translateY(0) scale(0.75);
        }
      }
    }

    &--variant-plain {
      background: transparent;
      border: none;
      min-height: auto;
      border-radius: 0;

      .v-field__outline {
        display: none;
      }

      .v-field__input {
        padding: 8px 0;
      }

      .v-field__label {
        left: 0;

        &--floating {
          top: 50%;
          transform: translateY(-50%);
        }

        &--active {
          top: -20px;
          font-size: 12px;
          transform: translateY(0) scale(0.75);
        }
      }
    }

    // 状态样式
    &--active {
      .v-field__label {
        color: rgb(var(--v-theme-primary));
      }
    }

    &--focused {
      .v-field__overlay {
        background: rgba(var(--v-theme-primary), 0.04);
      }

      .v-field__outline__start,
      .v-field__outline__end,
      .v-field__outline__notch {
        border-color: rgb(var(--v-theme-primary));
        border-width: 2px;
      }

      .v-field__label {
        color: rgb(var(--v-theme-primary));
      }
    }

    &--error {
      .v-field__label {
        color: rgb(var(--v-theme-error));
      }

      .v-field__outline__start,
      .v-field__outline__end,
      .v-field__outline__notch {
        border-color: rgb(var(--v-theme-error));
      }

      &.v-field--variant-filled {
        &::before {
          background: rgb(var(--v-theme-error));
        }

        &::after {
          background: rgb(var(--v-theme-error));
        }
      }

      &.v-field--variant-underlined {
        &::before {
          background: rgb(var(--v-theme-error));
        }

        &::after {
          background: rgb(var(--v-theme-error));
        }
      }
    }

    &--disabled {
      opacity: 0.38;
      pointer-events: none;
      cursor: not-allowed;

      .v-field__input {
        color: rgba(var(--v-theme-on-surface), 0.38);
      }

      .v-field__label {
        color: rgba(var(--v-theme-on-surface), 0.38);
      }

      .v-field__outline__start,
      .v-field__outline__end,
      .v-field__outline__notch {
        border-color: rgba(var(--v-theme-outline), 0.38);
        border-style: dotted;
      }

      &.v-field--variant-filled {
        background: rgba(var(--v-theme-on-surface), 0.02);

        &::before {
          background: rgba(var(--v-theme-on-surface), 0.38);
          border-style: dotted;
        }
      }

      &.v-field--variant-underlined {
        &::before {
          background: rgba(var(--v-theme-on-surface), 0.38);
          border-style: dotted;
        }
      }
    }

    &--dirty {
      .v-field__label {
        color: rgba(var(--v-theme-on-surface), 0.6);
      }
    }

    // 其他修饰符
    &--flat {
      box-shadow: none !important;
    }

    &--single-line {
      .v-field__label {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    &--center-affix {
      .v-field__prepend-inner,
      .v-field__append-inner {
        align-self: center;
      }
    }

    &--reverse {
      flex-direction: row-reverse;
    }

    &--no-label {
      .v-field__input {
        padding-top: 16px;
        padding-bottom: 16px;
      }
    }
  }

  // CSS 变量定义 (Material Design 3 主题)
  :root {
    --v-theme-primary: 103, 80, 164;
    --v-theme-on-primary: 255, 255, 255;
    --v-theme-primary-container: 234, 221, 255;
    --v-theme-on-primary-container: 33, 0, 93;
    --v-theme-secondary: 98, 91, 113;
    --v-theme-on-secondary: 255, 255, 255;
    --v-theme-secondary-container: 232, 222, 248;
    --v-theme-on-secondary-container: 30, 25, 43;
    --v-theme-tertiary: 125, 82, 96;
    --v-theme-on-tertiary: 255, 255, 255;
    --v-theme-tertiary-container: 255, 216, 228;
    --v-theme-on-tertiary-container: 55, 11, 30;
    --v-theme-error: 186, 26, 26;
    --v-theme-on-error: 255, 255, 255;
    --v-theme-error-container: 255, 218, 214;
    --v-theme-on-error-container: 65, 0, 2;
    --v-theme-background: 255, 251, 254;
    --v-theme-on-background: 28, 27, 31;
    --v-theme-surface: 255, 251, 254;
    --v-theme-on-surface: 28, 27, 31;
    --v-theme-surface-variant: 231, 224, 236;
    --v-theme-on-surface-variant: 73, 69, 79;
    --v-theme-outline: 121, 116, 126;
    --v-theme-outline-variant: 202, 196, 208;
    --v-theme-shadow: 0, 0, 0;
    --v-theme-scrim: 0, 0, 0;
    --v-theme-inverse-surface: 49, 48, 51;
    --v-theme-inverse-on-surface: 244, 239, 244;
    --v-theme-inverse-primary: 208, 188, 255;
  }

  // 动画
  @keyframes loader-progress {
    0% {
      transform: translateX(-100%);
    }
    50% {
      transform: translateX(0%);
    }
    100% {
      transform: translateX(100%);
    }
  }
</style>
