<!--
  TagInner.vue - DOM渲染层
  
  职责：
  - 纯DOM元素渲染
  - 最小化职责
  - 无状态组件
  - 无障碍访问支持
-->

<template>
  <span
    :aria-label="ariaLabel"
    :role="role"
    :aria-checked="ariaChecked"
    :aria-disabled="ariaDisabled"
    :tabindex="tabindex"
    @click="$emit('click', $event)"
    @keydown="$emit('keydown', $event)"
  >
    <slot />
  </span>
</template>

<script setup lang="ts">
  import type { TagInnerProps } from './types'

  /**
   * TagInner - DOM渲染层
   *
   * 特点：
   * 1. 纯DOM渲染
   * 2. 无逻辑处理
   * 3. 最小化职责
   * 4. 支持无障碍访问
   */

  defineProps<TagInnerProps>()

  defineEmits<{
    click: [event: MouseEvent]
    keydown: [event: KeyboardEvent]
  }>()
</script>

<script lang="ts">
  export default {
    name: 'TagInner',
  }
</script>
