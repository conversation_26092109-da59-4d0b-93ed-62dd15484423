<template>
  <div class="scrollbar-demo">
    <div class="demo-header">
      <h1>Scrollbar 滚动条组件</h1>
      <p>
        一个美观的自定义滚动条组件，可以包裹任何元素，让滚动条变成好看的样式！
      </p>
    </div>

    <!-- 基础示例 -->
    <div class="demo-section">
      <h2>基础用法</h2>
      <p>最简单的用法，设置最大高度后内容超出时会显示滚动条</p>

      <div class="demo-box">
        <Scrollbar max-height="200px">
          <div class="content-box">
            <h3>长内容示例</h3>
            <p
              v-for="i in 20"
              :key="i"
            >
              这是第 {{ i }} 行内容。Lorem ipsum dolor sit amet, consectetur
              adipiscing elit, sed do eiusmod tempor incididunt ut labore et
              dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
              exercitation ullamco laboris.
            </p>
          </div>
        </Scrollbar>
      </div>
    </div>

    <!-- 水平滚动 -->
    <div class="demo-section">
      <h2>水平滚动</h2>
      <p>当内容宽度超出容器时，会显示水平滚动条</p>

      <div class="demo-box">
        <Scrollbar
          max-width="400px"
          max-height="120px"
        >
          <div class="horizontal-content">
            <div
              class="card"
              v-for="i in 8"
              :key="i"
            >
              <h4>卡片 {{ i }}</h4>
              <p>这是卡片内容</p>
            </div>
          </div>
        </Scrollbar>
      </div>
    </div>

    <!-- 不同尺寸 -->
    <div class="demo-section">
      <h2>不同尺寸</h2>
      <p>提供 small、medium、large 三种尺寸</p>

      <div class="size-grid">
        <div class="size-item">
          <h4>Small</h4>
          <Scrollbar
            size="small"
            max-height="150px"
          >
            <div class="content-box">
              <p
                v-for="i in 12"
                :key="i"
              >
                小尺寸滚动条 - 第 {{ i }} 行
              </p>
            </div>
          </Scrollbar>
        </div>

        <div class="size-item">
          <h4>Medium (默认)</h4>
          <Scrollbar
            size="medium"
            max-height="150px"
          >
            <div class="content-box">
              <p
                v-for="i in 12"
                :key="i"
              >
                中等尺寸滚动条 - 第 {{ i }} 行
              </p>
            </div>
          </Scrollbar>
        </div>

        <div class="size-item">
          <h4>Large</h4>
          <Scrollbar
            size="large"
            max-height="150px"
          >
            <div class="content-box">
              <p
                v-for="i in 12"
                :key="i"
              >
                大尺寸滚动条 - 第 {{ i }} 行
              </p>
            </div>
          </Scrollbar>
        </div>
      </div>
    </div>

    <!-- 主题 -->
    <div class="demo-section">
      <h2>主题变体</h2>
      <p>支持浅色和深色主题</p>

      <div class="theme-grid">
        <div class="theme-item">
          <h4>浅色主题</h4>
          <Scrollbar
            theme="light"
            max-height="150px"
            class="light-demo"
          >
            <div class="content-box">
              <p
                v-for="i in 12"
                :key="i"
              >
                浅色主题滚动条 - 第 {{ i }} 行
              </p>
            </div>
          </Scrollbar>
        </div>

        <div class="theme-item">
          <h4>深色主题</h4>
          <Scrollbar
            theme="dark"
            max-height="150px"
            class="dark-demo"
          >
            <div class="content-box dark-content">
              <p
                v-for="i in 12"
                :key="i"
              >
                深色主题滚动条 - 第 {{ i }} 行
              </p>
            </div>
          </Scrollbar>
        </div>
      </div>
    </div>

    <!-- 始终显示 -->
    <div class="demo-section">
      <h2>始终显示</h2>
      <p>设置 always 属性可以让滚动条始终显示</p>

      <div class="demo-box">
        <Scrollbar
          :always="true"
          max-height="150px"
        >
          <div class="content-box">
            <p
              v-for="i in 12"
              :key="i"
            >
              始终显示滚动条 - 第 {{ i }} 行内容
            </p>
          </div>
        </Scrollbar>
      </div>
    </div>

    <!-- 程序控制 -->
    <div class="demo-section">
      <h2>程序控制</h2>
      <p>可以通过 ref 调用方法来控制滚动位置</p>

      <div class="demo-box">
        <div class="control-panel">
          <button
            @click="scrollToTop"
            class="control-btn"
          >
            滚动到顶部
          </button>
          <button
            @click="scrollToMiddle"
            class="control-btn"
          >
            滚动到中间
          </button>
          <button
            @click="scrollToBottom"
            class="control-btn"
          >
            滚动到底部
          </button>
        </div>

        <Scrollbar
          ref="controlScrollbar"
          max-height="200px"
        >
          <div class="content-box">
            <p
              v-for="i in 25"
              :key="i"
            >
              可控制的滚动条 - 第 {{ i }} 行内容
            </p>
          </div>
        </Scrollbar>
      </div>
    </div>

    <!-- 原生模式 -->
    <div class="demo-section">
      <h2>原生滚动条模式</h2>
      <p>设置 native 属性使用原生滚动条（但样式会被美化）</p>

      <div class="demo-box">
        <Scrollbar
          :native="true"
          max-height="150px"
        >
          <div class="content-box">
            <p
              v-for="i in 12"
              :key="i"
            >
              原生滚动条模式 - 第 {{ i }} 行内容
            </p>
          </div>
        </Scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Scrollbar } from '@speed-ui/ui'

  const controlScrollbar = ref()

  const scrollToTop = () => {
    controlScrollbar.value?.scrollTop(0)
  }

  const scrollToMiddle = () => {
    controlScrollbar.value?.scrollTop(200)
  }

  const scrollToBottom = () => {
    controlScrollbar.value?.scrollTop(999999)
  }
</script>

<style scoped>
  .scrollbar-demo {
    padding: 24px;
    max-width: 1000px;
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .demo-header {
    text-align: center;
    margin-bottom: 48px;
  }

  .demo-header h1 {
    font-size: 32px;
    color: #2c3e50;
    margin-bottom: 16px;
  }

  .demo-header p {
    font-size: 16px;
    color: #7f8c8d;
    line-height: 1.6;
  }

  .demo-section {
    margin-bottom: 48px;
  }

  .demo-section h2 {
    font-size: 24px;
    color: #2c3e50;
    margin-bottom: 8px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
  }

  .demo-section p {
    color: #7f8c8d;
    margin-bottom: 24px;
    line-height: 1.6;
  }

  .demo-box {
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    padding: 24px;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .content-box {
    padding: 16px;
  }

  .content-box p {
    margin: 8px 0;
    line-height: 1.6;
    color: #2c3e50;
  }

  .horizontal-content {
    display: flex;
    gap: 16px;
    padding: 16px;
    width: 800px;
  }

  .card {
    flex-shrink: 0;
    width: 150px;
    padding: 16px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
  }

  .card h4 {
    margin: 0 0 8px 0;
    color: #495057;
  }

  .card p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
  }

  .size-grid,
  .theme-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
  }

  .size-item,
  .theme-item {
    border: 1px solid #e1e8ed;
    border-radius: 6px;
    padding: 16px;
    background: #fff;
  }

  .size-item h4,
  .theme-item h4 {
    margin: 0 0 16px 0;
    color: #2c3e50;
    font-size: 16px;
  }

  .light-demo {
    background: #f8f9fa;
  }

  .dark-demo {
    background: #2d3748;
  }

  .dark-content p {
    color: #e2e8f0 !important;
  }

  .control-panel {
    margin-bottom: 16px;
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }

  .control-btn {
    padding: 8px 16px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s;
  }

  .control-btn:hover {
    background: #2980b9;
  }
</style>
