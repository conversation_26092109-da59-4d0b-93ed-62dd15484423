<div align="center">
  <img src="https://via.placeholder.com/120x120/409EFF/FFFFFF?text=Speed" alt="Speed UI Logo" width="120" height="120">
  
  # ⚡ Speed UI
  
  <p align="center">
    基于 Vue 3 + TypeScript + Vite 的现代化组件库
  </p>

  <p align="center">
    <img src="https://img.shields.io/badge/Vue-3.x-brightgreen?style=flat-square&logo=vue.js" alt="Vue 3">
    <img src="https://img.shields.io/badge/TypeScript-5.x-blue?style=flat-square&logo=typescript" alt="TypeScript">
    <img src="https://img.shields.io/badge/Vite-5.x-646CFF?style=flat-square&logo=vite" alt="Vite">
    <img src="https://img.shields.io/badge/License-MIT-green?style=flat-square" alt="License">
  </p>
</div>

---

## ✨ 特性

- **多级联动选择器** - 支持任意级别的联动选择，使用简单
- **表单验证集成** - 集成 VeeValidate，支持复杂验证场景
- **完整 TypeScript 支持** - 提供完善的类型定义
- **现代化设计** - 简洁优雅的视觉效果
- **按需加载** - 轻量级设计，优化包体积
- **响应式布局** - 支持移动端和桌面端
- **国际化支持** - 内置多语言切换

## 📦 组件

### 基础组件
- Button - 按钮组件
- Input - 输入框组件  
- Select - 选择器组件
- Form - 表单组件

### 布局组件
- Row/Col - 栅格布局

### 特色组件
- LinkedSelect - 多级联动选择器
- FormItem - 表单项组件

## 🏗️ 项目结构

```
speed-ui/
├── packages/ui/         # 核心组件库
├── packages/utils/      # 工具函数
├── playground/          # 开发演示环境
└── docs/               # 文档
```

## 🛠️ 开发

```bash
# 安装依赖
pnpm install

# 启动开发
pnpm dev

# 构建
pnpm build
```

## 📄 许可证

MIT License

---

<div align="center">
  <p>
    <strong>⭐ 如果这个项目对你有帮助，请给我们一个 Star！</strong>
  </p>
  
  <p>
    Made with ❤️ by <a href="https://github.com/your-username">Speed UI Team</a>
  </p>
  
  <p>
    <a href="https://github.com/your-username/speed-ui">GitHub</a> •
    <a href="https://speed-ui.dev">文档站点</a> •
    <a href="https://speed-ui.dev/playground">在线演示</a>
  </p>
</div> 