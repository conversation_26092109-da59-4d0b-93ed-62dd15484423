<template>
  <div
    :class="dropdownClasses"
    :style="dropdownStyles"
    role="application"
    :aria-label="ariaLabel"
  >
    <!-- 触发器区域 -->
    <div
      ref="triggerRef"
      class="sp-dropdown__trigger"
      v-bind="triggerProps"
    >
      <slot name="trigger" />
    </div>

    <!-- 浮层内容 -->
    <Teleport :to="popupContainer">
      <Transition name="sp-dropdown">
        <div
          v-if="visible"
          ref="contentRef"
          :class="contentClasses"
          :style="contentStyles"
          role="dialog"
          :aria-hidden="!visible"
          @click="handleContentClick"
          @keydown="handleContentKeydown"
        >
          <!-- 箭头指示器 -->
          <div
            v-if="arrow"
            ref="arrowRef"
            class="sp-dropdown__arrow"
            :style="arrowStyles"
          />

          <!-- 内容区域 -->
          <div class="sp-dropdown__content">
            <slot />
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue'

  defineOptions({
    name: 'SpDropdownInner',
  })

  // Props 接收父组件传递的数据
  const props = defineProps<{
    dropdownClasses: any[]
    dropdownStyles: Record<string, any>
    contentClasses: any[]
    contentStyles: Record<string, any>
    arrowStyles: Record<string, any>
    triggerProps: Record<string, any>
    visible: boolean
    arrow: boolean
    ariaLabel?: string
    popupContainer: string | HTMLElement
  }>()

  // Emits 定义事件
  const emit = defineEmits<{
    contentClick: [event: MouseEvent]
    contentKeydown: [event: KeyboardEvent]
  }>()

  // 内容点击处理
  const handleContentClick = (event: MouseEvent) => {
    emit('contentClick', event)
  }

  // 内容键盘事件处理
  const handleContentKeydown = (event: KeyboardEvent) => {
    emit('contentKeydown', event)
  }

  // 模板引用
  const triggerRef = ref<HTMLElement>()
  const contentRef = ref<HTMLElement>()
  const arrowRef = ref<HTMLElement>()

  // 暴露引用给父组件
  defineExpose({
    triggerRef,
    contentRef,
    arrowRef,
  })
</script>
