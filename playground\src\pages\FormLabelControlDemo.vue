<template>
  <div class="form-label-demo">
    <h1>表单标签统一控制演示</h1>
    
    <!-- 功能说明 -->
    <div class="feature-description">
      <h3>🎯 表单统一标签控制功能</h3>
      <p>sp-form 组件现在支持以下统一配置属性：</p>
      <ul>
        <li><strong>label-position</strong>: 'left' | 'right' | 'top' - 统一设置所有标签位置</li>
        <li><strong>label-width</strong>: string | number - 统一设置所有标签宽度</li>
        <li><strong>hide-label</strong>: boolean - 统一隐藏/显示所有标签</li>
      </ul>
      <p>这些配置会应用到所有 FormItem，但 FormItem 的个别配置仍然可以覆盖表单级别的配置。</p>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <h3>📋 控制面板</h3>
      <div class="controls">
        <div class="control-group">
          <label>标签位置 (label-position):</label>
          <select v-model="formConfig.labelPosition">
            <option value="left">左对齐 (left)</option>
            <option value="right">右对齐 (right)</option>
            <option value="top">顶部 (top)</option>
          </select>
        </div>
        
        <div class="control-group">
          <label>标签宽度 (label-width):</label>
          <input 
            v-model="formConfig.labelWidth" 
            type="text" 
            placeholder="例如: 120px 或 120"
          />
        </div>
        
        <div class="control-group">
          <label>隐藏标签 (hide-label):</label>
          <input 
            v-model="formConfig.hideLabel" 
            type="checkbox"
          />
        </div>
      </div>
    </div>

    <!-- 表单演示 -->
    <div class="demo-container">
      <h3>📝 表单演示</h3>
      <sp-form
        ref="formRef"
        :label-position="formConfig.labelPosition"
        :label-width="formConfig.labelWidth"
        :hide-label="formConfig.hideLabel"
        @submit="onSubmit"
        @invalid="onInvalid"
      >
        <!-- 基础字段 -->
        <sp-form-item 
          label="用户名" 
          name="username" 
          :rules="usernameRules"
          required
        >
          <sp-input
            placeholder="请输入用户名"
            clearable
          />
        </sp-form-item>
        
        <!-- 邮箱字段 -->
        <sp-form-item 
          label="邮箱地址"
          name="email" 
          :rules="emailRules"
          required
        >
          <sp-input
            type="email"
            placeholder="请输入邮箱"
            clearable
          />
        </sp-form-item>
        
        <!-- 密码字段 -->
        <sp-form-item 
          label="密码"
          name="password" 
          :rules="passwordRules"
          required
        >
          <sp-input
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </sp-form-item>
        
        <!-- 带个别配置的字段 - 演示覆盖表单级配置 -->
        <sp-form-item 
          :label="{ text: '年龄（独立配置）', position: 'top', width: '200px' }"
          name="age" 
          :rules="ageRules"
          required
        >
          <sp-input
            type="number"
            placeholder="这个字段有独立的标签配置"
            clearable
          />
        </sp-form-item>
        
        <!-- 隐藏标签的字段 -->
        <sp-form-item 
          :label="{ text: '隐藏标签字段', show: false }"
          name="hiddenLabelField"
        >
          <sp-input
            placeholder="这个字段的标签总是隐藏"
            clearable
          />
        </sp-form-item>
        
        <!-- Select 组件测试 -->
        <sp-form-item 
          label="选择器（受表单配置影响）"
          name="selectField"
        >
          <sp-select
            :options="selectOptions"
            placeholder="请选择一个选项"
            clearable
          />
        </sp-form-item>

        <sp-form-item name="buttons">
          <sp-button type="primary" :toggleable="false" @click="submitForm">
            提交表单
          </sp-button>
          <sp-button type="outline" :toggleable="false" @click="resetForm" style="margin-left: 12px;">
            重置表单
          </sp-button>
        </sp-form-item>
      </sp-form>
    </div>

    <!-- 配置信息显示 -->
    <div class="config-display">
      <h3>⚙️ 当前配置</h3>
      <pre>{{ JSON.stringify(formConfig, null, 2) }}</pre>
      
      <h4>🔍 调试信息</h4>
      <div class="debug-info">
        <p><strong>表单引用:</strong> {{ formRef ? '已获取' : '未获取' }}</p>
        <p><strong>标签位置:</strong> {{ formConfig.labelPosition }}</p>
        <p><strong>标签宽度:</strong> {{ formConfig.labelWidth }}</p>
        <p><strong>隐藏标签:</strong> {{ formConfig.hideLabel }}</p>
      </div>
    </div>

    <!-- 提交结果 -->
    <div v-if="submitResult" class="result-section">
      <h3>{{ submitResult.message }}</h3>
      
      <div v-if="submitResult.status === 'success'" class="form-data-display">
        <h4>📋 表单数据：</h4>
        <pre class="json-display">{{ JSON.stringify(submitResult.formData, null, 2) }}</pre>
      </div>
      
      <div v-else>
        <h4>❌ 验证错误：</h4>
        <pre>{{ JSON.stringify(submitResult.errors, null, 2) }}</pre>
      </div>
      
      <p class="timestamp">提交时间: {{ submitResult.timestamp }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import SpForm from '../../../packages/ui/src/components/Form/Form.vue'
import SpFormItem from '../../../packages/ui/src/components/Form/FormItem.vue'
import SpInput from '../../../packages/ui/src/components/Input/input.vue'
import SpButton from '../../../packages/ui/src/components/button/button.vue'
import SpSelect from '../../../packages/ui/src/components/Select/Select.vue'

const formRef = ref()
const submitResult = ref<any>(null)

// 表单配置
const formConfig = reactive({
  labelPosition: 'top' as 'left' | 'right' | 'top',
  labelWidth: '100px',
  hideLabel: false
})

// Select 选项
const selectOptions = [
  { label: '选项一', value: 'option1' },
  { label: '选项二', value: 'option2' },
  { label: '选项三', value: 'option3' },
]

// 验证规则
const usernameRules = (value: string) => {
  if (!value) return '用户名不能为空'
  if (value.length < 3) return '用户名至少3个字符'
  if (value.length > 20) return '用户名不能超过20个字符'
  if (!/^[a-zA-Z0-9_]+$/.test(value)) return '用户名只能包含字母、数字和下划线'
  return true
}

const emailRules = (value: string) => {
  if (!value) return '邮箱不能为空'
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(value)) return '请输入有效的邮箱地址'
  return true
}

const passwordRules = (value: string) => {
  if (!value) return '密码不能为空'
  if (value.length < 6) return '密码至少6个字符'
  return true
}

const ageRules = (value: string | number) => {
  if (!value) return '年龄不能为空'
  const numValue = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(numValue)) return '年龄必须是数字'
  if (numValue < 18) return '年龄不能小于18岁'
  if (numValue > 100) return '年龄不能大于100岁'
  return true
}

// 表单事件处理
const onSubmit = (values: Record<string, any>) => {
  console.log('🎉 表单提交成功! 获取到的数据:', values)
  
  submitResult.value = {
    status: 'success',
    message: '✅ 表单提交成功！',
    formData: values,
    timestamp: new Date().toLocaleString()
  }
}

const onInvalid = (errors: Record<string, string>) => {
  console.log('表单验证失败:', errors)
  submitResult.value = {
    status: 'error',
    message: '❌ 表单验证失败，请检查输入内容',
    errors: errors,
    timestamp: new Date().toLocaleString()
  }
}

const submitForm = async () => {
  try {
    await formRef.value?.validate()
  } catch (error) {
    console.error('表单提交失败:', error)
  }
}

const resetForm = () => {
  formRef.value?.resetFields()
  submitResult.value = null
}
</script>

<style scoped>
.form-label-demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.form-label-demo h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 30px;
}

.feature-description {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  margin-bottom: 20px;
}

.feature-description h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #495057;
}

.feature-description p {
  margin-bottom: 10px;
  color: #6c757d;
  line-height: 1.6;
}

.feature-description ul {
  margin: 10px 0;
  padding-left: 20px;
}

.feature-description li {
  margin-bottom: 8px;
  color: #6c757d;
  line-height: 1.5;
}

.feature-description strong {
  color: #495057;
  font-weight: 600;
}

.control-panel {
  background: #e3f2fd;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #bbdefb;
  margin-bottom: 20px;
}

.control-panel h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #1976d2;
}

.controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.control-group label {
  font-weight: 600;
  color: #1976d2;
  font-size: 14px;
}

.control-group select,
.control-group input[type="text"] {
  padding: 8px 12px;
  border: 1px solid #90caf9;
  border-radius: 4px;
  font-size: 14px;
}

.control-group input[type="checkbox"] {
  width: 20px;
  height: 20px;
}

.demo-container {
  background: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e4e7ed;
  margin-bottom: 20px;
}

.demo-container h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #606266;
}

.config-display {
  background: #fffbf0;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #ffd180;
  margin-bottom: 20px;
}

.config-display h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #f57c00;
}

.config-display pre {
  background-color: #fff;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #ffcc80;
  font-size: 12px;
  color: #303133;
  overflow-x: auto;
  white-space: pre-wrap;
}

.debug-info {
  background-color: #fff;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #ffcc80;
  margin-top: 10px;
}

.debug-info p {
  margin: 8px 0;
  color: #303133;
}

.result-section {
  margin-top: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.result-section h3 {
  margin-top: 0;
  color: #606266;
}

.form-data-display {
  margin-top: 20px;
}

.json-display {
  background-color: #fff;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  font-size: 12px;
  color: #303133;
  overflow-x: auto;
  white-space: pre-wrap;
}

.timestamp {
  margin-top: 10px;
  text-align: right;
  font-size: 0.8em;
  color: #909399;
}
</style> 