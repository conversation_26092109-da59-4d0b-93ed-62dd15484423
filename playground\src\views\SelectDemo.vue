<template>
  <div class="select-demo">
    <h1>🎯 Select 选择器组件演示</h1>
    <p>基于 Dropdown + List + InputAffix 组合实现的高性能选择器组件</p>

    <!-- 基础用法 -->
    <section class="demo-section">
      <h2>基础用法 (Basic)</h2>
      <div class="demo-container">
        <div class="select-wrapper">
          <sp-select
            v-model:value="basicValue"
            placeholder="请选择一个选项"
            style="width: 400px"
            variant="simple"
            clearable
          >
            <sp-select-option
              v-for="item in 25"
              :value="item"
              :label="'选项' + item"
            />
          </sp-select>
          <button
            @click="basicValue = 1"
            style="margin-top: 10px; padding: 5px 10px"
          >
            测试设置值为1
          </button>
          <button
            @click="basicValue = null"
            style="margin-left: 10px; padding: 5px 10px"
          >
            清空值
          </button>
        </div>
        <div class="demo-info">
          <h4>选中值:</h4>
          <code>{{ JSON.stringify(basicValue) }}</code>
          <p><small>基础的单选功能</small></p>
        </div>
      </div>
    </section>

    <!-- 多选模式 -->
    <section class="demo-section">
      <h2>多选模式 2(Multiple)</h2>
      <div class="demo-container">
        <div class="select-wrapper">
          <sp-select
            v-model:value="multipleValue"
            :options="basicOptions"
            placeholder="请选择多个选项"
            multiple
            clearable
            variant="simple"
            style="width: 300px"
          />
        </div>
        <div class="demo-info">
          <h4>选中值:</h4>
          <code>{{ JSON.stringify(multipleValue) }}</code>
          <p><small>支持多选和清除功能</small></p>
        </div>
      </div>
    </section>

    <!-- 搜索功能 -->
    <section class="demo-section">
      <h2>搜索功能 (Searchable)</h2>
      <div class="demo-container">
        <div class="select-wrapper">
          <sp-select
            v-model:value="searchValue"
            :options="searchOptions"
            placeholder="搜索并选择"
            searchable
            clearable
            style="width: 250px"
          />
        </div>
        <div class="demo-info">
          <h4>选中值:</h4>
          <code>{{ JSON.stringify(searchValue) }}</code>
          <p><small>支持输入关键词过滤选项</small></p>
        </div>
      </div>
    </section>

    <!-- 多选搜索 -->
    <section class="demo-section">
      <h2>多选搜索 (Multiple + Searchable)</h2>
      <div class="demo-container">
        <div class="select-wrapper">
          <sp-select
            v-model:value="multiSearchValue"
            :options="searchOptions"
            placeholder="搜索并多选"
            multiple
            searchable
            clearable
            :max-tag-count="2"
            style="width: 350px"
          />
        </div>
        <div class="demo-info">
          <h4>选中值:</h4>
          <code>{{ JSON.stringify(multiSearchValue) }}</code>
          <p><small>多选 + 搜索 + 标签折叠显示</small></p>
        </div>
      </div>
    </section>

    <!-- 不同尺寸 -->
    <section class="demo-section">
      <h2>不同尺寸 (Sizes)</h2>
      <div class="demo-container">
        <div class="select-wrapper">
          <div class="size-group">
            <div class="size-item">
              <label>Small:</label>
              <sp-select
                v-model:value="sizeValue1"
                placeholder="小尺寸"
                size="small"
                multiple
                style="width: 150px"
              >
                <sp-select-option
                  v-for="item in 25"
                  :value="item"
                  :label="'选项' + item"
                />
              </sp-select>
            </div>
            <div class="size-item">
              <label>Medium:</label>
              <sp-select
                v-model:value="sizeValue2"
                :options="basicOptions"
                placeholder="中等尺寸"
                multiple
                size="default"
                style="width: 180px"
              >
                <sp-select-option
                  v-for="item in 25"
                  :value="item"
                  :label="'选项' + item"
                />
              </sp-select>
            </div>
            <div class="size-item">
              <label>Large:</label>
              <sp-select
                v-model:value="sizeValue3"
                :options="basicOptions"
                placeholder="大尺寸"
                multiple
                size="large"
                style="width: 200px"
              >
                <sp-select-option
                  v-for="item in 25"
                  :value="item"
                  :label="'选项' + item"
                />
              </sp-select>
            </div>
          </div>
        </div>
        <div class="demo-info">
          <h4>选中值:</h4>
          <code>Small: {{ JSON.stringify(sizeValue1) }}</code>
          <code>Medium: {{ JSON.stringify(sizeValue2) }}</code>
          <code>Large: {{ JSON.stringify(sizeValue3) }}</code>
          <p><small>三种不同的尺寸规格</small></p>
        </div>
      </div>

      <div class="demo-container">
        <div class="select-wrapper">
          <div class="size-group">
            <div class="size-item">
              <label>Small:</label>
              <sp-select
                v-model:value="sizeValue1"
                :options="basicOptions"
                placeholder="小尺寸"
                size="small"
                style="width: 150px"
              />
            </div>
            <div class="size-item">
              <label>Medium:</label>
              <sp-select
                v-model:value="sizeValue2"
                :options="basicOptions"
                placeholder="中等尺寸"
                size="medium"
                style="width: 180px"
              />
            </div>
            <div class="size-item">
              <label>Large:</label>
              <sp-select
                v-model:value="sizeValue3"
                :options="basicOptions"
                placeholder="大尺寸"
                size="large"
                style="width: 200px"
              />
            </div>
          </div>
        </div>
        <div class="demo-info">
          <h4>选中值:</h4>
          <code>Small: {{ JSON.stringify(sizeValue1) }}</code>
          <code>Medium: {{ JSON.stringify(sizeValue2) }}</code>
          <code>Large: {{ JSON.stringify(sizeValue3) }}</code>
          <p><small>三种不同的尺寸规格</small></p>
        </div>
      </div>
    </section>

    <!-- 禁用状态 -->
    <section class="demo-section">
      <h2>禁用状态 (Disabled)</h2>
      <div class="demo-container">
        <div class="select-wrapper">
          <div class="disabled-group">
            <div class="disabled-item">
              <label>禁用选择器:</label>
              <sp-select
                v-model:value="disabledValue1"
                :options="basicOptions"
                placeholder="禁用状态"
                disabled
                style="width: 200px"
              />
            </div>
            <div class="disabled-item">
              <label>只读选择器:</label>
              <sp-select
                v-model:value="disabledValue2"
                :options="basicOptions"
                placeholder="只读状态"
                readonly
                style="width: 200px"
              />
            </div>
          </div>
        </div>
        <div class="demo-info">
          <h4>状态说明:</h4>
          <p>
            •
            <strong>disabled</strong>
            : 完全禁用，无法操作
          </p>
          <p>
            •
            <strong>readonly</strong>
            : 只读模式，可以查看但不能修改
          </p>
        </div>
      </div>
    </section>

    <!-- 加载状态 -->
    <section class="demo-section">
      <h2>加载状态 (Loading)</h2>
      <div class="demo-container">
        <div class="select-wrapper">
          <sp-select
            v-model:value="loadingValue"
            :options="loadingOptions"
            placeholder="加载中..."
            :loading="isLoading"
            style="width: 200px"
          />
          <button
            @click="toggleLoading"
            class="toggle-btn"
          >
            {{ isLoading ? '停止加载' : '开始加载' }}
          </button>
        </div>
        <div class="demo-info">
          <h4>选中值:</h4>
          <code>{{ JSON.stringify(loadingValue) }}</code>
          <p><small>模拟异步数据加载状态</small></p>
        </div>
      </div>
    </section>

    <!-- 简洁样式 -->
    <section class="demo-section">
      <h2>简洁样式 (Simple Variant)</h2>
      <div class="demo-container">
        <div class="select-wrapper">
          <div class="variant-group">
            <div class="variant-item">
              <label>默认样式:</label>
              <sp-select
                v-model:value="variantValue1"
                :options="basicOptions"
                placeholder="默认样式"
                style="width: 200px"
              />
            </div>
            <div class="variant-item">
              <label>简洁样式:</label>
              <sp-select
                v-model:value="variantValue2"
                :options="basicOptions"
                placeholder="简洁样式"
                variant="simple"
                style="width: 200px"
              />
            </div>
          </div>
        </div>
        <div class="demo-info">
          <h4>选中值:</h4>
          <code>默认: {{ JSON.stringify(variantValue1) }}</code>
          <code>简洁: {{ JSON.stringify(variantValue2) }}</code>
          <p><small>简洁样式去除了左边框和主题色，使用淡蓝色背景</small></p>
        </div>
      </div>
    </section>

    <!-- 自定义选项内容 -->
    <section class="demo-section">
      <h2>自定义选项内容 (Custom Options)</h2>
      <div class="demo-container">
        <div class="select-wrapper">
          <sp-select
            v-model:value="customValue"
            :options="customOptions"
            placeholder="选择用户"
            style="width: 250px"
          >
            <template #option="{ option }">
              <div class="custom-option">
                <div
                  class="avatar"
                  :style="{ backgroundColor: option.color }"
                >
                  {{ option.label.charAt(0) }}
                </div>
                <div class="user-info">
                  <div class="name">{{ option.label }}</div>
                  <div class="email">{{ option.email }}</div>
                </div>
              </div>
            </template>
          </sp-select>
        </div>
        <div class="demo-info">
          <h4>选中值:</h4>
          <code>{{ JSON.stringify(customValue) }}</code>
          <p><small>自定义选项显示内容</small></p>
        </div>
      </div>
    </section>

    <!-- 验证状态演示 -->
    <section class="demo-section">
      <h2>✅ 表单验证状态演示</h2>
      <div class="demo-container">
        <div class="select-wrapper">
          <div class="validation-group">
            <div class="validation-item">
              <label>✅ 成功状态 (绿色):</label>
              <sp-select
                v-model:value="successValue"
                :options="basicOptions"
                placeholder="验证成功"
                validate-state="success"
                validate-message="验证成功！数据已保存"
                show-validate-message
                style="width: 250px"
              />
            </div>

            <div class="validation-item">
              <label>⚠️ 警告状态 (黄色):</label>
              <sp-select
                v-model:value="warningValue"
                :options="basicOptions"
                placeholder="需要注意"
                validate-state="warning"
                validate-message="请注意：此选项可能影响其他设置"
                show-validate-message
                style="width: 250px"
              />
            </div>

            <div class="validation-item">
              <label>❌ 错误状态 (红色):</label>
              <sp-select
                v-model:value="errorValue"
                :options="basicOptions"
                placeholder="验证失败"
                validate-state="error"
                validate-message="错误：此字段为必填项"
                show-validate-message
                style="width: 250px"
              />
            </div>
          </div>
        </div>
        <div class="demo-info">
          <h4>验证状态值:</h4>
          <code>成功: {{ JSON.stringify(successValue) }}</code>
          <code>警告: {{ JSON.stringify(warningValue) }}</code>
          <code>错误: {{ JSON.stringify(errorValue) }}</code>
          <p><small>不同验证状态下的边框颜色和消息提示</small></p>
        </div>
      </div>
    </section>

    <!-- 动态验证状态 -->
    <section class="demo-section">
      <h2>🔄 动态验证状态</h2>
      <div class="demo-container">
        <div class="select-wrapper">
          <sp-select
            v-model:value="dynamicValidateValue"
            :options="basicOptions"
            placeholder="请选择一个选项"
            :validate-state="dynamicValidateState"
            :validate-message="dynamicValidateMessage"
            show-validate-message
            clearable
            style="width: 300px"
          />
          <div class="validation-tips">
            <p><strong>💡 验证规则:</strong></p>
            <ul>
              <li>未选择时：无验证状态</li>
              <li>选择"选项一"：成功状态（推荐选项）</li>
              <li>选择"选项二"：警告状态（需要注意）</li>
              <li>选择"选项三"：错误状态（不建议选择）</li>
              <li>其他选项：成功状态</li>
            </ul>
          </div>
        </div>
        <div class="demo-info">
          <h4>当前验证状态:</h4>
          <code>值: {{ JSON.stringify(dynamicValidateValue) }}</code>
          <code>状态: {{ dynamicValidateState || '无验证' }}</code>
          <code>消息: {{ dynamicValidateMessage || '无消息' }}</code>
          <p><small>根据用户选择动态改变验证状态</small></p>
        </div>
      </div>
    </section>

    <!-- 多选验证状态 -->
    <section class="demo-section">
      <h2>🎨 多选验证状态</h2>
      <div class="demo-container">
        <div class="select-wrapper">
          <div class="validation-group">
            <div class="validation-item">
              <label>多选成功:</label>
              <sp-select
                v-model:value="multipleSuccessValue"
                :options="basicOptions"
                placeholder="选择多个选项"
                multiple
                validate-state="success"
                :validate-message="`已选择 ${multipleSuccessValue.length} 个选项`"
                show-validate-message
                style="width: 300px"
              />
            </div>

            <div class="validation-item">
              <label>多选警告:</label>
              <sp-select
                v-model:value="multipleWarningValue"
                :options="basicOptions"
                placeholder="最多选择3个"
                multiple
                validate-state="warning"
                :validate-message="`已选择 ${multipleWarningValue.length} 个，建议不超过3个`"
                show-validate-message
                style="width: 300px"
              />
            </div>

            <div class="validation-item">
              <label>多选错误:</label>
              <sp-select
                v-model:value="multipleErrorValue"
                :options="basicOptions"
                placeholder="至少选择1个"
                multiple
                validate-state="error"
                validate-message="必须至少选择一个选项"
                show-validate-message
                style="width: 300px"
              />
            </div>
          </div>

          <button
            @click="testValidationStates"
            class="toggle-btn"
          >
            🎯 测试验证状态
          </button>
        </div>
        <div class="demo-info">
          <h4>多选验证值:</h4>
          <code>成功: {{ JSON.stringify(multipleSuccessValue) }}</code>
          <code>警告: {{ JSON.stringify(multipleWarningValue) }}</code>
          <code>错误: {{ JSON.stringify(multipleErrorValue) }}</code>
          <p><small>多选模式下的不同验证状态展示</small></p>
        </div>
      </div>
    </section>

    <!-- 复用说明 -->
    <section class="demo-section">
      <h2>🚀 组件复用架构</h2>
      <div class="reuse-info">
        <div class="reuse-item">
          <strong>Dropdown 组件:</strong>
          100% 复用定位、触发、动画功能
        </div>
        <div class="reuse-item">
          <strong>List + ListItem:</strong>
          95% 复用选择、多选、hover 功能
        </div>
        <div class="reuse-item">
          <strong>InputAffix:</strong>
          95% 复用前后缀、状态管理功能
        </div>
        <div class="reuse-item">
          <strong>样式系统:</strong>
          100% 复用 BEM 规范、主题变量
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, nextTick } from 'vue'

  // 基础选项数据
  const basicOptions = [
    { label: '选项一', value: 1 },
    { label: '选项二', value: 2 },
    { label: '选项三', value: 3 },
    { label: '选项四', value: 4 },
    { label: '选项五', value: 5 },
  ]

  // 搜索选项数据
  const searchOptions = [
    { label: 'Apple 苹果', value: 'apple' },
    { label: 'Banana 香蕉', value: 'banana' },
    { label: 'Cherry 樱桃', value: 'cherry' },
    { label: 'Dragon Fruit 火龙果', value: 'dragon' },
    { label: 'Elderberry 接骨木果', value: 'elder' },
    { label: 'Fig 无花果', value: 'fig' },
    { label: 'Grape 葡萄', value: 'grape' },
    { label: 'Honeydew 蜜瓜', value: 'honeydew' },
  ]

  // 自定义选项数据
  const customOptions = [
    {
      label: '张三',
      value: 'zhangsan',
      email: '<EMAIL>',
      color: '#1890ff',
    },
    {
      label: '李四',
      value: 'lisi',
      email: '<EMAIL>',
      color: '#52c41a',
    },
    {
      label: '王五',
      value: 'wangwu',
      email: '<EMAIL>',
      color: '#fa8c16',
    },
    {
      label: '赵六',
      value: 'zhaoliu',
      email: '<EMAIL>',
      color: '#eb2f96',
    },
  ]

  // 各种演示的状态
  const basicValue = ref<number | null>(null)

  // 调试：监听 basicValue 变化
  watch(basicValue, (newVal: number | null, oldVal: number | null) => {
    console.log('🎯 basicValue 变化:', oldVal, '->', newVal)
    console.log('🎯 basicValue 当前值:', basicValue.value)
    // 强制触发更新
    nextTick(() => {
      console.log('🎯 nextTick 后 basicValue:', basicValue.value)
    })
  })
  const multipleValue = ref([])
  const searchValue = ref(null)
  const multiSearchValue = ref([])
  const sizeValue1 = ref(null)
  const sizeValue2 = ref(2)
  const sizeValue3 = ref(null)
  const disabledValue1 = ref(1)
  const disabledValue2 = ref(2)
  const loadingValue = ref(null)
  const customValue = ref(null)
  const variantValue1 = ref(null)
  const variantValue2 = ref(null)

  // 验证状态相关的数据
  const successValue = ref(1)
  const warningValue = ref<number | null>(null)
  const errorValue = ref<number | null>(null)
  const dynamicValidateValue = ref<number | null>(null)
  const dynamicValidateState = ref<
    'success' | 'warning' | 'error' | undefined
  >()
  const dynamicValidateMessage = ref('')
  const multipleSuccessValue = ref([1, 2])
  const multipleWarningValue = ref([1, 2, 3])
  const multipleErrorValue = ref([])

  // 加载状态
  const isLoading = ref(false)
  const loadingOptions = ref([
    { label: '选项 A', value: 'a' },
    { label: '选项 B', value: 'b' },
    { label: '选项 C', value: 'c' },
  ])

  // 切换加载状态
  const toggleLoading = () => {
    isLoading.value = !isLoading.value
  }

  // 动态验证逻辑
  watch(dynamicValidateValue, newValue => {
    if (!newValue) {
      dynamicValidateState.value = undefined
      dynamicValidateMessage.value = ''
    } else if (newValue === 1) {
      dynamicValidateState.value = 'success'
      dynamicValidateMessage.value = '✅ 推荐选择！这是最佳选项'
    } else if (newValue === 2) {
      dynamicValidateState.value = 'warning'
      dynamicValidateMessage.value = '⚠️ 请注意：此选项可能需要额外配置'
    } else if (newValue === 3) {
      dynamicValidateState.value = 'error'
      dynamicValidateMessage.value = '❌ 不建议选择：此选项已过时'
    } else {
      dynamicValidateState.value = 'success'
      dynamicValidateMessage.value = '✅ 验证通过'
    }
  })

  // 测试验证状态
  const testValidationStates = () => {
    console.log('🎯 测试验证状态')

    // 设置各种验证状态的值
    successValue.value = 1
    warningValue.value = 2
    errorValue.value = 3
    dynamicValidateValue.value = 2

    // 设置多选验证值
    multipleSuccessValue.value = [1, 2]
    multipleWarningValue.value = [1, 2, 3, 4]
    multipleErrorValue.value = []

    console.log('验证状态已设置完成！')
  }
</script>

<style scoped>
  .select-demo {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .select-demo h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 10px;
  }

  .select-demo > p {
    text-align: center;
    color: #7f8c8d;
    margin-bottom: 40px;
  }

  .demo-section {
    margin-bottom: 50px;
    padding: 30px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background: #fafafa;
  }

  .demo-section h2 {
    color: #34495e;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #ecf0f1;
  }

  .demo-container {
    display: flex;
    gap: 30px;
    align-items: flex-start;
  }

  .select-wrapper {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .demo-info {
    flex: 1;
    min-width: 250px;
  }

  .demo-info h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
  }

  .demo-info code {
    display: block;
    padding: 10px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    color: #495057;
    word-break: break-all;
    margin-bottom: 8px;
    font-size: 12px;
  }

  .demo-info p {
    margin: 10px 0 0 0;
    color: #6c757d;
  }

  .demo-info small {
    font-size: 12px;
    color: #999;
  }

  /* 尺寸演示样式 */
  .size-group {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .size-item {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .size-item label {
    width: 70px;
    font-weight: 500;
    color: #2c3e50;
  }

  /* 禁用演示样式 */
  .disabled-group {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .disabled-item {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .disabled-item label {
    width: 100px;
    font-weight: 500;
    color: #2c3e50;
  }

  /* 变体演示样式 */
  .variant-group {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .variant-item {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .variant-item label {
    width: 80px;
    font-weight: 500;
    color: #2c3e50;
  }

  /* 切换按钮 */
  .toggle-btn {
    padding: 8px 16px;
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-left: 10px;
  }

  .toggle-btn:hover {
    background: #40a9ff;
  }

  /* 自定义选项样式 */
  .custom-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 4px 0;
  }

  .avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
  }

  .user-info {
    flex: 1;
  }

  .name {
    font-weight: 500;
    color: #2c3e50;
  }

  .email {
    font-size: 12px;
    color: #999;
  }

  /* 验证状态演示样式 */
  .validation-group {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .validation-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
  }

  .validation-item label {
    width: 180px;
    font-weight: 500;
    color: #2c3e50;
    margin-top: 8px;
  }

  .validation-tips {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
  }

  .validation-tips p {
    margin: 0 0 10px 0;
    font-weight: 600;
    color: #495057;
  }

  .validation-tips ul {
    margin: 0;
    padding-left: 20px;
  }

  .validation-tips li {
    margin-bottom: 5px;
    color: #6c757d;
  }

  /* 复用说明样式 */
  .reuse-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
  }

  .reuse-item {
    padding: 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #ddd;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .reuse-item strong {
    color: #2c3e50;
  }
</style>
