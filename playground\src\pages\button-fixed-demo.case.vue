<text>
# 按钮修复演示

展示修复后的 plain 和 link 按钮效果。
</text>

<template>
  <div class="button-demo">
    <h3>Plain 按钮修复</h3>
    <p>修复前：plain 按钮没有指定 type 时会显示紫色（primary色彩）</p>
    <p>修复后：plain 按钮默认显示灰色，只有明确指定 type="primary" 才显示紫色</p>
    <div class="demo-row">
      <sp-button plain>默认Plain（灰色）</sp-button>
      <sp-button plain type="primary">Primary Plain（紫色）</sp-button>
      <sp-button plain type="secondary">Secondary Plain</sp-button>
      <sp-button plain type="success">Success Plain</sp-button>
      <sp-button plain type="warning">Warning Plain</sp-button>
      <sp-button plain type="danger">Danger Plain</sp-button>
    </div>

    <h3>Link 按钮修复</h3>
    <p>修复前：link 按钮没有点击反馈效果</p>
    <p>修复后：link 按钮有轻微的缩放效果和颜色变化</p>
    <div class="demo-row">
      <sp-button link>默认Link</sp-button>
      <sp-button link type="primary">Primary Link</sp-button>
      <sp-button link type="secondary">Secondary Link</sp-button>
      <sp-button link type="success">Success Link</sp-button>
      <sp-button link type="warning">Warning Link</sp-button>
      <sp-button link type="danger">Danger Link</sp-button>
    </div>

    <h3>对比演示</h3>
    <div class="demo-row">
      <sp-button>普通按钮</sp-button>
      <sp-button text>文本按钮</sp-button>
      <sp-button plain>Plain按钮</sp-button>
      <sp-button link>Link按钮</sp-button>
    </div>
  </div>
</template>

<style scoped>
.button-demo {
  padding: 20px;
}

.demo-row {
  display: flex;
  gap: 12px;
  margin: 16px 0;
  flex-wrap: wrap;
}

h3 {
  color: #333;
  margin-top: 24px;
  margin-bottom: 8px;
}

p {
  color: #666;
  margin: 4px 0;
  font-size: 14px;
}
</style> 