# 更新日志

本文档记录了 Speed UI BEM 助手工具的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
- 初始版本发布
- 支持 BEM 类名生成
- 支持 CSS 选择器生成
- 支持 CSS 变量管理
- 支持自定义命名空间
- 完整的 TypeScript 类型支持
- 中文文档和注释

## [1.0.0] - 2024-01-01

### 新增
- **核心功能**
  - `useBEM(block)` - 创建默认 BEM 助手（使用 `sp` 前缀）
  - `useCustomBEM(block, namespace)` - 创建自定义命名空间 BEM 助手
  - `createCSSVar(name)` - 创建 CSS 变量名
  - `getCSSVar(name)` - 获取 CSS 变量值
  - `setCSSVar(name, value)` - 设置 CSS 变量声明

- **BEM 类名生成**
  - `b()` - 生成基础块类名
  - `e(element)` - 生成元素类名
  - `m(modifier)` - 生成修饰符类名
  - `em(element, modifier)` - 生成元素修饰符类名
  - `s(state)` - 生成状态类名

- **CSS 选择器生成**
  - `selector()` - 生成基础选择器
  - `selectorE(element)` - 生成元素选择器
  - `selectorM(modifier)` - 生成修饰符选择器
  - `selectorEM(element, modifier)` - 生成元素修饰符选择器
  - `selectorS(state)` - 生成状态选择器

- **CSS 变量管理**
  - `var(name)` - 生成组件级 CSS 变量名
  - `getVar(name)` - 获取组件级 CSS 变量值
  - `setVar(name, value)` - 设置组件级 CSS 变量声明
  - `vars(obj)` - 批量生成 CSS 变量对象
  - `globalVar(name)` - 生成全局 CSS 变量名
  - `getGlobalVar(name)` - 获取全局 CSS 变量值

- **开发体验**
  - 完整的 TypeScript 类型定义
  - 详细的中文 JSDoc 注释
  - 现代化的构建配置

### 技术特性
- 零依赖，轻量级设计
- 支持 ES Module 和 CommonJS
- 支持 UMD 格式用于浏览器直接引用
- 完整的 TypeScript 支持
- 遵循 BEM 命名规范
- 统一的 `sp-` 前缀规范

### 文档
- 完整的中文 README 文档
- API 参考文档
- 使用示例和最佳实践
- 贡献指南

---

## 版本说明

### 版本格式
- **主版本号**：当你做了不兼容的 API 修改
- **次版本号**：当你做了向下兼容的功能性新增
- **修订号**：当你做了向下兼容的问题修正

### 变更类型
- **新增** - 新功能
- **变更** - 对现有功能的变更
- **弃用** - 即将移除的功能
- **移除** - 已移除的功能
- **修复** - 问题修复
- **安全** - 安全相关的修复

### 链接
- [未发布]: https://github.com/your-org/speed-ui/compare/v1.0.0...HEAD
- [1.0.0]: https://github.com/your-org/speed-ui/releases/tag/v1.0.0



