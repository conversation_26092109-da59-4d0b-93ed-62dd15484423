import FieldContainer from './FieldContainer.vue'
import VField from './SPField.vue'
import FieldLabel from './FieldLabel.vue'
import type { App } from 'vue'

// 导出组件类型
export type {
  FieldContainerProps,
  FieldContainerInstance,
  FieldContainerSlotProps,
  // FieldContainerPrefixSlotProps,
  // FieldContainerSuffixSlotProps,
  FieldContainerFunctionsSlotProps,
} from './types'

// 组件安装函数
FieldContainer.install = (app: App) => {
  app.component('SpFieldContainer', FieldContainer)
}

export default FieldContainer
export { FieldContainer, VField, FieldLabel }
