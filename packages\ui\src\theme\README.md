# Speed UI 简化主题设置

## 🎯 设计理念

用户只需要设置一个主题色，组件库自动生成所有相关的颜色变化（hover、active、disabled 等），无需复杂的配置。

## 🚀 使用方法

### 1. 基础用法（推荐）

```typescript
// main.ts
import { setTheme } from 'speed-ui'

// 只设置主题色，不启用其他自动功能
setTheme('#10b981') // 绿色主题
```

### 2. 完整初始化（可选）

如果你需要尺寸系统等额外功能：

```typescript
// main.ts
import { initSpeedUIFull, setTheme } from 'speed-ui'

// 初始化所有功能（尺寸系统 + 默认主题）
initSpeedUIFull()

// 然后设置你的主题色
setTheme('#10b981')
```

### 3. 运行时切换

```typescript
import { setTheme } from 'speed-ui'

// 在任何地方切换主题
setTheme('#1890ff') // 蓝色
setTheme('#52c41a') // 绿色
setTheme('#722ed1') // 紫色
setTheme('#ec4899') // 粉色
```

### 4. 选择性初始化

```typescript
// 只需要主题功能
import { initThemeOnly } from 'speed-ui'
initThemeOnly('blue')

// 只需要尺寸系统
import { initSizeOnly } from 'speed-ui'
initSizeOnly()

// 自定义初始化
import { initSpeedUI } from 'speed-ui'
initSpeedUI({
  enableSizeSystem: true,
  enableDefaultTheme: false,
})
```

### 5. 使用预设主题

```typescript
import { setPresetTheme, PRESET_THEMES } from 'speed-ui'

// 使用预设主题
setPresetTheme('blue')
setPresetTheme('green')
setPresetTheme('purple')

// 查看所有预设主题
console.log(PRESET_THEMES)
// 输出: { blue: '#1890ff', green: '#52c41a', ... }
```

### 4. 获取当前主题

```typescript
import { getCurrentTheme } from 'speed-ui'

const currentColor = getCurrentTheme()
console.log(currentColor) // '#10b981'
```

### 5. 重置主题

```typescript
import { resetTheme } from 'speed-ui'

// 重置为默认蓝色主题
resetTheme()
```

## 🎨 自动生成的颜色

当你设置一个主题色时，系统会自动生成：

- **primary**: 主色调（你设置的颜色）
- **primaryHover**: 悬停色（亮 10%）
- **primaryActive**: 激活色（暗 10%）
- **primaryDisabled**: 禁用色（亮 40%）
- **primaryLightest**: 最浅色（亮 45%）
- **primaryLight**: 浅色（亮 30%）
- **primaryDark**: 深色（暗 20%）

## 📋 API 参考

### setTheme(color: string)

设置主题色

- **参数**: `color` - 十六进制颜色值，如 `#1890ff`
- **返回**: `void`

### getCurrentTheme(): string

获取当前主题色

- **返回**: 当前主题色的十六进制值

### resetTheme()

重置为默认主题

- **返回**: `void`

### setPresetTheme(themeName: string)

设置预设主题

- **参数**: `themeName` - 预设主题名称（blue、green、red 等）
- **返回**: `void`

### PRESET_THEMES

预设主题色对象

```typescript
{
  blue: '#1890ff',
  green: '#52c41a',
  red: '#ff4d4f',
  orange: '#fa8c16',
  purple: '#722ed1',
  pink: '#eb2f96',
  cyan: '#13c2c2',
  gray: '#595959'
}
```

## 💡 最佳实践

1. **在应用启动时设置主题**：

   ```typescript
   // main.ts
   import { setTheme } from 'speed-ui'

   setTheme('#your-brand-color')
   ```

2. **提供主题切换功能**：

   ```vue
   <template>
     <button @click="changeTheme('#1890ff')">蓝色</button>
     <button @click="changeTheme('#52c41a')">绿色</button>
   </template>

   <script setup>
     import { setTheme } from 'speed-ui'

     const changeTheme = color => {
       setTheme(color)
     }
   </script>
   ```

3. **保存用户选择**：

   ```typescript
   import { setTheme, getCurrentTheme } from 'speed-ui'

   // 保存主题到本地存储
   const saveTheme = color => {
     setTheme(color)
     localStorage.setItem('theme-color', color)
   }

   // 应用启动时恢复主题
   const savedTheme = localStorage.getItem('theme-color')
   if (savedTheme) {
     setTheme(savedTheme)
   }
   ```

## 🔧 技术实现

- 使用 CSS 变量动态更新主题色
- 自动计算颜色的明暗变化
- 支持十六进制颜色格式（#RRGGBB 或 #RGB）
- 浏览器兼容性良好

## 🆚 对比旧版本

### 旧版本（复杂）

```typescript
setSpeedUIThemeConfig({
  green: {
    primary: '#10b981',
    primaryHover: '#34d399',
    primaryActive: '#059669',
    primaryDisabled: '#86efac',
    primaryLightest: '#d1fae5',
    primaryLight: '#a7f3d0',
    primaryDark: '#047857',
  },
})
```

### 新版本（简单）

```typescript
setTheme('#10b981') // 就这一行！
```

简化了 90% 的配置代码，用户体验更好！
