<!--
  TextareaCore.vue - 直接复用 Input 的 composables，只修改模板使用 textarea
-->

<template>
  <div :class="rootClasses">
    <div
      ref="wrapperRef"
      :class="wrapperClasses"
      @click="handleWrapperClick"
    >
      <!-- 前缀区域 -->
      <div
        v-if="prefixIcon || slots.prefix"
        :class="prefixClasses"
        @click.stop
        @mousedown.prevent
      >
        <sp-icon
          v-if="prefixIcon"
          :name="prefixIcon"
          :size="iconSize"
          :class="prefixIconClasses"
        />
        <slot name="prefix"></slot>
      </div>

      <!-- 文本域容器 - 相对定位 -->
      <!-- <div class="sp-textarea__input-container"> -->
      <!-- 文本域 - 这里改成 textarea -->
      <textarea
        ref="inputRef"
        :value="value"
        :placeholder="placeholder"
        :disabled="computedDisabled"
        :readonly="readonly"
        :maxlength="maxlength"
        :rows="rows || 3"
        :cols="cols"
        :class="textareaClasses"
        :style="{
          overflow: autosize ? 'hidden' : 'auto',
        }"
        @input="handleInput"
        @change="handleChange"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
      />

      <!-- 后缀区域 - 直接定位在 textarea 上 -->
      <div
        v-if="showSuffix"
        :class="suffixClasses"
        @click.stop
        @mousedown.prevent
      >
        <!-- 清除按钮 -->
        <sp-icon
          v-if="showClearIcon"
          name="CloseCircle"
          :size="iconSize"
          clickable
          :class="clearIconClasses"
          @click.stop.prevent="handleClear"
          @mousedown.stop.prevent
        />

        <!-- 字数统计 -->
        <span
          v-if="showWordLimit"
          :class="wordCountClasses"
        >
          {{ wordCount }}/{{ maxlength }}
        </span>

        <!-- 自定义后缀图标 -->
        <sp-icon
          v-if="suffixIcon"
          :name="suffixIcon"
          :size="iconSize"
          :class="suffixIconClasses"
        />

        <!-- 后缀插槽 -->
        <slot name="suffix"></slot>
      </div>
    </div>
    <!-- </div> -->

    <!-- 加载动画条 -->
    <div
      v-if="loading"
      :class="loadingBarClasses"
    >
      <div :class="loadingProgressClasses"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useSlots, ref, nextTick, watch, computed } from 'vue'
  import SpIcon from '../icon/Icon.vue'
  import { useInputLogic, useTextareaStyles } from '../../composables'
  import type { TextareaProps, TextareaEmits } from './types'

  // ===== Props 和 Emits =====
  const props = withDefaults(defineProps<TextareaProps>(), {
    value: '',
    variant: 'default',
    effect: 'none',
    size: 'medium',
    disabled: false,
    readonly: false,
    clearable: false,
    showWordLimit: false,
    error: false,
    loading: false,
    rows: 3,
    resize: 'vertical',
    autosize: false,
  })

  const emit = defineEmits<TextareaEmits>()
  const slots = useSlots()

  // ===== 复用 Input 逻辑，使用 Textarea 专用样式 =====
  const logic = useInputLogic(props, emit)
  const styles = useTextareaStyles(props, {
    computedDisabled: logic.computedDisabled,
    isFocused: logic.isFocused,
  })

  // 从复用的逻辑中解构出需要的属性和方法
  const {
    // 引用
    wrapperRef,
    inputRef, // 这里实际上会是 textarea 元素

    // 状态
    isFocused,

    // 计算属性
    iconSize,
    wordCount,
    computedDisabled,

    // 显示逻辑
    showClearIcon,

    // 事件处理
    handleInput,
    handleChange,
    handleFocus,
    handleBlur,
    handleKeydown,
    handleWrapperClick,
    handleClear,

    // 方法
    focus,
    blur,
    select,
    clear,
  } = logic

  // ===== Textarea 特有的后缀显示逻辑 =====
  const showSuffix = computed(() => {
    return (
      showClearIcon.value ||
      props.showWordLimit ||
      props.suffixIcon ||
      slots.suffix
    )
  })

  // 直接使用 textarea 专用样式
  const {
    rootClasses,
    wrapperClasses,
    textareaClasses,
    prefixClasses,
    suffixClasses,
    prefixIconClasses,
    suffixIconClasses,
    clearIconClasses,
    wordCountClasses,
    loadingBarClasses,
    loadingProgressClasses,
  } = styles

  // ===== Textarea 特有的自动调整高度功能 =====
  const adjustHeight = () => {
    if (!props.autosize || !inputRef.value) return

    const textarea = inputRef.value as unknown as HTMLTextAreaElement
    const { autosize } = props

    // 重置高度以获取 scrollHeight
    textarea.style.height = 'auto'

    let height = textarea.scrollHeight

    // 处理 autosize 配置
    if (typeof autosize === 'object') {
      const { minRows = 1, maxRows } = autosize
      const lineHeight = parseInt(getComputedStyle(textarea).lineHeight) || 20

      // 计算最小高度
      const minHeight = lineHeight * minRows
      height = Math.max(height, minHeight)

      // 计算最大高度
      if (maxRows) {
        const maxHeight = lineHeight * maxRows
        height = Math.min(height, maxHeight)
      }
    }

    textarea.style.height = `${height}px`
  }

  // 监听 value 变化，更新高度
  watch(
    () => props.value,
    () => {
      if (props.autosize) {
        nextTick(() => adjustHeight())
      }
    }
  )

  // 监听 autosize 配置变化
  watch(
    () => props.autosize,
    () => {
      if (props.autosize) {
        nextTick(() => adjustHeight())
      }
    },
    { deep: true }
  )

  // ===== 暴露方法 =====
  defineExpose({
    focus,
    blur,
    select,
    clear,
    adjustHeight,
    get textarea() {
      return (inputRef.value as unknown as HTMLTextAreaElement) || null
    },
    get wrapper() {
      return wrapperRef.value || null
    },
  })
</script>

<script lang="ts">
  export default {
    name: 'TextareaCore',
  }
</script>
