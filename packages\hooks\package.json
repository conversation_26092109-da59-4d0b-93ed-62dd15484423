{"name": "@speed-ui/hooks", "version": "1.0.0", "description": "Speed UI Vue Composition Hooks", "main": "src/index.ts", "module": "src/index.ts", "types": "src/index.ts", "exports": {".": {"import": "./src/index.ts", "require": "./src/index.ts", "types": "./src/index.ts"}}, "files": ["lib", "es"], "scripts": {"build": "pnpm run build:lib && pnpm run build:es", "build:lib": "tsc -p tsconfig.lib.json", "build:es": "tsc -p tsconfig.es.json"}, "keywords": ["vue", "hooks", "composition-api", "speed-ui"], "author": "Speed UI Team", "license": "MIT", "peerDependencies": {"vue": "^3.0.0"}, "dependencies": {"@speed-ui/utils": "workspace:*"}, "devDependencies": {"typescript": "^5.0.0", "vue": "^3.4.0"}}