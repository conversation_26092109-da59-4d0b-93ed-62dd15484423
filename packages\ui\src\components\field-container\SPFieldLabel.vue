<template>
  <label
    :class="labelClasses"
    :for="forId"
    :aria-hidden="floating || undefined"
  >
    <slot />
  </label>
</template>

<script setup lang="ts">
  import { computed } from 'vue'

  interface SPFieldLabelProps {
    floating?: boolean
    forId?: string
  }

  const props = withDefaults(defineProps<SPFieldLabelProps>(), {
    floating: false,
  })

  const labelClasses = computed(() => [
    'sp-field-label',
    {
      'sp-field-label--floating': props.floating,
    },
  ])
</script>

<script lang="ts">
  export default {
    name: 'SPFieldLabel',
  }
</script>

<style lang="scss">
  .sp-field-label {
    position: absolute;
    left: 16px;
    color: rgba(var(--v-theme-on-surface), 0.6);
    font-size: 16px;
    line-height: 1;
    pointer-events: none;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: left top;
    max-width: calc(100% - 32px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    top: 50%;
    transform: translateY(-50%);

    &--floating {
      font-size: 12px;
      visibility: hidden;

      .sp-field.sp-field--active & {
        visibility: visible;
      }

      .sp-field--variant-filled & {
        top: 50%;
        transform: translateY(-50%);

        .sp-field.sp-field--active & {
          top: 12px;
          font-size: 12px;
          transform: translateY(0) scale(0.75);
          color: rgb(var(--v-theme-primary));
        }
      }

      .sp-field--variant-outlined & {
        transform: translateY(-50%);
        transform-origin: center;
        position: static;
        margin: 0 4px;
        background: rgb(var(--v-theme-surface));
        padding: 0;
        color: rgb(var(--v-theme-primary));
        white-space: nowrap;
        overflow: visible;
        text-overflow: unset;
        max-width: 100%;
      }
    }
  }
</style>
