<template>
  <div class="config-test-demo">
    <h1>配置系统测试</h1>
    <p>这个页面展示了在 main.ts 中设置的自定义尺寸配置效果</p>
    
    <div class="demo-section">
      <h2>预设尺寸对比（使用自定义配置）</h2>
      <div class="size-comparison">
        <div class="size-group">
          <h3>Small 尺寸 (对应 30px)</h3>
          <sp-input-field
            size="small"
            label="小尺寸输入框"
            placeholder="基础高度 30px，字体 13px"
            prefix-icon="User"
          />
          <sp-input-field
            size="small"
            variant="filled"
            label="填充变体"
            placeholder="填充样式"
            prefix-icon="Search"
          />
        </div>

        <div class="size-group">
          <h3>Medium 尺寸 (对应 48px)</h3>
          <sp-input-field
            size="medium"
            label="中等尺寸输入框"
            placeholder="基础高度 48px，字体 16px"
            prefix-icon="Search"
          />
          <sp-input-field
            size="medium"
            variant="filled"
            label="填充变体"
            placeholder="填充样式"
            prefix-icon="Search"
          />
        </div>

        <div class="size-group">
          <h3>Large 尺寸 (对应 75px)</h3>
          <sp-input-field
            size="large"
            label="大尺寸输入框"
            placeholder="基础高度 75px，字体 25px"
            prefix-icon="Location"
          />
          <sp-input-field
            size="large"
            variant="filled"
            label="填充变体"
            placeholder="填充样式"
            prefix-icon="Search"
          />
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>数字尺寸（使用自定义映射函数）</h2>
      <div class="custom-sizes">
        <sp-input-field 
          :size="40" 
          label="40px 高度" 
          placeholder="使用自定义映射函数"
          prefix-icon="User"
        />
        <sp-input-field 
          :size="60" 
          label="60px 高度" 
          placeholder="基准比例改为 50，字体更大"
          prefix-icon="Search"
        />
        <sp-input-field 
          :size="80" 
          label="80px 高度" 
          placeholder="更舒适的间距"
          prefix-icon="Mail"
        />
      </div>
    </div>

    <div class="config-info">
      <h2>当前配置信息</h2>
      <div class="config-details">
        <div class="config-item">
          <h4>Small 尺寸配置 (对应 30px)</h4>
          <ul>
            <li>基础高度: 30px</li>
            <li>字体大小: 13px</li>
            <li>上边距: 15px</li>
            <li>前缀左边距: 10px</li>
          </ul>
        </div>
        <div class="config-item">
          <h4>Medium 尺寸配置 (对应 48px)</h4>
          <ul>
            <li>基础高度: 48px</li>
            <li>字体大小: 16px</li>
            <li>上边距: 24px</li>
            <li>前缀左边距: 16px</li>
          </ul>
        </div>
        <div class="config-item">
          <h4>Large 尺寸配置 (对应 75px)</h4>
          <ul>
            <li>基础高度: 75px</li>
            <li>字体大小: 25px</li>
            <li>上边距: 38px</li>
            <li>前缀左边距: 25px</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 这个页面不需要额外的逻辑，只是展示配置效果
</script>

<style scoped>
.config-test-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 40px;
}

.size-comparison {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.size-group {
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.size-group h3 {
  margin-top: 0;
  color: #333;
  font-size: 16px;
}

.size-group > * + * {
  margin-top: 15px;
}

.custom-sizes {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.custom-sizes > * {
  flex: 1;
  min-width: 250px;
}

.config-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.config-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.config-item {
  background: white;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.config-item h4 {
  margin-top: 0;
  color: #555;
}

.config-item ul {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

.config-item li {
  margin-bottom: 5px;
  color: #666;
}
</style>
