<template>
  <div class="btn-demo">
    <div class="demo-header">
      <h1>🎯 Btn 按钮组件演示</h1>
      <p>展示新的按钮组件的各种变体和交互效果</p>
    </div>

    <!-- 基础变体 -->
    <section class="demo-section">
      <h2>📦 基础变体</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>Default 默认按钮</h3>
          <sp-btn
            variant="default"
            @click="handleClick('default')"
          >
            BUTTON
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>Outlined 边框按钮</h3>
          <sp-btn
            variant="outlined"
            @click="handleClick('outlined')"
          >
            BUTTON
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>Text 文本按钮</h3>
          <sp-btn
            variant="text"
            @click="handleClick('text')"
          >
            BUTTON
          </sp-btn>
        </div>
      </div>
    </section>

    <!-- 尺寸变体 -->
    <section class="demo-section">
      <h2>📏 尺寸变体</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>Small 小尺寸</h3>
          <sp-btn
            variant="default"
            size="small"
            @click="handleClick('small')"
          >
            BUTTON
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>Medium 中等尺寸</h3>
          <sp-btn
            variant="default"
            size="medium"
            @click="handleClick('medium')"
          >
            BUTTON
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>Large 大尺寸</h3>
          <sp-btn
            variant="default"
            size="large"
            @click="handleClick('large')"
          >
            BUTTON
          </sp-btn>
        </div>
      </div>
    </section>

    <!-- 状态演示 -->
    <section class="demo-section">
      <h2>🎭 状态演示</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>正常状态</h3>
          <sp-btn
            variant="default"
            @click="handleClick('normal')"
          >
            BUTTON
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>禁用状态</h3>
          <sp-btn
            variant="default"
            disabled
            @click="handleClick('disabled')"
          >
            BUTTON
          </sp-btn>
        </div>
      </div>
    </section>

    <!-- 组合演示 -->
    <section class="demo-section">
      <h2>🎨 组合演示</h2>
      <div class="demo-grid">
        <!-- Default 变体的所有尺寸 -->
        <div class="demo-group">
          <h3>Default 变体</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="default"
              size="small"
            >
              Small
            </sp-btn>
            <sp-btn
              variant="default"
              size="medium"
            >
              Medium
            </sp-btn>
            <sp-btn
              variant="default"
              size="large"
            >
              Large
            </sp-btn>
          </div>
        </div>

        <!-- Outlined 变体的所有尺寸 -->
        <div class="demo-group">
          <h3>Outlined 变体</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="outlined"
              size="small"
            >
              Small
            </sp-btn>
            <sp-btn
              variant="outlined"
              size="medium"
            >
              Medium
            </sp-btn>
            <sp-btn
              variant="outlined"
              size="large"
            >
              Large
            </sp-btn>
          </div>
        </div>

        <!-- Text 变体的所有尺寸 -->
        <div class="demo-group">
          <h3>Text 变体</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="text"
              size="small"
            >
              Small
            </sp-btn>
            <sp-btn
              variant="text"
              size="medium"
            >
              Medium
            </sp-btn>
            <sp-btn
              variant="text"
              size="large"
            >
              Large
            </sp-btn>
          </div>
        </div>

        <!-- 禁用状态 -->
        <div class="demo-group">
          <h3>禁用状态</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="default"
              disabled
            >
              Default
            </sp-btn>
            <sp-btn
              variant="outlined"
              disabled
            >
              Outlined
            </sp-btn>
            <sp-btn
              variant="text"
              disabled
            >
              Text
            </sp-btn>
          </div>
        </div>
      </div>
    </section>

    <!-- 交互说明 -->
    <section class="demo-section">
      <h2>💡 交互说明</h2>
      <div class="interaction-info">
        <ul>
          <li>
            <strong>鼠标悬停</strong>
            ：按钮会有轻微上移和阴影效果
          </li>
          <li>
            <strong>点击按下</strong>
            ：按钮会有按下效果，颜色加深
          </li>
          <li>
            <strong>焦点状态</strong>
            ：键盘导航时会显示焦点轮廓
          </li>
          <li>
            <strong>禁用状态</strong>
            ：按钮变灰且无法交互
          </li>
        </ul>
      </div>
    </section>

    <!-- 点击日志 -->
    <section class="demo-section">
      <h2>📝 点击日志</h2>
      <div class="click-log">
        <div
          v-if="clickLog.length === 0"
          class="no-clicks"
        >
          暂无点击记录，点击上面的按钮试试！
        </div>
        <div v-else>
          <div
            v-for="(log, index) in clickLog"
            :key="index"
            class="log-item"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-action">点击了</span>
            <span class="log-target">{{ log.target }}</span>
            <span class="log-button">按钮</span>
          </div>
        </div>
        <button
          v-if="clickLog.length > 0"
          @click="clearLog"
          class="clear-log"
        >
          清空日志
        </button>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  // 注意：sp-btn 组件已经通过 SpeedUI 插件全局注册，无需手动导入

  // 点击日志
  interface ClickLog {
    time: string
    target: string
  }

  const clickLog = ref<ClickLog[]>([])

  const handleClick = (target: string) => {
    const now = new Date()
    const time = now.toLocaleTimeString()

    clickLog.value.unshift({
      time,
      target,
    })

    // 限制日志数量
    if (clickLog.value.length > 10) {
      clickLog.value = clickLog.value.slice(0, 10)
    }
  }

  const clearLog = () => {
    clickLog.value = []
  }
</script>

<style scoped>
  .btn-demo {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .demo-header {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid #f0f0f0;
  }

  .demo-header h1 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 2.5rem;
  }

  .demo-header p {
    color: #7f8c8d;
    font-size: 1.2rem;
  }

  .demo-section {
    margin-bottom: 3rem;
  }

  .demo-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #ecf0f1;
    font-size: 1.5rem;
  }

  .demo-row {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
  }

  .demo-item {
    flex: 1;
    min-width: 200px;
    padding: 1.5rem;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    background: #fafbfc;
    text-align: center;
  }

  .demo-item h3 {
    color: #34495e;
    margin-bottom: 1rem;
    font-size: 1.1rem;
  }

  .demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
  }

  .demo-group {
    padding: 1.5rem;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    background: #fafbfc;
  }

  .demo-group h3 {
    color: #34495e;
    margin-bottom: 1rem;
    text-align: center;
    font-size: 1.1rem;
  }

  .demo-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    align-items: center;
  }

  .interaction-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
  }

  .interaction-info ul {
    margin: 0;
    padding-left: 1.5rem;
  }

  .interaction-info li {
    margin-bottom: 0.5rem;
    color: #495057;
  }

  .click-log {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    min-height: 100px;
  }

  .no-clicks {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 2rem;
  }

  .log-item {
    display: flex;
    gap: 0.5rem;
    padding: 0.5rem;
    margin-bottom: 0.25rem;
    background: white;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
  }

  .log-time {
    color: #6c757d;
    font-weight: bold;
  }

  .log-action {
    color: #495057;
  }

  .log-target {
    color: #007bff;
    font-weight: bold;
  }

  .log-button {
    color: #495057;
  }

  .clear-log {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
  }

  .clear-log:hover {
    background: #c82333;
  }
</style>
