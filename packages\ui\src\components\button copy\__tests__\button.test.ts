import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { h } from 'vue'
import Button from '../button.vue'

describe('Button 组件', () => {
  // 基础渲染测试
  it('应该正确渲染按钮', () => {
    const wrapper = mount(Button, {
      slots: {
        default: '测试按钮'
      }
    })
    expect(wrapper.text()).toBe('测试按钮')
    expect(wrapper.classes()).toContain('sp-button')
  })

  // 类型测试
  it('应该正确应用不同的按钮类型', () => {
    const types = ['primary', 'secondary', 'dashed', 'text', 'link']
    types.forEach(type => {
      const wrapper = mount(Button, {
        props: {
          [type]: true
        }
      })
      expect(wrapper.classes()).toContain(`sp-button--${type}`)
    })
  })

  // 尺寸测试
  it('应该正确应用不同的按钮尺寸', () => {
    const sizes = ['small', 'medium', 'large', 'huge'] as const
    sizes.forEach(size => {
      const wrapper = mount(Button, {
        props: { size }
      })
      expect(wrapper.classes()).toContain(`sp-button--${size}`)
    })
  })

  // 禁用状态测试
  it('应该在禁用状态下正确渲染', () => {
    const wrapper = mount(Button, {
      props: { disabled: true }
    })
    expect(wrapper.attributes('disabled')).toBeDefined()
    expect(wrapper.attributes('style')).toContain('opacity: 0.5')
    expect(wrapper.attributes('style')).toContain('cursor: not-allowed')
  })

  // 加载状态测试
  it('应该在加载状态下正确渲染', () => {
    const wrapper = mount(Button, {
      props: { loading: true }
    })
    expect(wrapper.find('.sp-button__loading').exists()).toBe(true)
    expect(wrapper.attributes('disabled')).toBeDefined()
  })

  // 点击事件测试
  it('应该在点击时触发事件', async () => {
    const wrapper = mount(Button)
    await wrapper.trigger('click')
    expect(wrapper.emitted('click')).toBeTruthy()
  })

  // 禁用状态下的点击测试
  it('在禁用状态下不应该触发点击事件', async () => {
    const wrapper = mount(Button, {
      props: { disabled: true }
    })
    await wrapper.trigger('click')
    expect(wrapper.emitted('click')).toBeFalsy()
  })

  // 加载状态下的点击测试
  it('在加载状态下不应该触发点击事件', async () => {
    const wrapper = mount(Button, {
      props: { loading: true }
    })
    await wrapper.trigger('click')
    expect(wrapper.emitted('click')).toBeFalsy()
  })

  // 可切换状态测试
  it('应该在可切换状态下正确工作', async () => {
    const wrapper = mount(Button, {
      props: { toggleable: true }
    })
    await wrapper.trigger('click')
    expect(wrapper.emitted('toggle')).toBeTruthy()
    expect(wrapper.emitted('toggle')?.[0]).toEqual([true])
  })

  // 倒计时功能测试
  it('应该正确处理倒计时功能', async () => {
    vi.useFakeTimers()
    const wrapper = mount(Button, {
      props: { time: 3 }
    })
    await wrapper.trigger('click')
    expect(wrapper.find('.sp-button__countdown').exists()).toBe(true)
    expect(wrapper.text()).toContain('3s')
    
    // 模拟时间流逝
    await vi.advanceTimersByTime(1000)
    expect(wrapper.text()).toContain('2s')
    
    await vi.advanceTimersByTime(1000)
    expect(wrapper.text()).toContain('1s')
    
    await vi.advanceTimersByTime(1000)
    expect(wrapper.find('.sp-button__countdown').exists()).toBe(false)
    
    vi.useRealTimers()
  })

  // 提示文本测试
  it('应该正确显示提示文本', () => {
    const tipText = '这是一个提示'
    const wrapper = mount(Button, {
      props: { tip: tipText }
    })
    const tip = wrapper.find('.sp-button__tip')
    expect(tip.exists()).toBe(true)
    expect(tip.text()).toBe(tipText)
  })

  // 自定义样式测试
  it('应该正确应用自定义样式', () => {
    const customStyle = { backgroundColor: 'red' }
    const wrapper = mount(Button, {
      props: { attrStyle: customStyle }
    })
    expect(wrapper.attributes('style')).toContain('background-color: red')
  })

  // 图标测试
  it('应该正确渲染图标', () => {
    const IconComponent = () => h('span', { class: 'test-icon' }, '图标')
    const wrapper = mount(Button, {
      props: {
        icon: IconComponent
      }
    })
    expect(wrapper.find('.sp-button__icon').exists()).toBe(true)
  })
}) 