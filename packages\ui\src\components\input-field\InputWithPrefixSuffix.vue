<!--
  InputWithPrefixSuffix.vue - 带前缀后缀的输入组件
  包装 BaseInput 并添加 prefix/suffix 支持
-->

<template>
  <div class="sp-input-with-prefix-suffix">
    <!-- 前缀区域（只在标签浮动时显示） -->
    <div
      v-if="shouldShowPrefix"
      :class="prefixClasses"
    >
      <!-- 前缀插槽 -->
      <slot
        name="prefix"
        :prefix-classes="prefixClasses"
        :icon-size="iconSize"
        :prefix="prefix"
        :is-floating="isLabelFloating"
      >
        <!-- 默认显示前缀内容 -->
        <span v-if="prefix">
          {{ prefix }}
        </span>
      </slot>
    </div>

    <!-- 输入元素 -->
    <BaseInput
      ref="inputRef"
      v-bind="$attrs"
      @update:value="$emit('update:value', $event)"
      @input="$emit('input', $event)"
      @change="$emit('change', $event)"
      @focus="$emit('focus', $event)"
      @blur="$emit('blur', $event)"
      @keydown="$emit('keydown', $event)"
      @keyup="$emit('keyup', $event)"
      @keypress="$emit('keypress', $event)"
      @click="$emit('click', $event)"
    />

    <!-- 后缀区域（只在标签浮动时显示） -->
    <div
      v-if="shouldShowSuffix"
      :class="suffixClasses"
    >
      <!-- 后缀插槽 -->
      <slot
        name="suffix"
        :suffix-classes="suffixClasses"
        :icon-size="iconSize"
        :suffix="suffix"
        :is-floating="isLabelFloating"
      >
        <!-- 默认显示后缀内容 -->
        <span v-if="suffix">
          {{ suffix }}
        </span>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, useSlots } from 'vue'
  import BaseInput from '../baseinput/baseinput'

  interface Props {
    prefix?: string
    suffix?: string
    prefixClasses?: string
    suffixClasses?: string
    shouldShowPrefix?: boolean
    shouldShowSuffix?: boolean
    isLabelFloating?: boolean
    iconSize?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    prefix: undefined,
    suffix: undefined,
    prefixClasses: '',
    suffixClasses: '',
    shouldShowPrefix: false,
    shouldShowSuffix: false,
    isLabelFloating: false,
    iconSize: 18,
  })

  const emit = defineEmits([
    'update:value',
    'input',
    'change',
    'focus',
    'blur',
    'keydown',
    'keyup',
    'keypress',
    'click',
  ])

  const slots = useSlots()
  const inputRef = ref()

  // 暴露 BaseInput 的方法
  const focus = () => inputRef.value?.focus()
  const blur = () => inputRef.value?.blur()
  const select = () => inputRef.value?.select()
  const clear = () => inputRef.value?.clear()

  defineExpose({
    focus,
    blur,
    select,
    clear,
    get inputRef() {
      return inputRef.value?.inputRef || null
    },
  })
</script>

<script lang="ts">
  export default {
    name: 'InputWithPrefixSuffix',
    inheritAttrs: false,
  }
</script>

<style scoped>
  .sp-input-with-prefix-suffix {
    display: flex;
    align-items: center;
    width: 100%;
  }
</style>
