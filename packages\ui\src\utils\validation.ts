/**
 * 验证规则接口
 */
export interface ValidationRule {
  /** 是否必填 */
  required?: boolean
  /** 最小长度 */
  minLength?: number
  /** 最大长度 */
  maxLength?: number
  /** 最小值 */
  min?: number
  /** 最大值 */
  max?: number
  /** 正则表达式 */
  pattern?: RegExp
  /** 自定义验证函数 */
  validator?: (value: any) => boolean | string
  /** 错误消息 */
  message?: string
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  /** 是否有效 */
  valid: boolean
  /** 错误消息 */
  message?: string
  /** 错误类型 */
  type?: string
}

/**
 * 预定义的验证规则
 */
export const ValidationRules = {
  /** 邮箱验证 */
  email: {
    pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
    message: '请输入有效的邮箱地址'
  },
  
  /** 手机号验证（中国大陆） */
  phone: {
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入有效的手机号码'
  },
  
  /** 身份证号验证（中国大陆） */
  idCard: {
    pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
    message: '请输入有效的身份证号码'
  },
  
  /** URL验证 */
  url: {
    pattern: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
    message: '请输入有效的URL地址'
  },
  
  /** 数字验证 */
  number: {
    pattern: /^-?\d+(\.\d+)?$/,
    message: '请输入有效的数字'
  },
  
  /** 整数验证 */
  integer: {
    pattern: /^-?\d+$/,
    message: '请输入有效的整数'
  },
  
  /** 正整数验证 */
  positiveInteger: {
    pattern: /^[1-9]\d*$/,
    message: '请输入正整数'
  },
  
  /** 密码强度验证（至少8位，包含大小写字母和数字） */
  strongPassword: {
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
    message: '密码至少8位，需包含大小写字母和数字'
  },
  
  /** 中文验证 */
  chinese: {
    pattern: /^[\u4e00-\u9fa5]+$/,
    message: '请输入中文字符'
  },
  
  /** 英文验证 */
  english: {
    pattern: /^[a-zA-Z]+$/,
    message: '请输入英文字符'
  },
  
  /** 字母数字验证 */
  alphanumeric: {
    pattern: /^[a-zA-Z0-9]+$/,
    message: '请输入字母或数字'
  }
}

/**
 * 验证单个值
 * @param value 要验证的值
 * @param rules 验证规则数组
 * @returns 验证结果
 */
export function validateValue(value: any, rules: ValidationRule[]): ValidationResult {
  // 如果没有规则，直接返回有效
  if (!rules || rules.length === 0) {
    return { valid: true }
  }
  
  for (const rule of rules) {
    const result = validateSingleRule(value, rule)
    if (!result.valid) {
      return result
    }
  }
  
  return { valid: true }
}

/**
 * 验证单个规则
 * @param value 要验证的值
 * @param rule 验证规则
 * @returns 验证结果
 */
export function validateSingleRule(value: any, rule: ValidationRule): ValidationResult {
  const stringValue = String(value || '')
  const numericValue = Number(value)
  
  // 必填验证
  if (rule.required && (!value || stringValue.trim() === '')) {
    return {
      valid: false,
      message: rule.message || '此字段为必填项',
      type: 'required'
    }
  }
  
  // 如果值为空且不是必填，跳过其他验证
  if (!value || stringValue.trim() === '') {
    return { valid: true }
  }
  
  // 最小长度验证
  if (rule.minLength !== undefined && stringValue.length < rule.minLength) {
    return {
      valid: false,
      message: rule.message || `最少需要${rule.minLength}个字符`,
      type: 'minLength'
    }
  }
  
  // 最大长度验证
  if (rule.maxLength !== undefined && stringValue.length > rule.maxLength) {
    return {
      valid: false,
      message: rule.message || `最多允许${rule.maxLength}个字符`,
      type: 'maxLength'
    }
  }
  
  // 最小值验证
  if (rule.min !== undefined && !isNaN(numericValue) && numericValue < rule.min) {
    return {
      valid: false,
      message: rule.message || `值不能小于${rule.min}`,
      type: 'min'
    }
  }
  
  // 最大值验证
  if (rule.max !== undefined && !isNaN(numericValue) && numericValue > rule.max) {
    return {
      valid: false,
      message: rule.message || `值不能大于${rule.max}`,
      type: 'max'
    }
  }
  
  // 正则表达式验证
  if (rule.pattern && !rule.pattern.test(stringValue)) {
    return {
      valid: false,
      message: rule.message || '格式不正确',
      type: 'pattern'
    }
  }
  
  // 自定义验证函数
  if (rule.validator) {
    const result = rule.validator(value)
    if (result === false) {
      return {
        valid: false,
        message: rule.message || '验证失败',
        type: 'custom'
      }
    }
    if (typeof result === 'string') {
      return {
        valid: false,
        message: result,
        type: 'custom'
      }
    }
  }
  
  return { valid: true }
}

/**
 * 验证表单数据
 * @param data 表单数据对象
 * @param schema 验证模式对象
 * @returns 验证结果对象
 */
export function validateForm(
  data: Record<string, any>,
  schema: Record<string, ValidationRule[]>
): Record<string, ValidationResult> {
  const results: Record<string, ValidationResult> = {}
  
  Object.keys(schema).forEach(key => {
    const value = data[key]
    const rules = schema[key]
    results[key] = validateValue(value, rules)
  })
  
  return results
}

/**
 * 检查表单是否有效
 * @param results 验证结果对象
 * @returns 是否有效
 */
export function isFormValid(results: Record<string, ValidationResult>): boolean {
  return Object.values(results).every(result => result.valid)
}

/**
 * 获取表单错误消息
 * @param results 验证结果对象
 * @returns 错误消息数组
 */
export function getFormErrors(results: Record<string, ValidationResult>): string[] {
  return Object.values(results)
    .filter(result => !result.valid)
    .map(result => result.message!)
}

/**
 * 创建验证规则构建器
 */
export class ValidationRuleBuilder {
  private rules: ValidationRule[] = []
  
  /**
   * 添加必填规则
   */
  required(message?: string): this {
    this.rules.push({
      required: true,
      message: message || '此字段为必填项'
    })
    return this
  }
  
  /**
   * 添加最小长度规则
   */
  minLength(length: number, message?: string): this {
    this.rules.push({
      minLength: length,
      message: message || `最少需要${length}个字符`
    })
    return this
  }
  
  /**
   * 添加最大长度规则
   */
  maxLength(length: number, message?: string): this {
    this.rules.push({
      maxLength: length,
      message: message || `最多允许${length}个字符`
    })
    return this
  }
  
  /**
   * 添加长度范围规则
   */
  lengthRange(min: number, max: number, message?: string): this {
    this.rules.push({
      minLength: min,
      message: message || `长度应在${min}-${max}个字符之间`
    })
    this.rules.push({
      maxLength: max,
      message: message || `长度应在${min}-${max}个字符之间`
    })
    return this
  }
  
  /**
   * 添加最小值规则
   */
  min(value: number, message?: string): this {
    this.rules.push({
      min: value,
      message: message || `值不能小于${value}`
    })
    return this
  }
  
  /**
   * 添加最大值规则
   */
  max(value: number, message?: string): this {
    this.rules.push({
      max: value,
      message: message || `值不能大于${value}`
    })
    return this
  }
  
  /**
   * 添加数值范围规则
   */
  range(min: number, max: number, message?: string): this {
    this.rules.push({
      min,
      message: message || `值应在${min}-${max}之间`
    })
    this.rules.push({
      max,
      message: message || `值应在${min}-${max}之间`
    })
    return this
  }
  
  /**
   * 添加正则表达式规则
   */
  pattern(regex: RegExp, message?: string): this {
    this.rules.push({
      pattern: regex,
      message: message || '格式不正确'
    })
    return this
  }
  
  /**
   * 添加邮箱验证规则
   */
  email(message?: string): this {
    this.rules.push({
      ...ValidationRules.email,
      message: message || ValidationRules.email.message
    })
    return this
  }
  
  /**
   * 添加手机号验证规则
   */
  phone(message?: string): this {
    this.rules.push({
      ...ValidationRules.phone,
      message: message || ValidationRules.phone.message
    })
    return this
  }
  
  /**
   * 添加URL验证规则
   */
  url(message?: string): this {
    this.rules.push({
      ...ValidationRules.url,
      message: message || ValidationRules.url.message
    })
    return this
  }
  
  /**
   * 添加数字验证规则
   */
  number(message?: string): this {
    this.rules.push({
      ...ValidationRules.number,
      message: message || ValidationRules.number.message
    })
    return this
  }
  
  /**
   * 添加自定义验证规则
   */
  custom(validator: (value: any) => boolean | string, message?: string): this {
    this.rules.push({
      validator,
      message: message || '验证失败'
    })
    return this
  }
  
  /**
   * 构建验证规则数组
   */
  build(): ValidationRule[] {
    return [...this.rules]
  }
  
  /**
   * 重置规则
   */
  reset(): this {
    this.rules = []
    return this
  }
}

/**
 * 创建验证规则构建器
 */
export function createValidationRules(): ValidationRuleBuilder {
  return new ValidationRuleBuilder()
}

/**
 * 常用验证规则快捷方法
 */
export const Rules = {
  /** 必填 */
  required: (message?: string) => createValidationRules().required(message).build(),
  
  /** 邮箱 */
  email: (required = false, message?: string) => {
    const builder = createValidationRules().email(message)
    return required ? builder.required().build() : builder.build()
  },
  
  /** 手机号 */
  phone: (required = false, message?: string) => {
    const builder = createValidationRules().phone(message)
    return required ? builder.required().build() : builder.build()
  },
  
  /** 密码 */
  password: (minLength = 6, required = true) => {
    const builder = createValidationRules().minLength(minLength)
    return required ? builder.required().build() : builder.build()
  },
  
  /** 确认密码 */
  confirmPassword: (originalPassword: string, required = true) => {
    const builder = createValidationRules().custom(
      (value) => value === originalPassword || '两次输入的密码不一致'
    )
    return required ? builder.required().build() : builder.build()
  },
  
  /** 数字范围 */
  numberRange: (min: number, max: number, required = false) => {
    const builder = createValidationRules().number().range(min, max)
    return required ? builder.required().build() : builder.build()
  },
  
  /** 文本长度 */
  textLength: (min: number, max: number, required = false) => {
    const builder = createValidationRules().lengthRange(min, max)
    return required ? builder.required().build() : builder.build()
  }
}