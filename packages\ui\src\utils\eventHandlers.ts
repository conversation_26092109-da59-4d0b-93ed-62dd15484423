import { nextTick } from 'vue'

/**
 * 通用事件处理器接口
 * 定义组件事件处理的标准接口
 */
export interface EventHandlers {
  /** 聚焦事件处理器 */
  handleFocus?: (event: FocusEvent) => void
  /** 失焦事件处理器 */
  handleBlur?: (event: FocusEvent) => void
  /** 输入事件处理器 */
  handleInput?: (event: Event) => void
  /** 变化事件处理器 */
  handleChange?: (event: Event) => void
  /** 点击事件处理器 */
  handleClick?: (event: MouseEvent) => void
  /** 键盘事件处理器 */
  handleKeydown?: (event: KeyboardEvent) => void
}

/**
 * 防抖函数
 * 在指定时间内只执行最后一次调用
 * @param func 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: ReturnType<typeof setTimeout>
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

/**
 * 节流函数
 * 在指定时间内最多执行一次
 * @param func 要节流的函数
 * @param delay 节流时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      func(...args)
    }
  }
}

/**
 * 创建通用的聚焦处理器
 * @param emit 事件发射器
 * @param setState 状态设置函数
 * @returns 聚焦事件处理器
 */
export function createFocusHandler(
  emit: (event: string, ...args: any[]) => void,
  setState?: (focused: boolean) => void
) {
  return (event: FocusEvent) => {
    if (setState) {
      setState(true)
    }
    emit('focus', event)
  }
}

/**
 * 创建通用的失焦处理器
 * @param emit 事件发射器
 * @param setState 状态设置函数
 * @param formHandler 表单处理函数
 * @returns 失焦事件处理器
 */
export function createBlurHandler(
  emit: (event: string, ...args: any[]) => void,
  setState?: (focused: boolean) => void,
  formHandler?: () => void
) {
  return (event: FocusEvent) => {
    if (setState) {
      setState(false)
    }
    if (formHandler) {
      formHandler()
    }
    emit('blur', event)
  }
}

/**
 * 创建通用的输入处理器
 * @param emit 事件发射器
 * @param updateValue 值更新函数
 * @param isComposing 是否正在输入法组合
 * @returns 输入事件处理器
 */
export function createInputHandler(
  emit: (event: string, ...args: any[]) => void,
  updateValue: (value: any) => void,
  isComposing?: { value: boolean }
) {
  return (event: Event) => {
    const target = event.target as HTMLInputElement | HTMLTextAreaElement
    const value = target.value
    
    // 如果正在输入法组合中，不处理输入
    if (isComposing?.value) return
    
    emit('input', value)
    updateValue(value)
  }
}

/**
 * 创建通用的变化处理器
 * @param emit 事件发射器
 * @returns 变化事件处理器
 */
export function createChangeHandler(
  emit: (event: string, ...args: any[]) => void
) {
  return (event: Event) => {
    const target = event.target as HTMLInputElement | HTMLTextAreaElement
    emit('change', target.value)
  }
}

/**
 * 创建通用的点击处理器
 * @param emit 事件发射器
 * @param disabled 是否禁用
 * @param loading 是否加载中
 * @param customHandler 自定义处理逻辑
 * @returns 点击事件处理器
 */
export function createClickHandler(
  emit: (event: string, ...args: any[]) => void,
  disabled?: boolean,
  loading?: boolean,
  customHandler?: (event: MouseEvent) => void
) {
  return (event: MouseEvent) => {
    // 如果禁用或加载中，不处理点击
    if (disabled || loading) {
      event.preventDefault()
      return
    }
    
    if (customHandler) {
      customHandler(event)
    }
    
    emit('click', event)
  }
}

/**
 * 创建键盘事件处理器
 * @param emit 事件发射器
 * @param keyHandlers 特定按键的处理器映射
 * @returns 键盘事件处理器
 */
export function createKeydownHandler(
  emit: (event: string, ...args: any[]) => void,
  keyHandlers?: Record<string, (event: KeyboardEvent) => void>
) {
  return (event: KeyboardEvent) => {
    // 处理特定按键
    if (keyHandlers && keyHandlers[event.key]) {
      keyHandlers[event.key](event)
    }
    
    emit('keydown', event)
  }
}

/**
 * 创建输入法组合事件处理器
 * @param isComposing 输入法组合状态引用
 * @param inputHandler 输入处理器
 * @returns 输入法组合事件处理器对象
 */
export function createCompositionHandlers(
  isComposing: { value: boolean },
  inputHandler?: (event: Event) => void
) {
  return {
    /**
     * 输入法组合开始
     */
    handleCompositionStart: () => {
      isComposing.value = true
    },
    
    /**
     * 输入法组合更新
     */
    handleCompositionUpdate: () => {
      isComposing.value = true
    },
    
    /**
     * 输入法组合结束
     */
    handleCompositionEnd: (event: CompositionEvent) => {
      isComposing.value = false
      // 组合结束后触发输入处理
      if (inputHandler) {
        inputHandler(event)
      }
    }
  }
}

/**
 * 创建元素操作工具
 * @param elementRef 元素引用
 * @returns 元素操作方法
 */
export function createElementOperations(elementRef: { value?: HTMLElement }) {
  return {
    /**
     * 聚焦元素
     */
    focus: () => {
      nextTick(() => {
        if (elementRef.value) {
          elementRef.value.focus()
        }
      })
    },
    
    /**
     * 失焦元素
     */
    blur: () => {
      nextTick(() => {
        if (elementRef.value) {
          elementRef.value.blur()
        }
      })
    },
    
    /**
     * 选中元素内容（仅适用于输入元素）
     */
    select: () => {
      nextTick(() => {
        if (elementRef.value && 'select' in elementRef.value) {
          (elementRef.value as HTMLInputElement).select()
        }
      })
    }
  }
}

/**
 * 事件处理器工厂
 * 根据配置创建完整的事件处理器集合
 */
export class EventHandlerFactory {
  private emit: (event: string, ...args: any[]) => void
  private setState?: (key: string, value: any) => void
  private updateValue?: (value: any) => void
  private formHandler?: () => void
  
  constructor(options: {
    emit: (event: string, ...args: any[]) => void
    setState?: (key: string, value: any) => void
    updateValue?: (value: any) => void
    formHandler?: () => void
  }) {
    this.emit = options.emit
    this.setState = options.setState
    this.updateValue = options.updateValue
    this.formHandler = options.formHandler
  }
  
  /**
   * 创建完整的事件处理器集合
   */
  createHandlers(config: {
    disabled?: boolean
    loading?: boolean
    isComposing?: { value: boolean }
    keyHandlers?: Record<string, (event: KeyboardEvent) => void>
    customClickHandler?: (event: MouseEvent) => void
  } = {}): EventHandlers {
    const handlers: EventHandlers = {}
    
    // 聚焦处理器
    handlers.handleFocus = createFocusHandler(
      this.emit,
      this.setState ? (focused) => this.setState!('focused', focused) : undefined
    )
    
    // 失焦处理器
    handlers.handleBlur = createBlurHandler(
      this.emit,
      this.setState ? (focused) => this.setState!('focused', focused) : undefined,
      this.formHandler
    )
    
    // 输入处理器
    if (this.updateValue) {
      handlers.handleInput = createInputHandler(
        this.emit,
        this.updateValue,
        config.isComposing
      )
    }
    
    // 变化处理器
    handlers.handleChange = createChangeHandler(this.emit)
    
    // 点击处理器
    handlers.handleClick = createClickHandler(
      this.emit,
      config.disabled,
      config.loading,
      config.customClickHandler
    )
    
    // 键盘处理器
    handlers.handleKeydown = createKeydownHandler(
      this.emit,
      config.keyHandlers
    )
    
    return handlers
  }
}