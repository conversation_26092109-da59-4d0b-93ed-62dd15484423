export type FormItemValidateState = 'success' | 'error' | 'warning' | 'validating' | ''

export interface FormItemRule {
  required?: boolean
  message?: string
  trigger?: 'blur' | 'change' | 'submit'
  type?: 'string' | 'number' | 'boolean' | 'method' | 'regexp' | 'integer' | 'float' | 'array' | 'object' | 'enum' | 'date' | 'url' | 'hex' | 'email'
  min?: number
  max?: number
  len?: number
  pattern?: RegExp
  validator?: (rule: FormItemRule, value: any, callback: (error?: string | Error) => void) => void
  asyncValidator?: (rule: FormItemRule, value: any, callback: (error?: string | Error) => void) => Promise<void>
}

export interface FormItemContext {
  prop?: string
  size: 'small' | 'default' | 'large'
  validateState: FormItemValidateState
  validate: (trigger?: string, callback?: Function) => Promise<boolean>
  resetField: () => void
  clearValidate: () => void
  scrollIntoView: () => void
}

export interface FormContext {
  model?: Record<string, any>
  rules?: Record<string, FormItemRule | FormItemRule[]>
  labelPosition?: 'right' | 'left' | 'top'
  labelWidth?: string
  labelSuffix?: string
  hideLabel?: boolean
  inline?: boolean
  disabled?: boolean
  size?: 'small' | 'default' | 'large'
  showMessage?: boolean
  inlineMessage?: boolean
  statusIcon?: boolean
  validateOnRuleChange?: boolean
  hideRequiredAsterisk?: boolean
  scrollToError?: boolean
  addFormItem?: (item: FormItemContext) => void
  removeFormItem?: (prop: string) => void
  emit?: (event: string, ...args: any[]) => void
} 