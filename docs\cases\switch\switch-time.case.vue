<text>
> 设置 `time` 属性后，开关中间会显示倒计时数字
</text>

<template>
    <div class="button-demo-row">
        <sp-switch v-model="value1" :time="60" onText="开启" offText="关闭" />
        <sp-switch v-model="value2" :time="30" size="large" />
        <sp-switch v-model="value3" :time="10" square onOutText="倒计时开关" />
    </div>
</template>

<script setup>
import { ref } from 'vue'

const value1 = ref(false)
const value2 = ref(false)
const value3 = ref(false)
</script>

<style scoped>
.switch-demo-row {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-start;
}
</style>